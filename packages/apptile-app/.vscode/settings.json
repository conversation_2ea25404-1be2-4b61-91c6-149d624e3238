{"cSpell.words": ["appbridge", "APPCONFIG", "APPSAVE", "Appsflyer", "apptile", "apptilepreview", "apptileprod", "apptileprvw", "bootsplash", "Bridgeinstance", "clsx", "datacenter", "datasource", "deeplink", "deeplinkUrl", "demoapptileprvw", "exopackage", "firstname", "gorhom", "iconfont", "idkey", "lastname", "middlewares", "moengage", "NSURL", "onesignal", "pbxproj", "Pressable", "qrcode", "queryrunner", "reactnativetsproject", "ReactNativeTSProjeect", "RNFS", "Roboto", "semibold", "Tappable", "topoCache", "TRBL", "videojs", "xcodeproj"], "cSpell.ignorePaths": ["ios/ReactNativeTSProject.xcodeproj/project.pbxproj"]}