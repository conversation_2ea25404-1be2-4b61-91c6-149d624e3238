android {
    packagingOptions {
        exclude 'META-INF/*.kotlin_module'
    }
}

dependencies {
    implementation "com.firework:sdk:+"
    // Uncomment the next line if you want to integrate the single-host live stream
    implementation "com.firework.external.livestream:singleHostPlayer:+"
    // Uncomment the next line if you want to integrate the multi-host live stream
    implementation "com.firework.external.livestream:multiHostPlayer:+"
}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'com.firework' && details.requested.name == 'sdk') {
            details.useVersion rootProject.ext.get("fwNativeVersion")
        }
        if (details.requested.group == 'com.firework.external.livestream' && details.requested.name == 'singleHostPlayer') {
            details.useVersion rootProject.ext.get("fwNativeVersion")
        }
        if (details.requested.group == 'com.firework.external.livestream' && details.requested.name == 'multiHostPlayer') {
            details.useVersion rootProject.ext.get("fwNativeVersion")
        }
    }
}