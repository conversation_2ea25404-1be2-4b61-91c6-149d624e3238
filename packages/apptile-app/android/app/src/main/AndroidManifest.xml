<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- ForMoengae (Don't remove) <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> ForMoengaeEnd -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove" />
    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:usesCleartextTraffic="true"
        android:theme="@style/AppTheme">

        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
            See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher_round" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
            notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <!-- <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/status_bar_color" /> -->
        <!-- ForFBIntegration (Don't remove) <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" /> ForFBIntegrationEnd -->
        <!-- ForFBIntegration (Don't remove) <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token" /> ForFBIntegrationEnd -->

        <!-- ForCleverTap (Don't remove) <meta-data android:name="CLEVERTAP_ACCOUNT_ID" android:value="cleverTap_id"/> ForCleverTapEnd -->
        <!-- ForCleverTap (Don't remove) <meta-data android:name="CLEVERTAP_TOKEN" android:value="cleverTap_token"/> ForCleverTapEnd -->
        <!-- ForCleverTap (Don't remove) <meta-data android:name="CLEVERTAP_REGION" android:value="cleverTap_region"/> ForCleverTapEnd -->
        <!-- ForFirework (Don't remove) <meta-data android:name="Firework:Appid" android:value="__firework_app_id__"/> ForFireworkEnd -->
            
        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:launchMode="singleTask"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        <!-- PreviewAppRequirement (Don't remove) </activity> PreviewAppRequirementEnd -->
        <!-- PreviewAppRequirement (Don't remove) <activity PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:name=".PreviewActivity" PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:label="@string/app_name" PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode" PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:launchMode="singleTask" PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:exported="true" PreviewAppRequirementEnd -->
            <!-- PreviewAppRequirement (Don't remove) android:windowSoftInputMode="adjustResize"> PreviewAppRequirementEnd -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="demoapptileprvw"/>
            </intent-filter>
        </activity>
        <activity
          android:name=".PIPActivity"
          android:theme="@style/Theme.Transparent"
          android:label="@string/pip_name"
          android:supportsPictureInPicture="true"
          android:configChanges=
              "keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
          android:launchMode="singleTask"
          android:windowSoftInputMode="adjustResize"
          android:exported="true">
        </activity>
    </application>
    <!-- Linking config for Android 11 (SDK 30)  -->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SENDTO" />
            <data android:scheme="mailto" />
        </intent>
    </queries>
</manifest>
