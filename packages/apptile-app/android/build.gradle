buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.0"
        okhttp = '4.12.0'
    }
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs "$rootDir/libs"
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://storage.googleapis.com/logrocket-maven/" }
        // jcenter() {
        //     content {
        //         includeModule("com.yqritc", "android-scalablevideoview")
        //     }
        // }
        flatDir {
            dirs "$rootDir/libs"
        }
    }
}

apply plugin: "com.facebook.react.rootproject"