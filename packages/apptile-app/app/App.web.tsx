/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * Generated with the TypeScript template
 * https://github.com/react-native-community/react-native-template-typescript
 *
 * @format
 */

import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';

import {configureDatasources, REGISTER_PLUGINS} from '../web/actions/editorActions';
import useMountEffect from '../web/common/hooks/useMountEffect';
import {initApptileConfig} from './common/apptile/ApptileConfigManager';
import {initPlugins} from './plugins/initPlugins';
import {RegisteredPlugins} from 'apptile-core';
import {LocalStorage as localStorage} from 'apptile-core';
import {AppContainer} from 'apptile-core';
import {DispatchActions} from 'apptile-core';
import {EditorRootState} from '../web/store/EditorRootState';
import {ApptileAnimationsContextProvider} from 'apptile-core';

const App = () => {
  const dispatch = useDispatch();
  const [apptileInit, setApptileInit] = useState(false);

  const appIdSelector = (state: EditorRootState) => state.apptile.appId;
  const appConfigFetchedReduxStateSelector = (state: EditorRootState) => state.appConfig.isFetched;

  const appId = useSelector(appIdSelector);
  const appConfigFetchedReduxState = useSelector(appConfigFetchedReduxStateSelector);

  useMountEffect(() => {
    initPlugins();
    const pluginsLoaded = [...RegisteredPlugins];
    dispatch({
      type: REGISTER_PLUGINS,
      payload: pluginsLoaded,
    });
  });

  useEffect(() => {
    initApptileConfig().then(() => {
      setApptileInit(true);
    });

    return () => {
      dispatch({
        type: DispatchActions.CLEAN_UP_APP,
      });
    };
  }, [dispatch, setApptileInit]);

  useEffect(() => {
    if (appConfigFetchedReduxState && appId) {
      localStorage.setNamespace(appId as string);
      dispatch(configureDatasources(appId as string));
    }
  }, [appId, appConfigFetchedReduxState, dispatch]);

  return (
    <ApptileAnimationsContextProvider>
      <AppContainer apptileInit={apptileInit} />
    </ApptileAnimationsContextProvider>
  );
};

export default App;
