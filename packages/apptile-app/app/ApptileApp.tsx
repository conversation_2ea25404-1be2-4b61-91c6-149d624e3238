import React, {lazy, Suspense} from 'react';
import {ActivityIndicator, View} from 'react-native';

const ApptileAppComponent = lazy(
  () =>
    import(
      /* webpackChunkName: "ApptileApp",
        webpackPreload: true */
      './App'
    ),
);

const AsyncApptileApp = (props): JSX.Element => {
  return (
    <Suspense
      fallback={
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" />
        </View>
      }>
      <ApptileAppComponent {...props} />
    </Suspense>
  );
};

const ApptileApp = AsyncApptileApp;
export default ApptileApp;
