import {IApptileAnalyticsEvent, getAppConstants, getUniqueDeviceId, logger} from 'apptile-core';
import {getOneSignalUserId, ONESIGNAL_APP_ID} from '../onesignalAnalytics/initOneSignal';
import axios from 'axios';

// Events API endpoints
const EVENTS_API_URL = 'https://events-api.apptile.io/event';
const ANALYTICS_API_URL = 'https://events-api.apptile.io/analytics';

interface NotificationEvent {
  eventType: string;
  appId: string;
  oneSignalUserId: string;
  oneSignalAppId: string;
  deviceId: string;
  payload: Record<string, any>;
}

interface AnalyticsEvent {
  eventType: string;
  [key: string]: any;
}

export const sendToApptileNotificationAnalytics = async (
  eventName: string,
  eventProperty: IApptileAnalyticsEvent['properties'],
) => {
  try {
    if (!eventName) return;

    eventProperty = eventProperty?.toJS ? eventProperty.toJS() : eventProperty ?? {};

    // Always send to analytics route (no filtering)
    await sendToAnalyticsRoute(eventName, eventProperty);

    // Filter events for notification route - only send allowed event types
    // if (!ALLOWED_EVENT_TYPES.includes(eventName)) {
    //   logger.info(`Event type '${eventName}' not in allowed list, skipping notification analytics`);
    //   return;
    // }

    // Send to notification route
    await sendToNotificationRoute(eventName, eventProperty);
  } catch (err) {
    logger.error('Error in sendToApptileNotificationAnalytics:', err);
  }
};

/**
 * Send event to analytics route - sends all events as-is with eventType added
 */
const sendToAnalyticsRoute = async (eventName: string, eventProperty: Record<string, any>) => {
  try {
    // Create analytics event payload
    const analyticsEvent: AnalyticsEvent = {
      eventType: eventName,
      ...eventProperty,
    };

    logger.info('Sending analytics event:', analyticsEvent);

    // Send to analytics API
    const response = await axios.post(ANALYTICS_API_URL, analyticsEvent, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    logger.info('Analytics event sent successfully:', response.data);
  } catch (err) {
    logger.error('Error sending analytics event to apptile:', err);
  }
};

/**
 * Send event to notification route - requires OneSignal data and filtering
 */
const sendToNotificationRoute = async (eventName: string, eventProperty: Record<string, any>) => {
  try {
    // Get required data for notification route
    const appId = getAppConstants().APPTILE_APP_ID;
    const oneSignalUserId = await getOneSignalUserId();
    const deviceId = getUniqueDeviceId();

    // Validate required data
    if (!appId) {
      logger.error('App ID not available, cannot send notification event');
      return;
    }

    if (!ONESIGNAL_APP_ID || !oneSignalUserId) {
      logger.error('OneSignal User ID not available, cannot send notification event');
      return;
    }

    // if (!deviceId) {
    //   logger.error('Device ID not available, cannot send notification event');
    //   return;
    // }

    // Create the notification event payload
    const notificationEvent: NotificationEvent = {
      eventType: eventName,
      appId: appId as string,
      oneSignalUserId: oneSignalUserId,
      oneSignalAppId: ONESIGNAL_APP_ID,
      deviceId: deviceId || '',
      payload: eventProperty,
    };

    logger.info('Sending notification event:', notificationEvent);

    // Send to notification API
    const response = await axios.post(EVENTS_API_URL, notificationEvent, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    logger.info('Notification event sent successfully to apptile:', response.data);
  } catch (err) {
    logger.error('Error sending notification event:', err);
  }
};
