import CleverTap from 'clevertap-react-native';
import {triggerCustomEventListener} from 'apptile-core';

export const initCleverTap = () => {
  createCleverTapNotificationChannel();
  CleverTap.addListener(CleverTap.CleverTapPushNotificationClicked, (e: any) => {
    if (e?.wzrk_dl) {
      handleDeeplink(e?.wzrk_dl);
    }
  });
};

const handleDeeplink = (deeplink?: string) => {
  if (!deeplink) return;
  try {
    triggerCustomEventListener('deeplink_request', deeplink);
  } catch (e) {
    toast.show('Failed to navigate to requested screen', {type: 'error', placement: 'top', duration: 1340});
  }
};

const createCleverTapNotificationChannel = () => {
  CleverTap.createNotificationChannel(
    'default', //channelId
    'Default Notifications', //channelName
    'Default Channel for Notifications', //channelDescription
    5, //NotificationPriority
    true,
  );
  //CleverTap.createNotificationChannelGroup('apptileGroup', 'demo-users');
  CleverTap.registerForPush(); // this need to be done ios
};
