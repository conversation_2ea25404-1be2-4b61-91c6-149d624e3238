import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';
export class Firebase {
  static async getAppInstanceId() {}

  static async setUser(_userId: string) {}

  static async sendEvent(eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) {
    try {
      if (!eventName) return;
      eventProperty = eventProperty?.toJS ? eventProperty.toJS() : eventProperty ?? {};
      logger.info('sendToFirebase', eventName, eventProperty);
    } catch (err) {
      logger.error(err);
    }
  }
}
