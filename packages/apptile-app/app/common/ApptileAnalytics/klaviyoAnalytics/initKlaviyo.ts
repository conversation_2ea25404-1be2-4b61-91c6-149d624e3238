import Klaviyo from 'react-native-klaviyo';
import {Platform} from 'react-native';
import {ApptileLocalSettings} from 'apptile-core';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {COMPANY_ID} from '.';

const initKlaviyo = () => {
  if (COMPANY_ID === '<KLAVIYO_COMPANY_ID>') return;

  if (Platform.OS === 'ios') {
    PushNotificationIOS.getApplicationIconBadgeNumber(badgeCount => {
      if (badgeCount > 0) {
        PushNotificationIOS.setApplicationIconBadgeNumber(0);
      }
    });
  }

  if (Platform.OS === 'android') {
    PushNotification.getApplicationIconBadgeNumber(badgeCount => {
      if (badgeCount > 0) {
        PushNotification.setApplicationIconBadgeNumber(0);
      }
    });
  }

  Klaviyo.initializeKlaviyoSDK(COMPANY_ID);

  if (Platform.OS === 'ios' && ApptileLocalSettings.APNSToken) {
    Klaviyo.setPushToken(ApptileLocalSettings.APNSToken);
  }

  // const deviceId = getUniqueDeviceId();
  // KlaviyoRN.setExternalId(deviceId);
};

export default initKlaviyo;
