import {LogLevel, OneSignal} from 'react-native-onesignal';

import {triggerCustomEventListener} from 'apptile-core';
import {Alert, Clipboard, Platform} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {store} from 'apptile-core';

const NOTIFICATION_DENIED_AT = 'notificationDeniedAt';

const getNotificationReOptInPeriod = () => {
  const DAYS_MS = 24 * 60 * 60 * 1000;
  const onesignalNotificationReOptInPeriod: number = store
    ?.getState()
    ?.appConfig?.current?.plugins?.get('Apptile')
    ?.config.get('onesignalNotficationReOptInPeriod');
  if (onesignalNotificationReOptInPeriod == null) {
    return 0;
  }
  return onesignalNotificationReOptInPeriod * DAYS_MS;
};
const handleDeeplink = (deeplink?: string) => {
  if (!deeplink) return;
  try {
    triggerCustomEventListener('deeplink_request', deeplink);
  } catch (e) {
    toast.show('Failed to navigate to requested screen', {type: 'error', placement: 'top', duration: 1340});
  }
};

OneSignal.Debug.setLogLevel(LogLevel.Verbose);

const ONESIGNAL_APP_ID = '<OneSignalAppId>';
OneSignal.initialize(ONESIGNAL_APP_ID);

OneSignal.Notifications.addEventListener('click', event => {
  logger.info('OneSignal: notification clicked:', event);
  new Promise(resolve => {
    setTimeout(() => {
      resolve(null);
    }, 1000);
  }).then(() => {
    if (Platform.OS === 'ios') handleDeeplink(event.notification.launchURL);
    else handleDeeplink(event.notification.launchUrl);
  });
});

const initOneSignal = async () => {
  await OneSignal.Notifications.canRequestPermission().then(async can => {
    const deniedAt = await AsyncStorage.getItem(NOTIFICATION_DENIED_AT);
    const now = new Date();
    if (!deniedAt || now - parseInt(deniedAt) >= getNotificationReOptInPeriod()) {
      await OneSignal.Notifications.requestPermission(true);
      let permissionGranted = await OneSignal.Notifications.canRequestPermission();
      if (!permissionGranted) {
        await AsyncStorage.setItem(NOTIFICATION_DENIED_AT, now.toString());
      } else {
        await AsyncStorage.removeItem(NOTIFICATION_DENIED_AT);
      }
    }
  });
};

const getOneSignalUserId = async () => {
  return await OneSignal.User.getOnesignalId();
};

async function getNotificationInfo() {
  console.log('getNotificationInfo Init');
  OneSignal.User.getOnesignalId()
    .then(oneSignalId => {
      if (oneSignalId) {
        Alert.alert('Device Info', `SubscriberId: ${oneSignalId}`, [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Copy SubscriberId',
            onPress: () => {
              Clipboard.setString(`${oneSignalId}`);
            },
          },
        ]);
      } else {
        Alert.alert('Unable to get Subscribption Id', 'Please try again later');
      }
    })
    .catch(() => Alert.alert('Unable to get Device Info', 'Please try again later'));
}

export {initOneSignal, getNotificationInfo, getOneSignalUserId, ONESIGNAL_APP_ID};
