import {Alert} from 'react-native';
import semver from 'semver';

import {getConfigValue} from 'apptile-core';
import {BundleApi} from 'apptile-core';
import {setActiveBundle, registerBundle, reloadBundle, getActiveBundle} from 'apptile-core';

export async function checkBundleUpdate(shouldAutoReloadBundle?: boolean, reloadOnAppFocusChange?: boolean) {
  const baseFrameworkVersion = (await getConfigValue('APPTILE_BASE_FRAMEWORK_VERSION')) as string;
  const activeBundle = await getActiveBundle();
  let activeFrameworkVersion = activeBundle || baseFrameworkVersion;
  if (semver.gt(baseFrameworkVersion, activeFrameworkVersion)) activeFrameworkVersion = baseFrameworkVersion;
  if (!activeFrameworkVersion) return logger.warn("Couldn't figure out the base framework version");
  const latestVersion = await BundleApi.getLatestBundle(activeFrameworkVersion);
  if (!latestVersion) return logger.warn("Couldn't fetch latest framework version");
  if (latestVersion.version === activeFrameworkVersion) {
    if (reloadOnAppFocusChange) return reloadBundle();
    return logger.info('You seem to have latest version running already');
  }
  return updateToBundle(
    latestVersion.version,
    latestVersion.bundle_url,
    shouldAutoReloadBundle || reloadOnAppFocusChange,
  );
}

function updateToBundle(frameworkVersion: string, bundleURL: string, shouldAutoReloadBundle?: boolean) {
  return BundleApi.download(frameworkVersion, bundleURL).then(() => {
    registerBundle(frameworkVersion, [BundleApi.getBundlesPath(frameworkVersion), BundleApi.getFileName()].join('/'));
    return getActiveBundle().then(activeBundle => {
      if (activeBundle !== frameworkVersion) {
        setActiveBundle(frameworkVersion);
        if (shouldAutoReloadBundle) return reloadBundle();
        Alert.alert('New OTA update!', 'Switch to our latest and greatest experience yet', [
          {text: 'Later', style: 'cancel'},
          {text: 'Sure!', onPress: () => reloadBundle()},
        ]);
      }
    });
  });
}
