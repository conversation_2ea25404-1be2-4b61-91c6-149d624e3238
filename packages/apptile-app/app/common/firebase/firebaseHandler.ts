import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {checkNotificationPermission, showLocalNotification} from './notificationHelper';

import {ApptileLocalSettings} from 'apptile-core';
import {triggerCustomEventListener, getUrlFromRemoteMessage} from 'apptile-core';
import {Platform} from 'react-native';

PushNotification.configure({
  onNotification: function (notification) {
    logger.info('NOTIFICATION:', notification);
    try {
      const url = getUrlFromRemoteMessage(notification);
      if (!url) return;
      triggerCustomEventListener('deeplink_request', url);
    } catch (e) {
      toast.show('Failed to navigate to requested screen', {type: 'error', placement: 'top', duration: 1340});
    }
    notification.finish(PushNotificationIOS.FetchResult.NoData);
  },
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },
  popInitialNotification: true,
  requestPermissions: true,
});

messaging().setBackgroundMessageHandler(async remoteMessage => {
  logger.info('Received in background', remoteMessage);
});

export async function requestNotificationPermission() {
  const enabled = await checkNotificationPermission();

  if (enabled) {
    if (Platform.OS === 'ios') {
      const apns = await messaging().getAPNSToken();
      if (apns) ApptileLocalSettings.APNSToken = apns;
    }

    return messaging()
      .getToken()
      .then(token => {
        ApptileLocalSettings.pushToken = token;
        const unsubscribe = messaging().onMessage(async notification => {
          showLocalNotification(notification);
        });
        return unsubscribe;
      })
      .catch((error: any) => {
        logger.error('NOTIFICATION Authorization ERROR :', error);
      });
  }
}
