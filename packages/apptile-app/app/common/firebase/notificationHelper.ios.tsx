import {FirebaseMessagingTypes} from '@react-native-firebase/messaging';
import PushNotificationIos from '@react-native-community/push-notification-ios';
import messaging from '@react-native-firebase/messaging';

export const showLocalNotification = async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
  PushNotificationIos.addNotificationRequest({
    id: remoteMessage.messageId as string,
    body: remoteMessage.notification?.body,
    title: remoteMessage.notification?.title,
    userInfo: remoteMessage?.data,
  });
};

export const checkNotificationPermission = async () => {
  // The following line is altered during Build if OneSignal is being initialized, please go through script if altering the code
  let authStatus = await messaging().requestPermission(); // RemoveRequestWhenOneSignalInitialized
  authStatus = await messaging().hasPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED || authStatus === messaging.AuthorizationStatus.PROVISIONAL;
  logger.info('Notification Authorization status:', authStatus);
  return enabled;
};
