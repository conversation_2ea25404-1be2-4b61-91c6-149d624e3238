import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textStyle: {
    fontSize: 16,
  },
  icon: {
    fontSize: 48,
    marginBottom: 8,
  },
});

export const EmptyPlaceholder = ({message}) => {
  return (
    <View style={styles.container}>
      <MaterialCommunityIcons style={styles.icon} name="contain" color={message ? 'white' : '#808080'} />
      <Text style={[styles.textStyle, {color: message ? 'white' : '#808080'}]}>
        {message || 'Drag-and-drop tiles here.'}
      </Text>
    </View>
  );
};
