import {FastSimonApiRecords} from './queries';

export const getQueryInputParams = (queryName: string) => {
  const queryDetails = FastSimonApiRecords[queryName];

  if (!queryDetails || !queryDetails.editableInputParams) {
    return {};
  }

  if (queryName === 'search') {
    return {
      query: {
        type: 'codeInput',
        props: {
          label: 'Search Query',
          placeholder: 'Enter search term...',
        },
      },
      facetsRequired: {
        type: 'codeInput',
        props: {
          label: 'Facets Required',
          placeholder: 'true or false',
        },
      },
      sortBy: {
        type: 'codeInput',
        props: {
          label: 'Sort By',
          placeholder:
            'relevancy, price_min_to_max, price_max_to_min, creation_date, creation_date_oldest, reviews, a_to_z, z_to_a',
        },
      },
      cdnCacheKey: {
        type: 'codeInput',
        props: {
          label: 'CDN Cache Key',
          placeholder: 'Enter CDN cache key',
        },
      },
      narrow: {
        type: 'codeInput',
        props: {
          label: 'Narrow',
          placeholder: 'Enter narrow filter...',
        },
      },
    };
  }

  if (queryName === 'autocompleteProduct') {
    return {
      query: {
        type: 'codeInput',
        props: {
          label: 'Search Query',
          placeholder: 'Enter search term...',
        },
      },
      cdnCacheKey: {
        type: 'codeInput',
        props: {
          label: 'CDN Cache Key',
          placeholder: 'Enter CDN cache key',
        },
      },
    };
  }

  if (queryName === 'collectionProducts') {
    return {
      collectionId: {
        type: 'codeInput',
        props: {
          label: 'Collection ID',
          placeholder: 'Enter collection ID...',
        },
      },
      facetsRequired: {
        type: 'codeInput',
        props: {
          label: 'Facets Required',
          placeholder: 'true or false',
        },
      },
      sortBy: {
        type: 'codeInput',
        props: {
          label: 'Sort By',
          placeholder:
            'relevancy, price_min_to_max, price_max_to_min, creation_date, creation_date_oldest, reviews, a_to_z, z_to_a',
        },
      },
      narrow: {
        type: 'codeInput',
        props: {
          label: 'Narrow',
          placeholder: 'Enter narrow filter...',
        },
      },
    };
  }

  if (queryName === 'searchFilters') {
    return {
      query: {
        type: 'codeInput',
        props: {
          label: 'Search Query',
          placeholder: 'Enter search term...',
        },
      },
      cdnCacheKey: {
        type: 'codeInput',
        props: {
          label: 'CDN Cache Key',
          placeholder: 'Enter CDN cache key',
        },
      },
      narrow: {
        type: 'codeInput',
        props: {
          label: 'Narrow',
          placeholder: 'Enter narrow filter...',
        },
      },
    };
  }

  if (queryName === 'collectionFilters') {
    return {
      collectionId: {
        type: 'codeInput',
        props: {
          label: 'Collection ID',
          placeholder: 'Enter collection ID...',
        },
      },
      narrow: {
        type: 'codeInput',
        props: {
          label: 'Narrow',
          placeholder: 'Enter narrow filter...',
        },
      },
    };
  }

  const widgets: Record<string, any> = {};
  Object.keys(queryDetails.editableInputParams).forEach(key => {
    widgets[key] = {
      type: 'codeInput',
      props: {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        placeholder: `Enter ${key}...`,
      },
    };
  });

  return widgets;
};

export const FastSimonEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'Proxy URL',
        placeholder: 'https://api.apptile.io/fastsimon-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'App ID',
        placeholder: 'Your Shopify App ID',
      },
    },
  ],
};
