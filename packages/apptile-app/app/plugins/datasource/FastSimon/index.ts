import {call} from 'redux-saga/effects';
import {PluginConfigType, AppPageTriggerOptions, PluginListingSettings} from 'apptile-core';
import {DatasourceQueryDetail} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {IFastSimonCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {FastSimonConfigType} from './types';
import {FastSimonApiRecords} from './queries';
import {executeQuery} from './utils';
import {getQueryInputParams, FastSimonEditors} from './config';

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'fastSimon',
  type: 'datasource',
  name: 'FastSimon',
  description: 'AI-powered search and discovery for eCommerce',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export default wrapDatasourceModel({
  name: 'fastSimon',
  config: {
    ...baseDatasourceConfig,
    proxyUrl: 'https://api.apptile.io/fast-simon',
    appId: '',
    queryRunner: 'queryrunner',
  } as FastSimonConfigType,

  resolveCredentialConfigs: function (credentials: IFastSimonCredentials): Partial<FastSimonConfigType> | boolean {
    const {appId, proxyUrl} = credentials;
    if (!appId) return false;

    return {
      appId: appId,
      proxyUrl: proxyUrl,
    };
  },

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<FastSimonConfigType>,
    dsModelValues: any,
  ): Generator<any, any, any> {
    const queryRunner = AjaxQueryRunner();

    let proxyUrl = dsModelValues?.get('proxyUrl');

    if (proxyUrl && proxyUrl.endsWith('/')) {
      proxyUrl = proxyUrl.slice(0, -1);
    }

    const appId = dsConfig?.config?.get('appId') || dsModelValues?.get('appId');

    queryRunner.initClient(proxyUrl, config => {
      config.headers = {
        'x-shopify-app-id': appId ?? '',
        ...config.headers,
      };
      return config;
    });

    let configData = null;
    try {
      const configResponse = yield queryRunner.runQuery(
        'get',
        '/api/config',
        {},
        {
          headers: {
            'x-shopify-app-id': appId ?? '',
          },
        },
      );

      configData = configResponse?.data || null;
    } catch (error) {
      // Config fetch failed but datasource can still work
    }

    const modelUpdates = [
      {
        selector: [dsConfig?.get('id'), 'queryRunner'],
        newValue: queryRunner,
      },
    ];

    if (configData) {
      modelUpdates.push({
        selector: [dsConfig?.get('id'), 'configData'],
        newValue: configData,
      });
    }

    return {
      modelUpdates,
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return FastSimonApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    return getQueryInputParams(queryName);
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'fastSimon';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): Generator<any, any, any> {
    const queryDetails = FastSimonApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    pluginListing,
  },
  editors: FastSimonEditors,
});
