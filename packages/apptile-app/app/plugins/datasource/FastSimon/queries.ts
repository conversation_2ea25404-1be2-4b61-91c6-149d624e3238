import {FastSimonQueryDetails} from './types';

export const autocompleteSearchQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/autocomplete/search',
  editableInputParams: {
    query: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any) => {
    const params = new URLSearchParams();
    if (inputVariables.query) {
      params.append('query', inputVariables.query);
    }
    return `${endpoint}?${params.toString()}`;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables.query && inputVariables.query.trim());
  },
};

export const autocompleteProductQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/autocomplete/product',
  editableInputParams: {
    query: '',
    cdnCacheKey: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any) => {
    const params = new URLSearchParams();
    if (inputVariables.query) {
      params.append('query', inputVariables.query);
    }
    if (inputVariables.cdnCacheKey) {
      params.append('cdnCacheKey', inputVariables.cdnCacheKey);
    }
    params.append('includeCategories', '1');
    return `${endpoint}?${params.toString()}`;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables.query && inputVariables.query.trim());
  },
};

export const searchQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/search',
  isPaginated: true,
  editableInputParams: {
    query: '',
    facetsRequired: false,
    sortBy: 'relevancy',
    cdnCacheKey: '',
    narrow: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any, paginationMeta: any) => {
    const params = new URLSearchParams();
    if (inputVariables.query) {
      params.append('query', inputVariables.query);
    }
    if (inputVariables.cdnCacheKey) {
      params.append('cdnCacheKey', inputVariables.cdnCacheKey);
    }
    if (inputVariables.facetsRequired !== undefined) {
      params.append('facetsRequired', inputVariables.facetsRequired ? '1' : '0');
    }
    if (inputVariables.sortBy) {
      params.append('sortBy', inputVariables.sortBy);
    }
    if (inputVariables.narrow) {
      params.append('narrow', inputVariables.narrow);
    }
    const page = paginationMeta?.currentPage || 1;
    params.append('page', page.toString());
    return `${endpoint}?${params.toString()}`;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables.query && inputVariables.query.trim());
  },
  paginationResolver: (inputVariables: any, _paginationMeta: any) => {
    return {
      ...inputVariables,
    };
  },
  transformer: (data: any, _paginationMeta: any) => {
    const currentPage = data?.p || 1;
    const totalPages = Math.ceil((data?.total_results || 0) / 20);
    const hasNextPage = currentPage < totalPages;

    return {
      data: data,
      hasNextPage: hasNextPage,
      paginationMeta: {
        nextPage: hasNextPage ? currentPage + 1 : currentPage,
        totalPages: totalPages,
        currentPage: currentPage,
      },
    };
  },
};

export const collectionProductsQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/collection/{collectionId}/products',
  isPaginated: true,
  editableInputParams: {
    collectionId: '',
    facetsRequired: false,
    sortBy: 'relevancy',
    narrow: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any, paginationMeta: any) => {
    const collectionId = inputVariables?.collectionId || '';
    let resolvedEndpoint = endpoint.replace('{collectionId}', collectionId);

    const params = new URLSearchParams();
    if (inputVariables?.facetsRequired !== undefined) {
      params.append('facetsRequired', inputVariables.facetsRequired ? '1' : '0');
    }
    if (inputVariables?.sortBy) {
      params.append('sortBy', inputVariables.sortBy);
    }
    if (inputVariables?.narrow) {
      params.append('narrow', inputVariables.narrow);
    }
    const page = paginationMeta?.currentPage || 1;
    params.append('page', page.toString());

    return `${resolvedEndpoint}?${params.toString()}`;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables?.collectionId && inputVariables.collectionId.trim());
  },
  paginationResolver: (inputVariables: any, _paginationMeta: any) => {
    return {
      ...inputVariables,
    };
  },
  transformer: (data: any, _paginationMeta: any) => {
    const currentPage = data?.p || 1;
    const totalPages = Math.ceil((data?.total_results || 0) / 20);
    const hasNextPage = currentPage < totalPages;

    return {
      data: data,
      hasNextPage: hasNextPage,
      paginationMeta: {
        nextPage: hasNextPage ? currentPage + 1 : currentPage,
        totalPages: totalPages,
        currentPage: currentPage,
      },
    };
  },
};

export const searchFiltersQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/search/filters',
  editableInputParams: {
    query: '',
    cdnCacheKey: '',
    narrow: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any) => {
    const params = new URLSearchParams();
    if (inputVariables.query) {
      params.append('query', inputVariables.query);
    }
    if (inputVariables.cdnCacheKey) {
      params.append('cdnCacheKey', inputVariables.cdnCacheKey);
    }
    if (inputVariables.narrow) {
      params.append('narrow', inputVariables.narrow);
    }
    return `${endpoint}?${params.toString()}`;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables.query && inputVariables.query.trim());
  },
};

export const collectionFiltersQuery: FastSimonQueryDetails = {
  queryType: 'get',
  endpoint: '/api/collection/{collectionId}/filters',
  editableInputParams: {
    collectionId: '',
    narrow: '',
  },
  endpointResolver: (endpoint: string, inputVariables: any) => {
    const collectionId = inputVariables?.collectionId || '';
    let resolvedEndpoint = endpoint.replace('{collectionId}', collectionId);
    const params = new URLSearchParams();
    if (inputVariables.narrow) {
      params.append('narrow', inputVariables.narrow);
    }
    resolvedEndpoint += `?${params.toString()}`;
    return resolvedEndpoint;
  },
  checkInputVariables: (inputVariables: Record<string, any>) => {
    return !!(inputVariables?.collectionId && inputVariables.collectionId.trim());
  },
};

export const FastSimonApiRecords: Record<string, FastSimonQueryDetails> = {
  autocompleteSearch: autocompleteSearchQuery,
  autocompleteProduct: autocompleteProductQuery,
  search: searchQuery,
  collectionProducts: collectionProductsQuery,
  searchFilters: searchFiltersQuery,
  collectionFilters: collectionFiltersQuery,
};
