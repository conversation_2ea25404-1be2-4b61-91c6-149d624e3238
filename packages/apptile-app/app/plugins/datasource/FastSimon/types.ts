import {DatasourceQueryDetail} from '../../query/index';
import {DatasourcePluginConfig} from '../datasourceTypes';

export type FastSimonConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  appId: string;
  proxyUrl: string;
  configData?: any;
};

export type SortByType =
  | 'relevancy'
  | 'price_min_to_max'
  | 'price_max_to_min'
  | 'creation_date'
  | 'creation_date_oldest'
  | 'reviews'
  | 'a_to_z'
  | 'z_to_a';

export type FastSimonQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};
