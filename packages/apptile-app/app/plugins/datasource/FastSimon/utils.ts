import Immutable from 'immutable';
import {PluginConfigType, AppPageTriggerOptions, PluginModelType} from 'apptile-core';
import {FastSimonConfigType} from './types';

export const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<FastSimonConfigType>, dsModelValues: PluginModelType) => {
    if (!dsConfig || !dsModelValues) return {};

    const dsPluginConfig = dsConfig?.get?.('config') as any as Immutable.Map<string, FastSimonConfigType>;
    if (!dsPluginConfig) return {};

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get && dsModelValues.get(value)) {
        if (value === 'configData' && key === 'cdnCacheKey') {
          const configData = dsModelValues.get(value);
          if (configData && configData.data && configData.data.cdn_cache_key) {
            return {...acc, [key]: configData.data.cdn_cache_key};
          }
        } else {
          return {...acc, [key]: dsModelValues.get(value)};
        }
      }
      return acc;
    }, {});
  };
};

export const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  if (!inputVariables || !editableInputParams) return {};

  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value, 10),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    if (!dsModelValues) throw new Error('Datasource model values not available');
    if (!inputVariables) throw new Error('Input variables not provided');

    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues) || {};
    }

    let isReadyToRun: boolean = true;
    let typedInputVariables = inputVariables || {};
    let typedDataVariables;

    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues?.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else if (!queryRunner) {
      queryResponse = {
        errors: {message: 'Query runner not initialized'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            'x-shopify-app-id': inputVariables?.appId || dsModelValues?.get('appId'),
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};
