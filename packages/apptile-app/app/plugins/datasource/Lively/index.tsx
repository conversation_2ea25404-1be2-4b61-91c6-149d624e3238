import {PluginConfigType, TriggerActionIdentifier, modelUpdateAction} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, ILivelyCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {getLiveStreamingData} from './liveStreams';
import _ from 'lodash';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {call} from 'redux-saga/effects';

export type LivelyPluginConfigType = DatasourcePluginConfig & ILivelyCredentials;

type IEditableParams = Record<string, any>;
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};
type LivelyQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};

const baseLivelyQuerySpec: Partial<LivelyQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    brandId: '',
    pastStreamsWidgetId: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {startIndex} = paginationMeta ?? {};
    return startIndex ? {...inputVariables, startIndex} : inputVariables;
  },
};

export const livelyApiRecords: Record<string, any> = {
  GetShoppableFeed: {
    ...baseLivelyQuerySpec,
    queryType: 'get',
    endpoint: '/widget/plugin/shoppable-feeds/',
    endpointResolver: (endpoint: any, inputParams: any) => {
      const {pastStreamsWidgetId, limit = 50, ...rest} = inputParams;
      const resolvedEndpoint = `${endpoint}${pastStreamsWidgetId}?limit=${limit}`;
      return resolvedEndpoint;
    },
    transformer: (data: any) => {
      return {
        data: data?.data,
        hasNextPage: false,
        paginationMeta: undefined,
      };
    },
    isPaginated: false,
    editableInputParams: {pastStreamsWidgetId: '', limit: ''},
  },
  GetShoppableChunks: {
    ...baseLivelyQuerySpec,
    queryType: 'get',
    endpoint: '/live-streaming/plugin/shoppable-feeds/chunks',
    endpointResolver: (endpoint: any, inputParams: any) => {
      const {shoppableFeedId, ...rest} = inputParams;
      const resolvedEndpoint = `${endpoint}?shoppable_feed_id=${shoppableFeedId}&page=1`;
      return resolvedEndpoint;
    },
    transformer: (rawdata: any) => {
      const data = rawdata?.data;
      data.feeds = data.streaming_shoppable_feeds;
      delete data.streaming_shoppable_feeds;
      return {
        data,
        hasNextPage: false,
        paginationMeta: undefined,
      };
    },
    isPaginated: false,
    editableInputParams: {shoppableFeedId: ''},
  },
  GetFeedProductsSeek: {
    ...baseLivelyQuerySpec,
    queryType: 'get',
    endpoint: '/live-streaming/product/time/',
    endpointResolver: (endpoint: any, inputParams: any) => {
      const {feedId, ...rest} = inputParams;
      const resolvedEndpoint = `${endpoint}${feedId}`;
      return resolvedEndpoint;
    },
    transformer: (data: any, inputParams: any, error: any) => {
      const {productsMapping} = inputParams;
      if (error)
        return {
          data: productsMapping,
          hasNextPage: false,
          paginationMeta: undefined,
        };

      return {
        data: productsMapping.map((e: any) => ({...e, seeks: data?.data?.[e.product_id]})),
        hasNextPage: false,
        paginationMeta: undefined,
      };
    },
    isPaginated: false,
    editableInputParams: {
      productsMapping: {},
      feedId: '',
    },
  },
  incrementView: {
    ...baseLivelyQuerySpec,
    queryType: 'post',
    endpoint: '/event-count/view-count',
    endpointResolver: (endpoint: string) => {
      return endpoint;
    },
    editableInputParams: {
      shoppable_feed_id: '',
    },
    inputResolver: (inputVariables: any) => {
      const {shoppable_feed_id} = inputVariables;

      return {
        shoppable_feed_id,
      };
    },
  },
  GetPdpShoppableFeed: {
    ...baseLivelyQuerySpec,
    queryType: 'get',
    endpoint: '/shoppable-feeds/product/v2',
    endpointResolver: (endpoint: any, inputParams: any) => {
      const {store_product_id, company_id, ...rest} = inputParams;
      const resolvedEndpoint = `${endpoint}?store_product_id=${store_product_id}&company_id=${company_id}`;
      return resolvedEndpoint;
    },
    transformer: (data: any) => {
      return {
        data: data?.data,
        hasNextPage: false,
        paginationMeta: undefined,
      };
    },
    isPaginated: false,
    editableInputParams: {
      store_product_id: '',
      company_id: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  getLiveStreamIdentifier: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return async function (dispatch, config, model, selector) {
        const brandId = model?.get('brandId');
        const response = await getLiveStreamingData(brandId);
        const newSelector = [...(selector as string[]), 'currentLiveStreams'];
        const upcomingSelector = [...(selector as string[]), 'upcomingLiveStreams'];
        const streams = response?.data?.data ?? [];
        const currentLiveStreams = {
          data: streams
            ?.filter((e: any) => e?.streaming_status == 2)
            ?.sort((a, b) => a.start_time_unix - b.start_time_unix),
        };
        const upcomingLiveStreams = {
          data: streams
            ?.filter((e: any) => e?.streaming_status == 1)
            ?.sort((a, b) => a.start_time_unix - b.start_time_unix),
        };
        const updates = [];
        if (!_.isEqual(model.get('currentLiveStreams'), currentLiveStreams)) {
          updates.push({
            selector: newSelector,
            newValue: currentLiveStreams,
          });
        }
        if (!_.isEqual(model.get('upcomingLiveStreams'), upcomingLiveStreams)) {
          updates.push({
            selector: upcomingSelector,
            newValue: upcomingLiveStreams,
          });
        }
        if (updates.length > 0) {
          dispatch(modelUpdateAction(updates, undefined, true));
        }
        return response.data;
      };
    },
  },
  getVideoClips: {
    type: TriggerActionIdentifier,
    actionMetadata: {
      editableInputParams: { bufferLimit: '' },
    },
    getValue(model, renderedValue, selector) {
      return async function (dispatch, config, model, selector, params) {
        dispatch(modelUpdateAction([{selector: selector.concat(['videoClipsLoading']), newValue: true}], undefined, true));
        const { bufferLimit } = params;
        
        const response = await executeQuery(model, config, model, 'GetShoppableFeed', { limit: bufferLimit })
        const shoppableFeedsIds = _.map(response?.data?.feeds, '_id');

        for (const shoppableFeedId of shoppableFeedsIds) {
          try {
            const response = await executeQuery(model, config, model, 'GetShoppableChunks', { shoppableFeedId: shoppableFeedId });
            if(response && response.data.feeds.length > 0) {
              const updates = [
                {
                  selector: selector.concat(['videoClips']),  
                  newValue: response.data.feeds
                }
              ]
              dispatch(modelUpdateAction([{selector: selector.concat(['videoClipsLoading']), newValue: false}], undefined, true));
              return dispatch(modelUpdateAction(updates, undefined, true));
            }
          } catch (error) {
            console.log("Video Clips Empty: ", error)
          } finally {
            dispatch(modelUpdateAction([{selector: selector.concat(['videoClipsLoading']), newValue: false}], undefined, true));
          }
        }
      }
    }
  }
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'lively',
  type: 'datasource',
  name: 'Lively Integration',
  description: 'Lively Widgets integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const livelyEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'brandId',
      props: {
        label: 'Brand Id',
      },
    },
    {
      type: 'codeInput',
      name: 'pastStreamsWidgetId',
      props: {
        label: 'Past Streams Widget Id',
      },
    },
  ],
};
//0qsx1cp735
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<SearchanizePluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, SearchanizePluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryName: string,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    const queryDetails = livelyApiRecords[queryName];

    if (!queryDetails) return;

    const {getNextPage, paginationMeta} = options ?? {};
    
    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, typedDataVariables, {
          ...options,
          headers: {
            ...queryDetails.headers,
            'x-company-id': typedDataVariables?.company_id || dsConfigVariables?.brandId,
            'X-Request-Source': 'APPTILE',
          },
        });
      } catch (error) {
        logger.error('error', error);
        queryResponse = {error};
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    const error = queryResponse && queryResponse.error ? queryResponse.error : null;
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, inputVariables, error);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: error ? [error] : [],
      hasError: !!error,
    };
  } catch (error) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: undefined,
      errors: [],
      hasError: false,
    }; 
  }
};

export default wrapDatasourceModel({
  name: 'lively',
  config: {
    ...baseDatasourceConfig,
    queryRunner: '',
    brandId: '',
    pastStreamsWidgetId: '',
    videoClips: [],
    getLiveStreamIdentifier: 'action',
    getVideoClips: 'action',
  } as LivelyPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<LivelyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient('https://api.lively.li');
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return livelyApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = livelyApiRecords && livelyApiRecords[queryName] ? livelyApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: ILivelyCredentials): Partial<LivelyPluginConfigType> | boolean {
    const {brandId} = credentials;
    if (!brandId) return false;
    return {
      brandId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['brandId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'lively';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryName, inputVariables, options)
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: livelyEditors,
});
