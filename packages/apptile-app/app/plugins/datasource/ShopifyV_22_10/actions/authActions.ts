import _ from 'lodash';

import {modelUpdateAction} from 'apptile-core';
import {navigateToScreen, sendAnalyticsEvent, triggerAction} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {ShopifyQueryDetails} from '..';
import {PluginConfig} from 'apptile-core';
import {ModelChange, Selector} from 'apptile-core';
import {LocalStorage} from 'apptile-core';
import {GetRegisteredPluginInfo, PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import {processShopifyGraphqlQueryResponse} from '../../utils';
import {APPTILE_GLOBAL_PLUGIN_ID} from 'apptile-core';
import {AppConfig, AppModelType} from 'apptile-core';
import Ajax<PERSON>ueryRunner from '../../AjaxWrapper/model';
import {PluginConfigType, PluginModelType} from '@/root/apptile-core/types';
import axios from 'axios';
import {generateCodeChallenge, generateCodeVerifier} from '../utils/pkce_utils';
import qs from 'qs';
import apolloQueryRunner from '../../ApolloWrapper/model';
import {
  CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
  CUSTOMER_ACCOUNT_CODE_VERIFIER_KEY_FOR_LOCAL_STORAGE,
  // CUSTOMER_ACCOUNT_CUSTOMER_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
} from '../constants';
import {AppState, AppStateStatus} from 'react-native';

export interface IAuthActionsDatasourcePluginConfigType {
  signInUser: string;
  signUpUser: string;
  signOutUser: string;
  clearAuthMessages: string;
  renewCustomerAccessToken: string;
  signInUserWithAccessToken: string;
  verifyAuthSession: string;
  customerActivateByUrl: string;
  loggedInUser: any;
  loggedInUserAccessToken: any;
  storefrontCustomerAccessTokenFromCustomerApi: any;
  // useCustomerApi: any;
  customerApiLoginScreen: string;
  authLoader: string;
  isloggedInUser: string;
  checkEmailExists: string;
  preAuthVerifyEmailState: string;
  changePreAuthVerifyEmailState: string;
  getAccessTokenFromCode: string;
  refreshCustomerApiAccessToken: string;
  // updateCustomerAPIAccessToken: string;
}

export const authActionsDatasourcePluginConfig: IAuthActionsDatasourcePluginConfigType = {
  signInUser: TriggerActionIdentifier,
  signUpUser: TriggerActionIdentifier,
  signOutUser: TriggerActionIdentifier,
  clearAuthMessages: TriggerActionIdentifier,
  renewCustomerAccessToken: TriggerActionIdentifier,
  signInUserWithAccessToken: TriggerActionIdentifier,
  verifyAuthSession: TriggerActionIdentifier,
  customerActivateByUrl: TriggerActionIdentifier,
  checkEmailExists: TriggerActionIdentifier,
  changePreAuthVerifyEmailState: TriggerActionIdentifier,
  getAccessTokenFromCode: TriggerActionIdentifier,
  refreshCustomerApiAccessToken: TriggerActionIdentifier,
  // updateCustomerAPIAccessToken: TriggerActionIdentifier,
  preAuthVerifyEmailState: '',
  loggedInUser: '',
  loggedInUserAccessToken: '',
  storefrontCustomerAccessTokenFromCustomerApi: '',
  authLoader: '',
  // useCustomerApi: '',
  customerApiLoginScreen: '',
  isloggedInUser: '',
};

export const authActionDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'screenSelector',
      name: 'signInRedirectScreenId',
      props: {
        label: 'Post signIn Redirect Screen',
      },
    },
    {
      type: 'screenSelector',
      name: 'signUpRedirectScreenId',
      props: {
        label: 'Post signUp Redirect Screen',
      },
    },
    {
      type: 'screenSelector',
      name: 'signOutRedirectScreenId',
      props: {
        label: 'Post signOut Redirect Screen',
      },
    },
    {
      type: 'checkbox',
      name: 'showCustomAuthErrorMessage',
      props: {
        label: 'Show Custom Error Message For Auth',
        checkedValue: false,
      },
    },
    // {
    //   type: 'checkbox',
    //   name: 'useCustomerApi',
    //   props: {
    //     label: 'Use Shopify New Customer Login',
    //     checkedValue: false,
    //   },
    // },
    {
      type: 'screenSelector',
      name: 'customerApiLoginScreen',
      props: {
        label: 'Customer API Login Screen',
      },
    },
  ],
};

export const authActionsDatasourcePropertySettings: PluginPropertySettings = {
  isloggedInUser: {
    getValue: (model, renderedValue, selector) => {
      // const loggedInUser = model[0]?.get('loggedInUser');
      const loggedInUser2 = model?.loggedInUser;
      // const loggedInUserAccessToken = model[0]?.get('loggedInUserAccessToken');
      const loggedInUserAccessToken2 = model?.loggedInUserAccessToken;
      return !!loggedInUser2 && !!loggedInUserAccessToken2;
    },
  },
  signInUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.signInUserCustomerApi : authActions.signInUser;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        password: '',
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: false,
      },
    },
  },
  // useCustomerApi: {
  //   updatesProps: ['signInUser'],
  // },

  checkEmailExists: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.checkEmailExists;
    },
    actionMetadata: {
      editableInputParams: {
        customerEmail: '',
      },
    },
  },

  changePreAuthVerifyEmailState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.changePreAuthVerifyEmailState;
    },
    actionMetadata: {
      editableInputParams: {
        preAuthVerifyEmailState: '',
      },
    },
  },
  signUpUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.signInUserCustomerApi : authActions.signUpUser;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        phone: '',
        acceptsMarketing: false,
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: false,
      },
    },
  },
  signOutUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.signOutUserCustomerApi : authActions.signOutUser;
    },
    actionMetadata: {
      editableInputParams: {
        skipPostSignOutRedirect: false,
      },
    },
  },
  clearAuthMessages: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.clearAuthMessages;
    },
  },
  renewCustomerAccessToken: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.refreshCustomerApiAccessToken : authActions.renewCustomerAccessToken;
    },
    actionMetadata: {
      editableInputParams: {
        customerAccessToken: '',
      },
    },
  },
  signInUserWithAccessToken: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signInUserWithAccessToken;
    },
    actionMetadata: {
      editableInputParams: {
        customerAccessToken: '',
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: false,
      },
    },
  },
  loggedInUser: {
    updatesProps: ['isloggedInUser'],
  },
  loggedInUserAccessToken: {
    updatesProps: ['isloggedInUser'],
  },
  verifyAuthSession: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.verifyAuthSessionCustomerApi : authActions.verifyAuthSession;
    },
    actionMetadata: {
      editableInputParams: {
        skipPostSignOutRedirect: false,
      },
    },
  },
  customerActivateByUrl: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.customerActivateByUrl;
    },
    actionMetadata: {
      editableInputParams: {
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: false,
        activationUrl: '',
        password: '',
      },
    },
  },
  getAccessTokenFromCode: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.getAccessTokenFromCode;
    },
    actionMetadata: {
      editableInputParams: {
        code: '',
        skipPostAuthRedirect: false,
        signInRedirectScreenIdOverride: '',
      },
    },
  },
  refreshCustomerApiAccessToken: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      const useCustomerApi: boolean = model?.useCustomerApi;
      return useCustomerApi ? authActions.refreshCustomerApiAccessToken : () => {};
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  // updateCustomerAPIAccessToken: {
  //   type: TriggerActionIdentifier,
  //   getValue(model, renderedValue, selector) {
  //     return authActions.updateCustomerAPIAccessToken;
  //   },
  //   actionMetadata: {
  //     editableInputParams: {},
  //   },
  // },
};

export interface ISignInUserPayload {
  email: string;
  password: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
  skipPostAuthRedirect: boolean;
}

export interface ISignUpUserPayload extends ISignInUserPayload {
  firstName: string;
  lastName: string;
  phone: string;
  acceptsMarketing: string;
}

export interface IAuthActionsInterface {
  signInUser: ActionHandler;
  signUpUser: ActionHandler;
}

export interface IRenewCustomerAccessTokenPayload {
  customerAccessToken: string;
}

export interface ISignInUserWithCustomerAccessToken {
  customerAccessToken: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
  skipPostAuthRedirect: boolean;
}

class AuthActions implements IAuthActionsInterface {
  private async queryExecutor(model: any, querySchema: ShopifyQueryDetails, inputVariables: any) {
    const queryRunner = model.get('queryRunner');
    const shopConfig = model.get('shop');
    const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
    const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
    const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig);
    return {transformedData, transformedError};
  }

  // TODO: Remove later
  private async fetchCustomerWithCustomerApi(
    model: any,
    querySchema: ShopifyQueryDetails,
    inputVariables: any,
    accessToken: string,
  ) {
    const queryRunner = apolloQueryRunner();
    const customerApiUrl = model.get('customerApiUrl');
    const shopConfig = model.get('shop');
    const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
    await queryRunner.initClient(customerApiUrl, () => {
      return {
        headers: {
          Authorization: accessToken,
        },
      };
    });
    const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
    const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig);
    // console.log('EXECUTED QUERY fetchCustomerWithCustomerApi', transformedData);
    return {transformedData, transformedError};
  }

  private async fetchSFCustomerAccessTokenWithCustomerAPI(
    model: any,
    querySchema: ShopifyQueryDetails,
    inputVariables: any,
    accessToken: string,
  ) {
    const queryRunner = apolloQueryRunner();
    const customerApiUrl = model.get('customerApiUrl');
    const shopConfig = model.get('shop');
    const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
    await queryRunner.initClient(customerApiUrl, () => {
      return {
        headers: {
          Authorization: accessToken,
        },
      };
    });
    const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
    const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig);
    return {transformedData, transformedError};
  }

  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  private async updateLocalStore(key: string, value: any) {
    await LocalStorage.setValue(key, value);
  }

  private async storeLoggedInUserDetails(value: any) {
    await this.updateLocalStore(`loggedInUser`, value);
  }

  private async storeloggedInUserAccessToken(value: any) {
    await this.updateLocalStore(`loggedInUserAccessToken`, value);
  }

  async changePreAuthVerifyEmailState(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) {
    const {preAuthVerifyEmailState} = params;
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['preAuthVerifyEmailState']),
      newValue: preAuthVerifyEmailState,
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  async checkEmailExists(
    dispatch,
    config: PluginConfigType<any>,
    model: PluginModelType,
    selector: Selector,
    params: any,
  ) {
    const {customerEmail} = params;
    if (customerEmail !== undefined) {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['authLoader']),
          newValue: true,
        },
      ];

      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const LoginHelperQueryRunner = AjaxQueryRunner();
      const LoginHelperUrl = model.get('preSignInApiUrl');
      const storefrontApiUrl = model.get('storefrontApiUrl');
      const sourceEntityId = storefrontApiUrl
        .replace(/(\/api\/20[\d][\d]-[\d][\d]\/graphql\.json)/gim, '')
        .replace('https://', '');
      // const LoginHelperUrl = 'https://54f3621d-fe59-4345-9381-e28ec69d2926.mock.pstmn.io';
      LoginHelperQueryRunner.initClient(LoginHelperUrl, config => {
        return config;
      });
      const LoginHelperQueryResponse = await LoginHelperQueryRunner.runQuery(
        'post',
        `/api/v1/pre-sign-in/email/exists`,
        {
          email: customerEmail,
          sourceEntityId: sourceEntityId,
        },
        {},
      );
      let doesCustomerExistBoolean = LoginHelperQueryResponse.data.exists;
      let newModelUpdates: ModelChange[] = [];
      newModelUpdates.push({
        selector: selector.concat(['customerEmailPreLogin']),
        newValue: customerEmail,
      });
      if (doesCustomerExistBoolean) {
        newModelUpdates.push({
          selector: selector.concat(['preAuthVerifyEmailState']),
          newValue: 'LoginPassword',
        });
      } else {
        newModelUpdates.push({
          selector: selector.concat(['preAuthVerifyEmailState']),
          newValue: 'CreateAccount',
        });
      }

      newModelUpdates.push({
        selector: selector.concat(['authLoader']),
        newValue: false,
      });
      dispatch(modelUpdateAction(newModelUpdates, undefined, true));
    }
  }

  private async setErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const showCustomAuthErrorMessage = model.get('showCustomAuthErrorMessage', false);
    const errorString = showCustomAuthErrorMessage && customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    if (skipPostAuthRedirect) {
      newModelUpdates.push({
        selector: selector.concat(['authErrorMessage']),
        newValue: errorString,
      });
    } else {
      toast.show(errorString, {
        type: 'error',
        placement: 'bottom',
        duration: 4000,
        style: {marginBottom: 80},
      });
    }

    setTimeout(() => dispatch(modelUpdateAction(newModelUpdates, undefined, true)), 1);
  }

  private async setBadParamErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const showCustomAuthErrorMessage = model.get('showCustomAuthErrorMessage', false);
    const errorString = showCustomAuthErrorMessage && customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });
    if (skipPostAuthRedirect) {
      newModelUpdates.push({
        selector: selector.concat(['authErrorMessage']),
        newValue: errorString,
      });
    } else {
      toast.show(errorString, {
        type: 'error',
        placement: 'bottom',
        duration: 4000,
        style: {marginBottom: 80},
      });
    }
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private async setSuccessMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    customerData: any,
    message: string,
  ) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    if (skipPostAuthRedirect) {
      newModelUpdates.push({
        selector: selector.concat(['authSuccessMessage']),
        newValue: message,
      });
    } else {
      toast.show(message, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private preSignInUser = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as ISignInUserPayload;
    const {email, password} = payload;
    const queries = this.getQueries();
    let customerData = null;
    const {transformedData: signInResponse, transformedError} = await this.queryExecutor(
      model,
      queries?.LoginCustomer,
      {
        email,
        password,
      },
    );

    if (!signInResponse || !signInResponse?.accessToken) {
      console.log(`Error signInResponse`, signInResponse);
      return {customerData, error: _.first(transformedError)?.message};
    }

    const {transformedData: fetchCustomerResponse, transformedError: fetchCustomerError} = await this.queryExecutor(
      model,
      queries?.FetchCustomer,
      {
        customerAccessToken: signInResponse?.accessToken,
      },
    );

    if (!fetchCustomerResponse) {
      console.log(`Error fetchCustomerResponse`, fetchCustomerResponse);
      return {customerData, error: _.first(fetchCustomerError)?.message};
    }

    await this.storeloggedInUserAccessToken(signInResponse);
    await this.storeLoggedInUserDetails(fetchCustomerResponse);

    customerData = {
      loggedInUser: fetchCustomerResponse,
      loggedInUserAccessToken: signInResponse,
    };
    return {customerData, error: null};
  };

  signInUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
      {
        selector: selector.concat(['authSuccessMessage']),
        newValue: null,
      },
      {
        selector: selector.concat(['authErrorMessage']),
        newValue: null,
      },
    ];
    const payload = params as ISignInUserPayload;
    const {successMessage, skipPostAuthRedirect, errorMessage, badParameterMessage} = payload;
    try {
      const signInRedirectScreenId = model.get('signInRedirectScreenId');
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const {customerData, error} = await this.preSignInUser(dispatch, config, model, selector, params);

      if (error) {
        console.log(`error`, error);
        this.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          error,
          badParameterMessage,
        );
        return {success: false, errorMessage: error};
      }

      if (!signInRedirectScreenId) {
        console.log(`Please configure signIn screen in shopify`);
      }
      let newModelUpdates: ModelChange[] = [];
      newModelUpdates.push({
        selector: selector.concat(['customerEmailPreLogin']),
        newValue: null,
      });
      newModelUpdates.push({
        selector: selector.concat(['preAuthVerifyEmailState']),
        newValue: '',
      });
      dispatch(modelUpdateAction(newModelUpdates, undefined, true));
      const successfullLoginMessage =
        model?.get('successfullLoginMessage', 'Logged in successfully!') || 'Logged in successfully!';
      this.setSuccessMessage(
        dispatch,
        config,
        model,
        selector,
        skipPostAuthRedirect,
        customerData,
        successfullLoginMessage,
      );
      if (!skipPostAuthRedirect) {
        dispatch(navigateToScreen(signInRedirectScreenId, {}));
      }
      await this.postUserSignIn(dispatch, config, model, selector, params, appConfig, appModel);
      return {success: true, errorMessage: null};
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
      return {success: false, errorMessage: error};
    }
  };

  signUpUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
      {
        selector: selector.concat(['authSuccessMessage']),
        newValue: null,
      },
      {
        selector: selector.concat(['authErrorMessage']),
        newValue: null,
      },
    ];

    const payload = params as ISignUpUserPayload;
    const {
      firstName,
      lastName,
      phone,
      email,
      password,
      acceptsMarketing,
      successMessage,
      skipPostAuthRedirect,
      badParameterMessage,
      errorMessage,
    } = payload;

    try {
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const queries = this.getQueries();
      const phoneObject = !_.isEmpty(phone) ? {phone} : {};
      const {transformedData: signUpResponse, transformedError: signUpError} = await this.queryExecutor(
        model,
        queries?.RegisterCustomer,
        {
          ...phoneObject,
          firstName,
          lastName,
          email,
          password,
          acceptsMarketing: !!acceptsMarketing,
        },
      );

      if (!signUpResponse || !signUpResponse?.id) {
        console.log(`Error signUpResponse`, signUpResponse);
        this.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          _.first(signUpError)?.message,
          badParameterMessage,
        );
        return;
      }

      const signUpRedirectScreenId = model.get('signUpRedirectScreenId');
      const {customerData, error} = await this.preSignInUser(dispatch, config, model, selector, params);

      if (error) {
        console.log(`error`, error);
        this.setErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          error?.message,
          badParameterMessage,
        );
        return;
      }

      if (!signUpRedirectScreenId) {
        console.log(`Please configure signUp screen in shopify`);
      }
      let newModelUpdates: ModelChange[] = [];

      newModelUpdates.push({
        selector: selector.concat(['customerEmailPreLogin']),
        newValue: null,
      });
      newModelUpdates.push({
        selector: selector.concat(['preAuthVerifyEmailState']),
        newValue: '',
      });
      dispatch(modelUpdateAction(newModelUpdates, undefined, true));

      this.setSuccessMessage(dispatch, config, model, selector, skipPostAuthRedirect, customerData, successMessage);

      if (!skipPostAuthRedirect && !error) {
        dispatch(navigateToScreen(signUpRedirectScreenId, {}));
      }
      await this.postUserSignIn(dispatch, config, model, selector, params, appConfig, appModel, true);
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
    }
  };

  signOutUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const payload = params;
    const {skipPostSignOutRedirect} = payload;
    const signOutRedirectScreenId = model.get('signOutRedirectScreenId');

    try {
      const resetloggedInUserValue = null;
      await this.storeloggedInUserAccessToken(resetloggedInUserValue);
      await this.storeLoggedInUserDetails(resetloggedInUserValue);

      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['loggedInUser']),
        newValue: resetloggedInUserValue,
      });

      modelUpdates.push({
        selector: selector.concat(['loggedInUserAccessToken']),
        newValue: resetloggedInUserValue,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));

      await this.postUserSignOut(dispatch, config, model, selector, params, appConfig, appModel);

      if (!signOutRedirectScreenId) {
        console.log(`Please configure signOut screen in shopify`);
      }

      if (!skipPostSignOutRedirect) {
        dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      }
    } catch (error) {
      console.log(`error while sign out`);
      if (!skipPostSignOutRedirect) {
        dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      }
    }
  };

  postUserSignIn = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
    isSignUp = false,
  ) => {
    try {
      const loggedInUser = await LocalStorage.getValue('loggedInUser');
      const payload = {
        pluginConfig: appConfig.getPlugin(APPTILE_GLOBAL_PLUGIN_ID),
        pluginModel: appModel.getPluginModel('', APPTILE_GLOBAL_PLUGIN_ID),
        pluginSelector: [APPTILE_GLOBAL_PLUGIN_ID],
        eventModelJS: {
          value: 'postSignInAction',
          params: {
            platformUserId: _.get(loggedInUser, 'id', undefined),
          },
        },
      };

      dispatch(triggerAction(payload));
      dispatch(
        sendAnalyticsEvent('track', isSignUp ? 'signup' : 'login', {
          userId: _.get(loggedInUser, 'id', ''),
          firstName: _.get(loggedInUser, 'firstName', ''),
          lastName: _.get(loggedInUser, 'lastName', ''),
          emailId: _.get(loggedInUser, 'email', ''),
          contactNumber: _.get(loggedInUser, 'phone', ''),
        }),
      );
      if (!loggedInUser?.tags?.includes('APPTILE_MOBILE_APP_USER')) {
        const appId = model.get('appId');
        await this.axiosInstance.request({
          method: 'POST',
          url: `https://api.apptile.io/registry/users/register`,
          headers: {
            'Content-Type': 'application/json',
          },
          data: {
            platformName: 'SHOPIFY',
            appId: appId,
            platformUserId: _.get(loggedInUser, 'id', ''),
          },
        });
      } else {
        console.log('customer already tagged');
      }
    } catch (error) {
      console.log(`Error while adding customer tags`, error);
    }
  };

  postUserSignOut = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    try {
      const payload = {
        pluginConfig: appConfig.getPlugin(APPTILE_GLOBAL_PLUGIN_ID),
        pluginModel: appModel.getPluginModel('', APPTILE_GLOBAL_PLUGIN_ID),
        pluginSelector: [APPTILE_GLOBAL_PLUGIN_ID],
        eventModelJS: {
          value: 'postSignOutAction',
        },
      };

      dispatch(triggerAction(payload));
      dispatch(sendAnalyticsEvent('track', 'logout', {}));
    } catch (error) {
      console.log(`Error while triggering postUserSignOut`, error);
    }
  };

  renewCustomerAccessToken = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];

    const payload = params as IRenewCustomerAccessTokenPayload;
    const {customerAccessToken} = payload;
    dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

    let newModelUpdates: ModelChange[] = [];

    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    const queries = this.getQueries();
    const {transformedData: renewTokenResponse} = await this.queryExecutor(model, queries?.CustomerAccessTokenRenew, {
      customerAccessToken,
    });

    if (!renewTokenResponse || !renewTokenResponse?.accessToken) {
      console.log(`Error renewTokenResponse`, renewTokenResponse);
      return;
    }

    const {transformedData: fetchCustomerResponse, transformedError: fetchCustomerError} = await this.queryExecutor(
      model,
      queries?.FetchCustomer,
      {
        customerAccessToken: renewTokenResponse?.accessToken,
      },
    );

    if (!fetchCustomerError) {
      console.log(`Error fetchCustomerResponse`, fetchCustomerError);
      return;
    }

    await this.storeloggedInUserAccessToken(renewTokenResponse);
    await this.storeLoggedInUserDetails(fetchCustomerResponse);

    newModelUpdates.push({
      selector: selector.concat(['loggedInUser']),
      newValue: fetchCustomerResponse,
    });

    newModelUpdates.push({
      selector: selector.concat(['loggedInUserAccessToken']),
      newValue: renewTokenResponse,
    });

    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  };

  signInUserWithAccessToken = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const payload = params as ISignInUserWithCustomerAccessToken;
    const {successMessage, skipPostAuthRedirect, errorMessage, customerAccessToken, badParameterMessage} = payload;

    try {
      const signInRedirectScreenId = model.get('signInRedirectScreenId');

      if (_.isEmpty(customerAccessToken)) {
        const error = 'Invalid customer access token';
        console.log(error);
        this.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          'Invalid customer access token',
          badParameterMessage,
        );
        return;
      }

      const loadingModelUpdates = [
        {
          selector: selector.concat(['authLoader']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const queries = this.getQueries();
      const {transformedData: renewTokenResponse, transformedError: renewTokenError} = await this.queryExecutor(
        model,
        queries?.CustomerAccessTokenRenew,
        {
          customerAccessToken,
        },
      );

      if (!renewTokenResponse || !renewTokenResponse?.accessToken) {
        console.log(`Error signInUserWithAccessToken`, renewTokenResponse);
        console.log(_.first(renewTokenError)?.message);
        this.setErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          _.first(renewTokenError)?.message,
          errorMessage,
        );
        return;
      }

      const {transformedData: fetchCustomerResponse, transformedError: fetchCustomerError} = await this.queryExecutor(
        model,
        queries?.FetchCustomer,
        {
          customerAccessToken: renewTokenResponse?.accessToken,
        },
      );

      if (!fetchCustomerResponse) {
        console.log(`Error fetchCustomerResponse`, fetchCustomerResponse);
        this.setErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          _.first(fetchCustomerError)?.message,
          errorMessage,
        );
        return;
      }

      await this.storeloggedInUserAccessToken(renewTokenResponse);
      await this.storeLoggedInUserDetails(fetchCustomerResponse);

      console.log(`User logged in successfully!`);

      if (!signInRedirectScreenId) {
        console.log(`Please configure signIn screen in shopify`);
      }

      this.setSuccessMessage(
        dispatch,
        config,
        model,
        selector,
        skipPostAuthRedirect,
        {
          loggedInUser: fetchCustomerResponse,
          loggedInUserAccessToken: renewTokenResponse,
        },
        successMessage,
      );

      if (!skipPostAuthRedirect) {
        dispatch(navigateToScreen(signInRedirectScreenId, {}));
      }

      await this.postUserSignIn(dispatch, config, model, selector, params, appConfig, appModel);
    } catch (error) {
      console.log(`Error signInUserWithAccessToken`, error);
      this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
    }
  };

  clearAuthMessages = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    let modelUpdates: ModelChange[] = [];
    modelUpdates.push({
      selector: selector.concat(['authSuccessMessage']),
      newValue: null,
    });

    modelUpdates.push({
      selector: selector.concat(['authErrorMessage']),
      newValue: null,
    });

    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  /**
   * Verify existing auth session validity and logout if it is expired or session disabled from admin
   */
  verifyAuthSession = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params;
    const {skipPostSignOutRedirect} = payload;
    const signOutRedirectScreenId = model.get('signOutRedirectScreenId');
    // const loggedInUserAccessToken = model.get('loggedInUserAccessToken');
    const loggedInUserAccessToken = (await LocalStorage.getValue('loggedInUserAccessToken')) as any;

    let newModelUpdates: ModelChange[] = [];

    try {
      const queries = this.getQueries();

      const loginSessionExpiredMessage = model?.get('loginSessionExpiredMessage');

      if (!loggedInUserAccessToken?.accessToken) {
        newModelUpdates.push({
          selector: selector.concat(['loggedInUserAccessToken']),
          newValue: null,
        });
        dispatch(modelUpdateAction(newModelUpdates, undefined, true));

        if (!signOutRedirectScreenId) {
          console.log(`Please configure signOut screen in shopify`);
        }

        if (!skipPostSignOutRedirect) {
          dispatch(navigateToScreen(signOutRedirectScreenId, {}));
        }

        if (!_.isEmpty(loginSessionExpiredMessage)) {
          toast.show(loginSessionExpiredMessage, {
            type: 'success',
            placement: 'bottom',
            duration: 3000,
            style: {marginBottom: 80},
          });
        }

        return;
      }

      const {transformedData: fetchCustomerResponse} = await this.queryExecutor(model, queries?.FetchCustomer, {
        customerAccessToken: loggedInUserAccessToken?.accessToken,
      });

      if (!fetchCustomerResponse?.id) {
        console.log(`Error fetchCustomerResponse`, fetchCustomerResponse);

        const resetloggedInUserValue = null;
        await this.storeloggedInUserAccessToken(resetloggedInUserValue);
        await this.storeLoggedInUserDetails(resetloggedInUserValue);

        newModelUpdates.push({
          selector: selector.concat(['loggedInUser']),
          newValue: null,
        });

        newModelUpdates.push({
          selector: selector.concat(['loggedInUserAccessToken']),
          newValue: null,
        });
        dispatch(modelUpdateAction(newModelUpdates, undefined, true));

        if (!signOutRedirectScreenId) {
          console.log(`Please configure signOut screen in shopify`);
        }

        if (!skipPostSignOutRedirect) {
          dispatch(navigateToScreen(signOutRedirectScreenId, {}));
        }

        if (!_.isEmpty(loginSessionExpiredMessage)) {
          toast.show(loginSessionExpiredMessage, {
            type: 'success',
            placement: 'bottom',
            duration: 3000,
            style: {marginBottom: 80},
          });
        }
        return;
      }

      await this.storeLoggedInUserDetails(fetchCustomerResponse);
      newModelUpdates.push({
        selector: selector.concat(['loggedInUser']),
        newValue: fetchCustomerResponse,
      });
      dispatch(modelUpdateAction(newModelUpdates, undefined, true));
    } catch (error) {
      console.log(`error in verifyAuthSession`);
    }
  };

  customerActivateByUrl = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const payload = params;
    const {successMessage, skipPostAuthRedirect, errorMessage, badParameterMessage} = payload;
    const queries = this.getQueries();

    try {
      const signInRedirectScreenId = model.get('signInRedirectScreenId');
      dispatch(
        modelUpdateAction(
          [
            {
              selector: selector.concat(['authLoader']),
              newValue: true,
            },
            {
              selector: selector.concat(['authSuccessMessage']),
              newValue: null,
            },
            {
              selector: selector.concat(['authErrorMessage']),
              newValue: null,
            },
          ],
          undefined,
          true,
        ),
      );

      const {transformedData: activateCustomerResp, transformedError: error} = await this.queryExecutor(
        model,
        queries?.CustomerActivateByUrl,
        params,
      );

      if (!_.isEmpty(error)) {
        console.log(`error`, error);
        this.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          error,
          badParameterMessage,
        );
        return {success: false, errorMessage: error};
      }

      if (!signInRedirectScreenId) {
        console.log(`Please configure signIn screen in shopify`);
      }

      this.storeloggedInUserAccessToken(activateCustomerResp.customerAccessToken);
      this.storeLoggedInUserDetails(activateCustomerResp.customer);

      if (!skipPostAuthRedirect) {
        dispatch(navigateToScreen(signInRedirectScreenId, {}));
      }

      dispatch(
        modelUpdateAction([
          {
            selector: selector.concat('loggedInUser'),
            newValue: activateCustomerResp.customer,
          },
          {
            selector: selector.concat('loggedInUserAccessToken'),
            newValue: activateCustomerResp.customerAccessToken,
          },
        ]),
      );

      await this.postUserSignIn(dispatch, config, model, selector, params, appConfig, appModel);

      this.setSuccessMessage(dispatch, config, model, selector, skipPostAuthRedirect, {}, successMessage);
      return {success: true, errorMessage: null};
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
      return {success: false, errorMessage: error};
    }
  };

  /*
  New Customer API Functions.
  */
  // SOME CONSTANT FROM SHOPIFY
  SHOPIFY_CUSTOMER_API_CLIENT_ID_CONSTANT = '30243aa5-17c1-465a-8493-944bcc4e88aa';
  axiosInstance = axios.create();
  private currentAppState: AppStateStatus = AppState.currentState;
  private appStateSubscription: ReturnType<typeof AppState.addEventListener> | null = null;
  private refreshIntervalId: ReturnType<typeof setInterval> | null = null;

  private getShopifyAuthUrl = async (
    customerApiAuthBaseUrl: string,
    customerApiClientId: string,
    customerApiRedirectUri: string,
  ) => {
    const authorizationRequestUrl = new URL(`${customerApiAuthBaseUrl}/oauth/authorize`);
    authorizationRequestUrl.searchParams.append('scope', 'openid email customer-account-api:full');
    authorizationRequestUrl.searchParams.append('client_id', customerApiClientId);
    authorizationRequestUrl.searchParams.append('response_type', 'code');
    authorizationRequestUrl.searchParams.append('redirect_uri', customerApiRedirectUri);

    const verifier = generateCodeVerifier();
    logger.info('ShopifyCustomerApi::getShopifyAuthUrl verifier: ', verifier);
    const challenge = generateCodeChallenge(verifier);
    logger.info('ShopifyCustomerApi::getShopifyAuthUrl challenge: ', challenge);

    await LocalStorage.setValue(CUSTOMER_ACCOUNT_CODE_VERIFIER_KEY_FOR_LOCAL_STORAGE, verifier);

    authorizationRequestUrl.searchParams.append('code_challenge', challenge);
    authorizationRequestUrl.searchParams.append('code_challenge_method', 'S256');
    const shopifyAuthUrl = authorizationRequestUrl.href.replace('authorize/', 'authorize');

    return shopifyAuthUrl;
  };

  private obtainAccessToken = async (
    code: string,
    customerApiAuthBaseUrl: string,
    customerApiClientId: string,
    customerApiRedirectUri: string,
  ) => {
    const codeVerifier = (await LocalStorage.getValue(CUSTOMER_ACCOUNT_CODE_VERIFIER_KEY_FOR_LOCAL_STORAGE)) as string;
    logger.info('ShopifyCustomerApi::obtainAccessToken codeVerifier: ', codeVerifier);

    const params = {
      grant_type: 'authorization_code',
      redirect_uri: customerApiRedirectUri,
      client_id: customerApiClientId,
      code: code,
      code_verifier: codeVerifier,
    };
    logger.info('Sending Token Request params', qs.stringify(params));
    const response = await this.axiosInstance.request({
      method: 'POST',
      url: `${customerApiAuthBaseUrl}/oauth/token`,
      headers: {'content-type': 'application/x-www-form-urlencoded'},
      data: qs.stringify(params),
    });

    const {access_token, expires_in, id_token, refresh_token} = response.data;

    const timestamp = new Date().getTime();

    await LocalStorage.setValue(CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE, {
      access_token,
      expires_in,
      id_token,
      refresh_token,
      timestamp,
    });
    logger.info('obtainAccessToken', {access_token, expires_in, id_token, refresh_token, timestamp});

    // await this.makeUseAccessToken(access_token, customerApiAuthBaseUrl, customerApiClientId);
  };

  private refreshAccessToken = async (
    oldAccessTokenData: any,
    customerApiAuthBaseUrl: string,
    customerApiClientId: string,
    customerApiRedirectUri: string,
  ) => {
    const params = {
      grant_type: 'refresh_token',
      client_id: customerApiClientId,
      refresh_token: oldAccessTokenData?.refresh_token,
    };
    logger.info('refreshAccessToken Sending Refresh Token Request params', qs.stringify(params));
    const response = await this.axiosInstance.request({
      method: 'POST',
      url: `${customerApiAuthBaseUrl}/oauth/token`,
      headers: {'content-type': 'application/x-www-form-urlencoded'},
      data: qs.stringify(params),
    });

    const {access_token, expires_in, refresh_token} = response.data;

    const timestamp = new Date().getTime();

    // const oldAccessTokenData = await LocalStorage.getValue(CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE) as any;
    const newAccessTokenData = {
      ...oldAccessTokenData,
      access_token,
      expires_in,
      refresh_token,
      timestamp,
    };
    await LocalStorage.setValue(CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE, newAccessTokenData);
    logger.info('refreshAccessToken', newAccessTokenData);

    // await this.makeUseAccessToken(access_token, customerApiAuthBaseUrl, customerApiClientId);
  };

  // private makeUseAccessToken = async (
  //   accessToken: string,
  //   customerApiAuthBaseUrl: string,
  //   customerApiClientId: string,
  // ) => {
  //   const params = {
  //     grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
  //     client_id: customerApiClientId,
  //     audience: this.SHOPIFY_CUSTOMER_API_CLIENT_ID_CONSTANT,
  //     subject_token: accessToken,
  //     subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
  //     scopes: 'https://api.customers.com/auth/customer.graphql',
  //   };

  //   logger.info('Sending Access Token params', qs.stringify(params));
  //   const response = await this.axiosInstance.request({
  //     method: 'POST',
  //     url: `${customerApiAuthBaseUrl}/oauth/token`,
  //     headers: {'content-type': 'application/x-www-form-urlencoded'},
  //     data: qs.stringify(params),
  //   });

  //   const {access_token, expires_in} = await response.data;
  //   const timestamp = new Date().getTime();

  //   // await LocalStorage.setValue('ShopifyCustomerAccessToken', {access_token, expires_in});
  //   await LocalStorage.setValue(CUSTOMER_ACCOUNT_CUSTOMER_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE, {
  //     access_token,
  //     expires_in,
  //     timestamp,
  //   });
  //   logger.info('makeUseAccessToken', {access_token, expires_in, timestamp});
  // };

  private updateCustomerApiTokenAndCustomerData = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    accessTokenData: any,
  ) => {
    try {
      const queries = this.getQueries();

      const {transformedData: fetchCustomerResponse, transformedError: fetchCustomerError} =
        await this.fetchCustomerWithCustomerApi(
          model,
          queries?.FetchCustomerWithCustomerAPI,
          {},
          accessTokenData?.accessToken,
        );
      if (!fetchCustomerResponse) {
        console.log(`Error fetchCustomerResponse`, fetchCustomerResponse);
        return {customerData: null, error: _.first(fetchCustomerError)?.message};
      }

      const {transformedData: storeFrontTokenResponse, transformedError: storeFrontTokenError} =
        await this.fetchSFCustomerAccessTokenWithCustomerAPI(
          model,
          queries?.StorefrontCustomerAccessTokenCreateWithCustomerAPI,
          {},
          accessTokenData?.accessToken,
        );

      await this.storeloggedInUserAccessToken(accessTokenData);
      await this.storeLoggedInUserDetails(fetchCustomerResponse);

      const customerData = {
        loggedInUser: fetchCustomerResponse,
        loggedInUserAccessToken: accessTokenData,
        storefrontCustomerAccessTokenFromCustomerApi: storeFrontTokenResponse,
      };

      return customerData;
    } catch (error) {
      console.log(`Error updateCustomerApiTokenAndCustomerData`, error);
      return {success: false, error, errorMessage: error};
    }
  };

  private updateCustomerAPIQueryRunner = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    accessTokenData: any,
  ) => {
    try {
      const customerAPIQueryRunner = apolloQueryRunner();
      const customerApiUrl = model.get('customerApiUrl');
      await customerAPIQueryRunner.initClient(customerApiUrl, () => {
        return {
          headers: {
            Authorization: accessTokenData?.accessToken,
          },
        };
      });
      const queryRunnerModelUpdates = [
        {
          selector: selector.concat(['customerAPIQueryRunner']),
          newValue: customerAPIQueryRunner,
        },
      ];
      dispatch(modelUpdateAction(queryRunnerModelUpdates, undefined, true));
    } catch (error) {
      console.log(`Error updateCustomerAPIQueryRunner`, error);
    }
  };

  private async updateModelAndToast(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    customerData: any,
    message: string,
  ) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    if (customerData?.error) {
      toast.show('Something went wrong! Please try again later', {
        type: 'error',
        placement: 'bottom',
        duration: 1000,
        style: {marginBottom: 80},
      });

      return;
    }

    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    if (skipPostAuthRedirect) {
      newModelUpdates.push({
        selector: selector.concat(['authSuccessMessage']),
        newValue: message,
      });
    }
    if (message) {
      toast.show(message, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private triggerCustomerAccountAuthAnalyticsEvent = async (dispatch: any, loggedInUser: any) => {
    try {
      const creationDate = loggedInUser?.creationDate;

      if (creationDate) {
        const creationTime = new Date(creationDate).getTime();
        const currentTime = new Date().getTime();

        if (!isNaN(creationTime)) {
          // Since Customer API doesn't give us any hint on whether the user has just signed up or logged in
          // If user's creation date is within the last 5 minutes, considering it as signup for now
          const isSignUp = currentTime - creationTime < 5 * 60 * 1000;

          dispatch(
            sendAnalyticsEvent('track', isSignUp ? 'signup' : 'login', {
              userId: _.get(loggedInUser, 'id', ''),
              firstName: _.get(loggedInUser, 'firstName', '') || '',
              lastName: _.get(loggedInUser, 'lastName', '') || '',
              emailId: _.get(loggedInUser, 'email', '') || '',
              contactNumber: _.get(loggedInUser, 'phone', '') || '',
            }),
          );
        }
      }
    } catch (err) {
      logger.error('error during sending auth anaytics: ', err);
    }
  };

  private triggerAutomaticTokenRefresh = (dispatch, config, model) => {
    if (this.refreshIntervalId) return;

    const refreshCustomerApiAccessTokenAction = {
      pluginConfig: config,
      pluginModel: model,
      pluginSelector: ['shopify'],
      eventModelJS: {
        value: 'refreshCustomerApiAccessToken',
        params: {},
      },
    };

    this.refreshIntervalId = setInterval(() => {
      dispatch(triggerAction(refreshCustomerApiAccessTokenAction));
    }, 900000); // 15 minutes
  };

  private clearRefreshInterval = () => {
    if (this.refreshIntervalId) {
      // Remove Interval and clean up refreshIntervalId
      clearInterval(this.refreshIntervalId);
      this.refreshIntervalId = null;
    }
  };

  private addAppStateListener = (dispatch, config, model) => {
    if (this.appStateSubscription) return;

    const refreshCustomerApiAccessTokenAction = {
      pluginConfig: config,
      pluginModel: model,
      pluginSelector: ['shopify'],
      eventModelJS: {
        value: 'refreshCustomerApiAccessToken',
        params: {},
      },
    };

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (this.currentAppState.match(/inactive|background/) && nextAppState === 'active') {
        logger.info('BACK TO FOCUS', nextAppState);
        dispatch(triggerAction(refreshCustomerApiAccessTokenAction));
      }
      this.currentAppState = nextAppState;
    };

    this.appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
  };

  private removeAppStateListener = () => {
    if (this.appStateSubscription) {
      // Remove listener and clean up subscription
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
  };

  signInUserCustomerApi = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      // let newModelUpdates: ModelChange[] = [];
      // newModelUpdates.push({
      //   selector: selector.concat(['customerEmailPreLogin']),
      //   newValue: null,
      // });
      // newModelUpdates.push({
      //   selector: selector.concat(['preAuthVerifyEmailState']),
      //   newValue: '',
      // });
      // dispatch(modelUpdateAction(newModelUpdates, undefined, true));
      const customerApiAuthBaseUrl = model.get('customerApiAuthBaseUrl');
      const customerApiClientId = model.get('customerApiClientId');
      const customerApiRedirectUri = model.get('customerApiRedirectUri');
      const customerApiLoginScreen = model.get('customerApiLoginScreen');

      if (_.isEmpty(customerApiAuthBaseUrl) || _.isEmpty(customerApiClientId) || _.isEmpty(customerApiRedirectUri)) {
        toast.show('Customer Account API Configuration Incomplete', {
          type: 'error',
          placement: 'bottom',
          duration: 1000,
          style: {marginBottom: 80},
        });

        return;
      }

      logger.info(
        'signInUserCustomerApi',
        customerApiAuthBaseUrl,
        customerApiClientId,
        customerApiRedirectUri,
        customerApiLoginScreen,
      );

      const loginURL = await this.getShopifyAuthUrl(
        customerApiAuthBaseUrl,
        customerApiClientId,
        customerApiRedirectUri,
      );

      dispatch(navigateToScreen(customerApiLoginScreen, {url: loginURL}));
    } catch (error) {
      console.log(`Error signInResponse`, error);
      return {success: false, errorMessage: error};
    }
  };

  getAccessTokenFromCode = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
      {
        selector: selector.concat(['authSuccessMessage']),
        newValue: null,
      },
      {
        selector: selector.concat(['authErrorMessage']),
        newValue: null,
      },
    ];

    const {code, signInRedirectScreenIdOverride, skipPostAuthRedirect} = params;
    const signInRedirectScreenId = model.get('signInRedirectScreenId');

    try {
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const customerApiAuthBaseUrl = model.get('customerApiAuthBaseUrl');
      const customerApiClientId = model.get('customerApiClientId');
      const customerApiRedirectUri = model.get('customerApiRedirectUri');

      logger.info('getAccessTokenFromCode', customerApiAuthBaseUrl, customerApiClientId, customerApiRedirectUri);

      await this.obtainAccessToken(code, customerApiAuthBaseUrl, customerApiClientId, customerApiRedirectUri);

      const {access_token, expires_in, timestamp} = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;
      logger.info('getAccessTokenFromCode', {access_token, expires_in, timestamp});

      const customerAccessTokenData = {
        accessToken: access_token,
        expiresIn: expires_in,
        timestamp,
      };

      this.triggerAutomaticTokenRefresh(dispatch, config, model);
      this.addAppStateListener(dispatch, config, model);

      await this.updateCustomerAPIQueryRunner(dispatch, config, model, selector, customerAccessTokenData);

      const customerData = await this.updateCustomerApiTokenAndCustomerData(
        dispatch,
        config,
        model,
        selector,
        customerAccessTokenData,
      );

      this.updateModelAndToast(dispatch, config, model, selector, true, customerData, 'Logged In Successfully');

      if (customerData?.loggedInUser) {
        this.triggerCustomerAccountAuthAnalyticsEvent(dispatch, customerData?.loggedInUser);
        if (!customerData?.loggedInUser?.tags?.includes('APPTILE_MOBILE_APP_USER')) {
          const appId = model.get('appId');
          await this.axiosInstance.request({
            method: 'POST',
            url: `https://api.apptile.io/registry/users/register`,
            headers: {
              'Content-Type': 'application/json',
            },
            data: {
              platformName: 'SHOPIFY',
              appId: appId,
              platformUserId: _.get(customerData?.loggedInUser, 'id', ''),
            },
          });
        } else {
          console.log('customer already tagged');
        }
      }

      logger.info(
        'getAccessTokenFromCode Redirect Configuration',
        signInRedirectScreenId,
        signInRedirectScreenIdOverride,
        skipPostAuthRedirect,
      );
      if (skipPostAuthRedirect) return;

      if (!signInRedirectScreenId && !signInRedirectScreenIdOverride) {
        logger.info('Configure Post Sign In Redirect Screen globally or in action!');
        return;
      }
      const redirectScreenId = _.isEmpty(signInRedirectScreenIdOverride)
        ? signInRedirectScreenId
        : signInRedirectScreenIdOverride;
      dispatch(navigateToScreen(redirectScreenId, {}));
    } catch (error) {
      logger.error(`Error getAccessTokeFromCode`, error);
      toast.show('Something went wrong! Please try again later', {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
      const redirectScreenId = _.isEmpty(signInRedirectScreenIdOverride)
        ? signInRedirectScreenId
        : signInRedirectScreenIdOverride;
      dispatch(navigateToScreen(redirectScreenId, {}));
      return {success: false, errorMessage: error};
    }
  };

  signOutUserCustomerApi = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const payload = params;
    const {skipPostSignOutRedirect} = payload;
    const signOutRedirectScreenId = model.get('signOutRedirectScreenId');

    try {
      const resetloggedInUserValue = null;
      await this.storeloggedInUserAccessToken(resetloggedInUserValue);
      await this.storeLoggedInUserDetails(resetloggedInUserValue);

      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['loggedInUser']),
        newValue: resetloggedInUserValue,
      });

      modelUpdates.push({
        selector: selector.concat(['loggedInUserAccessToken']),
        newValue: resetloggedInUserValue,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));

      // if (!signOutRedirectScreenId) {
      //   console.log(`Please configure signOut screen in shopify`);
      // }

      // if (!skipPostSignOutRedirect) {
      //   dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      // }

      const accessTokenData = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;
      logger.info('signOutUserCustomerApi', accessTokenData);
      const customerApiAuthBaseUrl = model.get('customerApiAuthBaseUrl');
      const customerApiLoginScreen = model.get('customerApiLoginScreen');

      logger.info('signOutUserCustomerApi', customerApiAuthBaseUrl, customerApiLoginScreen);

      const logoutURL = `${customerApiAuthBaseUrl}/logout?id_token_hint=${accessTokenData?.id_token}`;
      logger.info('signOutUserCustomerApi', logoutURL);
      dispatch(navigateToScreen(customerApiLoginScreen, {url: logoutURL}));

      await LocalStorage.removeItem(CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE);
      // await LocalStorage.removeItem(CUSTOMER_ACCOUNT_CUSTOMER_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE);

      await this.postUserSignOut(dispatch, config, model, selector, params, appConfig, appModel);

      this.clearRefreshInterval();
      this.removeAppStateListener();
    } catch (error) {
      console.log(`error while sign out`);
      if (!skipPostSignOutRedirect) {
        dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      }
    }
  };

  verifyAuthSessionCustomerApi = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params;
    const {skipPostSignOutRedirect} = payload;
    const signOutRedirectScreenId = model.get('signOutRedirectScreenId');
    // const loggedInUserAccessToken = model.get('loggedInUserAccessToken');
    const loggedInUserAccessToken = (await LocalStorage.getValue('loggedInUserAccessToken')) as any;

    let newModelUpdates: ModelChange[] = [];

    try {
      const accessTokenData = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;

      if (!loggedInUserAccessToken?.accessToken || !accessTokenData?.refresh_token) {
        logger.info(
          'verifyAuthSessionCustomerApi',
          `no valid accessToken/refreshToken ${loggedInUserAccessToken?.accessToken}, ${accessTokenData?.refresh_token}`,
        );
        const resetloggedInUserValue = null;
        await this.storeloggedInUserAccessToken(resetloggedInUserValue);
        await this.storeLoggedInUserDetails(resetloggedInUserValue);

        newModelUpdates.push({
          selector: selector.concat(['loggedInUserAccessToken']),
          newValue: null,
        });
        newModelUpdates.push({
          selector: selector.concat(['loggedInUserAccessToken']),
          newValue: null,
        });
        dispatch(modelUpdateAction(newModelUpdates, undefined, true));

        if (!signOutRedirectScreenId) {
          console.log(`Please configure signOut screen in shopify`);
        }

        if (!skipPostSignOutRedirect) {
          dispatch(navigateToScreen(signOutRedirectScreenId, {}));
        }

        return;
      }

      const customerApiAuthBaseUrl = model.get('customerApiAuthBaseUrl');
      const customerApiClientId = model.get('customerApiClientId');
      const customerApiRedirectUri = model.get('customerApiRedirectUri');

      await this.refreshAccessToken(
        accessTokenData,
        customerApiAuthBaseUrl,
        customerApiClientId,
        customerApiRedirectUri,
      );

      const {access_token, expires_in, timestamp} = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;
      logger.info('verifyAuthSessionCustomerApi', {access_token, expires_in, timestamp});

      const customerAccessTokenData = {
        accessToken: access_token,
        expiresIn: expires_in,
        timestamp,
      };

      this.triggerAutomaticTokenRefresh(dispatch, config, model);
      this.addAppStateListener(dispatch, config, model);

      await this.updateCustomerAPIQueryRunner(dispatch, config, model, selector, customerAccessTokenData);

      const customerData = await this.updateCustomerApiTokenAndCustomerData(
        dispatch,
        config,
        model,
        selector,
        customerAccessTokenData,
      );

      this.updateModelAndToast(dispatch, config, model, selector, true, customerData, '');
    } catch (error) {
      logger.error(`error in verifyAuthSession`);

      const resetloggedInUserValue = null;
      // await this.storeloggedInUserAccessToken(resetloggedInUserValue);
      // await this.storeLoggedInUserDetails(resetloggedInUserValue);

      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['loggedInUser']),
        newValue: resetloggedInUserValue,
      });

      modelUpdates.push({
        selector: selector.concat(['loggedInUserAccessToken']),
        newValue: resetloggedInUserValue,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    }
  };

  refreshCustomerApiAccessToken = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
      {
        selector: selector.concat(['authSuccessMessage']),
        newValue: null,
      },
      {
        selector: selector.concat(['authErrorMessage']),
        newValue: null,
      },
    ];

    const {} = params;

    try {
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const accessTokenData = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;

      const customerApiAuthBaseUrl = model.get('customerApiAuthBaseUrl');
      const customerApiClientId = model.get('customerApiClientId');
      const customerApiRedirectUri = model.get('customerApiRedirectUri');

      await this.refreshAccessToken(
        accessTokenData,
        customerApiAuthBaseUrl,
        customerApiClientId,
        customerApiRedirectUri,
      );

      const {access_token, expires_in, timestamp} = (await LocalStorage.getValue(
        CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE,
      )) as any;
      logger.info('refreshCustomerApiAccessToken', {access_token, expires_in, timestamp});

      const customerAccessTokenData = {
        accessToken: access_token,
        expiresIn: expires_in,
        timestamp,
      };

      this.triggerAutomaticTokenRefresh(dispatch, config, model);
      this.addAppStateListener(dispatch, config, model);

      await this.updateCustomerAPIQueryRunner(dispatch, config, model, selector, customerAccessTokenData);

      const customerData = await this.updateCustomerApiTokenAndCustomerData(
        dispatch,
        config,
        model,
        selector,
        customerAccessTokenData,
      );

      this.updateModelAndToast(dispatch, config, model, selector, true, customerData, '');
    } catch (error) {
      logger.error(`Error refreshCustomerApiAccessToken`, error);
      // toast.show('Something went wrong! Please try again later', {
      //   type: 'error',
      //   placement: 'bottom',
      //   duration: 2000,
      //   style: {marginBottom: 80},
      // });
      // return {success: false, errorMessage: error};
    }
  };
}

const authActions = new AuthActions();
export default authActions;
