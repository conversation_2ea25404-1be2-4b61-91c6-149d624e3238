import {PluginEditorsConfig} from '@/root/apptile-core';
import {IApptileCartMergedCheckout} from '@/root/temp/app/plugins/datasource/Shopify-v1/types';
import {
  ActionHandler,
  GetRegisteredPluginInfo,
  LocalStorage,
  PluginConfig,
  Selector,
  getAppConstants,
  modelUpdateAction,
  navigateToScreen,
  sendAnalyticsEvent,
} from 'apptile-core';
import axios from 'axios';
import _ from 'lodash';
import {ShopifyQueryDetails, makeInputParamsResolver} from '..';
import {processShopifyGraphqlQueryResponse} from '../../utils';
import {
  SHOPIFY_CONFIG_CART_ID_KEY_DEFAULT,
  SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_MODEL_AUCTION_CART_SYNC_KEY,
  SHOPIFY_MODEL_CART_ERROR_KEY,
  SHOPIFY_MODEL_CART_ID_KEY,
  SHOPIFY_MODEL_CART_ITEMS_KEY,
  SHOPIFY_MODEL_CART_OBJECT_KEY,
  SHOPIFY_MODEL_CART_SYNC_KEY,
  SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY,
  SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY,
  SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY,
  SHOPIFY_SUBSCRIPTION_CART_STATUS,
  STOREFRONT_ACCESS_TOKEN,
} from '../constants';
import {UPDATE_CART_BUYER_IDENTITY_FOR_SHIPPING_ADDRESS} from '../mutations/customerRecovery';
import {CART_ATTRIBUTES_UPDATE, GET_CART_DETAILS} from '../queries/shoppingCart';
import {CUSTOMER_METAFIELD_FETCH} from '../queries/customerAuthentication';
import {
  CUSTOMER_METAFIELD_DELETE_CUSTOMER_API,
  CUSTOMER_METAFIELD_FETCH_CUSTOMER_API,
  CUSTOMER_METAFIELD_SET_CUSTOMER_API,
  CUSTOMER_METAFIELDS_FETCH_CUSTOMER_API,
} from '../queries/customerAPIQueries';

const apptileCheckoutCustomAtt = getAppConstants().APPTILE_CHECKOUT_CUSTOM_ATTRIBUTE as string;

export interface CheckoutActionInteface {
  increaseCartLineItemQuantity: ActionHandler;
  decreaseCartLineItemQuantity: ActionHandler;
  removeCartLineItem: ActionHandler;
  addCartLineItems: ActionHandler;
  alterCartLineItemsQuantities: ActionHandler;
  syncLocalCartStateWithShopify: ActionHandler;
  clearLocalCartState: ActionHandler;
  updateCartDiscountCode: ActionHandler;
  removeCartDiscountCode: ActionHandler;
  addCartAttributes: ActionHandler;
  addCartNote: ActionHandler;
  duplicateCart: ActionHandler;
  // Pooling cart in case of subscription order to get the status of cart null
  // as we do not have any reliable mechanism to check payment status
  getSubscriptionCartStatus: ActionHandler;
  // syncCheckoutState: ActionHandler;
  syncCheckoutState: ActionHandler;

  // Cart Assist Sub-Actions
  syncCartAssist: ActionHandler;
  clearCartAssist: ActionHandler;

  createCartIfNotExists: ActionHandler;
  createAuctionCart: ActionHandler;
}

export interface CartProductVariantQuantityChangeParam {
  merchandiseId: string;
  quantity: number;
  syncWithShopify: boolean;
  sellingPlanId: string;
  itemPrice?: number;
  successToastText?: string;
  cartLineId?: string;
  attributes?: any;
}

export interface CartAddVariantsChangeParam {
  itemsToAdd: [
    {
      merchandiseId: string;
      quantity: number;
      sellingPlanId?: string;
      itemPrice?: number;
      attributes?: any;
    },
  ];
  syncWithShopify: boolean;
  successToastText?: string;
}

export interface CartAlterVariantsQuantitiesParam {
  itemsToAdd: [
    {
      merchandiseId: string;
      quantity: number;
      sellingPlanId?: string;
      itemPrice?: number;
      attributes?: any;
    },
  ];
  itemsToRemove: [
    {
      merchandiseId: string;
      sellingPlanId?: string;
      attributes?: any;
    },
  ];
  overrideItemQuantities?: boolean;
  syncWithShopify: boolean;
  successToastText?: string;
}

export interface updateDiscountCodeParams {
  discountCode?: string;
  syncWithShopify: boolean;
}

export interface CartDuplicateParam {
  syncWithShopify: boolean;
}

export interface PollSubscriptionCart {
  cartId: string;
}

export interface UpdateCheckoutShippingAddressParams {
  cartId: string;
  customerAccessToken: string;
  addressId: string;
  shopDomain: string;
  redirectTo: string;
}

export const checkoutActionDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'screenSelector',
      name: 'orderSuccessScreenId',
      props: {
        label: 'Order Success Screen',
      },
    },
  ],
};

/**
 * newQuantity: offline Cart Line item quanity
 * quantity: Cart line item quantity synced with shopify
 * displayQuantity: Introduced to manage scenarion where we have'nt synced with shopify yet.
 */
class CheckoutActions implements CheckoutActionInteface {
  private getCartIdKey(config: PluginConfig) {
    const cartIdLocalStorageKey =
      config.config.get(SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE) || SHOPIFY_CONFIG_CART_ID_KEY_DEFAULT;
    return cartIdLocalStorageKey;
  }

  // private getCartLineItemsKey(config: PluginConfig) {
  //   const cartLineItemsLocalStorageKey = config.config.get(SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE);
  //   return cartLineItemsLocalStorageKey;
  // }

  // private getCartKey(config: PluginConfig) {
  //   const cartLocalStorageKey = config.config.get(SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE);
  //   return cartLocalStorageKey;
  // }

  // private getCheckoutKey(config: PluginConfig) {
  //   const cartLocalStorageKey = config.config.get(SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE);
  //   return cartLocalStorageKey;
  // }

  // private async storeLineItemsLocally(config: PluginConfig, cartLineItems: []) {
  //   const cartLineItemsLocalStorageKey = this.getCartLineItemsKey(config);
  //   await LocalStorage.setValue(cartLineItemsLocalStorageKey, cartLineItems);
  // }

  // private async storeCartObjectLocally(config: PluginConfig, cartObject) {
  //   const cartLocalStorageKey = this.getCartKey(config);
  //   if (cartObject) await LocalStorage.setValue(cartLocalStorageKey, cartObject);
  // }

  // private async deleteCartObjectLocally(config: PluginConfig) {
  //   const cartLocalStorageKey = this.getCartKey(config);
  //   await LocalStorage.removeItem(cartLocalStorageKey);
  // }

  // private async storeCheckoutObjectLocally(config: PluginConfig, checkoutObject) {
  //   const checkoutLocalStorageKey = this.getCheckoutKey(config);
  //   await LocalStorage.setValue(checkoutLocalStorageKey, checkoutObject);
  // }

  // private async getCartObjectFromLocalStorage(config: PluginConfig) {
  //   const cartLocalStorageKey = this.getCartKey(config)
  //   const cartObject = await LocalStorage.getValue(cartLocalStorageKey)
  //   return cartObject
  // }

  private async storeCartIdLocally(config: PluginConfig, cartId: string) {
    const cartIdKey = this.getCartIdKey(config);
    await LocalStorage.setValue(cartIdKey, cartId);
  }

  private async getCartIdFromLocalStorage(config: PluginConfig) {
    const cartIdLocalStorageKey = this.getCartIdKey(config);
    const cartId = await LocalStorage.getValue(cartIdLocalStorageKey);
    return cartId;
  }

  private async updateCartId(dispatch, config: PluginConfig, model, selector: Selector, cartId: string) {
    await this.storeCartIdLocally(config, cartId);
    this.initCart(dispatch, config, model, selector, {});
  }

  private async getCartObjectFromModel(model: any) {
    const cartObject = model.get(SHOPIFY_MODEL_CART_OBJECT_KEY);
    return cartObject;
  }

  private async fetchCartDetailsFromShopify(config: PluginConfig, model: any) {
    let cartObject,
      cartError = null;
    try {
      // let localCartObject = model.get(SHOPIFY_MODEL_CART_OBJECT_KEY);
      let cartId = await this.getCartIdFromLocalStorage(config);
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();

      // if (_.isEmpty(localCartObject)) {
      //   return {cartObject, cartError};
      // }

      let {transformedData, transformedError} = await this.queryExecutor(model, queries?.GetCartDetails, {
        cartId,
      });
      // Add check for apptileCheckoutCustomAtt
      if (transformedData && !this.hasApptileAttribute(transformedData)) {
        await this.ensureCartAttributes(model, cartId);
        // Fetch cart again to get updated attributes
        const result = await this.queryExecutor(model, queries?.GetCartDetails, {
          cartId,
        });
        transformedData = result.transformedData;
        transformedError = result.transformedError;
      }
      cartObject = transformedData;
      cartError = transformedError;
      return {cartObject, cartError};
    } catch (error) {
      return {cartObject: null, error};
    }
  }

  private async addCustomAttributeToCart(model: any, cartId: string) {
    const queryRunner = model.get('queryRunner');
    await queryRunner.runQuery('query', CART_ATTRIBUTES_UPDATE, {
      attributes: [
        {
          key: 'source_name',
          value: apptileCheckoutCustomAtt,
        },
      ],
      cartId,
    });
  }

  private async getActiveMetafieldForCartAssist(model: any, customerAccessToken: string) {
    let queryRunner;
    const namespace = 'Apptile-CartAssist';
    const key = 'smart_cart_id';

    const isCustomerAPIEnabled = model.get('useCustomerApi', false);

    if (isCustomerAPIEnabled) {
      queryRunner = model.get('customerAPIQueryRunner');
      const {data} = await queryRunner.runQuery('query', CUSTOMER_METAFIELD_FETCH_CUSTOMER_API, {namespace, key}, {});
      return data?.customer?.metafields[0]?.value ?? null;
    }

    queryRunner = model.get('queryRunner');
    const {data} = await queryRunner.runQuery(
      'query',
      CUSTOMER_METAFIELD_FETCH,
      {customerAccessToken, namespace, key},
      {},
    );
    return data?.customer?.metafields[0]?.value ?? null;
  }

  private async updateCustomerCartAssistMetafieldUsingCustomerApi(model: any, key: string, value: any) {
    const queryRunner = model.get('customerAPIQueryRunner');
    const loggedInUser = model.get('loggedInUser');

    if (loggedInUser?.id) {
      const metafields = [
        {
          // key: 'smart_cart_id',
          key,
          namespace: 'Apptile-CartAssist',
          ownerId: loggedInUser.id,
          value,
        },
      ];

      const data = await queryRunner.runQuery('query', CUSTOMER_METAFIELD_SET_CUSTOMER_API, {metafields}, {});
    }
    return;
  }

  private async assertValidCartExists(dispatch, config: PluginConfig, model, selector: Selector) {
    const customerAccessToken = !_.isEmpty(model.get('loggedInUserAccessToken')?.accessToken)
      ? model.get('loggedInUserAccessToken').accessToken
      : null;
    // Checking if there is an existing metafield while adding products
    // if present syncing the carts
    const cartAssistEnabled = model.get('cartAssistEnabled', false);
    if (cartAssistEnabled && customerAccessToken) {
      const currentCartId = await this.getCartIdFromLocalStorage(config);
      const cartIdFromMetafield = await this.getActiveMetafieldForCartAssist(model, customerAccessToken);
      const cartGidFromMetafield = `gid://shopify/Cart/${cartIdFromMetafield}`;
      if (cartIdFromMetafield && cartGidFromMetafield != currentCartId) {
        toast.show('Found your existing cart. Syncing...', {
          type: 'success',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
        await this.storeCartIdLocally(config, `gid://shopify/Cart/${cartIdFromMetafield}`);
        await this.addCustomAttributeToCart(model, `gid://shopify/Cart/${cartIdFromMetafield}`);
        return;
      }
    }

    const isCustomerAPIEnabled = model.get('useCustomerApi', false);
    let sfAccessTokenFromCustomerApi = null;
    if (isCustomerAPIEnabled) {
      const sfAccessTokenData = model.get('storefrontCustomerAccessTokenFromCustomerApi');
      sfAccessTokenFromCustomerApi = !_.isEmpty(sfAccessTokenData?.customerAccessToken)
        ? sfAccessTokenData?.customerAccessToken
        : null;
    }

    const currentCartId = await this.getCartIdFromLocalStorage(config);
    const currentCartObject = await this.getCartObjectFromModel(model);
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);

    // Cart exists exit out !
    if (currentCartId && !_.isEmpty(currentCartObject)) return;
    if (!currentCartId) {
      // When there is no cart from the metafield and also from the localstorage (new cart created in mobile)
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();
      const email = !_.isEmpty(model.get('loggedInUser')?.email) ? model.get('loggedInUser').email : null;
      console.log(`Creating new cart as currentCartId: ${currentCartId} and cartObject: ${currentCartObject}`);
      const loadingModelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const shoppingCartCreateQuery = queries?.CreateShoppingCartV1;
      const idempotencyKey = new Date().getTime().toString(36) + new Date().getUTCMilliseconds();

      const createCartInput = {
        customAttributes: [
          {
            key: 'source_name',
            value: apptileCheckoutCustomAtt,
          },
          {
            key: 'apptile_idempotency_key',
            value: idempotencyKey,
          },
        ],
        lines: [],
        customerAccessToken: isCustomerAPIEnabled ? sfAccessTokenFromCustomerApi : customerAccessToken,
        email,
      };

      let {transformedData, transformedError: error} = await this.queryExecutor(
        model,
        shoppingCartCreateQuery,
        createCartInput,
      );
      await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);
      const loadedModelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
    }
    if (currentCartObject && !this.hasApptileAttribute(currentCartObject)) {
      await this.ensureCartAttributes(model, currentCartObject.id);
    }
  }

  // private async syncLocalStateWithShopify(
  //   dispatch: any,
  //   config: PluginConfig,
  //   model: any,
  //   selector: Selector,
  //   lineItems: any,
  //   syncWithShopify: boolean,
  //   discountCode: string,
  //   removeDiscount: boolean,
  //   createNewCart: boolean = false,
  // ) {
  //   const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  //   const queries = shopifyDatasourceConfig?.plugin?.getQueries();

  //   const customerAccessToken = !_.isEmpty(model.get('loggedInUserAccessToken')?.accessToken)
  //     ? model.get('loggedInUserAccessToken').accessToken
  //     : null;

  //   const email = !_.isEmpty(model.get('loggedInUser')?.email) ? model.get('loggedInUser').email : null;

  //   // const customerAccessToken = model.get('customerAccessToken');
  //   // const queryRunner = model.get('queryRunner');
  //   // const shopConfig = model.get('shop');
  //   let updatedCartObject = null;
  //   let {cartObject, cartError} = await this.fetchCartDetailsFromShopify(model);
  //   updatedCartObject = {transformedData: cartObject, transformedError: cartError};

  //   if (_.isEmpty(cartObject) || !_.isEmpty(cartError) || createNewCart) {
  //     console.log(
  //       `Creating new cart as cartObject: ${cartObject}, cartError:${cartError}, createNewCart:${createNewCart}`,
  //     );

  //     const shoppingCartCreateQuery = queries?.CreateShoppingCartV1;
  //     const {addLines: linesToAdd} = this.getShopifyAndLocalItemDiff(lineItems, []);
  //     const idempotencyKey = new Date().getTime().toString(36) + new Date().getUTCMilliseconds();

  //     const createCartInput = {
  //       customAttributes: [
  //         {
  //           key: 'source_name',
  //           value: apptileCheckoutCustomAtt,
  //         },
  //         {
  //           key: 'apptile_idempotency_key',
  //           value: idempotencyKey,
  //         },
  //       ],
  //       lines: linesToAdd,
  //       customerAccessToken,
  //       email,
  //     };

  //     let {transformedData, transformedError: error} = await this.queryExecutor(
  //       model,
  //       shoppingCartCreateQuery,
  //       createCartInput,
  //     );
  //     return {cartObject: transformedData, error};
  //   } else {
  //     // To Speed up cart loading experience
  //     const updatedLineItems = this.getLineItemsFromCart(cartObject);
  //     let oldlineItems = updatedLineItems.filter((e: any) => e.merchandiseId && e.merchandiseId.trim());

  //     const lineItemsSelector = selector.concat([SHOPIFY_MODEL_CART_ITEMS_KEY]);
  //     const cartIdSelector = selector.concat([SHOPIFY_MODEL_CART_ID_KEY]);
  //     logger.info('Synced CartID : ', cartObject?.id);
  //     dispatch(
  //       modelUpdateAction(
  //         [
  //           {
  //             selector: cartIdSelector,
  //             newValue: cartObject?.id,
  //           },
  //           {
  //             selector: lineItemsSelector,
  //             newValue: [...oldlineItems],
  //           },
  //         ],
  //         undefined,
  //         true,
  //       ),
  //     );
  //   }

  //   const cartLineItems = cartObject.lines;
  //   // const cartLineItemsMap = _.keyBy(cartLineItems, function (lineItem) {
  //   //   return !lineItem?.sellingPlanAllocation?.sellingPlan?.id
  //   //     ? `${lineItem.variant.id}-${lineItem.quantity}`
  //   //     : `${lineItem.variant.id}-${lineItem.sellingPlanAllocation.sellingPlan.id}-${lineItem.quantity}`;
  //   // });

  //   // const localLineItemsMap = _.keyBy(lineItems, function (lineItem) {
  //   //   return lineItem.sellingPlanId
  //   //     ? `${lineItem.merchandiseId}-${lineItem.sellingPlanId}-${lineItem.quantity}`
  //   //     : `${lineItem.merchandiseId}-${lineItem.quantity}`;
  //   // });

  //   // const cartKeys = Object.keys(cartLineItemsMap);
  //   // const localKeys = Object.keys(localLineItemsMap);

  //   // const linesToUpdate = [];
  //   // const linesToAdd = [];
  //   // localKeys.forEach(function (mId) {
  //   //   if (cartLineItemsMap[mId]) {
  //   //     const lineItemId = cartLineItemsMap[mId].id;
  //   //     if (cartLineItemsMap[mId].quantity === localLineItemsMap[mId].quantity && localLineItemsMap[mId].newQuantity) {
  //   //       linesToUpdate.push({
  //   //         id: lineItemId,
  //   //         merchandiseId: localLineItemsMap[mId].merchandiseId,
  //   //         quantity: localLineItemsMap[mId].newQuantity,
  //   //       });
  //   //     }
  //   //   } else {
  //   //     const sellingPlanItem = localLineItemsMap[mId]?.sellingPlanId
  //   //       ? {sellingPlanId: localLineItemsMap[mId].sellingPlanId}
  //   //       : {};
  //   //     linesToAdd.push({
  //   //       merchandiseId: localLineItemsMap[mId].merchandiseId,
  //   //       quantity: localLineItemsMap[mId].quantity,
  //   //       ...sellingPlanItem,
  //   //     });
  //   //   }
  //   // });
  //   // const mIdsToDelete = cartKeys.filter(function (mId) {
  //   //   return localKeys.indexOf(mId) < 0;
  //   // });
  //   const {
  //     addLines: linesToAdd,
  //     updateLines: linesToUpdate,
  //     deleteIds: mIdsToDelete,
  //   } = this.getShopifyAndLocalItemDiff(lineItems, cartLineItems);

  //   // We tried to perform cart edit operation in parallelly, but shopify API is not thread safe.
  //   // The output was not as expected. So all edits will be performed sequentially.
  //   // let isCartUpdated = false;
  //   if (linesToAdd && linesToAdd.length > 0) {
  //     updatedCartObject = await this.queryExecutor(model, queries?.CartLinesAdd, {
  //       cartId: cartObject.id,
  //       lines: linesToAdd,
  //     });
  //   }
  //   if (linesToUpdate && linesToUpdate.length > 0) {
  //     // isCartUpdated = true;
  //     updatedCartObject = await this.queryExecutor(model, queries?.CartLinesUpdate, {
  //       lines: linesToUpdate,
  //       cartId: cartObject.id,
  //     });
  //   }
  //   if (mIdsToDelete && mIdsToDelete.length > 0) {
  //     // isCartUpdated = true;
  //     // const lineIds = mIdsToDelete.map(x => cartLineItemsMap[x].id);
  //     updatedCartObject = await this.queryExecutor(model, queries?.CartLinesRemove, {
  //       cartId: cartObject.id,
  //       lineIds: mIdsToDelete,
  //     });
  //   }

  //   if (discountCode) {
  //     // isCartUpdated = true;
  //     updatedCartObject = await this.queryExecutor(model, queries?.CartDiscountUpdate, {
  //       cartId: cartObject.id,
  //       discountCodes: [discountCode],
  //     });
  //   }

  //   if (removeDiscount) {
  //     // isCartUpdated = true;
  //     updatedCartObject = await this.queryExecutor(model, queries?.CartDiscountUpdate, {
  //       cartId: cartObject.id,
  //       discountCodes: [],
  //     });
  //   }

  //   if (customerAccessToken && !cartObject?.buyerIdentity?.customerId) {
  //     this.queryExecutor(model, queries?.CartBuyerIdentityUpdate, {
  //       cartId: cartObject.id,
  //       customerAccessToken,
  //       email,
  //     });
  //   }

  //   if (!updatedCartObject) {
  //     updatedCartObject = await this.queryExecutor(model, queries?.GetCartDetails, {cartId: cartObject.id});
  //   }

  //   const {transformedData, transformedError: error} = updatedCartObject;
  //   return {cartObject: transformedData, error};
  // }

  initCart = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    logger.info('[SHOPIFY] Init Cart Called !!!');
    const cartId = await this.getCartIdFromLocalStorage(config);
    const cartIdSelector = selector.concat([SHOPIFY_MODEL_CART_ID_KEY]);
    const cartObjectSelector = selector.concat([SHOPIFY_MODEL_CART_OBJECT_KEY]);
    const lineItemsSelector = selector.concat([SHOPIFY_MODEL_CART_ITEMS_KEY]);
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    if (!cartId) {
      dispatch(
        modelUpdateAction(
          [
            {
              selector: cartIdSelector,
              newValue: null,
            },
            {
              selector: cartObjectSelector,
              newValue: undefined,
            },
            {
              selector: lineItemsSelector,
              newValue: [],
            },
            {
              selector: selector.concat([SHOPIFY_MODEL_CART_ERROR_KEY]),
              newValue: [],
            },
          ],
          undefined,
          true,
        ),
      );
    } else {
      dispatch(
        modelUpdateAction(
          [
            {
              selector: shopifySyncSelector,
              newValue: true,
            },
          ],
          undefined,
          true,
        ),
      );
      const {cartObject, cartError} = await this.fetchCartDetailsFromShopify(config, model);
      const cartLineItems = this.getLineItemsFromCart(cartObject);
      logger.info('[SHOPIFY] Cart Fetched for cartId: ' + cartId);
      dispatch(
        modelUpdateAction(
          [
            {
              selector: cartIdSelector,
              newValue: cartId,
            },
            {
              selector: cartObjectSelector,
              newValue: cartObject,
            },
            {
              selector: lineItemsSelector,
              newValue: [...cartLineItems],
            },
            {
              selector: selector.concat([SHOPIFY_MODEL_CART_ERROR_KEY]),
              newValue: cartError,
            },
            {
              selector: shopifySyncSelector,
              newValue: false,
            },
          ],
          undefined,
          true,
        ),
      );
    }
  };

  increaseCartLineItemQuantity = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }
    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as CartProductVariantQuantityChangeParam;
    const {merchandiseId, quantity, syncWithShopify, sellingPlanId, attributes, itemPrice, successToastText} = payload;

    if (_.isEmpty(merchandiseId)) {
      logger.error(`invalid merchandiseId`, merchandiseId);
      return;
    }

    let lineItems = [...model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY)];

    if (lineItems && lineItems.length > 0) {
      const matchingLineItem = lineItems.filter(x => {
        if (sellingPlanId) {
          return x.merchandiseId === merchandiseId && x.sellingPlanId === sellingPlanId;
        } else {
          return x.merchandiseId === merchandiseId;
        }
      })[0];

      if (matchingLineItem) {
        matchingLineItem.newQuantity = (matchingLineItem.newQuantity ?? matchingLineItem.quantity) + quantity;
        matchingLineItem.displayQuantity = matchingLineItem.newQuantity;

        dispatch(sendAnalyticsEvent('track', 'updateCart', matchingLineItem));

        this.sendUpdateCartAnalyticsEvent(dispatch, lineItems);
      } else {
        let maxCartLineItemLimit = model?.get('maxCartLineItemLimit');
        let cartLineItemLimitExceededMessage = model?.get('cartLineItemLimitExceededMessage');

        if (lineItems?.length > 0 && lineItems.length >= maxCartLineItemLimit) {
          toast.show(cartLineItemLimitExceededMessage || 'You can not add more than 25 items on cart.', {
            type: 'error',
            placement: 'bottom',
            duration: 2000,
            style: {marginBottom: 80},
          });
          return;
        }

        const sellingPlanItem = sellingPlanId ? {sellingPlanId: sellingPlanId} : {};
        lineItems.push({
          merchandiseId: merchandiseId,
          newQuantity: quantity,
          quantity: quantity,
          displayQuantity: quantity,
          itemPrice: itemPrice,
          ...sellingPlanItem,
        });

        this.sendUpdateCartAnalyticsEvent(dispatch, lineItems);
      }
    } else {
      const sellingPlanItem = sellingPlanId ? {sellingPlanId: sellingPlanId} : {};
      lineItems = [
        {
          merchandiseId: merchandiseId,
          newQuantity: quantity,
          quantity: quantity,
          displayQuantity: quantity,
          itemPrice: itemPrice,
          ...sellingPlanItem,
        },
      ];

      this.sendUpdateCartAnalyticsEvent(dispatch, lineItems);
    }

    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    const cartId = await this.getCartIdFromLocalStorage(config);
    const loadingModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesAdd, {
      cartId,
      lines: [
        {
          merchandiseId,
          quantity,
          ...(sellingPlanId ? {sellingPlanId} : {}),
          ...(attributes && Array.isArray(attributes)
            ? {attributes: attributes?.map(a => ({key: a.key, value: a.value}))}
            : {}),
        },
      ],
    });
    await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);

    try {
      this.sendCartAnalyticsForNotifications(dispatch, transformedData, 'addLineItem', cartId);
    } catch (error) {
      logger.error('Error sending cart analytics for notifications:', error);
    }

    const loadedModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: false,
      },
    ];
    dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
    // this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify);
    const addToCartMessage = model?.get('CartLineItemAddtoCartMessage');
    if (!transformedError?.length) {
      toast.show(addToCartMessage || 'Product added to the cart', {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    if (transformedError?.length) {
      const isInvalidCartIdError = transformedError?.some((err: any) =>
        err?.message?.includes('Variable $cartId of type ID! was provided invalid value'),
      );
      if (isInvalidCartIdError) {
        await this.clearLocalCartState(dispatch, config, model, selector, {
          skipAnalyticsPurchaseEvent: true,
          navigateToSuccessScreen: false,
        });
        toast.show('Your cart has been expired. Creating a new cart...', {
          type: 'warning',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
        await this.increaseCartLineItemQuantity(dispatch, config, model, selector, {
          merchandiseId,
          quantity,
          syncWithShopify,
          sellingPlanId,
          attributes,
          itemPrice,
          successToastText,
        });
      } else {
        toast.show(transformedError[0]?.message ?? 'Could not add item to cart', {
          type: 'error',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
      }
    }
  };

  decreaseCartLineItemQuantity = async (dispatch, config, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }

    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as CartProductVariantQuantityChangeParam;
    const {merchandiseId, quantity, syncWithShopify, sellingPlanId, cartLineId} = payload;

    let lineItems = [...model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY)];
    if (lineItems && lineItems.length > 0) {
      let matchingLineItem;
      if (cartLineId) {
        matchingLineItem = lineItems.filter(x => x.id === cartLineId)[0];
      }
      if (!matchingLineItem) {
        matchingLineItem = lineItems.filter(x => {
          if (sellingPlanId) {
            return x.merchandiseId === merchandiseId && x.sellingPlanId === sellingPlanId;
          } else {
            return x.merchandiseId === merchandiseId;
          }
        })[0];
      }
      if (matchingLineItem) {
        // matchingLineItem.quantity -= quantity;
        matchingLineItem.newQuantity = (matchingLineItem.newQuantity ?? matchingLineItem.quantity) - quantity;

        if (matchingLineItem.newQuantity <= 0) {
          // const index = lineItems.indexOf(matchingLineItem);
          // lineItems.splice(index, 1);
          matchingLineItem.newQuantity = 0;
        }

        let newQuantity = (matchingLineItem.displayQuantity = matchingLineItem.newQuantity);

        this.sendUpdateCartAnalyticsEvent(dispatch, lineItems);

        const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
        const queries = shopifyDatasourceConfig?.plugin?.getQueries();
        const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
        const cartId = await this.getCartIdFromLocalStorage(config);
        const loadingModelUpdates = [
          {
            selector: shopifySyncSelector,
            newValue: true,
          },
        ];
        dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
        const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesUpdate, {
          cartId,
          lines: [
            {
              id: matchingLineItem?.id,
              merchandiseId,
              quantity: newQuantity,
              ...(sellingPlanId ? {sellingPlanId} : {}),
            },
          ],
        });
        await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);

        try {
          this.sendCartAnalyticsForNotifications(dispatch, transformedData, 'decreaseLineItem', cartId);
        } catch (error) {
          logger.error('Error sending cart analytics for notifications:', error);
        }

        const loadedModelUpdates = [
          {
            selector: shopifySyncSelector,
            newValue: false,
          },
        ];
        dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
        // this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify);
        if (transformedError?.length) {
          const isInvalidCartIdError = transformedError?.some((err: any) =>
            err?.message?.includes('Variable $cartId of type ID! was provided invalid value'),
          );
          if (isInvalidCartIdError) {
            await this.clearLocalCartState(dispatch, config, model, selector, {
              skipAnalyticsPurchaseEvent: true,
              navigateToSuccessScreen: false,
            });
            toast.show('Your cart has been expired. Clearing current cart.', {
              type: 'warning',
              placement: 'bottom',
              duration: 2000,
              style: {marginBottom: 80},
            });
          } else {
            toast.show(transformedError[0]?.message ?? 'Could not remove item from cart', {
              type: 'error',
              placement: 'bottom',
              duration: 2000,
              style: {marginBottom: 80},
            });
          }
        }
      }
    }
  };

  removeCartLineItem = async (dispatch, config, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }

    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as CartProductVariantQuantityChangeParam;
    const {merchandiseId, syncWithShopify, sellingPlanId, cartLineId} = payload;

    let lineItems = [...model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY)];
    if (lineItems && lineItems.length > 0) {
      let matchingLineItem;
      if (cartLineId) {
        matchingLineItem = lineItems.filter((x: any) => x.id === cartLineId)[0];
      }
      if (!matchingLineItem) {
        matchingLineItem = lineItems.filter((x: any) => {
          if (sellingPlanId) {
            return x.merchandiseId === merchandiseId && x.sellingPlanId === sellingPlanId;
          } else {
            return x.merchandiseId === merchandiseId;
          }
        })[0];
      }
      if (matchingLineItem) {
        matchingLineItem.newQuantity = 0;
        matchingLineItem.displayQuantity = matchingLineItem.newQuantity;

        this.sendUpdateCartAnalyticsEvent(dispatch, lineItems);

        const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
        const queries = shopifyDatasourceConfig?.plugin?.getQueries();
        const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
        const cartId = await this.getCartIdFromLocalStorage(config);
        const loadingModelUpdates = [
          {
            selector: shopifySyncSelector,
            newValue: true,
          },
        ];
        dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
        const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesRemove, {
          cartId,
          lineIds: [matchingLineItem?.id],
        });
        await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);

        try {
          this.sendCartAnalyticsForNotifications(dispatch, transformedData, 'removeLineItem', cartId);
        } catch (error) {
          logger.error('Error sending cart analytics for notifications:', error);
        }

        const loadedModelUpdates = [
          {
            selector: shopifySyncSelector,
            newValue: false,
          },
        ];
        dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
        // this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify);
        const removeFromCartMessage = model?.get('CartLineItemRemoveFromCartMessage');
        if (!transformedError?.length) {
          toast.show(removeFromCartMessage || 'Product removed from the cart', {
            type: 'success',
            placement: 'bottom',
            duration: 2000,
            style: {marginBottom: 80},
          });
        }
        if (transformedError?.length) {
          const isInvalidCartIdError = transformedError?.some((err: any) =>
            err?.message?.includes('Variable $cartId of type ID! was provided invalid value'),
          );
          if (isInvalidCartIdError) {
            await this.clearLocalCartState(dispatch, config, model, selector, {
              skipAnalyticsPurchaseEvent: true,
              navigateToSuccessScreen: false,
            });
            toast.show('Your cart has been expired. Clearing current cart.', {
              type: 'warning',
              placement: 'bottom',
              duration: 2000,
              style: {marginBottom: 80},
            });
          } else {
            toast.show(transformedError[0]?.message ?? 'Could not remove item from cart', {
              type: 'error',
              placement: 'bottom',
              duration: 2000,
              style: {marginBottom: 80},
            });
          }
        }
      }
    }
  };

  addCartLineItems = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }
    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as CartAddVariantsChangeParam;
    const {itemsToAdd, syncWithShopify, successToastText} = payload;

    const invalidItems = _.filter(itemsToAdd, i => !i.merchandiseId || !i.quantity);

    if (!_.isEmpty(invalidItems)) {
      logger.error('Invalid list of items detected:', {items: itemsToAdd, invalidItems});
      return;
    }

    let lineItems = [...model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY)];

    let maxCartLineItemLimit = model?.get('maxCartLineItemLimit');
    let cartLineItemLimitExceededMessage = model?.get('cartLineItemLimitExceededMessage');
    let netLineItemsInCart: number = lineItems?.length || 0;
    itemsToAdd.forEach(item => {
      const matchingLineItem = lineItems.filter(x => {
        if (x.sellingPlanId) {
          return x.merchandiseId === item.merchandiseId && x?.sellingPlanId === item?.sellingPlanId;
        } else {
          return x.merchandiseId === item.merchandiseId;
        }
      })[0];

      if (!matchingLineItem) netLineItemsInCart += 1;
    });

    if (netLineItemsInCart > maxCartLineItemLimit) {
      toast.show(cartLineItemLimitExceededMessage || 'You can not add more than 25 items on cart.', {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
      return;
    }

    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    const cartId = await this.getCartIdFromLocalStorage(config);
    const loadingModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    const linesToAdd = itemsToAdd.map(item => ({
      merchandiseId: item.merchandiseId,
      quantity: item.quantity,
      ...(item?.sellingPlanId ? {sellingPlanId: item?.sellingPlanId} : {}),
      ...(item?.attributes ? {attributes: item?.attributes?.map(a => ({key: a.key, value: a.value}))} : {}),
    }));
    const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesAdd, {
      cartId,
      lines: linesToAdd,
    });
    await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);
    const loadedModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: false,
      },
    ];
    dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
    // this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify);
    if (!transformedError?.length && successToastText) {
      toast.show(successToastText, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    if (transformedError?.length) {
      const isInvalidCartIdError = transformedError?.some((err: any) =>
        err?.message?.includes('Variable $cartId of type ID! was provided invalid value'),
      );
      if (isInvalidCartIdError) {
        await this.clearLocalCartState(dispatch, config, model, selector, {
          skipAnalyticsPurchaseEvent: true,
          navigateToSuccessScreen: false,
        });
        toast.show('Your cart has been expired. Creating a new cart...', {
          type: 'warning',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
        await this.addCartLineItems(dispatch, config, model, selector, {
          itemsToAdd,
          syncWithShopify,
          successToastText,
        });
      } else {
        toast.show(transformedError[0]?.message ?? 'Could not add item to cart', {
          type: 'error',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
      }
    }
  };

  // alterCartLineItemsQuantities = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
  //   try {
  //     await this.assertValidCartExists(dispatch, config, model, selector);

  //     const payload = params as CartAlterVariantsQuantitiesParam;
  //     const {itemsToAdd, itemsToRemove, overrideItemQuantities, syncWithShopify, successToastText} = payload;
  //     let overrideQuantities: boolean = overrideItemQuantities === true ? true : false;
  //     let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);

  //     let maxCartLineItemLimit = model?.get('maxCartLineItemLimit');
  //     let cartLineItemLimitExceededMessage = model?.get('cartLineItemLimitExceededMessage');
  //     let netLineItemsInCart: number = lineItems?.length || 0;
  //     itemsToAdd.forEach(item => {
  //       const matchingLineItem = lineItems.filter(x => {
  //         if (x.sellingPlanId) {
  //           return x.merchandiseId === item.merchandiseId && x?.sellingPlanId === item?.sellingPlanId;
  //         } else {
  //           return x.merchandiseId === item.merchandiseId;
  //         }
  //       })[0];

  //       if (!matchingLineItem) netLineItemsInCart += 1;
  //     });
  //     itemsToRemove?.forEach(item => {
  //       const matchingLineItem = lineItems.filter(x => {
  //         if (x.sellingPlanId) {
  //           return x.merchandiseId === item.merchandiseId && x?.sellingPlanId === item?.sellingPlanId;
  //         } else {
  //           return x.merchandiseId === item.merchandiseId;
  //         }
  //       })[0];

  //       if (matchingLineItem) netLineItemsInCart -= 1;
  //     });
  //     if (netLineItemsInCart > maxCartLineItemLimit) {
  //       toast.show(cartLineItemLimitExceededMessage || 'You can not add more than 25 items on cart.', {
  //         type: 'error',
  //         placement: 'bottom',
  //         duration: 2000,
  //         style: {marginBottom: 80},
  //       });
  //       return;
  //     }

  //     const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  //     const queries = shopifyDatasourceConfig?.plugin?.getQueries();
  //     const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
  //     const cartId = await this.getCartIdFromLocalStorage(config);

  //     const loadingModelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: true,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

  //     const linesToAdd = [];
  //     const linesToUpdate = [];
  //     const linesToRemove = [];
  //     if (overrideQuantities) {
  //       itemsToAdd.forEach(item => {
  //         const matchingLineItem = lineItems.filter(x => {
  //           if (x.sellingPlanId) {
  //             return x.merchandiseId === item.merchandiseId && x?.sellingPlanId === item?.sellingPlanId;
  //           } else {
  //             return x.merchandiseId === item.merchandiseId;
  //           }
  //         })[0];

  //         if (matchingLineItem) {
  //           linesToUpdate.push({
  //             id: matchingLineItem?.id,
  //             merchandiseId: item.merchandiseId,
  //             quantity: item.quantity,
  //             ...(item?.sellingPlanId ? {sellingPlanId: item.sellingPlanId} : {}),
  //           });
  //         } else {
  //           linesToAdd.push({
  //             merchandiseId: item.merchandiseId,
  //             quantity: item.quantity,
  //             ...(item?.sellingPlanId ? {sellingPlanId: item.sellingPlanId} : {}),
  //           });
  //         }
  //       });
  //     } else {
  //       itemsToAdd.forEach(item => {
  //         linesToAdd.push({
  //           merchandiseId: item.merchandiseId,
  //           quantity: item.quantity,
  //           ...(item?.sellingPlanId ? {sellingPlanId: item.sellingPlanId} : {}),
  //         });
  //       });
  //     }

  //     itemsToRemove?.forEach(item => {
  //       const matchingLineItem = lineItems.filter(x => {
  //         if (x.sellingPlanId) {
  //           return x.merchandiseId === item.merchandiseId && x?.sellingPlanId === item?.sellingPlanId;
  //         } else {
  //           return x.merchandiseId === item.merchandiseId;
  //         }
  //       })[0];

  //       if (matchingLineItem && matchingLineItem?.id) {
  //         linesToRemove.push(matchingLineItem.id);
  //       }
  //     });

  //     let finalTransformedData, finalTransformedError;

  //     if (linesToAdd.length > 0) {
  //       const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesAdd, {
  //         cartId,
  //         lines: linesToAdd,
  //       });

  //       finalTransformedData = transformedData;
  //       finalTransformedError = transformedError;
  //     }

  //     if (linesToUpdate.length > 0) {
  //       const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesUpdate, {
  //         cartId,
  //         lines: linesToUpdate,
  //       });

  //       finalTransformedData = transformedData;
  //       finalTransformedError = transformedError;
  //     }

  //     if (linesToRemove.length > 0) {
  //       const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartLinesRemove, {
  //         cartId,
  //         lineIds: linesToRemove,
  //       });

  //       finalTransformedData = transformedData;
  //       finalTransformedError = transformedError;
  //     }

  //     await this.updateModelFromCartObject(dispatch, config, model, selector, finalTransformedData);

  //     const loadedModelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: false,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));

  //     if (!finalTransformedError && successToastText) {
  //       toast.show(successToastText, {
  //         type: 'success',
  //         placement: 'bottom',
  //         duration: 2000,
  //         style: {marginBottom: 80},
  //       });
  //     }
  //   } catch (err) {
  //     console.log(err);
  //     const loadedModelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: false,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
  //   }
  // };

  alterCartLineItemsQuantities = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }
    // const payload = params as CartAlterVariantsQuantitiesParam;
    // const {itemsToAdd, itemsToRemove, overrideItemQuantities, syncWithShopify, successToastText} = payload;
    // let overrideQuantities: boolean = overrideItemQuantities === true ? true : false;
    // let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);
    // let maxCartLineItemLimit = model?.get('maxCartLineItemLimit');
    // let cartLineItemLimitExceededMessage = model?.get('cartLineItemLimitExceededMessage');
    // let netItemsInCart: number;
    // if (lineItems && lineItems.length > 0) {
    //   let numberOfItemsBeingAdded: number =
    //     itemsToAdd && itemsToAdd?.length > 0
    //       ? itemsToAdd.length - _.intersectionBy(itemsToAdd, lineItems, 'merchandiseId').length
    //       : 0;
    //   let numberOfItemsBeingRemoved: number =
    //     itemsToRemove && itemsToRemove?.length > 0
    //       ? _.intersectionBy(itemsToRemove, lineItems, 'merchandiseId').length
    //       : 0;
    //   netItemsInCart = lineItems.length + numberOfItemsBeingAdded - numberOfItemsBeingRemoved;
    // } else {
    //   let numberOfItemsBeingAdded: number = itemsToAdd.length ?? 0;
    //   let numberOfItemsBeingRemoved: number = itemsToRemove.length ?? 0;
    //   netItemsInCart = numberOfItemsBeingAdded - numberOfItemsBeingRemoved;
    // }
    // if (netItemsInCart > maxCartLineItemLimit) {
    //   toast.show(cartLineItemLimitExceededMessage || 'You can not add more than 25 items on cart.', {
    //     type: 'error',
    //     placement: 'bottom',
    //     duration: 2000,
    //     style: {marginBottom: 80},
    //   });
    //   return;
    // }
    // if (itemsToAdd && itemsToAdd?.length > 0) {
    //   itemsToAdd.forEach(item => {
    //     if (_.isEmpty(item.merchandiseId)) {
    //       logger.error(`invalid merchandiseId`, item.merchandiseId);
    //       return;
    //     }
    //     if (lineItems && lineItems.length > 0) {
    //       const matchingLineItem = lineItems.filter(x => {
    //         if (item.sellingPlanId) {
    //           return x.merchandiseId === item.merchandiseId && x.sellingPlanId === item.sellingPlanId;
    //         } else {
    //           return x.merchandiseId === item.merchandiseId;
    //         }
    //       })[0];
    //       if (matchingLineItem) {
    //         if (overrideQuantities) {
    //           matchingLineItem.newQuantity = item.quantity;
    //           matchingLineItem.displayQuantity = matchingLineItem.newQuantity;
    //         } else {
    //           matchingLineItem.newQuantity =
    //             (matchingLineItem.newQuantity ?? matchingLineItem.quantity) + item.quantity;
    //           matchingLineItem.displayQuantity = matchingLineItem.newQuantity;
    //         }
    //       } else {
    //         const sellingPlanItem = item.sellingPlanId ? {sellingPlanId: item.sellingPlanId} : {};
    //         lineItems.push({
    //           merchandiseId: item.merchandiseId,
    //           newQuantity: item.quantity,
    //           displayQuantity: item.quantity,
    //           itemPrice: item.itemPrice,
    //           ...sellingPlanItem,
    //         });
    //       }
    //     } else {
    //       const sellingPlanItem = item.sellingPlanId ? {sellingPlanId: item.sellingPlanId} : {};
    //       lineItems = [
    //         {
    //           merchandiseId: item.merchandiseId,
    //           newQuantity: item.quantity,
    //           displayQuantity: item.quantity,
    //           itemPrice: item.itemPrice,
    //           ...sellingPlanItem,
    //         },
    //       ];
    //     }
    //   });
    // }
    // if (itemsToRemove && itemsToRemove?.length > 0) {
    //   itemsToRemove.forEach(item => {
    //     if (lineItems && lineItems.length > 0) {
    //       const matchingLineItem = lineItems.filter((x: any) => {
    //         if (item.sellingPlanId) {
    //           return x.merchandiseId === item.merchandiseId && x.sellingPlanId === item.sellingPlanId;
    //         } else {
    //           return x.merchandiseId === item.merchandiseId;
    //         }
    //       })[0];
    //       if (matchingLineItem) {
    //         matchingLineItem.newQuantity = 0;
    //         matchingLineItem.displayQuantity = matchingLineItem.newQuantity;
    //       }
    //     }
    //   });
    // }
    // this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify);
    // if (successToastText) {
    //   toast.show(successToastText, {
    //     type: 'success',
    //     placement: 'bottom',
    //     duration: 2000,
    //     style: {marginBottom: 80},
    //   });
    // }
  };

  duplicateCart = async (dispatch, config, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }
    // const payload = params as CartDuplicateParam;
    // const {syncWithShopify} = payload;
    // let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);
    // if (lineItems && lineItems.length > 0) {
    //   await this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify, '', false, true);
    // }
  };

  syncLocalCartStateWithShopify = async (dispatch, config, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }
    // let lineItems = (model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY) || []).filter(
    //   (e: any) => e.merchandiseId && e.merchandiseId.trim(),
    // );
    // await this.applyChanges(dispatch, config, model, selector, lineItems, true);

    const currentCartId = await this.getCartIdFromLocalStorage(config);
    const loggedInUser = model.get('loggedInUser');
    const loggedInUserAccessToken = model.get('loggedInUserAccessToken');

    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);

    if (currentCartId && !_.isEmpty(loggedInUser?.email) && !_.isEmpty(loggedInUserAccessToken?.accessToken)) {
      const currentCartObject = await this.getCartObjectFromModel(model);

      if (_.isEmpty(currentCartObject?.buyerIdentity?.customerId)) {
        try {
          const loadingModelUpdates = [
            {
              selector: shopifySyncSelector,
              newValue: true,
            },
          ];
          dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

          const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
          const queries = shopifyDatasourceConfig?.plugin?.getQueries();

          const {transformedData, transformedError} = await this.queryExecutor(
            model,
            queries?.CartBuyerIdentityUpdate,
            {
              cartId: currentCartId,
              customerAccessToken: loggedInUserAccessToken?.accessToken,
              email: loggedInUser.email,
            },
          );

          await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);
        } catch (error) {
          logger.error('Error updating buyer identity', error);
        } finally {
          const loadingModelUpdates = [
            {
              selector: shopifySyncSelector,
              newValue: false,
            },
          ];
          dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
        }
      }
    }
    const cartAssistEnabled = model.get('cartAssistEnabled', false);
    if (cartAssistEnabled) {
      await this.syncCartAssist(dispatch, config, model, selector, params);
    }
  };

  // private logCartCleanupEvent = async (dispatch, config, model, selector: Selector, params: any, cartObject: any) => {
  //   try {
  //     if (!_.isEmpty(cartObject)) {
  //       const apptile_idempotency_att = _.find(
  //         cartObject?._raw?.attributes,
  //         att => att?.key == 'apptile_idempotency_key',
  //       );
  //       const apptile_idempotency = apptile_idempotency_att?.value;
  //       const logEvent = {
  //         cartId: cartObject.id,
  //         apptile_idempotency: apptile_idempotency,
  //       };
  //       logger.info(`Could not sendCartCleanupEvent as logEvent: `, logEvent);
  //     } else {
  //       logger.info(`Could not sendCartCleanupEvent as cartObject is empty: `, cartObject);
  //     }
  //   } catch (error) {
  //     logger.error(`Could not sendCartCleanupEvent error: `, error);
  //   }
  // };

  private sendPurchaseEvent = async (dispatch, config, model, selector: Selector, params: any, cartObject: any) => {
    try {
      if (!_.isEmpty(cartObject)) {
        const items = !_.isEmpty(cartObject?.lines)
          ? _.map(cartObject.lines, function (t) {
              return {
                title: t.variant.product.title,
                currency: cartObject?._raw?.cost?.subtotalAmount?.currencyCode,
                variantTitle: t.variant.title,
                price: t.variant.salePrice,
                quantity: t.quantity,
                variantId: t.variant.id,
                productId: t.variant.product.id,
                productType: t.variant.product.productType,
                discount: t.lineItemDiscount,
                brand: t.variant.product.vendor,
              };
            })
          : [];

        const orderId = cartObject?.id;
        const totalValue = parseFloat(cartObject?.totalAmount);
        const currency = cartObject?._raw?.cost?.subtotalAmount?.currencyCode;
        const orderName = cartObject?.id;
        const totalItems = cartObject?.lines?.length || 0;
        const taxPrice = cartObject?.totalTaxAmount ? parseFloat(cartObject?.totalTaxAmount) : 0;

        const eventParams = {
          items,
          orderId,
          totalValue,
          currency,
          orderName,
          totalItems,
          taxPrice,
        } as any;
        logger.info(`Sending PurchaseEvent Event: `, eventParams);
        dispatch(sendAnalyticsEvent('track', 'purchase', eventParams));
      } else {
        logger.info(`Could not send PurchaseEvent as cartObject is empty: `, cartObject);
      }
    } catch (error) {
      logger.error(`Could not send PurchaseEvent error: `, error);
    }
  };

  private sendLiveStreamPurchaseEvent = async (
    dispatch,
    config,
    currentLiveConfig: any,
    streamProducts: any,
    cartObject: any,
  ) => {
    try {
      if (!_.isEmpty(streamProducts) && !_.isEmpty(cartObject)) {
        const items =
          !_.isEmpty(streamProducts) && !_.isEmpty(cartObject)
            ? _.map(streamProducts, function (t) {
                return {
                  title: t.variant.product.title,
                  currency: cartObject?._raw?.cost?.subtotalAmount?.currencyCode,
                  variantTitle: t.variant.title,
                  price: t.variant.salePrice,
                  quantity: t.quantity,
                  variantId: t.variant.id,
                  productId: t.variant.product.id,
                  productType: t.variant.product.productType,
                  discount: t.lineItemDiscount,
                  brand: t.variant.product.vendor,
                };
              })
            : [];

        const amountCal = streamProducts.reduce((accumulator, product) => {
          return accumulator + product.variant.salePrice * product.quantity;
        }, 0);

        const orderId = cartObject?.id;
        const totalValue = parseFloat(amountCal);
        const currency = cartObject?._raw?.cost?.subtotalAmount?.currencyCode;
        const orderName = cartObject?.id;
        const totalItems = streamProducts?.length || 0;
        const streamId = currentLiveConfig.streaming_id;

        const eventParams = {
          items,
          orderId,
          totalValue,
          currency,
          orderName,
          totalItems,
          streamId,
        } as any;
        logger.info(`Sending streamPurchaseEvent Event: `, eventParams);
        dispatch(sendAnalyticsEvent('track', 'streamPurchase', eventParams));
      } else {
        logger.info(`Could not send streamPurchaseEvent as cartObject is empty: `, cartObject);
      }
    } catch (error) {
      logger.error(`Could not send PurchaseEvent error: `, error);
    }
  };

  clearLocalCartState = async (dispatch, config, model, selector: Selector, params: any) => {
    const payload = params;

    const lineItemsSelector = selector.concat([SHOPIFY_MODEL_CART_ITEMS_KEY]);
    const cartIdSelector = selector.concat([SHOPIFY_MODEL_CART_ID_KEY]);
    const cartSelector = selector.concat([SHOPIFY_MODEL_CART_OBJECT_KEY]);
    const checkoutSelector = selector.concat([SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY]);

    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    dispatch(
      modelUpdateAction(
        [
          {
            selector: shopifySyncSelector,
            newValue: true,
          },
        ],
        undefined,
        true,
      ),
    );

    const customerAccessToken = !_.isEmpty(model.get('loggedInUserAccessToken')?.accessToken)
      ? model.get('loggedInUserAccessToken').accessToken
      : null;
    const {skipAnalyticsPurchaseEvent, navigateToSuccessScreen, streamProducts, currentLiveConfig} = payload;

    const cartObject: any = await this.getCartObjectFromModel(model);
    try {
      logger.info(`CartClean:: starting CartClean for: `, cartObject?.id);

      if (!skipAnalyticsPurchaseEvent) {
        this.sendPurchaseEvent(dispatch, config, model, selector, params, cartObject).catch(e => {
          logger.error(e);
        });
        if (streamProducts && currentLiveConfig) {
          if (streamProducts.length !== 0) {
            await this.sendLiveStreamPurchaseEvent(dispatch, config, currentLiveConfig, streamProducts, cartObject);
          }
        }
      }
      const cartAssistEnabled = model.get('cartAssistEnabled', false);
      if (cartAssistEnabled) {
        const cartIdFromMetafield = await this.getActiveMetafieldForCartAssist(model, customerAccessToken);
        if (cartIdFromMetafield) await this.clearCartAssist(dispatch, config, model, selector, params);
      }
      this.storeCartIdLocally(config, '');
      // await Promise.all([this.storeLineItemsLocally(config, []), this.deleteCartObjectLocally(config)]);
      // this.storeCheckoutObjectLocally(config, null);
      // this.logCartCleanupEvent(dispatch, config, model, selector, params, cartObject);

      const modelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: false,
        },
        {
          selector: cartIdSelector,
          newValue: null,
        },
        {
          selector: lineItemsSelector,
          newValue: [],
        },
        {
          selector: cartSelector,
          newValue: null,
        },
        {
          selector: checkoutSelector,
          newValue: null,
        },
      ];
      setTimeout(() => {
        dispatch(modelUpdateAction(modelUpdates, undefined, true));
        if (navigateToSuccessScreen) {
          const orderSuccessScreenId = model.get('orderSuccessScreenId') || 'OrderSuccess';
          dispatch(navigateToScreen(orderSuccessScreenId, {checkoutId: cartObject?.id}));
        }
      }, 100);

      logger.info(`CartClean:: CartClean was successful for: `, cartObject?.id);
    } catch (error) {
      logger.error(`CartClean:: CartClean was failed for: ${cartObject?.id} error: ${error}`);

      const modelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: false,
        },
        {
          selector: cartIdSelector,
          newValue: null,
        },
        {
          selector: lineItemsSelector,
          newValue: [],
        },
        {
          selector: cartSelector,
          newValue: null,
        },
        {
          selector: checkoutSelector,
          newValue: null,
        },
      ];
      setTimeout(() => {
        dispatch(modelUpdateAction(modelUpdates, undefined, true));
        if (navigateToSuccessScreen) {
          const orderSuccessScreenId = model.get('orderSuccessScreenId') || 'OrderSuccess';
          dispatch(navigateToScreen(orderSuccessScreenId, {checkoutId: cartObject?.id}));
        }
      }, 100);
    }
  };
  // private async applyChanges(
  //   dispatch: any,
  //   config: PluginConfig,
  //   model: any,
  //   selector: Selector,
  //   lineItems: any,
  //   syncWithShopify: boolean = false,
  //   discountCode: string = '',
  //   removeDiscount: boolean = false,
  //   createNewCart: boolean = false,
  // ) {
  //   const modelUpdates = [];
  //   let cartObject: any = {};
  //   // const skipCartCreation = model?.get('skipCartCreation');
  //   // const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  //   // const queries = shopifyDatasourceConfig?.plugin?.getQueries();

  //   // if (skipCartCreation) {
  //   //   const checkoutObject = await this.applyCheckoutChanges(dispatch, config, model, selector, lineItems);
  //   //   const updatedLineItems = this.getLineItemsFromCart(checkoutObject);
  //   //   lineItems = updatedLineItems;
  //   // } else {
  //   const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
  //   const subscriptionPurchaseSelector = selector.concat([SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY]);
  //   try {
  //     const loadingModelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: true,
  //       },
  //       {
  //         selector: subscriptionPurchaseSelector,
  //         newValue: false,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

  //     ({cartObject} = await this.syncLocalStateWithShopify(
  //       dispatch,
  //       config,
  //       model,
  //       selector,
  //       lineItems,
  //       syncWithShopify,
  //       discountCode,
  //       removeDiscount,
  //       createNewCart,
  //     ));

  //     let isSubscriptionPurchase = this.isSubscriptionPurchase(lineItems);
  //     // isSubscriptionPurchase: if true cartLineItem will be used to update currentCheckoutLines otherwise checkoutLines will be used

  //     logger.info('Persisting Cart Id Locally: ', cartObject?.id);
  //     await this.storeCartIdLocally(config, cartObject?.id);
  //     await this.storeCartObjectLocally(config, cartObject);
  //     const cartSelector = selector.concat([SHOPIFY_MODEL_CART_OBJECT_KEY]);
  //     modelUpdates.push({
  //       selector: cartSelector,
  //       newValue: cartObject,
  //     });

  //     const updatedLineItems = this.getLineItemsFromCart(cartObject);
  //     lineItems = updatedLineItems.filter((e: any) => e.merchandiseId && e.merchandiseId.trim());

  //     // if (!isSubscriptionPurchase) {
  //     //   await this.applyCheckoutChanges(dispatch, config, model, selector, updatedLineItems);
  //     // }

  //     modelUpdates.push({
  //       selector: shopifySyncSelector,
  //       newValue: false,
  //     });

  //     modelUpdates.push({
  //       selector: subscriptionPurchaseSelector,
  //       newValue: isSubscriptionPurchase,
  //     });

  //     // isSubscriptionPurchase: if true cartLineItem will be used to update currentCheckoutLines otherwise checkoutLines will be used
  //     // if (isSubscriptionPurchase) {
  //     const checkoutQualifiedLines = {
  //       ...cartObject,
  //       webUrl: cartObject?.checkoutUrl || cartObject?.webUrl,
  //       cartId: cartObject?.id,
  //     } as IApptileCartMergedCheckout;
  //     await this.storeCheckoutObjectLocally(config, checkoutQualifiedLines);
  //     const checkoutSelector = selector.concat([SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY]);
  //     modelUpdates.push({
  //       selector: checkoutSelector,
  //       newValue: checkoutQualifiedLines,
  //     });

  //     const isReadyForCheckout = this.isReadyForCheckout(cartObject);
  //     const isReadyForCheckoutSelector = selector.concat([SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY]);
  //     modelUpdates.push({
  //       selector: isReadyForCheckoutSelector,
  //       newValue: isReadyForCheckout,
  //     });
  //     // }
  //   } catch (error) {
  //     logger.error(error);
  //     modelUpdates.push({
  //       selector: shopifySyncSelector,
  //       newValue: false,
  //     });
  //   }
  //   // }

  //   await this.storeLineItemsLocally(config, lineItems);
  //   const lineItemsSelector = selector.concat([SHOPIFY_MODEL_CART_ITEMS_KEY]);
  //   modelUpdates.push({
  //     selector: lineItemsSelector,
  //     newValue: [...lineItems],
  //   });
  //   dispatch(modelUpdateAction(modelUpdates, undefined, true));
  // }

  private async updateModelFromCartObject(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    cartObject: any,
  ) {
    // Add check at the beginning
    if (cartObject && !this.hasApptileAttribute(cartObject)) {
      await this.ensureCartAttributes(model, cartObject.id);
    }
    const modelUpdates = [];
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    const subscriptionPurchaseSelector = selector.concat([SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY]);
    var lineItems = [];
    try {
      logger.info('Persisting Cart Id Locally: ', cartObject?.id);

      const cartAssistEnabled = model.get('cartAssistEnabled', false);
      const customerAccessToken = !_.isEmpty(model.get('loggedInUserAccessToken')?.accessToken)
        ? model.get('loggedInUserAccessToken').accessToken
        : null;

      if (cartAssistEnabled && customerAccessToken) {
        // ~ Syncing the cart only when the cart is new
        const cartIdFromLocalStorage = await this.getCartIdFromLocalStorage(config);
        cartObject.id != cartIdFromLocalStorage &&
          (await this.syncCartAssist(dispatch, config, model, selector, {}, cartObject));
      }

      await this.storeCartIdLocally(config, cartObject?.id);

      // await this.storeCartObjectLocally(config, cartObject);
      const cartSelector = selector.concat([SHOPIFY_MODEL_CART_OBJECT_KEY]);
      modelUpdates.push({
        selector: cartSelector,
        newValue: cartObject,
      });

      const updatedLineItems = this.getLineItemsFromCart(cartObject);
      lineItems = updatedLineItems.filter((e: any) => e.merchandiseId && e.merchandiseId.trim());

      let isSubscriptionPurchase = this.isSubscriptionPurchase(lineItems);
      // isSubscriptionPurchase: if true cartLineItem will be used to update currentCheckoutLines otherwise checkoutLines will be used

      // if (!isSubscriptionPurchase) {
      //   await this.applyCheckoutChanges(dispatch, config, model, selector, updatedLineItems);
      // }

      modelUpdates.push({
        selector: shopifySyncSelector,
        newValue: false,
      });

      modelUpdates.push({
        selector: subscriptionPurchaseSelector,
        newValue: isSubscriptionPurchase,
      });

      const checkoutQualifiedLines = {
        ...cartObject,
        webUrl: cartObject?.checkoutUrl || cartObject?.webUrl,
        cartId: cartObject?.id,
      } as IApptileCartMergedCheckout;
      // await this.storeCheckoutObjectLocally(config, checkoutQualifiedLines);
      const checkoutSelector = selector.concat([SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY]);
      modelUpdates.push({
        selector: checkoutSelector,
        newValue: checkoutQualifiedLines,
      });

      const isReadyForCheckout = this.isReadyForCheckout(cartObject);
      const isReadyForCheckoutSelector = selector.concat([SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY]);
      modelUpdates.push({
        selector: isReadyForCheckoutSelector,
        newValue: isReadyForCheckout,
      });
    } catch (error) {
      logger.error(error);
      modelUpdates.push({
        selector: shopifySyncSelector,
        newValue: false,
      });
    }

    // await this.storeLineItemsLocally(config, lineItems);
    const lineItemsSelector = selector.concat([SHOPIFY_MODEL_CART_ITEMS_KEY]);
    modelUpdates.push({
      selector: lineItemsSelector,
      newValue: [...lineItems],
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  private getLineItemsFromCart(cartObject) {
    const cartLines = cartObject?.lines;
    let lineItems = [];
    if (cartLines) {
      cartLines.forEach(function (line) {
        // logger.info(line);

        const sellingPlanItem = line?.subscriptionProduct?.id ? {sellingPlanId: line?.subscriptionProduct?.id} : {};
        const attributes = line?.attributes ? {attributes: line?.attributes} : {};
        //Handeling Out of stock scenario line.variant?.availableForSale ? line.quantity : 1 as checkout removes the 0 qty line items
        lineItems.push({
          id: line.id,
          merchandiseId: line.variant.id,
          quantity: line.variant?.availableForSale ? line.quantity : 1,
          newQuantity: line.variant?.availableForSale ? line.quantity : 1,
          displayQuantity: line.variant?.availableForSale ? line.quantity : 1,
          itemPrice: line.variant.salePrice,
          ...sellingPlanItem,
          ...attributes,
        });
      });
    }
    return lineItems;
  }

  updateCartDiscountCode = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }

    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as updateDiscountCodeParams;
    const {syncWithShopify, discountCode} = payload;
    const discountCodeString = discountCode ? discountCode : '';
    if (discountCodeString) {
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();
      const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
      const cartId = await this.getCartIdFromLocalStorage(config);
      const loadingModelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartDiscountUpdate, {
        cartId,
        discountCode: [discountCodeString],
      });
      await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);
      const loadedModelUpdates = [
        {
          selector: shopifySyncSelector,
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));
    }
    // let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);
    // await this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify, discountCodeString);
  };
  removeCartDiscountCode = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    // if (this.getCartLineItemsKey(config) == null) {
    //   logger.error('please define cartLineItemsKey');
    //   return;
    // }

    await this.assertValidCartExists(dispatch, config, model, selector);

    const payload = params as updateDiscountCodeParams;
    const {syncWithShopify} = payload;

    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
    const cartId = await this.getCartIdFromLocalStorage(config);
    const loadingModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    const {transformedData, transformedError} = await this.queryExecutor(model, queries?.CartDiscountUpdate, {
      cartId,
      discountCodes: [],
    });
    await this.updateModelFromCartObject(dispatch, config, model, selector, transformedData);
    const loadedModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: false,
      },
    ];
    dispatch(modelUpdateAction(loadedModelUpdates, undefined, true));

    // let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);
    // await this.applyChanges(dispatch, config, model, selector, lineItems, syncWithShopify, '', true);
  };

  getSubscriptionCartStatus = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    const payload = params as PollSubscriptionCart;
    const {cartId} = payload ?? {};
    let cartStatus = false;

    if (cartId) {
      const queryRunner = model.get('queryRunner');
      const response = await queryRunner.runQuery('query', GET_CART_DETAILS, {cartId}, {});
      const {data} = response;
      const cartData = (data && data?.cart) ?? null;
      if (!cartData) {
        cartStatus = true;
      }
    }

    const shopifySyncSelector = selector.concat([SHOPIFY_SUBSCRIPTION_CART_STATUS]);
    const loadingModelUpdates = [
      {
        selector: shopifySyncSelector,
        newValue: cartStatus,
      },
    ];
    dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
  };

  addCartAttributes = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    const {attributes} = params;
    const cartId = await this.getCartIdFromLocalStorage(config);
    if (attributes) {
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();
      this.queryExecutor(model, queries?.CartAttributesUpdate, {
        attributes: attributes,
        cartId,
      });
    }
  };

  addCartNote = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    const {note} = params;
    const cartId = await this.getCartIdFromLocalStorage(config);
    if (note) {
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();
      this.queryExecutor(model, queries?.CartNoteUpdate, {
        cartId,
        note: note,
      });
    }
  };

  // syncCheckoutState = async (dispatch, config, model, selector: Selector, params: any) => {
  //   if (this.getCartLineItemsKey(config) == null) {
  //     logger.error('please define cartLineItemsKey');
  //     return;
  //   }

  //   let lineItems = model?.get(SHOPIFY_MODEL_CART_ITEMS_KEY);
  //   await this.applyCheckoutChanges(dispatch, config, model, selector, lineItems);
  // };

  private async queryExecutor(model: any, querySchema: ShopifyQueryDetails, inputVariables: any) {
    try {
      const queryRunner = model.get('queryRunner');
      const shopConfig = model.get('shop');
      const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;

      //Required to resolve languageCode context in case where countryCode and LanguageCode is different
      //ie.  Le Pro 1600 countryCode: CA, langaugeCode: FR
      let contextInputParam;
      if (querySchema && querySchema.contextInputParams) {
        const contextInputParamResolve = makeInputParamsResolver(querySchema.contextInputParams);
        contextInputParam = contextInputParamResolve(model);
      }

      const response = await queryRunner.runQuery(
        querySchema.queryType,
        querySchema.gqlTag,
        {...input, ...contextInputParam},
        {},
      );
      const tResponse = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig, model);
      return tResponse;
    } catch (error) {
      console.warn(`queryExecutor failed with error : ${error}`);
      return {
        rawData: null,
        transformedData: null,
        transformedHasError: true,
        transformedError: [error],
        queryHasNextPage: false,
        paginationDetails: {},
      };
    }
  }

  // private getLineItemsForCheckoutReplace(localLineItems: any[]) {
  //   const addLines = [];
  //   for (const localLineItem of localLineItems) {
  //     const sellingPlan = localLineItem.sellingPlanId ? {sellingPlanId: localLineItem.sellingPlanId} : {};
  //     addLines.push({
  //       variationId: localLineItem.merchandiseId,
  //       quantity: localLineItem.newQuantity,
  //       ...sellingPlan,
  //     });
  //   }
  //   return addLines.filter(a => a.quantity > 0);
  // }

  // private getShopifyAndLocalItemDiff(localLineItems: any[], cartLineItems: any[]) {
  //   const updateLines: any[] = [];
  //   const addLines: any[] = [];
  //   const deleteIds: string[] = [];
  //   const sortedCartLineItems = _.sortBy(cartLineItems, [
  //     function (o) {
  //       return o?.lineItemDiscount;
  //     },
  //   ]);

  //   const findUpdateLineItems = (localLineItem: any, cartLineItem: any) => {
  //     if (localLineItem.newQuantity === 0 || localLineItem.quantity === 0) {
  //       deleteIds.push(cartLineItem.id);
  //     } else if (localLineItem.newQuantity && localLineItem.newQuantity !== localLineItem.quantity) {
  //       const sellingPlan = localLineItem.sellingPlanId ? {sellingPlanId: localLineItem.sellingPlanId} : {};
  //       updateLines.push({
  //         id: cartLineItem.id,
  //         variationId: localLineItem.merchandiseId,
  //         quantity: localLineItem.newQuantity,
  //         ...sellingPlan,
  //       });
  //     }
  //   };

  //   for (let localLineItem of localLineItems) {
  //     let exists = false;
  //     for (let cartLineItem of sortedCartLineItems) {
  //       if (
  //         localLineItem.sellingPlanId &&
  //         cartLineItem?.subscriptionProduct?.id &&
  //         cartLineItem.variant.id === localLineItem.merchandiseId &&
  //         cartLineItem.quantity === localLineItem.quantity &&
  //         localLineItem.sellingPlanId === cartLineItem?.subscriptionProduct?.id
  //       ) {
  //         exists = true;
  //         findUpdateLineItems(localLineItem, cartLineItem);
  //       } else if (
  //         cartLineItem.variant.id === localLineItem.merchandiseId &&
  //         cartLineItem.quantity === localLineItem.quantity
  //       ) {
  //         exists = true;
  //         findUpdateLineItems(localLineItem, cartLineItem);
  //         break;
  //       } else if (cartLineItem.variant.id === localLineItem.merchandiseId) {
  //         exists = true;
  //         findUpdateLineItems(localLineItem, cartLineItem);
  //       }
  //     }
  //     if (!exists && localLineItem.newQuantity > 0) {
  //       const sellingPlan = localLineItem.sellingPlanId ? {sellingPlanId: localLineItem.sellingPlanId} : {};
  //       addLines.push({
  //         variationId: localLineItem.merchandiseId,
  //         quantity: localLineItem.newQuantity,
  //         ...sellingPlan,
  //       });
  //     }
  //   }
  //   return {
  //     addLines,
  //     updateLines,
  //     deleteIds,
  //   };
  // }

  // syncCheckoutWithCartItems = async (model: any, lines) => {
  //   let checkoutObject = model.get(SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY);
  //   let updatedCheckoutObject = null;

  //   const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  //   const queries = shopifyDatasourceConfig?.plugin?.getQueries();

  //   const mutationTransformer = {transformer: TransformActionCheckoutMutations};
  //   // const queryTransformer = {transformer: TransformActionCheckoutQuery};
  //   let checkoutId = checkoutObject?.checkoutId;
  //   let checkoutCustomerAssociated = model?.getIn(['currentCheckout', 'checkoutCustomerAssociated']) ?? false;

  //   if (!(checkoutObject && checkoutObject.checkoutId) || checkoutObject?.completedAt) {
  //     const createCheckoutV1Query = queries?.CreateCheckoutV1;

  //     updatedCheckoutObject = await this.queryExecutor(
  //       model,
  //       {...createCheckoutV1Query, ...mutationTransformer},
  //       {
  //         note: '',
  //         customAttributes: [
  //           {
  //             key: 'source_name',
  //             value: apptileCheckoutCustomAtt,
  //           },
  //         ],
  //       },
  //     );
  //     checkoutId = updatedCheckoutObject?.transformedData?.id;
  //   }

  //   // const flushCheckoutItemForSync = model?.get('flushCheckoutItemForSync');

  //   // if (flushCheckoutItemForSync) {
  //   let linesToFlush = this.getLineItemsForCheckoutReplace(lines);
  //   const querySchema = queries?.CheckoutLineItemsReplace;

  //   try {
  //     updatedCheckoutObject = await this.queryExecutor(
  //       model,
  //       {...querySchema, ...mutationTransformer},
  //       {
  //         checkoutId: checkoutId,
  //         lineItems: linesToFlush,
  //       },
  //     );
  //   } catch (error) {
  //     if (_.includes(error?.message, 'Checkout is already completed')) {
  //       console.warn('Checkout cleanup was not properly create new checkout');

  //       checkoutCustomerAssociated = false;
  //       linesToFlush = this.getLineItemsForCheckoutReplace(lines);
  //       const createCheckoutV1Query = queries?.CreateCheckoutV1;

  //       updatedCheckoutObject = await this.queryExecutor(
  //         model,
  //         {...createCheckoutV1Query, ...mutationTransformer},
  //         {
  //           lineItems: linesToFlush,
  //           note: '',
  //           customAttributes: [
  //             {
  //               key: 'source_name',
  //               value: apptileCheckoutCustomAtt,
  //             },
  //           ],
  //         },
  //       );
  //       checkoutId = updatedCheckoutObject?.transformedData?.id;
  //     } else {
  //       console.warn(`Error while replacing checkout`, error);
  //       throw error;
  //     }
  //   }
  //   // } else {
  //   //   const newCheckoutUpdatedObject = await this.applyCheckoutChangesWithDiff(
  //   //     checkoutObject,
  //   //     lines,
  //   //     queries,
  //   //     model,
  //   //     mutationTransformer,
  //   //     checkoutId,
  //   //   );
  //   //   if (newCheckoutUpdatedObject) {
  //   //     updatedCheckoutObject = newCheckoutUpdatedObject;
  //   //   }

  //   //   if (_.isEmpty(updatedCheckoutObject)) {
  //   //     const querySchema = queries?.GetCheckoutDetails;
  //   //     updatedCheckoutObject = await this.queryExecutor(
  //   //       model,
  //   //       {...querySchema, ...queryTransformer},
  //   //       {
  //   //         checkoutId: checkoutId,
  //   //       },
  //   //     );
  //   //   }
  //   // }

  //   try {
  //     // Associate checkout with customer
  //     const customerAccessToken = model?.getIn(['loggedInUserAccessToken', 'accessToken']);
  //     const UpdateCheckoutAssociateCustomerMutation = queries?.UpdateCheckoutAssociateCustomer;

  //     if (!checkoutCustomerAssociated && checkoutId && customerAccessToken && UpdateCheckoutAssociateCustomerMutation) {
  //       this.queryExecutor(model, UpdateCheckoutAssociateCustomerMutation, {
  //         checkoutId,
  //         customerAccessToken,
  //       });
  //       checkoutCustomerAssociated = true;
  //     }
  //   } catch (error) {
  //     logger.error(error);
  //   }

  //   const {transformedData} = updatedCheckoutObject;
  //   return {...transformedData, checkoutCustomerAssociated} as IApptileCartMergedCheckout;
  // };

  // private async applyCheckoutChangesWithDiff(
  //   checkoutObject: any,
  //   lines: any,
  //   queries: any,
  //   model: any,
  //   mutationTransformer,
  //   checkoutId: any,
  // ) {
  //   const checkoutLineItems = (checkoutObject?.checkoutId && checkoutObject?.lines) || [];
  //   const {addLines, updateLines, deleteIds} = this.getShopifyAndLocalItemDiff(lines, checkoutLineItems);

  //   let updatedCheckoutObject: any;
  //   if (addLines && addLines.length > 0) {
  //     const querySchema = queries?.CheckoutLineItemsAdd;
  //     updatedCheckoutObject = await this.queryExecutor(
  //       model,
  //       {...querySchema, ...mutationTransformer},
  //       {
  //         checkoutId: checkoutId,
  //         lineItems: addLines,
  //       },
  //     );
  //   }
  //   if (updateLines && updateLines.length > 0) {
  //     const querySchema = queries?.CheckoutLineItemsUpdate;
  //     updatedCheckoutObject = await this.queryExecutor(
  //       model,
  //       {...querySchema, ...mutationTransformer},
  //       {
  //         checkoutId: checkoutId,
  //         lineItems: updateLines,
  //       },
  //     );
  //   }
  //   if (deleteIds && deleteIds.length > 0) {
  //     // const lineIds = mIdsToDelete.map(x => checkoutLineItemsMap[x].checkoutLineId);
  //     const querySchema = queries?.CheckoutLineItemsRemove;
  //     updatedCheckoutObject = await this.queryExecutor(
  //       model,
  //       {...querySchema, ...mutationTransformer},
  //       {
  //         checkoutId: checkoutId,
  //         lineItemIds: deleteIds,
  //       },
  //     );
  //   }

  //   return updatedCheckoutObject;
  // }

  private isSubscriptionPurchase(lineItems) {
    return _.some(lineItems, v => v?.sellingPlanId && v?.newQuantity > 0);
  }

  private isReadyForCheckout(currentCart: any) {
    let isReady = true;
    if (!currentCart) isReady = false;
    const outofStockitemAvilable = _.filter(currentCart?.lines, ln => ln?.variant?.quantityAvailableForSale < 1);
    if (!_.isEmpty(outofStockitemAvilable)) isReady = false;
    return isReady;
  }

  // private async applyCheckoutChanges(
  //   dispatch: any,
  //   config: PluginConfig,
  //   model: any,
  //   selector: Selector,
  //   lineItems: any,
  // ) {
  //   let modelUpdates = [];

  //   const shopifySyncSelector = selector.concat([SHOPIFY_MODEL_CART_SYNC_KEY]);
  //   const isReadyForCheckoutSelector = selector.concat([SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY]);
  //   try {
  //     modelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: true,
  //       },
  //       {
  //         selector: isReadyForCheckoutSelector,
  //         newValue: false,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(modelUpdates, undefined, true));
  //     const checkoutQualifiedLines = await this.syncCheckoutWithCartItems(model, lineItems);

  //     await this.storeCheckoutObjectLocally(config, checkoutQualifiedLines);
  //     const checkoutSelector = selector.concat([SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY]);

  //     const checkoutReady = this.isReadyForCheckout(checkoutQualifiedLines);

  //     modelUpdates = [
  //       {
  //         selector: checkoutSelector,
  //         newValue: checkoutQualifiedLines,
  //       },
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: false,
  //       },
  //       {
  //         selector: isReadyForCheckoutSelector,
  //         newValue: checkoutReady,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(modelUpdates, undefined, true));
  //     return checkoutQualifiedLines;
  //   } catch (error) {
  //     logger.error(error);
  //     modelUpdates = [
  //       {
  //         selector: shopifySyncSelector,
  //         newValue: false,
  //       },
  //       {
  //         selector: isReadyForCheckoutSelector,
  //         newValue: false,
  //       },
  //     ];
  //     dispatch(modelUpdateAction(modelUpdates, undefined, true));
  //   }
  // }

  private sendUpdateCartAnalyticsEvent(dispatch, lineItems) {
    setTimeout(() => {
      try {
        let totalQuantity = 0;
        let numberOfProducts = 0;
        for (let index = 0; index < lineItems.length; ++index) {
          const itemsNewQty = lineItems[index]?.newQuantity ?? 0;
          totalQuantity += itemsNewQty;
          numberOfProducts += itemsNewQty <= 0 ? 0 : 1;
        }

        dispatch(
          sendAnalyticsEvent('track', 'updateCartQuantity', {
            totalQuantity,
            numberOfProducts,
          }),
        );
      } catch (err) {
        logger.error(err);
      }
    }, 50);
  }

  private sendCartAnalyticsForNotifications(dispatch, cartObject: any, actionType: string, cartId: string) {
    let lineItems = [];
    try {
      const updatedLineItems = this.getLineItemsFromCart(cartObject);
      lineItems = updatedLineItems.filter((e: any) => e.merchandiseId && e.merchandiseId.trim());
      dispatch(sendAnalyticsEvent('track', 'customUpdateCart', {
        lineItems,
        actionType,
        cartId,
      }));
    } catch (error) {
      logger.error('Error in sendCartAnalyticsForNotifications', error);
    }
  }

  updateCheckoutShippingAddress = async (dispatch, config, model, selector: Selector, params: any) => {
    const payload = params as UpdateCheckoutShippingAddressParams;

    const cartSelector = selector.concat([SHOPIFY_MODEL_CART_OBJECT_KEY]);
    const {cartId, customerAccessToken, addressId, shopDomain, redirectTo} = payload;

    const input = {
      cartId: cartId,
      buyerIdentity: {
        customerAccessToken: customerAccessToken,
        deliveryAddressPreferences: {
          customerAddressId: addressId,
        },
      },
    };

    const storefrontAccessToken: string = model.get(STOREFRONT_ACCESS_TOKEN);

    // TODO: We need to depreacate explicit call as we are now officially migration to shopify api version 2024-07
    await axios
      .post(
        `https://${shopDomain}/api/2024-07/graphql.json`,
        {
          query: UPDATE_CART_BUYER_IDENTITY_FOR_SHIPPING_ADDRESS,
          variables: input,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-shopify-storefront-access-token': storefrontAccessToken,
          },
        },
      )
      .then(response => {
        // console.log('response', response);
        // const modelUpdates = [
        //   {
        //     selector: cartSelector,
        //     newValue: {checkoutUrl: response.data.data.cartBuyerIdentityUpdate.cart.checkoutUrl},
        //   },
        // ];
        // dispatch(modelUpdateAction(modelUpdates, undefined, true));
        dispatch(navigateToScreen(redirectTo, {}));
      })
      .catch(error => {
        console.error('Error:', error);
      });
  };

  syncCartAssist = async (dispatch, config, model, selector: Selector, params: any, cartObject?: any) => {
    const customerAccessToken = model.get('loggedInUserAccessToken')?.accessToken;
    const isCustomerAPIEnabled = model.get('useCustomerApi', false);
    const namespace = 'Apptile-CartAssist';
    const checkedout_ids_key = 'smart_cart_checkedout_ids';
    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const queryRunner = model.get('queryRunner');
    const customerAPIQueryRunner = model.get('customerAPIQueryRunner');
    const appId = model.get('appId');
    const cartAssistAPIUrl = model.get('cartAssistAPIUrl');

    // This is will be called when a new cart is created from the mobile using increaseCartLineItem action
    if (cartObject?.id) {
      if (isCustomerAPIEnabled && customerAccessToken) {
        // Updating customer metafield using customer api
        await this.updateCustomerCartAssistMetafieldUsingCustomerApi(
          model,
          'smart_cart_id',
          cartObject?.id?.match(/Cart\/(.+)/)[1],
        );
      } else {
        await axios.post(
          cartAssistAPIUrl + '/update',
          {
            customerAccessToken,
            cartId: cartObject?.id?.match(/Cart\/(.+)/)[1],
          },
          {
            headers: {
              'x-shopify-app-id': appId,
              'x-shopify-customer-access-token': customerAccessToken,
            },
          },
        );
      }
      return;
    }

    // Check whether the current cartId in the localstorage == checkedOutIds
    // ? true -> Delete the local storage
    // logger.info('syncCart::Entered !!!!', customerAccessToken);
    let response;
    if (isCustomerAPIEnabled) {
      response = await customerAPIQueryRunner.runQuery('query', CUSTOMER_METAFIELD_FETCH_CUSTOMER_API, {
        namespace,
        key: checkedout_ids_key,
      });
    } else {
      response = await queryRunner.runQuery(
        'query',
        CUSTOMER_METAFIELD_FETCH,
        {customerAccessToken, namespace, key: checkedout_ids_key},
        {},
      );
    }

    const data = response.data;

    const currentCartKey = ((await this.getCartIdFromLocalStorage(config)) as string)?.split('/')?.pop();
    // logger.info('syncCart::Checking old carts !!!!', currentCartKey, JSON.stringify(data));
    if (data.customer.metafields?.length && data.customer?.metafields[0]?.value) {
      const metafields = JSON.parse(data.customer?.metafields[0]?.value);
      if (metafields?.includes(currentCartKey)) {
        console.log('METAFIELD INCLUDES CURRENTCARTID');
        toast.show('Cart checked out from web!', {
          type: 'warning',
          placement: 'bottom',
          duration: 2500,
          style: {marginBottom: 80},
        });
        await this.clearLocalCartState(dispatch, config, model, selector, {
          skipAnalyticsPurchaseEvent: true,
          navigateToSuccessScreen: false,
        });
      }
    }
    // Fetching the cartId from the metafield
    const metafieldCartId = await this.getActiveMetafieldForCartAssist(model, customerAccessToken);

    // If cartId is there in the metafield replace the local storage
    // else update the metafield
    if (metafieldCartId) {
      const cartGID = `gid://shopify/Cart/${metafieldCartId}`;
      if (currentCartKey != metafieldCartId) await this.addCustomAttributeToCart(model, cartGID);
      let {transformedData, transformedError} = await this.queryExecutor(model, queries?.GetCartDetails, {
        cartId: cartGID,
      });
      if (transformedData) {
        this.updateCartId(dispatch, config, model, selector, cartGID);
      } else {
        // This cart has expired and needs to be cleared.
        // await axios.get(cartAssistAPIUrl + '/clear', {
        //   headers: {
        //     'x-shopify-app-id': appId,
        //     'x-shopify-customer-access-token': customerAccessToken,
        //   },
        // });
        await this.clearLocalCartState(dispatch, config, model, selector, {
          skipAnalyticsPurchaseEvent: true,
          navigateToSuccessScreen: false,
        });
      }
    } else {
      const cartId: string = await this.getCartIdFromLocalStorage(config);
      if (cartId) {
        if (isCustomerAPIEnabled) {
          await this.updateCustomerCartAssistMetafieldUsingCustomerApi(model, 'smart_cart_id', currentCartKey);
        } else {
          await axios.post(
            cartAssistAPIUrl + '/update',
            {
              customerAccessToken,
              cartId: cartId?.match(/Cart\/(.+)/)[1],
            },
            {
              headers: {
                'x-shopify-app-id': appId,
                'x-shopify-customer-access-token': customerAccessToken,
              },
            },
          );
        }
      }
    }
  };

  clearCartAssist = async (dispatch, config, model, selector: Selector, params: any) => {
    const customerAccessToken = model.get('loggedInUserAccessToken')?.accessToken;
    const appId = model.get('appId');
    const cartAssistAPIUrl = model.get('cartAssistAPIUrl');
    const isCustomerAPIEnabled = model.get('useCustomerApi', false);
    const customerAPIQueryRunner = model.get('customerAPIQueryRunner');
    const loggedInUser = model.get('loggedInUser');

    const namespace = 'Apptile-CartAssist';
    const checkedout_ids_key = 'smart_cart_checkedout_ids';
    const smart_cart_id_key = 'smart_cart_id';

    if (isCustomerAPIEnabled) {
      const {data} = await customerAPIQueryRunner.runQuery('query', CUSTOMER_METAFIELDS_FETCH_CUSTOMER_API, {
        metafieldIdentifiers: [
          {namespace, key: checkedout_ids_key},
          {namespace, key: smart_cart_id_key},
        ],
      });

      const metafields = data.customer?.metafields;

      const currentCartId = metafields.find((metafield: any) => metafield?.key == 'smart_cart_id')?.value;
      if (!currentCartId || currentCartId == null) {
        logger.info('Skipping becuase the currentCartId is null');
        return;
      }

      const checkedOutIdsJsonStr = metafields.find(
        (metafield: any) => metafield?.key == 'smart_cart_checkedout_ids',
      )?.value;
      const checkedOutIds = checkedOutIdsJsonStr ? JSON.parse(checkedOutIdsJsonStr) : [];
      if (checkedOutIds.includes(currentCartId)) {
        logger.info(
          `Skipping becuase the currentCartId is already checked out for customer: ${'customerId'} under shop: ${'storeName'}`,
        );
      }
      if (checkedOutIds.length == 5) checkedOutIds.shift();

      checkedOutIds.push(currentCartId);

      await this.updateCustomerCartAssistMetafieldUsingCustomerApi(
        model,
        checkedout_ids_key,
        JSON.stringify(checkedOutIds),
      );

      const data1 = await customerAPIQueryRunner.runQuery('query', CUSTOMER_METAFIELD_DELETE_CUSTOMER_API, {
        metafields: {namespace, key: smart_cart_id_key, ownerId: loggedInUser.id},
      });
    } else {
      // Clear the metafield and Local storage
      await axios.get(cartAssistAPIUrl + '/clear', {
        headers: {
          'x-shopify-app-id': appId,
          'x-shopify-customer-access-token': customerAccessToken,
        },
      });
    }
  };

  createCartIfNotExists = async (dispatch, config, model, selector: Selector, params: any) => {
    await this.assertValidCartExists(dispatch, config, model, selector);

    const {cartObject, cartError} = await this.fetchCartDetailsFromShopify(config, model);

    await this.updateModelFromCartObject(dispatch, config, model, selector, cartObject);
  };

  private hasApptileAttribute(cartObject: any): boolean {
    if (!cartObject?.attributes) return false;
    return cartObject.attributes.some(
      (attr: any) => attr.key === 'source_name' && attr.value === apptileCheckoutCustomAtt,
    );
  }

  private async ensureCartAttributes(model: any, cartId: string): Promise<void> {
    try {
      const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasourceConfig?.plugin?.getQueries();
      await this.queryExecutor(model, queries?.CartAttributesUpdate, {
        attributes: {
            source_name: apptileCheckoutCustomAtt,
          },
        cartId,
      });
    } catch (error) {
      logger.error('Error ensuring cart attributes:', error);
    }
  }

  createAuctionCart = async (dispatch, config, model, selector: Selector, params: any) => {
    const auctionSyncSelector = selector.concat([SHOPIFY_MODEL_AUCTION_CART_SYNC_KEY]);
    const email = !_.isEmpty(model.get('loggedInUser')?.email) ? model.get('loggedInUser').email : null;
    const customerAccessToken = !_.isEmpty(model.get('loggedInUserAccessToken')?.accessToken) ? model.get('loggedInUserAccessToken').accessToken : null;
    dispatch(modelUpdateAction([{
      selector: auctionSyncSelector,
      newValue: true,
    }], undefined, true));

    const { auctionItems, navigatePostCreationScreen } = params;
    const lineItemsMap = new Map<string, {
      merchandiseId: string;
      quantity: number;
      attributes: Array<{
        key: string;
        value: any;
      }>
    }>();

    auctionItems.forEach((item: any) => {
      const variantId = item.variant?.id;
      if (!variantId) return;
      
      const existingItem = lineItemsMap.get(variantId);
      if (existingItem) {
        existingItem.quantity += 1;
        const existingWinningBid = parseInt(existingItem.attributes[0].value);
        if (item.winningBid > existingWinningBid) {
          existingItem.attributes[0].value = String(item.winningBid);
        }
      } else {
        lineItemsMap.set(variantId, {
          merchandiseId: variantId,
          quantity: 1,
          attributes: [
            {
              key: '_winningBid', // Don't remove the underscore as it hides the attribute from the checkout
              value: String(item.winningBid)
            }
          ]
        });
      }
    });

    const lineItems = Array.from(lineItemsMap.values());
    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const shoppingCartCreateQuery = queries?.CreateShoppingCartV1;

    const createCartInput = {
      customAttributes: [
        {
          key: 'source_name',
          value: apptileCheckoutCustomAtt
        },
        {
          key: 'source_type',
          value: 'auction'
        }
      ],
      lines: lineItems.map(item => ({
        merchandiseId: item.merchandiseId,
        quantity: item.quantity,
        attributes: item.attributes
      })),
      customerAccessToken: customerAccessToken,
      email
    }

    let {transformedData, transformedError} = await this.queryExecutor(
      model,
      shoppingCartCreateQuery,
      createCartInput
    )

    dispatch(modelUpdateAction([{
      selector: auctionSyncSelector,
      newValue: false,
    }], undefined, true));
    
    if(!transformedError?.length && navigatePostCreationScreen) {
      dispatch(navigateToScreen(navigatePostCreationScreen, {checkoutId: transformedData?.id, webUrl: transformedData?.checkoutUrl}))
    }

    if(transformedError?.length) {
      console.log('Error in creating auction cart', transformedError)
      toast.show(transformedError[0]?.message ?? 'Could not add item to cart', {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
  }
}

const checkoutActions = new CheckoutActions();
export default checkoutActions;
