import {ShopifyQueryDetails} from '..';
import * as customerAPIGqls from '../queries/customerAPIQueries';
import * as CustomerTransformer from '../customerAPITransformers/customerTransformers';

export const CustomerAPICustomerQueryRecords: Record<string, ShopifyQueryDetails> = {
  FetchCustomerWithCustomerAPI: {
    queryType: 'query',
    gqlTag: customerAPIGqls.CUSTOMER_FETCH,
    transformer: CustomerTransformer.TransformCustomerApiGetCustomerQuery,
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: () => ({}),
    isCustomerAPI: true,
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  FetchCustomer: {
    queryType: 'query',
    gqlTag: customerAPIGqls.CUSTOMER_FETCH,
    transformer: CustomerTransformer.TransformCustomerApiGetCustomerQuery,
    editableInputParams: {
      customerAccessToken: '',
    },
    contextInputParams: {
      customerMetafields: 'customerMetafields',
    },
    inputResolver: () => ({}),
    isCustomerAPI: true,
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  UpdateCustomer: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.CUSTOMER_UPDATE,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiUpdateCustomerQuery,
    editableInputParams: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, firstName, lastName} = inputVariables;
      return {input: {firstName, lastName}};
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  StorefrontCustomerAccessTokenCreateWithCustomerAPI: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.STOREFRONT_CUSTOMER_ACCESS_TOKEN_CREATE,
    transformer: CustomerTransformer.TransformStorefrontCustomerAccessTokenMutation,
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: () => ({}),
    isCustomerAPI: true,
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  GetCustomerAddresses: {
    queryType: 'query',
    gqlTag: customerAPIGqls.GET_CUSTOMER_ADDRESSES,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiGetCustomerAddresses,
    editableInputParams: {
      customerAccessToken: '',
    },
    // inputResolver: (inputVariables: {customerAccessToken: string}) => {
    //   const {customerAccessToken} = inputVariables;
    //   return {customerAccessToken};
    // },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  GetCustomerDefaultAddress: {
    queryType: 'query',
    gqlTag: customerAPIGqls.GET_CUSTOMER_DEFAULT_ADDRESSES,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiGetCustomerDefaultAddresses,
    editableInputParams: {
      customerAccessToken: '',
    },
    // inputResolver: (inputVariables: {customerAccessToken: string}) => {
    //   const {customerAccessToken} = inputVariables;
    //   return {customerAccessToken};
    // },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  CustomerAddressCreate: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.CUSTOMER_ADDRESS_CREATE,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiCustomerAddressMutation,
    editableInputParams: {
      address1: '',
      address2: '',
      city: '',
      company: '',
      country: '',
      countryCode: '',
      firstName: '',
      lastName: '',
      phone: '',
      stateCode: '',
      state: '',
      zip: '',
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, state, stateCode, country, countryCode, phone, ...restAddress} = inputVariables;
      return {
        address: {
          ...restAddress,
          phoneNumber: phone,
          zoneCode: stateCode ? stateCode : state,
          territoryCode: countryCode ? countryCode : country,
        },
      };
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  CustomerAddressUpdate: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.CUSTOMER_ADDRESS_UPDATE,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiCustomerAddressMutation,
    editableInputParams: {
      address1: '',
      address2: '',
      city: '',
      company: '',
      country: '',
      countryCode: '',
      firstName: '',
      lastName: '',
      phone: '',
      stateCode: '',
      state: '',
      zip: '',
      customerAccessToken: '',
      id: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, state, stateCode, country, countryCode, phone, id, ...restAddress} = inputVariables;
      return {
        address: {
          ...restAddress,
          phoneNumber: phone,
          zoneCode: stateCode ? stateCode : state,
          territoryCode: countryCode ? countryCode : country,
        },
        addressId: id,
      };
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  CustomerAddressDelete: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.CUSTOMER_ADDRESS_DELETE,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiDeleteCustomerAddress,
    editableInputParams: {
      id: '',
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, id} = inputVariables;
      return {addressId: id};
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  UpdateCustomerDefaultAddress: {
    queryType: 'mutation',
    gqlTag: customerAPIGqls.UPDATE_CUSTOMER_DEFAULT_ADDRESS,
    isCustomerAPI: true,
    transformer: CustomerTransformer.TransformCustomerApiCustomerAddressMutation,
    editableInputParams: {
      customerAccessToken: '',
      id: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, id} = inputVariables;
      return {addressId: id};
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },
};
