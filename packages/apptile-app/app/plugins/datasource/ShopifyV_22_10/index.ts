import {DocumentNode} from 'graphql';
import _ from 'lodash';
import {call, put, select, spawn} from 'redux-saga/effects';

import {Editors, PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  ImmutableMapType,
  LocalStorage,
  PluginConfigType,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
  datasourceTypeModelSel,
  selectPluginConfig,
  store,
  logger,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import apolloQueryRunner from '../ApolloWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IShopifyCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {processShopifyGraphqlQueryResponse} from '../utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  IAuthActionsDatasourcePluginConfigType,
  authActionDatasourceEditor,
  authActionsDatasourcePluginConfig,
  authActionsDatasourcePropertySettings,
} from './actions/authActions';
import checkoutActions, {checkoutActionDatasourceEditor} from './actions/checkoutAction';
import {canMakeApplePayment, initApplePay, resetApplePayStatus} from './actions/shopifyApplePayActions';
import recentlyViewedAction from './actions/shopifyRecentlyViewedActions';
import countrySwitchActions from './actions/switchCountryAction';
import {
  ITagCustomerActionsDatasourcePluginConfigType,
  tagCustomerActionsDatasourceEditor,
  tagCustomerActionsDatasourcePluginConfig,
  tagCustomerActionsDatasourcePropertySettings,
} from './actions/tagCustomerActions';
import {CartQueryRecords} from './apiQueryRecords/cartQueryRecords';
import {collectionQueryRecords} from './apiQueryRecords/collectionQueryRecords';
import {CustomerQueryRecords} from './apiQueryRecords/customerQueryRecords';
import {CustomerAPICustomerQueryRecords} from './customerAPIQueryRecords/customerQueryRecords';
import {CustomerAPIOrderQueryRecords} from './customerAPIQueryRecords/orderQueryRecords';
import {onlineStoreRecords} from './apiQueryRecords/onlineStoreQueryRecords';
import {ProductCacheByHandle} from './cache/ProductCacheByHandle';
import {
  SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY,
  SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY,
  SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY,
} from './constants';
import {initShopifyGenerator} from './generators';
import * as BlogGqls from './queries/blog';
import * as MetaObjectGqls from './queries/metaobject';
import * as OrdersGqls from './queries/orders';
import * as ProductGqls from './queries/product';
import * as CollectionGqls from './queries/productCollection';
import {GET_SHOP, GET_SHOP_CURRENCIES} from './queries/shopDetails';
import {
  TransformGetArticle,
  TransformGetArticles,
  TransformGetBlog,
  TransformGetBlogs,
} from './transformers/blogTransformer';
import {TransformProductMetafields_V2} from './transformers/metafieldsTransformer';
import {MetaObjectTransformer, MetaObjectsTransformer} from './transformers/metaobjectTransformer';
import {TransformCustomerOrder, TransformCustomerOrders} from './transformers/ordersTransformer';
import {
  TransformCollectionDetailsByHandle,
  TransformGetCollectionProductsQuery,
  TransformGetCollections,
  TransformGetMediaDataFromMediaId,
  TransformGetProductVariantByOptions,
  TransformGetProductVariantsQuery,
  TransformGetProductsByHandlesQuery,
  TransformGetProductsPaginatedQuery,
  TransformGetProductsQuery,
  TransformProductFilters,
  TransformProductMetafields,
  TransformProductQueries,
} from './transformers/productTransformer';
import {IShopifyMetafieldInput} from './types';
import {formatDisplayPrice} from './utils/utils';
import { GET_PREDICTIVE_SEARCH_SUGGESTIONS } from './queries/search';
import { TransformPredictiveSearch } from './transformers/searchTransformer';
import authActions from './actions/authActions';
import currencyActions from './actions/currencyActions';
import {countriesList, Currency} from '@/root/web/common/currencyConstants';
import { allAvailablePlans, navigateToScreenReset } from 'apptile-core';
import { Platform } from 'react-native';

const CURRENT_STOREFRONT_API_VERSION = '2024-10';

export interface ShopifyPluginConfigType
  extends DatasourcePluginConfig,
    IAuthActionsDatasourcePluginConfigType,
    ITagCustomerActionsDatasourcePluginConfigType {
  storefrontApiUrl: string;
  preSignInApiUrl: string;
  storefrontAccessToken: string;
  countryCode: string;
  languageCode: string;
  customerAccessToken: string;
  shop: any;
  productMetafields: Array<IShopifyMetafieldInput> | string;
  collectionMetafields: Array<IShopifyMetafieldInput> | string;
  variantMetafields: Array<IShopifyMetafieldInput> | string;
  customerMetafields: Array<IShopifyMetafieldInput> | string;
  productCacheController: any;
  productCacheByHandle: any;
  currentCartLineItemsLSKey: string;
  currentCartLSKey: string;
  currentCartIdLSKey: string;
  currentCartId: string;
  currentCartLineItems: any[];
  currentCart: any;
  currentCartError: any;
  currentCheckoutLSKey: string;
  currentCheckout: any;
  skipCartCreation: boolean;
  flushCheckoutItemForSync: boolean;
  syncingCartStatus: boolean;
  subscriptionCartStatus: boolean;
  formatCurrency: string | ((amount: number) => string);
  increaseCartLineItemQuantity: string;
  createCartIfNotExists: string;
  createAuctionCart: string;
  decreaseCartLineItemQuantity: string;
  removeCartLineItem: string;
  addCartLineItems: string;
  alterCartLineItemsQuantities: string;
  syncLocalCartStateWithShopify: string;
  duplicateCart: string;
  getSubscriptionCartStatus: string;
  clearLocalCartState: string;
  maxCartLineItemLimit: number;
  cartLineItemLimitExceededMessage: string;
  loginSessionExpiredMessage: string;
  updateCheckoutShippingAddress: string;
  queryRunner: any;
  useCustomerApi: any;
  customerApiUrl: any;
  customerApiAuthBaseUrl: any;
  customerApiClientId: any;
  customerApiRedirectUri: any;
  mandatoryLogin: boolean;
  mandatoryLoginRedirectScreenId: string;
  shopifyCurrencyList: Currency[];
  setCurrency: string;
  selectedCurrency: string;
}
export type TransformerFunction = (
  data: any,
  context: any,
  model?: ImmutableMapType<any>,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

export type ShopifyQueryDetails = DatasourceQueryDetail & {
  queryType: 'query' | 'mutation';
  gqlTag: DocumentNode;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  //add the type here
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
  inputResolver?: (inputVariables: any, dsModelValues?: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  isCustomerAPI?: boolean;
  headerResolver?: (params: any) => any;
};

export const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (modelValues: ImmutableMapType) => {
    const dsPluginConfig = modelValues; //.get('config') as any as Immutable.Map<string, ShopifyPluginConfigType>;
    if (!dsPluginConfig) return;

    let contextParams = Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      return acc;
    }, {});
    return contextParams;
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const shopifyApiRecords: Record<string, ShopifyQueryDetails> = {
  ...CartQueryRecords,
  // ...CustomerQueryRecords,
  ...onlineStoreRecords,
  ...collectionQueryRecords,
  // ...CustomerAPICustomerQueryRecords,
  GetShop: {
    queryType: 'query',
    gqlTag: GET_SHOP,
  },

  //Orders
  // GetOrders: {
  //   queryType: 'query',
  //   gqlTag: OrdersGqls.GET_ORDERS,
  //   transformer: TransformCustomerOrders,
  //   contextInputParams: {
  //     countryCode: 'countryCode',
  //   },
  //   editableInputParams: {
  //     sortKey: 'PROCESSED_AT',
  //     reverse: false,
  //     first: 10,
  //     after: '',
  //     customerAccessToken: '',
  //   },
  //   isPaginated: true,
  //   paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
  //     const {after} = paginationMeta;
  //     return {...inputVariables, after};
  //   },
  //   checkInputVariabes: (inputVariables: Record<string, any>) => {
  //     const {customerAccessToken} = inputVariables;
  //     return !_.isEmpty(customerAccessToken);
  //   },
  // },
  // GetOrderDetails: {
  //   queryType: 'query',
  //   gqlTag: OrdersGqls.GET_ORDER_DETAILS,
  //   transformer: TransformCustomerOrder,
  //   contextInputParams: {
  //     countryCode: 'countryCode',
  //   },
  //   editableInputParams: {
  //     sortKey: 'PROCESSED_AT',
  //     reverse: false,
  //     first: 10,
  //     after: '',
  //     customerAccessToken: '',
  //     orderId: '',
  //   },
  //   isPaginated: true,
  //   paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
  //     const {after} = paginationMeta;
  //     return {...inputVariables, after};
  //   },
  // },

  //Products and collection
  GetProductVariationsWithOptions: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_VARIATIONS_WITH_OPTIONS,
    transformer: TransformGetProductVariantByOptions,
    editableInputParams: {
      productId: '',
      selectedOptions: [],
      //This array of objects with the following properties: name, value
    },
  },

  GetProductFilters: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_PRODUCT_FILTERS,
    transformer: TransformProductFilters,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode'
    },
    editableInputParams: {
      collectionHandle: 'frontpage',
    },
    isPaginated: false,
  },

  GetSearchFilters: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_SEARCH_FILTERS,
    // transformer: TransformProductFilters,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    editableInputParams: {
      query: '',
      productFilters: '',
      first: '',
    },
    isPaginated: false,
  },

  GetProductMetafields: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_METAFIELDS,
    transformer: TransformProductMetafields,
    editableInputParams: {
      productId: '',
      identifiers: '', //identifiers are the keys and namespaces of the metafields
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productId, identifiers} = inputVariables;
      return !!productId && !!identifiers;
    },
  },

  GetProductMetafieldsByHandle: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_METAFIELDS_BY_HANDLE,
    transformer: TransformProductMetafields,
    editableInputParams: {
      productHandle: '',
      identifiers: '', //identifiers are the keys and namespaces of the metafields
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productHandle, identifiers} = inputVariables;
      return !!productHandle && !!identifiers;
    },
  },

  GetProductByHandle: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_BY_HANDLE,
    contextInputParams: {
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    transformer: TransformProductQueries,
    editableInputParams: {
      productHandle: '',
    },
  },

  GetProductByHandles: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_PRODUCTS_BY_HANDLES,
    contextInputParams: {
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    editableInputParams: {
      productHandles: '',
      first: 10,
    },
    inputResolver: inputVariables => {
      return {
        ...inputVariables,
        productHandles: inputVariables?.productHandles?.join(' OR '),
      };
    },
    transformer: TransformGetProductsByHandlesQuery,
  },

  GetProduct: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT,
    transformer: TransformProductQueries,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    editableInputParams: {
      productId: '',
    },
  },

  GetProductByIds: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_BY_IDS,
    transformer: TransformGetProductsQuery,
    editableInputParams: {
      productIds: '',
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productIds} = inputVariables;
      return !!productIds;
    },
  },
  GetVariantByIds: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_VARIANT_BY_IDS,
    transformer: TransformGetProductVariantsQuery,
    editableInputParams: {
      variantIds: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {variantIds} = inputVariables;
      return !!variantIds;
    },
  },

  GetProductRecomendations: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_RECOMENDATIONS,
    transformer: TransformGetProductsQuery,
    editableInputParams: {
      productId: '',
      intent: "RELATED or COMPLEMENTARY"
    },
    inputResolver: inputVariables => {
      const {intent, productId} = inputVariables;
      return {
        productId: productId,
        intent: _.isEmpty(intent) ? "RELATED" : intent,
      };
    },
    contextInputParams: {
      presentmentCurrencies: 'currency',
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
  },

  SearchProducts: {
    queryType: 'query',
    gqlTag: CollectionGqls.SEARCH_PRODUCTS,
    transformer: TransformGetProductsPaginatedQuery,
    contextInputParams: {
      presentmentCurrencies: 'currency',
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      return {
        ...inputVariables,
        query: inputVariables?.query ?? '',
      };
    },
    editableInputParams: {
      query: 'frontpage',
      sortKey: 'BEST_SELLING',
      reverse: false,
      first: 10,
      after: 0,
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  getSearchSuggestions: {
    queryType: 'query',
    gqlTag: GET_PREDICTIVE_SEARCH_SUGGESTIONS,
    transformer: TransformPredictiveSearch,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      return {
        ...inputVariables,
        query: inputVariables?.query ?? '',
      };
    },
    editableInputParams: {
      query: 'frontpage',
      limit: 10,
      limitScope: 0,
      searchableFields: '',
      unavailableProducts: '',
      types: ''
    },
    isPaginated: false,
  },

  SearchProductsWithFilters: {
    queryType: 'query',
    gqlTag: CollectionGqls.SEARCH_PRODUCTS_WITH_FILTERS,
    transformer: TransformGetProductsPaginatedQuery,
    contextInputParams: {
      presentmentCurrencies: 'currency',
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      return {
        ...inputVariables,
        query: inputVariables?.query ?? '',
      };
    },
    editableInputParams: {
      query: 'frontpage',
      sortKey: 'RELEVANCE',
      reverse: false,
      first: 20,
      after: 0,
      productFilters: '',
      unavailableProducts: 'SHOW',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  GetCollectionProductsById: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_COLLECTION_ID_PRODUCTS,
    transformer: TransformGetCollectionProductsQuery,
    contextInputParams: {
      countryCode: 'countryCode',
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
    },
    editableInputParams: {
      id: '',
      sortKey: 'BEST_SELLING',
      reverse: false,
      first: 10,
      after: '',
      filters: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  GetCollectionProductsByHandle: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS,
    transformer: TransformGetCollectionProductsQuery,
    contextInputParams: {
      productMetafields: 'productMetafields',
      variantMetafields: 'variantMetafields',
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    editableInputParams: {
      collectionHandle: 'frontpage',
      sortKey: 'BEST_SELLING',
      reverse: false,
      first: 10,
      after: '',
      filters: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {collectionHandle} = inputVariables;
      return !!collectionHandle;
    },
  },
  GetProductVariantMetafieldByHandle: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_VARIANT_METAFIELDS_BY_HANDLE,
    contextInputParams: {
      countryCode: 'countryCode',
    },
    transformer: TransformProductQueries,
    editableInputParams: {
      productHandle: '',
      identifiers: '', //identifiers are the keys and namespaces of the metafields
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productHandle, identifiers} = inputVariables;
      return !!productHandle && !!identifiers;
    },
  },
  GetCollectionDetailsByHandle: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_COLLECTION_BY_HANDLE,
    transformer: TransformCollectionDetailsByHandle,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
      collectionMetafields: 'collectionMetafields',
    },
    editableInputParams: {
      collectionHandle: 'frontpage',
    },
    isPaginated: false,
  },

  GetCollections: {
    queryType: 'query',
    gqlTag: CollectionGqls.GET_ALL_COLLECTIONS,
    transformer: TransformGetCollections,
    contextInputParams: {
      collectionMetafields: 'collectionMetafields',
    },
    editableInputParams: {
      first: 10,
      after: '',
      reverse: false,
      sortKey: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  //Blogs and articles
  GetBlog: {
    queryType: 'query',
    gqlTag: BlogGqls.GET_BLOG,
    transformer: TransformGetBlog,
    editableInputParams: {
      blogId: '',
    },
  },

  GetBlogs: {
    queryType: 'query',
    gqlTag: BlogGqls.GET_BLOGS,
    transformer: TransformGetBlogs,
    editableInputParams: {
      first: 10,
      after: '',
      query: '',
      reverse: false,
      sortKey: 'CREATED_AT',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  GetArticle: {
    queryType: 'query',
    gqlTag: BlogGqls.GET_ARTICLE,
    transformer: TransformGetArticle,
    editableInputParams: {
      blogHandle: '',
      articleHandle: '',
    },
  },

  GetArticles: {
    queryType: 'query',
    gqlTag: BlogGqls.GET_ARTICLES,
    transformer: TransformGetArticles,
    editableInputParams: {
      query: '',
      first: 10,
      after: '',
      sortKey: '',
      reverse: true,
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },

  GetBlogByHandle: {
    queryType: 'query',
    gqlTag: BlogGqls.GET_BLOG_BY_HANDLE,
    transformer: TransformGetBlog,
    editableInputParams: {
      blogHandle: '',
    },
  },

  GetMediaDataFromMediaId: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_MEDIA_DATA_FROM_MEDIAID,
    editableInputParams: {
      mediaGIDs: '',
    },
    transformer: TransformGetMediaDataFromMediaId,
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {mediaGIDs} = inputVariables;
      return !_.isEmpty(mediaGIDs);
    },
  },

  GetMetaObjectById: {
    queryType: 'query',
    gqlTag: MetaObjectGqls.GET_SHOPIFY_METAOBJECT,
    editableInputParams: {
      id: '',
    },
    transformer: MetaObjectTransformer,
  },

  GetMetaObjectsByType: {
    queryType: 'query',
    gqlTag: MetaObjectGqls.GET_SHOPIFY_METAOBJECTS,
    editableInputParams: {
      type: '',
      first: 10,
    },
    transformer: MetaObjectsTransformer,
  },

  GetProductMetafieldsByHandle_V2: {
    queryType: 'query',
    gqlTag: ProductGqls.GET_PRODUCT_METAFIELDS_BY_HANDLE_V2,
    editableInputParams: {
      productHandle: '',
      identifiers: '', //identifiers are the keys and namespaces of the metafields
    },
    transformer: TransformProductMetafields_V2,
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productHandle, identifiers} = inputVariables;
      return !!productHandle && !!identifiers;
    },
  },

  GetSupportedCurrencies: {
    queryType: 'query',
    gqlTag: GET_SHOP_CURRENCIES,
    transformer: (data: any) => {
      const currencies = data?.shop?.paymentSettings?.enabledPresentmentCurrencies || [];
      const defaultCurrency = data?.shop?.paymentSettings?.currencyCode;
      return {
        data: {
          currencies,
          defaultCurrency,
        },
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {
  formatCurrency: {
    getValue(model, renderedValue, selector) {
      // const oldValue = model[0]?.get('shop');
      const newValue = model?.shop;
      const countryCode = model?.countryCode;
      const languageCode = model?.languageCode;
      const enhancedShopConfig = {
        ...newValue,
        countryCode,
        languageCode,
      };
      return formatDisplayPrice(enhancedShopConfig);
    },
  },
  shop: {
    updatesProps: ['formatCurrency'],
  },
  productMetafields: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? [] : renderedValue;
    },
  },
  collectionMetafields: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? [] : renderedValue;
    },
  },
  variantMetafields: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? [] : renderedValue;
    },
  },
  initCart: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.initCart;
    },
  },
  increaseCartLineItemQuantity: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.increaseCartLineItemQuantity;
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        sellingPlanId: '',
        attributes: '',
        itemPrice: '',
        successToastText: 'Item added successfully!',
      },
    },
  },
  createCartIfNotExists: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.createCartIfNotExists;
    },
    actionMetadata: {
      editableInputParams: {}
    },
  },
  createAuctionCart: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.createAuctionCart;
    },
    actionMetadata: {
      editableInputParams: {
        auctionItems: '',
        navigatePostCreationScreen: '',
      }
    },
  },
  updateCartDiscountCode: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.updateCartDiscountCode;
    },
    actionMetadata: {
      editableInputParams: {
        syncWithShopify: '{{false}}',
        discountCode: '',
      },
    },
  },
  addCartAttributes: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.addCartAttributes;
    },
    actionMetadata: {
      editableInputParams: {
        attributes: '{ color: "red", size: "M" }',
      },
    },
  },
  addCartNote: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.addCartNote;
    },
    actionMetadata: {
      editableInputParams: {
        note: 'Please add a note.',
      },
    },
  },
  removeCartDiscountCode: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.removeCartDiscountCode;
    },
    actionMetadata: {
      editableInputParams: {
        syncWithShopify: '{{false}}',
      },
    },
  },
  decreaseCartLineItemQuantity: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.decreaseCartLineItemQuantity;
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        sellingPlanId: '',
        cartLineId: '',
      },
    },
  },
  removeCartLineItem: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.removeCartLineItem;
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        cartLineId: '',
        sellingPlanId: '',
      },
    },
  },
  addCartLineItems: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.addCartLineItems;
    },
    actionMetadata: {
      editableInputParams: {
        itemsToAdd: '{{[{merchandiseId:"",quantity:1,itemPrice:2.0,sellingPlanId:""}]}}',
        syncWithShopify: '{{false}}',
        successToastText: 'Items altered successfully!',
      },
    },
  },
  alterCartLineItemsQuantities: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.alterCartLineItemsQuantities;
    },
    actionMetadata: {
      editableInputParams: {
        itemsToAdd: '{{[{merchandiseId:"",quantity:1,itemPrice:2.0,sellingPlanId:""}]}}',
        itemsToRemove: '{{[{merchandiseId:"",sellingPlanId:""}]}}',
        overrideItemQuantities: '{{true}}',
        syncWithShopify: '{{false}}',
        successToastText: 'Items altered successfully!',
      },
    },
  },
  syncLocalCartStateWithShopify: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.syncLocalCartStateWithShopify;
    },
  },
  clearLocalCartState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.clearLocalCartState;
    },
    actionMetadata: {
      editableInputParams: {
        skipAnalyticsPurchaseEvent: false,
        navigateToSuccessScreen: false,
        streamProducts: '',
        currentLiveConfig: '',
      },
    },
  },
  initApplePay: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return initApplePay;
    },
    actionMetadata: {
      editableInputParams: {
        checkoutId: '',
      },
    },
  },
  canMakeApplePayment: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return canMakeApplePayment;
    },
  },
  resetApplePayStatus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return resetApplePayStatus;
    },
  },
  addToRecentlyViewed: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return recentlyViewedAction.addToRecentlyViewed;
    },
    actionMetadata: {
      editableInputParams: {
        productId: '',
      },
    },
  },
  populateRecentlyViewed: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return recentlyViewedAction.populateRecentlyViewedItems;
    },
  },
  duplicateCart: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.duplicateCart;
    },
    actionMetadata: {
      editableInputParams: {
        syncWithShopify: '{{false}}',
      },
    },
  },
  getSubscriptionCartStatus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.getSubscriptionCartStatus;
    },
    actionMetadata: {
      editableInputParams: {
        cartId: '',
      },
    },
  },
  syncCheckoutState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.syncCheckoutState;
    },
  },
  changeCountryCode: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return countrySwitchActions.changeCountryCode;
    },
    actionMetadata: {
      editableInputParams: {
        countryCode: '',
      },
    },
  },
  updateCheckoutShippingAddress: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.updateCheckoutShippingAddress;
    },
    actionMetadata: {
      editableInputParams: {
        cartId: '',
        customerAccessToken: '',
        addressId: '',
        shopDomain: '',
        redirectTo: '',
      },
    },
  },
  useCustomerApi: {
    updatesProps: [
      'signInUser',
      'signUpUser',
      'signOutUser',
      'verifyAuthSession',
      'refreshCustomerApiAccessToken',
      'renewCustomerAccessToken',
    ],
  },
  ...authActionsDatasourcePropertySettings,
  ...tagCustomerActionsDatasourcePropertySettings,
  setCurrency: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return currencyActions.setCurrency;
    },
    actionMetadata: {
      editableInputParams: {
        currencyCode: '',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'shopifyV_22_10',
  type: 'datasource',
  name: 'Shopify Storefront v2022-10',
  description: 'Shopify store front integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const shopifyEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'storefrontApiUrl',
      props: {
        label: 'API url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'preSignInApiUrl',
      props: {
        label: 'Pre Sign In API URL',
        placeholder: 'https://api.apptile.io/auth-manager',
      },
    },
    {
      type: 'codeInput',
      name: 'storefrontAccessToken',
      props: {
        label: 'Storefront Access Token',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'languageCode',
      props: {
        label: 'language Code',
        placeholder: '{{languageCode.value}}',
      },
    },
    {
      type: 'editorSectionHeader',
      name: 'currencies allowed',
      props: {
        label: 'Country & Currency Settings',
      },
      isVisibleInV2: true,
      category: 'appSettings',
    },
    {
      type: 'dropDown',
      name: 'countryCode',
      isVisibleInV2: true,
      category: 'appSettings',
      props: {
        label: 'Default Country',
        options: countriesList,
        nameKey: 'country',
        valueKey: 'countryCode',
      },
      reloadOnChange: true,
    },
    {
      type: 'currencySelector',
      name: 'shopifyCurrencyList',
      props: {
        label: 'Currencies Allowed',
        placeholder: 'Select available currencies',
        disableBinding: true,
      },
      isVisibleInV2: true,
      category: 'appSettings',
      basePlan: allAvailablePlans.PRO,
    },
    {
      type: 'checkbox',
      name: 'useCustomerApi',
      props: {
        label: 'Use Shopify New Customer Login',
        checkedValue: false,
      },
    },
    {
      type: 'editorSectionHeader',
      name: 'mandatory login',
      props: {
        label: 'Mandatory Login',
      },
      isVisibleInV2: true,
      category: 'appSettings',
    },
    {
      type: 'checkbox',
      name: 'mandatoryLogin',
      props: {
        label: 'Enable Mandatory Login',
        checkedValue: false,
      },
      isVisibleInV2: true,
      category: 'appSettings',
    },
    {
      type: 'screenSelector',
      name: 'mandatoryLoginRedirectScreenId',
      props: {
        label: 'Mandatory Login Redirect Screen',
        placeholder: 'Select a screen',
        disableBinding: true,
      },
      isVisibleInV2: true,
      category: 'appSettings',
    },
    {
      type: 'codeInput',
      name: 'customerApiUrl',
      props: {
        label: 'Customer API Url',
        placeholder: 'https://shopify.com/<shop_id>/account/customer/api/2024-07/graphql',
      },
    },
    {
      type: 'codeInput',
      name: 'customerApiAuthBaseUrl',
      props: {
        label: 'Customer API Auth Base Url',
        placeholder: 'https://shopify.com/authentication/<shop_id>',
      },
    },
    {
      type: 'codeInput',
      name: 'customerApiClientId',
      props: {
        label: 'Customer API Client ID',
        placeholder: 'shp_abcd-efgh',
      },
    },
    {
      type: 'codeInput',
      name: 'customerApiRedirectUri',
      props: {
        label: 'Customer API Redirect URI',
        placeholder: 'shop.<shop_id>.app://callback',
      },
    },
    {
      type: 'codeInput',
      name: 'skipCartCreation',
      props: {
        label: 'Skip Cart Workflow',
        placeholder: '{{true}}',
      },
    },
    {
      type: 'codeInput',
      name: 'flushCheckoutItemForSync',
      props: {
        label: 'Replace Checkout Line Items while syncing',
        placeholder: '{{false}}',
      },
    },
    {
      type: 'codeInput',
      name: SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE,
      props: {
        label: 'Local storage key for\n storing cart Id',
        placeholder: 'currentCartId',
      },
    },
    {
      type: 'codeInput',
      name: SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE,
      props: {
        label: 'Local storage key for\n storing cart object',
        placeholder: 'currentCart',
      },
    },
    {
      type: 'codeInput',
      name: SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE,
      props: {
        label: 'Local storage key for\n storing cart line items',
        placeholder: 'currentCartLineItems',
      },
    },
    {
      type: 'codeInput',
      name: SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE,
      props: {
        label: 'Local storage key for\n storing checkout object',
        placeholder: 'currentCheckout',
      },
    },
    {
      type: 'codeInput',
      name: 'applePayMerchantId',
      props: {
        label: 'Apple Pay Merchant ID',
        placeholder: 'merchant.com.apptile.test',
      },
    },
    {
      type: 'editorSectionHeader',
      name: 'cart',
      props: {
        label: 'Cart',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'maxCartLineItemLimit',
      props: {
        label: 'Cart Line Item Maximum Limit',
        placeholder: '25',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'cartLineItemLimitExceededMessage',
      props: {
        label: 'Cart Line Item Limit Exceeded Message',
        placeholder: 'You can not add more than 25 items on cart',
      },
      isVisibleInV2: true,
       category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'CartLineItemAddtoCartMessage',
      props: {
        label: 'Added to Cart',
        placeholder: 'Product added to the Cart',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'CartLineItemRemoveFromCartMessage',
      props: {
        label: 'Removed From Cart',
        placeholder: 'Product removed from the Cart',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'editorSectionHeader',
      name: 'login',
      props: {
        label: 'Login',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'successfullLoginMessage',
      props: {
        label: 'Successfull Login',
        placeholder: 'You have been logged in successfully',
      },
      isVisibleInV2: true,
      category: 'Alerts',
    },
    {
      type: 'codeInput',
      name: 'loginSessionExpiredMessage',
      props: {
        label: 'Login Session Expired Message',
        placeholder: 'Your session has expired, Please sign-in again.',
      },
    },
    ...(authActionDatasourceEditor.basic as Editors<any>),
    ...(checkoutActionDatasourceEditor.basic as Editors<any>),
    ...(tagCustomerActionsDatasourceEditor.basic as Editors<any>),
    // {
    //   type: 'codeInput',
    //   name: 'setCurrency',
    //   props: {
    //     label: 'Set Currency',
    //     placeholder: 'Enter currency code (e.g., USD)',
    //   },
    // },
  ],
  advanced: [
    {
      type: 'codeInput',
      name: 'productMetafields',
      props: {
        label: 'Product Metafields',
      },
    },
    {
      type: 'codeInput',
      name: 'variantMetafields',
      props: {
        label: 'Variant Metafields',
      },
    },
    {
      type: 'codeInput',
      name: 'collectionMetafields',
      props: {
        label: 'Collection Metafields',
      },
    },
    {
      type: 'codeInput',
      name: 'customerMetafields',
      props: {
        label: 'Customer Metafields',
      },
    },
  ],
};

const getFilteredQueryRecords = useCustomerApi => {
  return {
    ...shopifyApiRecords,
    ...(useCustomerApi ? CustomerAPICustomerQueryRecords : CustomerQueryRecords),
    ...(useCustomerApi
      ? CustomerAPIOrderQueryRecords
      : {
          GetOrders: {
            queryType: 'query',
            gqlTag: OrdersGqls.GET_ORDERS,
            transformer: TransformCustomerOrders,
            contextInputParams: {
              countryCode: 'countryCode',
            },
            editableInputParams: {
              sortKey: 'PROCESSED_AT',
              reverse: false,
              first: 10,
              after: '',
              customerAccessToken: '',
            },
            isPaginated: true,
            paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
              const {after} = paginationMeta;
              return {...inputVariables, after};
            },
            checkInputVariabes: (inputVariables: Record<string, any>) => {
              const {customerAccessToken} = inputVariables;
              return !_.isEmpty(customerAccessToken);
            },
          },
          GetOrderDetails: {
            queryType: 'query',
            gqlTag: OrdersGqls.GET_ORDER_DETAILS,
            transformer: TransformCustomerOrder,
            contextInputParams: {
              countryCode: 'countryCode',
            },
            editableInputParams: {
              sortKey: 'PROCESSED_AT',
              reverse: false,
              first: 10,
              after: '',
              customerAccessToken: '',
              orderId: '',
            },
            isPaginated: true,
            paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
              const {after} = paginationMeta;
              return {...inputVariables, after};
            },
          },
        }),
  };
};

const configMigration = (pluginConfig: PluginConfig) => {
  return pluginConfig.set(
    'config',
    pluginConfig.config.set(
      'storefrontApiUrl',
      pluginConfig.config
        .get('storefrontApiUrl')
        ?.replace(
          /(\/api\/20[\d][\d]-[\d][\d]\/graphql\.json)/gim,
          '/api/' + CURRENT_STOREFRONT_API_VERSION + '/graphql.json',
        ),
    ),
  );
};

export default wrapDatasourceModel({
  name: 'shopifyV_22_10',
  config: {
    ...baseDatasourceConfig,
    storefrontApiUrl: '',
    preSignInApiUrl: '',
    storefrontAccessToken: '',
    customerAccessToken: '',
    countryCode: '',
    languageCode: '',
    shop: '',
    productMetafields: '{{[]}}',
    collectionMetafields: '{{[]}}',
    variantMetafields: '{{[]}}',
    customerMetafields: '{{[]}}',
    productCacheController: '',
    productCacheByHandle: '',
    cartAssistEnabled: '',
    cartAssistAPIUrl: '',
    currentCartLineItemsLSKey: '',
    currentCartLSKey: '',
    currentCheckoutLSKey: '',
    currentCartIdLSKey: '',
    currentCartId: '',
    currentCartLineItems: [],
    currentCart: '',
    currentCartError: '',
    currentCheckout: '',
    skipCartCreation: false,
    syncingCartStatus: false,
    flushCheckoutItemForSync: false,
    subscriptionCartStatus: false,
    formatCurrency: 'function',
    initCart: 'action',
    increaseCartLineItemQuantity: 'action',
    createCartIfNotExists: 'action',
    createAuctionCart: 'action',
    decreaseCartLineItemQuantity: 'action',
    removeCartLineItem: 'action',
    addCartLineItems: 'action',
    alterCartLineItemsQuantities: 'action',
    syncLocalCartStateWithShopify: 'action',
    updateCartDiscountCode: 'action',
    addCartAttributes: 'action',
    addCartNote: 'action',
    removeCartDiscountCode: 'action',
    clearLocalCartState: 'action',
    initApplePay: 'action',
    canMakeApplePayment: 'action',
    resetApplePayStatus: 'action',
    applePayMerchantId: '',
    applePayOrderId: '',
    isApplePayAvailable: '',
    addToRecentlyViewed: 'action',
    populateRecentlyViewed: 'action',
    recentlyViewedList: '',
    recentlyViewedItems: '',
    duplicateCart: 'action',
    getSubscriptionCartStatus: 'action',
    syncCheckoutState: 'action',
    changeCountryCode: 'action',
    updateCheckoutShippingAddress: 'action',
    clearCartAssist: 'action',
    queryRunner: 'queryRunner',
    useCustomerApi: '',
    customerApiUrl: '',
    customerApiAuthBaseUrl: '',
    customerApiClientId: '',
    customerApiRedirectUri: '',
    mandatoryLogin: false,
    shopifyCurrencyList: [],
    mandatoryLoginRedirectScreenId: '',
    ...authActionsDatasourcePluginConfig,
    ...tagCustomerActionsDatasourcePluginConfig,
    maxCartLineItemLimit: 25,
    cartLineItemLimitExceededMessage: 'You can not add more than 25 items on cart',
    loginSessionExpiredMessage: '',
    setCurrency: 'action',
    selectedCurrency: '',
  } as ShopifyPluginConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) => {},
  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    // const dsModelValues = yield select(selectPluginStageModel, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    // Early mandatory login check - happens before any other initialization
    if (pageLoad) {
      const mandatoryLogin = dsConfig.config.get('mandatoryLogin');
      const mandatoryLoginRedirectScreenId = dsConfig.config.get('mandatoryLoginRedirectScreenId');
      const useCustomerApi = dsConfig.config.get('useCustomerApi');
      const customerApiLoginScreen = dsConfig.config.get('customerApiLoginScreen');
      // Check if user is logged in from localStorage
      const loggedInUser = yield call(LocalStorage.getValue, 'loggedInUser');

      if (!loggedInUser && mandatoryLogin && Platform.OS !== 'web') {
        if (useCustomerApi && customerApiLoginScreen) {
          try {
            const customerApiAuthBaseUrl = dsConfig.config.get('customerApiAuthBaseUrl');
            const customerApiClientId = dsConfig.config.get('customerApiClientId');
            const customerApiRedirectUri = dsConfig.config.get('customerApiRedirectUri');
  
            if (
              _.isEmpty(customerApiAuthBaseUrl) ||
              _.isEmpty(customerApiClientId) ||
              _.isEmpty(customerApiRedirectUri)
            ) {
              toast.show('Customer Account API Configuration Incomplete', {
                type: 'error',
                placement: 'bottom',
                duration: 1000,
                style: {marginBottom: 80},
              });
              // return;
            }
            const loginURL = yield call(
              authActions.getShopifyAuthUrl,
              customerApiAuthBaseUrl,
              customerApiClientId,
              customerApiRedirectUri,
            );
            yield put(navigateToScreenReset(customerApiLoginScreen, {url: loginURL}));
          } catch (error) {
            console.log(`Error signInResponse`, error);
            return {success: false, errorMessage: error};
          }
        }
        // If mandatory login is enabled and user is not logged in, redirect to the mandatory login screen
        else if (mandatoryLoginRedirectScreenId) {
          yield put(
            navigateToScreenReset(mandatoryLoginRedirectScreenId, {
              source: 'mandatoryLogin',
            }),
          );
          // Return early to prevent further initialization
          // return {modelUpdates: []};
        }
      }
    }

    if (pageLoad) {
      // console.time('Shopify: initDatasource took: ');
      const queryRunner = apolloQueryRunner();
      const customerAPIQueryRunner = apolloQueryRunner();
      yield spawn(function* () {
        yield call(queryRunner.initClient, dsConfig.config.get('storefrontApiUrl'), (_, {headers}) => {
          return {
            headers: {
              ...headers,
              'X-Shopify-Storefront-Access-Token': dsConfig.config.get('storefrontAccessToken'),
            },
          };
        });

        if (dsConfig.config.get('useCustomerApi')) {
          yield call(customerAPIQueryRunner.initClient, dsConfig.config.get('customerApiUrl'), (_, {headers}) => {
            return {
              headers: {
                ...headers,
              },
            };
          });
        }

        const appId = dsConfig.config.get('appId');
        yield call(initShopifyGenerator, dsConfig, queryRunner, appId);
      });
      const productCacheByHandle = new ProductCacheByHandle();
      const previousShopData = yield call(LocalStorage.getValue, 'shopData');
      const cartAssistModel = datasourceTypeModelSel(state, 'cartAssist');
      // console.log('DEBUG: Previous Shop data', JSON.stringify(previousShopData));
      // console.timeEnd('Shopify: initDatasource took: ');
      const modelUpdates = [
        {
          selector: [dsConfig.get('id'), 'productCacheController'],
          newValue: productCacheByHandle,
        },
        {
          selector: [dsConfig.get('id'), 'productCacheByHandle'],
          newValue: productCacheByHandle.getCacheMap(),
        },
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'customerAPIQueryRunner'],
          newValue: customerAPIQueryRunner,
        },
        {
          selector: [dsConfig.get('id'), SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY],
          newValue: false,
        },
        {
          selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY],
          newValue: false,
        },
        {
          selector: [dsConfig.get('id'), SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY],
          newValue: '',
        },
      ];
      if (cartAssistModel) {
        modelUpdates.push({
          selector: [dsConfig.get('id'), 'cartAssistEnabled'],
          newValue: cartAssistModel?.get('enabled', false),
        });
        modelUpdates.push({
          selector: [dsConfig.get('id'), 'cartAssistAPIUrl'],
          newValue: cartAssistModel?.get('apiBaseUrl', 'https://api.apptile.io/shopify-app-proxy/cart-sync'),
        });
      }
      if (previousShopData) {
        modelUpdates.push({
          selector: [dsConfig.get('id'), 'shop'],
          newValue: previousShopData?.data?.shop,
        });
      }
      return {modelUpdates};
    }
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    // TODO: Make the shopify ds id not hardcoded
    const useCustomerApi = store.getState().stageModel.getModelValue([]).getIn(['shopify', 'useCustomerApi']);

    return getFilteredQueryRecords(useCustomerApi);
  },
  getQueryInputParams: function (queryName: string) {
    const useCustomerApi = store.getState().stageModel.getModelValue([]).getIn(['shopify', 'useCustomerApi']);
    const queryRecords = getFilteredQueryRecords(useCustomerApi);
    const queryDetails = queryRecords && queryRecords[queryName] ? queryRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },
  resolveCredentialConfigs: function (credentials: IShopifyCredentials): Partial<ShopifyPluginConfigType> | boolean {
    const {storeName, storefrontAccessToken, shopManagerApiUrl, appId, preSignInApiUrl} = credentials ?? {};
    if (!storeName || !storefrontAccessToken || !appId) return false;
    const shopManagerApiUrlSub = _.isEmpty(shopManagerApiUrl) ? {} : {shopManagerApiUrl};
    return {
      storefrontApiUrl: `https://${storeName}/api/${CURRENT_STOREFRONT_API_VERSION}/graphql.json`,
      storefrontAccessToken: storefrontAccessToken,
      appId: appId,
      preSignInApiUrl: !_.isEmpty(preSignInApiUrl) ? preSignInApiUrl : 'https://api.apptile.io/auth-manager',
      ...shopManagerApiUrlSub,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['storefrontApiUrl', 'storefrontAccessToken', 'shopManagerApiUrl', 'appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'shopify';
  },
  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    // const queryDetails = shopifyApiRecords[queryName];
    const currentState = yield select();
    var currentModel = currentState?.stageModel.getModelValue([]);
    const dsModelId = currentModel.get(dsConfig.get('id'));
    const mandatoryLogin = dsConfig.config.get('mandatoryLogin');
    const mandatoryLoginRedirectScreenId = dsConfig.config.get('mandatoryLoginRedirectScreenId');
    const useCustomerApi = dsConfig.config.get('useCustomerApi');
    const customerApiLoginScreen = dsConfig.config.get('customerApiLoginScreen');
    const loggedInUser = dsModelId.get('loggedInUser');
    // If mandatory login is enabled and user is not logged in, redirect to the mandatory login screen
    
    if (!loggedInUser && mandatoryLogin && Platform.OS !== 'web') {
      if (useCustomerApi && customerApiLoginScreen) {
        try {
          const customerApiAuthBaseUrl = dsConfig.config.get('customerApiAuthBaseUrl');
          const customerApiClientId = dsConfig.config.get('customerApiClientId');
          const customerApiRedirectUri = dsConfig.config.get('customerApiRedirectUri');

          if (
            _.isEmpty(customerApiAuthBaseUrl) ||
            _.isEmpty(customerApiClientId) ||
            _.isEmpty(customerApiRedirectUri)
          ) {
            toast.show('Customer Account API Configuration Incomplete', {
              type: 'error',
              placement: 'bottom',
              duration: 1000,
              style: {marginBottom: 80},
            });
            return;
          }

          logger.info(
            'signInUserCustomerApi',
            customerApiAuthBaseUrl,
            customerApiClientId,
            customerApiRedirectUri,
            customerApiLoginScreen,
          );

          // Use authActions instance to call getShopifyAuthUrl
          const loginURL = yield call(
            authActions.getShopifyAuthUrl,
            customerApiAuthBaseUrl,
            customerApiClientId,
            customerApiRedirectUri,
          );

          yield put(navigateToScreenReset(customerApiLoginScreen, {url: loginURL}));
        } catch (error) {
          console.log(`Error signInResponse`, error);
          return {success: false, errorMessage: error};
        }
      }
      // If mandatory login is enabled and user is not logged in, redirect to the mandatory login screen
      else if (mandatoryLoginRedirectScreenId) {
        yield put(
          navigateToScreenReset(mandatoryLoginRedirectScreenId, {
            source: 'mandatoryLogin',
          }),
        );
      }
    }

    const queryRecords = getFilteredQueryRecords(useCustomerApi);
    const queryDetails = queryRecords[queryName];

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options;

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsModelValues);
    }

    let typedInputVariables;
    let isReadyToRun = true;
    if (queryDetails && queryDetails.editableInputParams) {
      const typedInputs = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      // inputResolver:: To handle nested input in graphql
      typedInputVariables = queryDetails.inputResolver
        ? queryDetails.inputResolver(typedInputs, dsModelValues)
        : typedInputs;

      // nullChecker:: To check if the query has all the necessary inputs
      if (queryDetails.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    const headers = queryDetails?.headerResolver ? queryDetails.headerResolver(inputVariables) : {};

    const {cachePolicy, ...restOptions} = options as AppPageTriggerOptions;
    //To Avoid cache on getNextPage query
    const newCachePolicy = !getNextPage && cachePolicy ? {cachePolicy} : {cachePolicy: 'no-cache'};
    let queryResponse;
    if (isReadyToRun) {
      const isCustomerAPIEnabled = dsModelValues.get('useCustomerApi');

      let queryRunner;

      if (isCustomerAPIEnabled && queryDetails?.isCustomerAPI) {
        queryRunner = dsModelValues.get('customerAPIQueryRunner');
      } else {
        queryRunner = dsModelValues.get('queryRunner');
      }

      let queryTag = '';
      if (typeof queryDetails.gqlTag === 'string' || typeof queryDetails.gqlTag === 'object') {
        queryTag = queryDetails.gqlTag;
      } else if (typeof queryDetails.gqlTag === 'function') {
        queryTag = queryDetails.gqlTag(dsModel, dsConfig);
      }

      // To avoid  ERROR  [ApolloError: Promise timed out] Mostly PDP/PLP Helper missing exceptions handling.
      try {
        queryResponse = yield call(
          queryRunner.runQuery,
          queryDetails.queryType,
          queryTag,
          {...typedInputVariables, ...contextInputParam},
          {...headers, ...restOptions, ...newCachePolicy},
        );
      } catch (error: any) {
        console.warn(`Shopify ds runQuery queryName: ${queryName} failed with : ${error}`);
        queryResponse = {
          errors: error?.message,
          data: null,
        };
      }
    } else {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    }

    //

    var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
      processShopifyGraphqlQueryResponse(queryResponse, queryDetails, dsModelValues.get('shop'), dsModelValues);

    // const data = transformedData;
    return yield {
      rawData,
      data: transformedData,
      hasError: transformedHasError,
      errors: transformedError,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
    };
  },
  options: {
    propertySettings,
    pluginListing,
    configMigration,
  },
  editors: shopifyEditors,
});
