import gql from 'graphql-tag';

export const CUSTOMER_FETCH = gql`
  query customer($customerMetafields: [HasMetafieldsIdentifier!] = []) {
    customer {
      id
      firstName
      lastName
      creationDate
      emailAddress {
        emailAddress
        marketingState
      }
      phoneNumber {
        phoneNumber
        marketingState
      }
      tags
      defaultAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
      addresses(first: 10) {
        pageInfo {
          hasNextPage
        }
        nodes {
          id
          formatted(withName: true)
          address1
          address2
          city
          company
          country
          firstName
          formattedArea
          lastName
          name
          phoneNumber
          province
          territoryCode
          zip
          zoneCode
        }
      }
      metafields(identifiers: $customerMetafields) {
        namespace
        key
        value
      }
    }
  }
`;

export const CUSTOMER_UPDATE = gql`
  mutation customerUpdate($input: CustomerUpdateInput!) {
    customerUpdate(input: $input) {
      customer {
        firstName
        lastName
        displayName
        emailAddress {
          emailAddress
          marketingState
        }
        phoneNumber {
          phoneNumber
          marketingState
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const STOREFRONT_CUSTOMER_ACCESS_TOKEN_CREATE = gql`
  mutation storefrontCustomerAccessTokenCreate {
    storefrontCustomerAccessTokenCreate {
      customerAccessToken
      userErrors {
        field
        message
      }
    }
  }
`;

export const GET_CUSTOMER_ADDRESSES = gql`
  query customerAddresses {
    customer {
      defaultAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
      addresses(first: 20) {
        pageInfo {
          hasNextPage
          endCursor
          startCursor
          hasPreviousPage
        }
        nodes {
          id
          formatted(withName: true)
          address1
          address2
          city
          company
          country
          firstName
          formattedArea
          lastName
          name
          phoneNumber
          province
          territoryCode
          zip
          zoneCode
        }
      }
    }
  }
`;

export const GET_CUSTOMER_DEFAULT_ADDRESSES = gql`
  query customerAddresses {
    customer {
      defaultAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
    }
  }
`;

export const CUSTOMER_ADDRESS_CREATE = gql`
  mutation customerAddressCreate($address: CustomerAddressInput!) {
    customerAddressCreate(address: $address) {
      customerAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const CUSTOMER_ADDRESS_UPDATE = gql`
  mutation customerAddressUpdate($addressId: ID!, $address: CustomerAddressInput!) {
    customerAddressUpdate(addressId: $addressId, address: $address) {
      customerAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const CUSTOMER_ADDRESS_DELETE = gql`
  mutation customerAddressDelete($addressId: ID!) {
    customerAddressDelete(addressId: $addressId) {
      deletedAddressId
      userErrors {
        field
        message
      }
    }
  }
`;

export const UPDATE_CUSTOMER_DEFAULT_ADDRESS = gql`
  mutation customerAddressUpdate($addressId: ID!) {
    customerAddressUpdate(addressId: $addressId, defaultAddress: true) {
      customerAddress {
        id
        formatted(withName: true)
        address1
        address2
        city
        company
        country
        firstName
        formattedArea
        lastName
        name
        phoneNumber
        province
        territoryCode
        zip
        zoneCode
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// Used for Cart Assist metafield fetch
export const CUSTOMER_METAFIELD_FETCH_CUSTOMER_API = gql`
  query customerMetafieldFetch($namespace: String!, $key: String!) {
    customer {
      id
      firstName
      lastName
      emailAddress {
        emailAddress
      }
      phoneNumber {
        phoneNumber
      }
      metafields(identifiers: [{namespace: $namespace, key: $key}]) {
        namespace
        key
        value
      }
    }
  }
`;

export const CUSTOMER_METAFIELDS_FETCH_CUSTOMER_API = gql`
  query customerMetafieldFetch($metafieldIdentifiers: [HasMetafieldsIdentifier!]!) {
    customer {
      id
      firstName
      lastName
      emailAddress {
        emailAddress
      }
      phoneNumber {
        phoneNumber
      }
      metafields(identifiers: $metafieldIdentifiers) {
        namespace
        key
        value
      }
    }
  }
`;

export const CUSTOMER_METAFIELD_SET_CUSTOMER_API = gql`
  mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        key
        namespace
        value
        createdAt
        updatedAt
      }
      userErrors {
        field
        message
        code
      }
    }
  }
`;

export const CUSTOMER_METAFIELD_DELETE_CUSTOMER_API = gql`
  mutation metafieldsDelete($metafields: [MetafieldIdentifierInput!]!) {
    metafieldsDelete(metafields: $metafields) {
      deletedMetafields {
        key
        namespace
        ownerId
      }
      userErrors {
        field
        message
      }
    }
  }
`
