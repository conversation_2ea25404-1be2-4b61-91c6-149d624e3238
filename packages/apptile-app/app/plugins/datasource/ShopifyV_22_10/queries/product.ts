import gql from 'graphql-tag';

// TODO(samyam) this transform should only happen on carousel queries
export const PRODUCT_IMAGE = `
        id
        altText
        height
        width
        url (transform: {maxWidth: 1080, maxHeight: 1080})
`;
export const PRODUCT_IMAGE_COMPRESSED = `
        id
        altText
        height
        width
        url (transform: {maxWidth: 480, maxHeight: 480})
`;

const PRODUCT_VARIANT_METAFIELDS_BLOCK = `
      metafields(identifiers: $variantMetafields) {
        id
        key
        value
        namespace
        description
        type
        reference {
          ... on Metaobject {
              handle
              id
              onlineStoreUrl
              type
              updatedAt
          }
          ... on GenericFile {
              alt
              id
              mimeType
              originalFileSize
              url
          }
          ... on Collection {
              description
              descriptionHtml
              handle
              id
              onlineStoreUrl
              title
              trackingParameters
              updatedAt
          }
          ... on MediaImage {
              alt
              id
              mediaContentType
          }
          ... on Page {
              body
              bodySummary
              createdAt
              handle
              id
              onlineStoreUrl
              title
              trackingParameters
              updatedAt
          }
          ... on ProductVariant {
              availableForSale
              barcode
              currentlyNotInStock
              id
              quantityAvailable
              requiresShipping
              sku
              taxable
              title
              weight
              weightUnit
          }
          ... on Product {
              availableForSale
              createdAt
              description
              descriptionHtml
              handle
              id
              isGiftCard
              onlineStoreUrl
              productType
              publishedAt
              requiresSellingPlan
              tags
              title
              totalInventory
              trackingParameters
              updatedAt
              vendor
          }
          ... on Video {
              alt
              id
              mediaContentType
          }
      }
      }
`;

function makeProductVariantQueryBlock(mfBlock: string) {
  const PRODUCT_VARIANT_BLOCK = `
      availableForSale
      barcode
      compareAtPrice {
        amount
        currencyCode
      }
      ${mfBlock}
      currentlyNotInStock
      id
      title
      weight
      sellingPlanAllocations(first:20) {
        edges {
          node {
            sellingPlan {
              id
              name
              options {
                name
                value
              }
            }
            priceAdjustments {
              price {
                amount
                currencyCode
              }
              compareAtPrice {
                amount
                currencyCode
              }
              perDeliveryPrice {
                amount
                currencyCode
              }
              unitPrice {
                amount
                currencyCode
              }
            }
          }
        }
      }
      quantityAvailable
      requiresShipping
      selectedOptions {
        name
        value
      }
      sku
      unitPrice {
        amount
        currencyCode
      }
      unitPriceMeasurement {
        measuredType
        quantityUnit
        quantityValue
        referenceUnit
        referenceValue
      }
      weightUnit
      price {
        amount
        currencyCode
      }
      image {
        ${PRODUCT_IMAGE}
      }`;
  return PRODUCT_VARIANT_BLOCK;
}
export const PRODUCT_VARIANT = makeProductVariantQueryBlock('');
export const PRODUCT_DETAILS = makeProductQueryBlock(PRODUCT_VARIANT, '', false);
export const PRODUCT_VARIANT_METAFIELDS = makeProductVariantQueryBlock(PRODUCT_VARIANT_METAFIELDS_BLOCK);
const PRODUCT_METAFIELDS_BLOCK = `
      metafields(identifiers: $productMetafields) {
        id
        key
        value
        namespace
        description
        type
        references(first: 50) {
          edges {
              node {
                  ... on Product {
                    availableForSale
                    handle
                    id
                    isGiftCard
                    productType
                    tags
                    title
                    totalInventory
                    vendor
                    featuredImage {
                      altText
                      height
                      id
                      src
                      url (transform: {maxWidth: 480, maxHeight: 480})
                      width
                    }
                }
            }
          }
        }
        reference {
          ... on GenericFile {
            id
            previewImage {
              url
            }
            url
          }
          ... on MediaImage {
            id
            previewImage {
              url
            }
            image {
              url
            }
          }
        }
      }
`;

function makeProductQueryBlock(variantBlock: string, mfBlock: string, compressedImage?: boolean) {
  const PRODUCT_DETAILS_BLOCK = `
        id
        handle
        title
        description
        descriptionHtml
        availableForSale
        totalInventory
        onlineStoreUrl
        productType
        tags
        vendor
        requiresSellingPlan
        createdAt
        ${mfBlock}
        collections(first: 20) {
          edges {
            node {
              id
              title
              handle
            }
          }
        }
        media(first: 250) {
          edges {
            node {
              alt
              mediaContentType
              ... on ExternalVideo {
                id
                alt
                embeddedUrl
                host
                originUrl
              }
              ... on Video {
                alt
                id
                mediaContentType
                sources {
                  format
                  height
                  mimeType
                  url
                  width
                }
              }

              previewImage {
                id
                altText
                height
                width
                url
              }
            }
          }
        }
        sellingPlanGroups(first: 10) {
          edges {
            node {
              name
              appName
              sellingPlans(first: 20) {
                edges {
                  node {
                    id
                    name
                    description
                    recurringDeliveries
                    priceAdjustments {
                      orderCount
                      adjustmentValue {
                        __typename
                        ... on SellingPlanPercentagePriceAdjustment {
                          adjustmentPercentage
                        }
                        ... on SellingPlanFixedAmountPriceAdjustment {
                          adjustmentAmount {
                            amount
                            currencyCode
                          }
                        }
                        ... on SellingPlanFixedPriceAdjustment {
                          price {
                            amount
                            currencyCode
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        variants(first: 250) {
          edges {
            cursor
            node {
              ${variantBlock}
            }
          }
        }
        options(first: 50) {
          id
          name
          values
        }
        priceRange {
          maxVariantPrice {
            amount
            currencyCode
          }
          minVariantPrice {
            amount
            currencyCode
          }
        }
        compareAtPriceRange {
          minVariantPrice {
            amount
            currencyCode
          }
          maxVariantPrice {
            amount
            currencyCode
          }
        }
        productType
        images(first: 200) {
          edges {
            node {
              ${compressedImage ? PRODUCT_IMAGE_COMPRESSED : PRODUCT_IMAGE}
            }
          }
        }
  `;
  return PRODUCT_DETAILS_BLOCK;
}

export const PRODUCT_DETAILS_FOR_DROPDOWN = `
id
handle
title
onlineStoreUrl
totalInventory
media(first: 1) {
  edges {
    node {
      alt
      mediaContentType
      previewImage {
        id
        altText
        height
        width
        url
      }
    }
  }
}
variants(first: 250) {
  edges {
    node {
      id
      barcode
    }
  }
}
images(first: 1) {
  edges {
    node {
      ${PRODUCT_IMAGE}
    }
  }
}
priceRange {
  maxVariantPrice {
    amount
    currencyCode
  }
  minVariantPrice {
    amount
    currencyCode
  }
}
`;
export const PRODUCT_DETAILS_COMPRESSED = makeProductQueryBlock(PRODUCT_VARIANT, '', true);
export const PRODUCT_DETAILS_METAFIELDS = makeProductQueryBlock(
  PRODUCT_VARIANT_METAFIELDS,
  PRODUCT_METAFIELDS_BLOCK,
  false,
);
export const PRODUCT_DETAILS_METAFIELDS_CAROUSEL = makeProductQueryBlock(
  PRODUCT_VARIANT_METAFIELDS,
  PRODUCT_METAFIELDS_BLOCK,
  true,
);

export const GET_PRODUCT = gql`
  query GetProduct($productId: ID $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode)  {
    product(id: $productId) {
      ${PRODUCT_DETAILS}
    }
  }
`;

export const GET_PRODUCT_BY_IDS = gql`
  query GetProduct($productIds: [ID!]!, $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode)  {
    nodes(ids: $productIds) {
      ... on Product {
        ${PRODUCT_DETAILS_COMPRESSED}
      }
    }
  }
`;

export const GET_PRODUCT_BY_HANDLE = gql`
  query GetProduct($productHandle: String!, $productMetafields: [HasMetafieldsIdentifier!]!, $variantMetafields: [HasMetafieldsIdentifier!]!, $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode)  {
    productByHandle(handle: $productHandle) {
      ${PRODUCT_DETAILS_METAFIELDS}
    }
  }
`;

export const GET_PRODUCT_VARIATIONS_WITH_OPTIONS = gql`
  query GetProduct($productId: ID, $selectedOptions: [SelectedOptionInput!]!) {
    product(id: $productId) {
      id
      variantBySelectedOptions(selectedOptions: $selectedOptions) {
        ${PRODUCT_VARIANT}
      }
    }
  }
`;

export const GET_PRODUCT_RECOMENDATIONS = gql`
  query GetProductRecommendations(
    $productId: ID!,  
    $productMetafields: [HasMetafieldsIdentifier!]!,
    $variantMetafields: [HasMetafieldsIdentifier!]!,
    $countryCode: CountryCode,
    $languageCode: LanguageCode,
    $intent: ProductRecommendationIntent!
    ) @inContext(country: $countryCode, language: $languageCode) {
    productRecommendations(productId: $productId, intent: $intent) {
      ${PRODUCT_DETAILS_METAFIELDS}
    }
  }
`;

export const GET_PRODUCT_METAFIELDS = gql`
  query GetProduct($productId: ID, $identifiers: [HasMetafieldsIdentifier!]!) {
    product(id: $productId) {
      id
      metafields(identifiers: $identifiers) {
        id
        key
        value
        namespace
        description
        type
      }
    }
  }
`;

export const GET_PRODUCT_METAFIELDS_BY_HANDLE = gql`
  query GetProduct($productHandle: String!, $identifiers: [HasMetafieldsIdentifier!]!) {
    productByHandle(handle: $productHandle) {
      id
      metafields(identifiers: $identifiers) {
        id
        key
        value
        namespace
        description
        type
      }
    }
  }
`;

// export const GET_META_OBJECT_IN_PRODUCT_METAFIELDS = gql`
// reference {
//   ... on Metaobject {
//       handle
//       id
//       onlineStoreUrl
//       type
//       updatedAt
//       fields {
//           key
//           type
//           value
//       }
//   }
// }`;

export const GET_VARIANT_BY_IDS = gql`
  query GetVariants($variantIds: [ID!]!) {
    nodes(ids: $variantIds) {
      ... on ProductVariant {
        ${PRODUCT_VARIANT}
        product{
          id
          handle
          title
          description
          descriptionHtml
          availableForSale
          priceRange {
            maxVariantPrice {
              amount
              currencyCode
            }
            minVariantPrice {
              amount
              currencyCode
            }
          }
          compareAtPriceRange {
            minVariantPrice {
              amount
              currencyCode
            }
            maxVariantPrice {
              amount
              currencyCode
            }
          }
          productType
          images(first: 10) {
            edges {
              node {
                ${PRODUCT_IMAGE}
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_PRODUCT_VARIANT_METAFIELDS_BY_HANDLE = gql`
  query GetProductVariantMetafieldByHandle($productHandle: String!, $identifiers: [HasMetafieldsIdentifier!]!) {
    productByHandle(handle: $productHandle) {
      id
      variants(first: 100) {
        edges {
          node {
            ${PRODUCT_VARIANT}
            metafields(identifiers: $identifiers) {
              id
              key
              value
              namespace
              description
              type
            }
          }
        }
      }
    }
  }
`;

export const GET_MEDIA_DATA_FROM_MEDIAID = gql`
  query GetMediaDataFromMediaId($mediaGIDs: [ID!]!) {
    nodes(ids: $mediaGIDs) {
      ... on MediaImage {
        id
        mediaContentType
        alt
        image {
          src
          originalSrc
          id
          height
          altText
          url
          width
        }
      }
    }
  }
`;

export const GET_PRODUCT_METAFIELDS_BY_HANDLE_V2 = gql`
  query GetProduct($productHandle: String!, $identifiers: [HasMetafieldsIdentifier!]!) {
    productByHandle(handle: $productHandle) {
      id
      metafields(identifiers: $identifiers) {
        id
        description
        namespace
        reference {
          ... on Metaobject {
            handle
            id
            type
            fields {
              key
              reference {
                ... on MediaImage {
                  alt
                  id
                  image {
                    altText
                    id
                    url
                  }
                }
              }
              type
              value
            }
          }
        }
        type
        value
        key
        references(first: 10) {
          edges {
            cursor
            node {
              ... on Metaobject {
                handle
                id
                type
                fields {
                  key
                  reference {
                    ... on MediaImage {
                      image {
                        altText
                        id
                        url
                      }
                    }
                  }
                  type
                  value
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const EXTENDED_SEARCH_PRODUCTS_FOR_DROPDOWN = gql`
  query SearchProducts(
    $query: String!
    $sortKey: ProductSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
  ) @inContext(country: $countryCode) {
    products(query: $query, first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
      edges {
        cursor
        node {
          ${PRODUCT_DETAILS}
        }
      }
    }
  }
`;
