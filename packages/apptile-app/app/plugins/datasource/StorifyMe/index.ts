import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IStorifyMeCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';

export type StorifyMePluginConfigType = DatasourcePluginConfig & IStorifyMeCredentials;

export const storifyMeApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'storifyMe',
  type: 'datasource',
  name: 'StorifyMe Integration',
  description: 'StorifyMe Widgets integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const storifyMeEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'accountId',
      props: {
        label: 'Account Id',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'API Key',
      },
    },
    {
      type: 'codeInput',
      name: 'env',
      props: {
        label: 'Env (EU/US)',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'storifyMe',
  config: {
    ...baseDatasourceConfig,
    accountId: '',
    apiKey: '',
    env: '',
  } as WishlistPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<StorifyMePluginConfigType>, dsModelValues: any) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return storifyMeApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = storifyMeApiRecords && storifyMeApiRecords[queryName] ? storifyMeApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IStorifyMeCredentials): Partial<WishlistPluginConfigType> | boolean {
    const {accountId, apiKey, env} = credentials;
    if (!accountId || !apiKey || !env) return false;
    return {
      accountId,
      apiKey,
      env,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['accountId', 'apiKey', 'env'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'storifyMe';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: storifyMeEditors,
});
