import _ from 'lodash';
import {ActionHandler, modelUpdateAction} from 'apptile-core';
import {processShopifyGraphqlQueryResponse} from '../../utils';
import {gql} from '@apollo/client';

export interface ISwatchKingActions {
  fetchProductsImagesWithIds: ActionHandler;
}

class SwatchKingActions implements ISwatchKingActions {
  fetchProductsImagesWithIds: ActionHandler = async (
    dispatch,
    config,
    model,
    selector,
    params,
    appConfig,
    appModel,
  ) => {
    const {productIds, groupId} = params;
    const productGroupCache = model?.get('productGroupCache') || {};

    const gqlTag = this.generateQuery(productIds);

    try {
      const response = await this.queryExecutor(appConfig, appModel, model, {gqlTag}, {});

      const groupCache = {
        groupId: groupId,
        productIds: productIds,
        images: Object.values(response.transformedData)
          .filter(d => !!d)
          .map((d: any) => ({
            id: d.id,
            handle: d.handle,
            image: d.variants?.nodes?.[0]?.image,
            featuredImage: d.featuredImage,
          })),
      };

      productGroupCache[groupId] = groupCache;

      const newModelUpdates = [
        {
          selector: selector.concat(['productGroupCache']),
          newValue: productGroupCache,
        },
      ];

      dispatch(modelUpdateAction(newModelUpdates, undefined, true));
    } catch (error) {
      console.error('Error fetching product images:', error);
    }
  };

  private queryExecutor = async (
    appConfig: any,
    appModel: any,
    pluginModel: any,
    querySchema: any,
    inputVariables: any,
  ) => {
    const shopifyDsModel = appModel?.getModelValue(['shopify']);
    const queryRunner = shopifyDsModel?.get('queryRunner');
    const shopConfig = shopifyDsModel?.get('shop');

    if (!queryRunner) {
      throw new Error('Query runner not available.');
    }

    const response = await queryRunner.runQuery('query', querySchema.gqlTag, {}, {});
    return processShopifyGraphqlQueryResponse(response, querySchema, shopConfig, pluginModel);
  };

  private generateQuery = (productIds: string[]) => {
    const variantImageBlock = `{
      id
      handle
      featuredImage {
        altText
        height
        id
        originalSrc
        src
        transformedSrc
        url (transform: {maxWidth: 480, maxHeight: 480})
        width
      }
      variants(first: 1) {
        nodes {
          image {
            altText
            height
            id
            originalSrc
            src
            transformedSrc
            url (transform: {maxWidth: 480, maxHeight: 480})
            width
          }
        }
      }
    }`;

    const queries = productIds
      .map(
        (productId, index) => `product${index}: product(id: "gid://shopify/Product/${productId}") ${variantImageBlock}`,
      )
      .join('\n');

    return gql`query Product {
      ${queries}
    }`;
  };
}

const swatchKingActions = new SwatchKingActions();
export default swatchKingActions;
