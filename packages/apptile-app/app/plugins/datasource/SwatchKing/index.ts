import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginPropertySettings,
  RootState,
  TriggerActionIdentifier,
} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {baseDatasourceConfig} from '../base';
import {IntegrationPlatformType} from '../datasourceTypes';
import swatchKingActions from './actions';

const propertySettings: PluginPropertySettings = {
  fetchProductsImagesWithIds: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return swatchKingActions.fetchProductsImagesWithIds;
    },
    actionMetadata: {
      editableInputParams: {
        productIds: '{{[]}}',
        groupId: '',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'swatchKing',
  type: 'datasource',
  name: '<PERSON><PERSON> King',
  description: 'Swatch King Description',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const swatchKingEditors = {
  basic: [],
};

export default wrapDatasourceModel({
  name: 'swatchKing',

  config: {
    ...baseDatasourceConfig,
    fetchProductsImagesWithIds: 'action',
    productGroupCache: '',
  },

  resolveCredentialConfigs: function (): Partial<any> | boolean {
    return {};
  },

  getQueries: function (): Record<string, any> {
    return {};
  },

  getQueryInputParams: function () {
    return {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'swatchKing';
  },

  initDatasource: function* (dsModel: any, dsConfig: any, dsModelValues: any) {},

  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {},

  options: {
    propertySettings,
    pluginListing,
  },

  editors: swatchKingEditors,
});
