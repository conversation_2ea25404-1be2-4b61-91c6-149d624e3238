import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from '../../../common/datatypes/types';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import { PluginPropertySettings, TriggerActionIdentifier } from 'apptile-core';
import { modelUpdateAction } from 'apptile-core';
import axios from 'axios';

export type TokiRewardsConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  loading: string;
  referralLink: string;
  getReferralLink: string;
};

type TokiRewardsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

const TokiRewardsApiRecords: Record<string, TokiRewardsQueryDetails> = {
  getUserAuthToken: {
    queryType: 'get',
    endpoint: '/users',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}/${inputParams?.userId}/auth-token`;
    },
    editableInputParams: {
      userId: '',
    },
    isPaginated: false,
  },
  getUserByEmail: {
    queryType: 'get',
    endpoint: '/users',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?email=${inputParams.email}`;
    },
    editableInputParams: {
      email: '',
    },
    isPaginated: false,
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'tokiRewards',
  type: 'datasource',
  name: 'Toki Rewards',
  description: 'Toki Rewards',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const TokiRewardsEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiToken',
      props: {
        label: 'apiToken',
        placeholder: '',
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  getReferralLink: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return async function (dispatch, config, model, selector, params) {
        const { shop_domain, mail, address } = params
        const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        if(!regex.test(mail)) {
          toast.show("Invalid email address", {
            type: 'error',
            placement: 'bottom',
          })
          return;
        }
        try {
          dispatch(modelUpdateAction([{ selector: selector.concat(['loading']), newValue: true }], undefined, true));
          const response = await axios.get(`https://api.buildwithtoki.com/integrations/shopify/referAFriend?shop_domain=${shop_domain}&mail=${mail}&address=${address}`)
          dispatch(modelUpdateAction([{ selector: selector.concat(['referralLink']), newValue: response.data.link }], undefined, true));
        } catch (error) { 
          console.error(error);
          toast.show("Cannot generate referral link", {
            type: 'error',
            placement: 'bottom',
          })
        } finally {
          dispatch(modelUpdateAction([{ selector: selector.concat(['loading']), newValue: false }], undefined, true));
        }
      }
    },
    actionMetadata: {
      editableInputParams: {
        shop_domain: '',
        mail: '',
        address: ''
      }
    }
  }
}


const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<TokiRewardsConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, TokiRewardsConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');
    const apiToken = dsModelValues.get('apiToken');
    if (!apiToken) {
      throw new Error('Missing apiToken');
    }
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            Authorization: `Bearer ${apiToken}`,
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'tokiRewards',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.buildwithtoki.com/external',
    queryRunner: 'queryrunner',
    loading: 'false',
    referralLink: '',
    getReferralLink: 'action'
  } as TokiRewardsConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<TokiRewardsConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return TokiRewardsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      TokiRewardsApiRecords && TokiRewardsApiRecords[queryName] ? TokiRewardsApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'tokiRewards';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = TokiRewardsApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: TokiRewardsEditors,
});
