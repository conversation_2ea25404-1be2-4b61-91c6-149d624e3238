import { AppPageTriggerOptions, PluginConfigType, PluginListingSettings, PluginPropertySettings, TriggerActionIdentifier, modelUpdateAction } from 'apptile-core';
import { DatasourceQueryDetail, DatasourceQueryReturnValue } from '../../query/';
import { baseDatasourceConfig } from '../base';
import { DatasourcePluginConfig, IntegrationPlatformType } from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';


export type TryWithMirraPluginConfigType = DatasourcePluginConfig & {
  limit: string;
  shopifyDS: string;
  syncing: string;
  addToCart: string;
}

export const tryWithMirraApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  addToCart: {
    type: TriggerActionIdentifier,
    getValue: (model, renderedValue, selector) => {
      return async function (dispatch, config, model, selector, params, appConfig, appModel) {
        const shopifyDS = model?.get('shopifyDS')
        const shopifyPluginConfig = appConfig.getPlugin('shopify')
        const shopifyDSModel = appModel?.getModelValue(['shopify'])
        const shopifyIncreaseCartLineItemQuantityAction = shopifyDS['increaseCartLineItemQuantity']
        
        try {
          dispatch(modelUpdateAction([{ selector: selector.concat(['syncing']), newValue: true }], undefined, true));
          await shopifyIncreaseCartLineItemQuantityAction(dispatch, shopifyPluginConfig, shopifyDSModel, ['shopify'], {...params})
        } catch (error) {
          console.log("Cannot add item to cart", error)
        } finally {
          dispatch(modelUpdateAction([{ selector: selector.concat(['syncing']), newValue: false }], undefined, true));
        }
      }
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        sellingPlanId: '',
        attributes: '',
        itemPrice: '',
        successToastText: 'Item added successfully!',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'tryWithMirra',
  type: 'datasource',
  name: 'TryWithMirra',
  description: 'TryWithMirra Widgets integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const tryWithMirraEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'limit',
      props: {
        label: 'Limit',
        placeholder: '0',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'tryWithMirra',
  config: {
    ...baseDatasourceConfig,
    limit: '',
    shopifyDS: '{{shopify}}',
    syncing: 'false',
    addToCart: 'action'
  } as TryWithMirraPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<TryWithMirraPluginConfigType>, dsModelValues: any) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return tryWithMirraApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = tryWithMirraApiRecords && tryWithMirraApiRecords[queryName] ? tryWithMirraApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'tryWithMirra';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: tryWithMirraEditors,
});
