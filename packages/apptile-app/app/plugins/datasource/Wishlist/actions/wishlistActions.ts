import {ActionHandler} from '../../../triggerAction';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {executeQuery, wishlistApiRecords} from '../index';
import _, {get} from 'lodash';
import {IProduct, IWishlistProduct, IProductFromWishlistProduct} from '../types/index';
import {GetRegisteredPlugin} from 'apptile-core';
import {call} from 'redux-saga/effects';

export interface WishistActionInterface {
  getAllWishlistProducts: ActionHandler;
  addProductToWishlist: ActionHandler;
  removeProductFromWishlist: ActionHandler;
}

export interface wishlistProductsPayload {
  customerAccessToken: string;
}
export interface wishlistAddProductsPayload {
  customerEmail: string;
  customerId: number;
  productId: number;
  productHandle: string;
  productObj?: IProduct;
}

async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = wishlistApiRecords[queryName];
  const queryDetails = wishlistApiRecords[queryName];
  if (!queryDetails) return;

  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}

class WishistActions implements WishistActionInterface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    key: string,
  ) {
    const modelUpdates = [];
    const lineItemsSelector = selector.concat([key]);
    modelUpdates.push({
      selector: lineItemsSelector,
      newValue: lineItems,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  private transformWishlistProducts = (products: IProductFromWishlistProduct[]) => {
    const transformedProducts = products.map(product => {
      return {
        handle: product.product_handle,
        id: product.product_id,
        image: product.product_image,
        title: product.product_title,
      };
    });
    return transformedProducts as IWishlistProduct[];
  };

  getAllWishlistProducts = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as wishlistProductsPayload;
    const result = await runQuery(model, config, 'viewWishlist', payload);
    const wishlistProducts = get(result, ['data', 'data'], []);
    this.applyChanges(dispatch, config, model, selector, this.transformWishlistProducts(wishlistProducts), 'products');
  };

  addProductToWishlist = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as wishlistAddProductsPayload;
    const {productObj} = payload;
    const wishlistProducts = model.get('products');
    wishlistProducts.push(productObj);
    await this.applyChanges(dispatch, config, model, selector, wishlistProducts, 'products');
    await runQuery(model, config, 'addToWishList', payload);
  };

  removeProductFromWishlist = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params as wishlistAddProductsPayload;
    const {productId} = payload;
    const wishlistProducts = model.get('products');
    const filteredWishlist = wishlistProducts.filter((products: IWishlistProduct) => products.id !== productId);
    await this.applyChanges(dispatch, config, model, selector, filteredWishlist, 'products');
    await runQuery(model, config, 'removeFromWishlist', payload);
  };
}

const wishistActions = new WishistActions();
export default wishistActions;
