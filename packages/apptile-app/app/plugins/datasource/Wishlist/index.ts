import {modelUpdateAction} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import axios from 'axios';
import {store} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IFlitsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import wrapDatasourceModel from '../wrapDatasourceModel';
import wishistActions from './actions/wishlistActions';
import {IProductFromWishlistProduct, IWishlistProduct} from './types/index';
import {get} from 'lodash';

export interface WishlistPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  userId: string;
  appId: string;
  products: IWishlistProduct[] | null;
  getAllWishlistProducts: any;
  addProductToWishlist: any;
  removeProductFromWishlist: any;
  customerAccessToken: string;
}

type IEditableParams = Record<string, any>;

type WishlistQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  endpointResolver: (inputParams: any) => string;
  editableInputParams: IEditableParams;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<WishlistPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, WishlistPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams as {[key: string]: any}).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const wishlistApiRecords: Record<string, WishlistQueryDetails> = {
  addToWishList: {
    queryType: 'post',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist/add_to_wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      productId: '',
      productHandle: '',
      productObj: {},
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    inputResolver: (inputVariables: any) => {
      const {productId, productHandle} = inputVariables;
      return {
        product_id: productId,
        product_handle: productHandle,
        wsl_product_count: 1,
      };
    },
  },
  removeFromWishlist: {
    queryType: 'delete',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist/remove_from_wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      productId: '',
      productHandle: '',
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    inputResolver: (inputVariables: any) => {
      const {productId, productHandle} = inputVariables;
      return {
        product_id: productId,
        product_handle: productHandle,
        wsl_product_count: 1,
      };
    },
  },
  viewWishlist: {
    queryType: 'get',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    inputResolver: (inputVariables: any) => {
      const {customerEmail} = inputVariables;
      return {
        customer_email: customerEmail,
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {
  getAllWishlistProducts: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.getAllWishlistProducts;
    },
    actionMetadata: {
      editableInputParams: {customerAccessToken: ''},
    },
  },
  addProductToWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.addProductToWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', productObj: {}, customerAccessToken: ''},
    },
  },
  removeProductFromWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.removeProductFromWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', customerAccessToken: ''},
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'flits',
  type: 'datasource',
  name: 'Flits Wishlist Integration',
  description: 'Custom wishlist integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const wishlistEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'https://app.getflits.com/api',
      props: {
        label: 'API Base url',
        placeholder: 'https://services.mybcapps.com',
      },
    },
    {
      type: 'codeInput',
      name: 'userId',
      defultValue: '',
      props: {
        label: 'User Id',
        placeholder: 'User Id provided by Flits',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      defultValue: '',
      props: {
        label: 'App ID',
        placeholder: 'Apptile app id',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: WishlistQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    let {endpointResolver, contextInputParams, editableInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    let endpoint = endpointResolver && endpointResolver(contextInputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables},
      {
        headers: {
          'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken ?? '',
        },
      },
    );
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

const transformWishlistProducts = (products: IProductFromWishlistProduct[]) => {
  const transformedProducts = products.map(product => {
    return {
      handle: product.product_handle,
      id: product.product_id,
      image: product.product_image,
      title: product.product_title,
    };
  });
  return transformedProducts as IWishlistProduct[];
};

export default wrapDatasourceModel({
  name: 'wishlist',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://app.getflits.com/api',
    appId: '',
    userId: '',
    products: null,
    getAllWishlistProducts: 'action',
    addProductToWishlist: 'action',
    removeProductFromWishlist: 'action',
    customerAccessToken: '{{customerAccessToken.value.accessToken}}',
  } as WishlistPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'X-Shopify-App-Id': dsConfig.config?.get('appId') ?? '',
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'userId'],
          newValue: dsConfig.config.get('userId'),
        },
        {
          selector: [dsConfig.get('id'), 'appId'],
          newValue: dsConfig.config.get('appId'),
        },
        {
          selector: [dsConfig.get('id'), 'products'],
          newValue: dsConfig.config.get('products'),
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return wishlistApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = wishlistApiRecords && wishlistApiRecords[queryName] ? wishlistApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IFlitsCredentials): Partial<WishlistPluginConfigType> | boolean {
    const {proxyUrl, userId, appId} = credentials;
    if (!proxyUrl || !userId || !appId) return false;
    return {
      apiBaseUrl: proxyUrl,
      appId: appId,
      userId: userId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'appId', 'userId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'flits';
  },

  onModelUpdate: function* (model: RootState, dsConfig: string, dsModelValues: string) {
    const dispatch = store.dispatch;

    const userId = dsModelValues.get('userId');
    const appId = dsModelValues.get('appId');
    const apiBaseUrl = dsModelValues.get('apiBaseUrl');
    const products = dsModelValues.get('products');

    const customerAccessToken = dsModelValues.get('customerAccessToken');

    if (customerAccessToken && products === null) {
      axios
        .get(`${apiBaseUrl}/api/1/${userId}/wishlist`, {
          headers: {
            'x-shopify-customer-access-token': customerAccessToken ?? '',
            'X-Shopify-App-Id': appId ?? '',
            'Content-Type': 'application/json',
          },
        })
        .then(response => {
          const modelUpdates = [];
          const wishlistProducts = get(response, ['data', 'data'], []);
          modelUpdates.push({
            selector: [dsConfig.get('id'), 'products'],
            newValue: transformWishlistProducts(wishlistProducts),
          });
          dispatch(modelUpdateAction(modelUpdates, undefined, true));
        });
    }
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = wishlistApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: wishlistEditors,
});
