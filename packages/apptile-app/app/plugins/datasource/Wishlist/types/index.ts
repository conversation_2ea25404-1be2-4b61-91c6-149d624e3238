export interface IProduct {
  customerEmail: string;
  customerId: number;
  productId: number;
  productHandle: string;
}

export interface IProductFromWishlistProduct {
  created_at: string;
  customer_email: string;
  customer_id: number;
  customers_count: number;
  product_handle: string;
  id: number;
  product_id: number;
  product_image: string;
  updated_at: string;
  product_title: string;
}

export interface IWishlistProduct {
  handle: string;
  id: number;
  image: string;
  title: string;
}
