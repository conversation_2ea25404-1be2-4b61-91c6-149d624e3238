import {modelUpdateAction, navigateToScreen} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';

import _ from 'lodash';
import {Selector} from 'apptile-core';

import {PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {executeQuery} from '../index';
import {IWishlistPlusLoginDsPluginConfigType} from '../types';

export const wishlistPlusActionDsPluginConfig: IWishlistPlusLoginDsPluginConfigType = {
  generateRegid: TriggerActionIdentifier,
  addProductToList: TriggerActionIdentifier,
  removeProductFromList: TriggerActionIdentifier,
  refreshWishlist: TriggerActionIdentifier,
};

export const simplyOTPLoginActionDatasourceEditor: PluginEditorsConfig<any> = {};

// When you select action in event, it shows the fields
export const wishlistPlusActionDsPropertySettings: PluginPropertySettings = {
  generateRegid: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishlistPlus.generateRegid;
    },
    actionMetadata: {
      editableInputParams: {
        useragenttype: 'mobile',
        useremail: '{{shopify.loggedInUser.email}}',
        uuid: '',
      },
    },
  },
  refreshWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishlistPlus.refreshWishlist;
    },
    actionMetadata: {
      editableInputParams: {
        regid: '',
        sessionid: '',
      },
    },
  },
  addProductToList: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishlistPlus.addProductToList;
    },
    actionMetadata: {
      editableInputParams: {
        regid: '',
        sessionid: '',
        lid: '',
        productId: '',
        variantId: '',
        uriOfProduct: '',
      },
    },
  },
  removeProductFromList: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishlistPlus.removeProductFromList;
    },
    actionMetadata: {
      editableInputParams: {
        regid: '',
        sessionid: '',
        lid: '',
        productId: '',
        variantId: '',
        uriOfProduct: '',
      },
    },
  },
};

class WishlistPlus {
  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];

    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }

  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('wishlistPlus');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  generateRegid = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {data: generateRegidResponse, errors} = await this.queryExecutor(model, config, 'generateRegid', {
      useragenttype: 'mobile',
      useremail: params.useremail,
      uuid: params.uuid,
    });

    const modelUpdates = [
      {
        selector: selector.concat(['sessionid']),
        newValue: generateRegidResponse.sessionid,
      },
      {
        selector: selector.concat(['regid']),
        newValue: generateRegidResponse.regid,
      },
    ];
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
  refreshWishlist = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {data: refreshWishlistResponse, errors} = await this.queryExecutor(
      model,
      config,
      'fetchAllListsWithContent',
      {
        regid: params.regid,
        sessionid: params.sessionid,
      },
    );

    console.log('refreshWishlistResponse', refreshWishlistResponse);
    const result = [];

    refreshWishlistResponse.forEach(wishlist => {
      wishlist.items.forEach(item => {
        // Create an object with productId and variantId
        const obj = {
          productId: item.productId,
          variantId: item.variantId,
        };

        // Push the object into the result array
        result.push(obj);
      });
    });

    const modelUpdates = [
      {
        selector: selector.concat(['wishlistDetails']),
        newValue: refreshWishlistResponse,
      },
      {
        selector: selector.concat(['combinedItemsInAllList']),
        newValue: result,
      },
    ];
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  addProductToList = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {data: addProductToListResponse, errors} = await this.queryExecutor(model, config, 'addProductToList', {
      regid: params.regid,
      sessionid: params.sessionid,
      lid: params.lid,
      productId: params.productId,
      variantId: params.variantId,
      uriOfProduct: params.uriOfProduct,
    });
  };
  removeProductFromList = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {data: removeProductFromWishlistResponse, errors} = await this.queryExecutor(
      model,
      config,
      'removeProductFromList',
      {
        regid: params.regid,
        sessionid: params.sessionid,
        lid: params.lid,
        productId: params.productId,
        variantId: params.variantId,
        uriOfProduct: params.uriOfProduct,
      },
    );
  };
}

const wishlistPlus = new WishlistPlus();
export default wishlistPlus;
