import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IWishlistPlusCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {wishlistPlusActionDsPluginConfig, wishlistPlusActionDsPropertySettings} from './actions/wishlistPlusActions';
import QueryString from 'qs';
import {Buffer} from 'buffer';

import {TransformFetchAllListsWithContent} from './transformers';

export type WishlistPlusConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  apiEndpoint: string;
  apiKey: string;
  pid: string;
};

type WishlistPlusQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  headersResolver?: (inputVariables: any) => any;
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any} | undefined,
) => {
  if (!inputVariables) return inputVariables;

  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (typeof editableInputParams[key] === 'number') {
      return {
        ...acc,
        [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
      };
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
    return acc;
  }, {});
};

const wishlistPlusApiRecords: Record<string, WishlistPlusQueryDetails> = {
  generateRegid: {
    queryType: 'post',
    endpoint: '/storeadmin/v3/user/generate-regid',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      useragenttype: '',
      useremail: '',
      uuid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        useragenttype: inputVariables.useragenttype,
        useremail: inputVariables.useremail,
        uuid: inputVariables.uuid,
      });
    },
    isPaginated: false,
  },
  fetchAllListsWithContent: {
    queryType: 'post',
    endpoint: '/api/v3/lists/fetch-lists?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      regid: '',
      sessionid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
      });
    },
    isPaginated: false,
    transformer: TransformFetchAllListsWithContent,
  },
  addProductToList: {
    queryType: 'post',
    endpoint: '/api/v3/lists/update-ctx?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      regid: '',
      sessionid: '',
      lid: '',
      productId: '',
      variantId: '',
      uriOfProduct: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
        lid: inputVariables.lid,
        a: JSON.stringify([
          {empi: inputVariables.productId, epi: inputVariables.variantId, du: inputVariables.uriOfProduct},
        ]),
      });
    },
    isPaginated: false,
  },
  removeProductFromList: {
    queryType: 'post',
    endpoint: '/api/v3/lists/update-ctx?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      regid: '',
      sessionid: '',
      lid: '',
      productId: '',
      variantId: '',
      uriOfProduct: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
        lid: inputVariables.lid,
        d: JSON.stringify([
          {empi: inputVariables.productId, epi: inputVariables.variantId, du: inputVariables.uriOfProduct},
        ]),
      });
    },
    isPaginated: false,
  },
  fetchContentsOfSpecificList: {
    queryType: 'post',
    endpoint: '/api/v3/lists/fetch-list-with-contents?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      lid: '',
      regid: '',
      sessionid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        lid: inputVariables.lid,
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
      });
    },
    isPaginated: false,
  },
  createList: {
    queryType: 'post',
    endpoint: '/api/v3/lists/create?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      listName: '',
      regid: '',
      sessionid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        lname: inputVariables.listName,
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
      });
    },

    headersResolver(inputVariables) {
      return {
        'user-agent': 'mobileApp',
      };
    },
    isPaginated: false,
  },
  makeListPublic: {
    queryType: 'post',
    endpoint: '/api/v3/lists/markPublic',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      lid: '',
      regid: '',
      sessionid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        lid: inputVariables.lid,
        regid: inputVariables.regid,
        sessionid: inputVariables.useremail,
      });
    },
    isPaginated: false,
  },
  shareListViaEmail: {
    queryType: 'post',
    endpoint: '/api/v3/lists/emailList?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      lid: '',
      regid: '',
      sessionid: '',
      fromname: '',
      toemail: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        lid: inputVariables.lid,
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
        fromname: inputVariables.fromname,
        toemail: inputVariables.toemail,
      });
    },
    isPaginated: false,
  },
  deleteList: {
    queryType: 'post',
    endpoint: '/api/v3/lists/delete-list?pid={urlEncodedPid}',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      lid: '',
      regid: '',
      sessionid: '',
    },
    inputResolver: inputVariables => {
      return QueryString.stringify({
        lid: inputVariables.lid,
        regid: inputVariables.regid,
        sessionid: inputVariables.sessionid,
      });
    },

    headersResolver(inputVariables) {
      return {
        'user-agent': 'mobileApp',
      };
    },
    isPaginated: false,
  },
};

const propertySettings: PluginPropertySettings = {
  ...wishlistPlusActionDsPropertySettings,
  hideFilledWishlistIcon: {
    getValue(model, renderedValue, selector) {
      return (carouselProductId, carouselVariantId, items) => {
        for (let i = 0; i < items.length; i++) {
          const productId = items[i].productId;
          const variantId = items[i].variantId;

          if (productId == carouselProductId && variantId == carouselVariantId) {
            return 0;
          }
        }
        return 1;
      };
    },
  },
  hideUnfilledWishlistIcon: {
    getValue(model, renderedValue, selector) {
      return (carouselProductId, carouselVariantId, items) => {
        for (let i = 0; i < items.length; i++) {
          const productId = items[i].productId;
          const variantId = items[i].variantId;

          if (productId == carouselProductId && variantId == carouselVariantId) {
            return 1;
          }
        }
        return 0;
      };
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'wishlistPlus',
  type: 'datasource',
  name: 'Wishlist Plus',
  description: 'Wishlist Plus',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const WishlistPlusEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiEndpoint',
      value: 'https://swymstore-v3premium-01.swymrelay.com',
      props: {
        label: 'API Url',
        placeholder: 'https://swymstore-v3premium-01.swymrelay.com',
        value: 'https://swymstore-v3premium-01.swymrelay.com',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'apiKey',
        placeholder: 'xxxxxxxx',
      },
    },
    {
      type: 'codeInput',
      name: 'pid',
      props: {
        label: 'pid',
        placeholder: 'xxxxxxx',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let {endpointResolver, headersResolver, editableInputParams, endpoint} = queryDetails ?? {};

    endpoint = endpointResolver && endpointResolver(endpoint, inputVariables);

    const pid = dsConfig.config?.get('pid'); // Use appId from config
    const encodedPid = encodeURIComponent(pid); // URL-encode the pid

    endpoint = endpoint.replace('{urlEncodedPid}', encodedPid);

    const headers = headersResolver && headersResolver(inputVariables);

    const queryRunner = dsModelValues.get('queryRunner');

    const typedInputs = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    const typedInputVariables = queryDetails.inputResolver ? queryDetails.inputResolver(typedInputs) : typedInputs;

    const queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, typedInputVariables, {
      headers: headers,
    });
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = queryDetails.transformer ? queryDetails.transformer(rawData) : rawData;
    return {rawData, data: transformedData, hasNextPage: false, paginationMeta: null};
  } catch (error) {
    console.error(error);
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: error?.response?.data?.message,
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'wishlistPlus',
  config: {
    ...baseDatasourceConfig,
    queryRunner: 'queryrunner',
    apiEndpoint: 'https://swymstore-v3premium-01.swymrelay.com',
    apiKey: '',
    pid: '',
    regid: '',
    sessionid: '',
    wishlistDetails: '',
    combinedItemsInAllList: '',
    hideFilledWishlistIcon: 'function',
    hideUnfilledWishlistIcon: 'function',
    ...wishlistPlusActionDsPluginConfig,
  } as WishlistPlusConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<WishlistPlusConfigType>, dsModelValues: any) => {
    const queryRunner = AjaxQueryRunner();
    let apiEndpoint = dsModelValues.get('apiEndpoint'); // Get apiBaseUrl from config
    if (apiEndpoint.endsWith('/')) {
      apiEndpoint = apiEndpoint.slice(0, -1);
    }

    const apiKey = dsConfig.config?.get('apiKey'); // Use appId from config
    const pid = dsConfig.config?.get('pid'); // Use appId from config
    const encodedPid = encodeURIComponent(pid);

    apiEndpoint = apiEndpoint.replace('{urlEncodedPid}', encodedPid);

    const combination = `${pid}:${apiKey}`;
    const encodedAuthToken = Buffer.from(combination).toString('base64');

    const authToken = `Basic ${encodedAuthToken}`;

    queryRunner.initClient(apiEndpoint, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: authToken,
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return wishlistPlusApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      wishlistPlusApiRecords && wishlistPlusApiRecords[queryName] ? wishlistPlusApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IWishlistPlusCredentials,
  ): Partial<WishlistPlusConfigType> | boolean {
    const {apiEndpoint, apiKey, pid} = credentials;
    if (!apiEndpoint || !apiKey || !pid) return false;
    return {
      apiEndpoint: apiEndpoint,
      apiKey: apiKey,
      pid: pid,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiEndpoint', 'apiKey', 'pid'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'wishlistPlus';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = wishlistPlusApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: WishlistPlusEditors,
});
