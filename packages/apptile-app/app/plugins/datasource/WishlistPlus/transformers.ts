import _ from 'lodash';

export const TransformFetchAllListsWithContent: TransformerFunction<any, any> = data => {
  console.log('TransformFetchAllListsWithContent ran succcessfully');
  return data.map(wishlist => ({
    lid: wishlist.lid,
    wishlistName: wishlist.lname,
    createdBy: wishlist.cby,
    createdAt: wishlist.cts,
    updatedAt: wishlist.uts,
    itemCount: wishlist.cnt,
    items: wishlist.listcontents.map(item => {
      const handleMatch = item.du.match(/products\/([^?]+)(?:\?|$)/);
      const handle = handleMatch ? handleMatch[1] : null;

      return {
        itemId: item.id,
        productTitle: item.dt,
        productHandle: handle,
        brand: item.bt,
        price: item.pr,
        variant: item.vi,
        sku: item.sku,
        productUrl: item.du,
        imageUrl: item.iu,
        handle, // Add the extracted handle here
        createdAt: item.cts,
        updatedBy: item.uby,
        productId: item.empi,
        variantId: item.epi,
      };
    }),
    userInfo: {
      firstName: wishlist.userinfo?.fname,
      lastName: wishlist.userinfo?.lname,
      email: wishlist.userinfo?.em,
      ordersCount: wishlist.userinfo?.orders_count,
      totalSpent: wishlist.userinfo?.total_spent,
    },
  }));
};
