import {modelUpdateAction} from 'apptile-core';
import {executeQuery} from '..';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';

//#region Interfaces

export interface IWizzySearchActionsDatasourcePluginConfig {
  getSuggestions: string;
  clearSuggestions: string;
  predictions: any;
}

export interface IWizzySearch {
  getSuggestions: ActionHandler;
  clearSuggestions: ActionHandler;
}

//#endregion

export const wizzySearchDatasourcePluginConfig: IWizzySearchActionsDatasourcePluginConfig = {
  getSuggestions: TriggerActionIdentifier,
  clearSuggestions: TriggerActionIdentifier,
  predictions: {},
};

export const wizzySearchDatasourcePropertySettings: PluginPropertySettings = {
  getSuggestions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wizzySearchActions.getSuggestions;
    },
    actionMetadata: {
      editableInputParams: {
        q: '',
        currency: '',
        suggestionsCount: '',
        includeOutOfStock: '',
        getAllVariants: '',
        minQueryLength: '',
        productsCount: '',
        sections: '',
        sort: '',
      },
    },
  },
  clearSuggestions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wizzySearchActions.clearSuggestions;
    },
  },
};

class WizzySearchActions implements IWizzySearch {
  //#region Private methods
  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];
    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }
  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('wizzySearch');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }
  //#endregion

  getSuggestions = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const modelUpdates: ModelChange[] = [];

    const {data: predictions} = await this.queryExecutor(model, config, 'getSuggestion', {
      ...params,
    });
    console.log('data', predictions);

    modelUpdates.push({
      selector: selector.concat(['predictions']),
      newValue: predictions,
    });
    return dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  clearSuggestions = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const modelUpdates: ModelChange[] = [];

    modelUpdates.push({
      selector: selector.concat(['predictions']),
      newValue: null,
    });
    return dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const wizzySearchActions = new WizzySearchActions();
export default wizzySearchActions;
