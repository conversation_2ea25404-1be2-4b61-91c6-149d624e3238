import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  IWizzySearchActionsDatasourcePluginConfig,
  wizzySearchDatasourcePluginConfig,
  wizzySearchDatasourcePropertySettings,
} from './actions/wizzysearchAction';
import {
  WizzySearchGetFilterTransformer,
  WizzySearchGetProductsTransformer,
  WizzySearchGetSuggestionsTransformer,
} from './transformer';
export type WizzySearchConfigType = DatasourcePluginConfig &
  IWizzySearchActionsDatasourcePluginConfig & {
    queryRunner: any;
  };

type WizzySearchQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any, hasNextPage?: boolean) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariabes?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

const WizzySearchApiRecords: Record<string, WizzySearchQueryDetails> = {
  getSuggestion: {
    queryType: 'post',
    endpoint: '/autocomplete',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    editableInputParams: {
      q: '',
      currency: '',
      suggestionsCount: '',
      includeOutOfStock: '',
      getAllVariants: '',
      minQueryLength: '',
      productsCount: '',
      sections: '',
      sort: '',
    },
    transformer: WizzySearchGetSuggestionsTransformer,
    isPaginated: false,
    headerType: 'application/x-www-form-urlencoded',
  },
  getFilters: {
    queryType: 'post',
    endpoint: '/products/filter',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    transformer: WizzySearchGetFilterTransformer,
    editableInputParams: {
      filters: '',
      group: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const filters = JSON.parse(inputVariables.filters);
      const input = {...filters, page: paginationMeta + 1};
      return {
        ...inputVariables,
        filters: JSON.stringify(input),
      };
    },
    headerType: 'application/json',
  },
  searchProducts: {
    queryType: 'post',
    endpoint: '/products/search',
    endpointResolver: (endpoint, inputParams, paginationMeta, hasNextPage) => {
      if (hasNextPage) {
        return '/products/filter';
      } else {
        return endpoint;
      }
    },
    transformer: WizzySearchGetProductsTransformer,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const filters = paginationMeta;
      const input = {...filters, page: paginationMeta.page + 1};
      return {
        group: 'page',
        filters: JSON.stringify(input),
      };
    },
    isPaginated: true,
    editableInputParams: {
      q: '',
      currency: '',
      includeOutOfStock: '',
      getAllVariants: '',
      productsCount: '',
      facets: '',
      swatch: '',
      minQueryLength: '',
      sort: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  ...wizzySearchDatasourcePropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'wizzySearch',
  type: 'datasource',
  name: 'Wizzy Search',
  description: 'Wizzy Search AutoComplete',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const WizzySearchEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'x-store-id',
      props: {
        label: 'x-store-id',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'x-api-key',
      props: {
        label: 'x-api-key',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<WizzySearchConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, WizzySearchConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta, getNextPage);
    }

    const wizzyStoreId = dsModelValues.get('x-store-id');
    const wizzyApiKey = dsModelValues.get('x-api-key');

    if (!wizzyStoreId || !wizzyApiKey) throw new Error('StoreId or API Key is missing');

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            'x-store-id': wizzyStoreId,
            'x-api-key': wizzyApiKey,
            'Content-Type': queryDetails?.headerType || 'application/json',
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'wizzySearch',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.wizzy.ai/v1',
    queryRunner: 'queryrunner',
    ...wizzySearchDatasourcePluginConfig,
  } as WizzySearchConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<WizzySearchConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return WizzySearchApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      WizzySearchApiRecords && WizzySearchApiRecords[queryName] ? WizzySearchApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'wizzySearch';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = WizzySearchApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: WizzySearchEditors,
});
