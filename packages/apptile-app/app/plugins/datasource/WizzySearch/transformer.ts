import {JSONMapperSchema, jsonArrayMapper} from '../ShopifyV_22_10/utils/utils';

export const WizzySearchGetSuggestionsTransformer = (data: any) => {
  if (!data) return;
  const {payload} = data;

  const result = {
    categories: null,
    searchSuggestions: null,
    products: null,
  };

  if (payload?.categories) {
    result.categories = payload?.categories.map((category: any) => {
      return {
        title: category.value,
        collectionHandle: category?.filters?.categories.length > 0 ? category.filters.categories[0] : null,
      };
    });
  }

  if (payload?.others) {
    result.searchSuggestions = payload?.others?.map((other: any) => ({title: other?.value}));
  }

  if (payload?.products) {
    result.products = payload?.products.result?.map((product: any) => {
      return {
        title: product.name,
        image: product.mainImage,
        productHandle: product?.attributes?.find((attribute: any) => attribute?.id === 'product_handle')?.values[0]
          .value[0],
      };
    });
  }

  return {data: result};
};

export const WizzySearchGetFilterTransformer = (data: any) => {
  if (!data) return;
  const {result} = data?.payload;
  if (!result) return;
  const hasNextPage = data?.payload?.pages - data?.payload?.filters?.page;
  const currentPage = data?.payload?.filters?.page;
  const products = result?.map((product: any) => {
    const productHandle = product?.attributes?.find((attribute: any) => attribute?.id === 'product_handle')?.values[0]
      .value[0];
    return {
      name: product.name,
      image: product.mainImage,
      originalPrice: product.price,
      sellingPrice: product.sellingPrice,
      discountPercentage: product.discountPercentage,
      productHandle: productHandle,
    };
  });

  return {
    data: products,
    hasNextPage: !!hasNextPage,
    paginationMeta: currentPage,
  };
};

export const WizzySearchGetProductsTransformer = (data: any) => {
  if (!data && !data?.payload?.result) return;
  const resultArray = data.payload.result;
  const transformedData = resultArray.map(item => {
    const objectContainingHandle = item?.attributes?.find((attribute: any) => attribute?.id === 'product_handle');
    let handle;
    try {
      handle = objectContainingHandle?.values[0].value[0];
    } catch {
      logger.error('Error in getting handle from wizzy search');
    }

    return {
      discountPercentage: item?.discountPercentage,
      finalPrice: item?.finalPrice,
      discountPrice: item?.discount,
      originalPrice: item?.price,
      sellingPrice: item?.sellingPrice,
      image: item?.mainImage,
      name: item?.name,
      url: item?.url,
      inStock: item?.inStock,
      productHandle: handle,
    };
  });
  return {data: transformedData};
};
