import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {DatasourcePluginConfig, IYagiOrderCancellableCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {baseDatasourceConfig} from '../base';
import {Platform} from 'react-native';
import {transformGetOrderCancellableCheckQueryData} from './transformers';

export type YagiConfigType = DatasourcePluginConfig &
  IYagiOrderCancellableCredentials & {
    queryRunner: any;
  };

type YagiCancellableQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const YagiCancellableApiRecords: Record<string, YagiCancellableQueryDetails> = {
  checkOrderCancellable: {
    queryType: 'get',
    endpoint: '/api/shops',
    endpointResolver: (endpoint: string, inputVariables: any) =>
      `${endpoint}/${inputVariables.shopDomain}?order=${inputVariables.orderId}`,
    contextInputParams: {
      shopDomain: '',
    },
    editableInputParams: {
      orderId: '',
    },
    transformer: transformGetOrderCancellableCheckQueryData,
  },
  cancelOrder: {
    queryType: 'post',
    endpoint: '/api/orders/cancel',
    contextInputParams: {
      shopDomain: '',
    },
    editableInputParams: {
      order: '',
      cancel_reason: '',
      other_cancel_reason: '',
    },
    inputResolver: (inputVariables: any) => {
      const {shopDomain, order, cancel_reason, other_cancel_reason} = inputVariables;

      return {
        shop: shopDomain,
        order,
        cancel_reason,
        other_cancel_reason,
        is_edit: false,
      };
    },
    // transformer: transformListReviewsData,
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'yagiOrderCancellable',
  type: 'datasource',
  name: 'Yagi Order Cancellable',
  description: 'Give customers control to cancel their order!',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const YagiCancellableEditor = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'https://app.cancellable.app',
      props: {
        label: 'API Base url',
        placeholder: 'https://app.cancellable.app',
      },
    },
    {
      type: 'codeInput',
      name: 'shopDomain',
      props: {
        label: 'Shop domain',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<YagiConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, YagiConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'yagiOrderCancellable',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://app.cancellable.app',
    shopDomain: '',
    queryRunner: 'queryrunner',
  } as YagiConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<YagiConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return YagiCancellableApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      YagiCancellableApiRecords && YagiCancellableApiRecords[queryName] ? YagiCancellableApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'yagiOrderCancellable';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = YagiCancellableApiRecords[queryName];
    const shopDomain = dsConfig.config.get('shopDomain');
    const apiUrl = dsConfig.config.get('apiBaseUrl');

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    let allContextAndTypedInput = {...typedInputVariables, ...contextInputParam};
    typedDataVariables = queryDetails.inputResolver
      ? yield call(queryDetails.inputResolver, allContextAndTypedInput)
      : allContextAndTypedInput;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, allContextAndTypedInput, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint,
      typedDataVariables,
      Platform.OS === 'web' && options
        ? {
            ...options,
            headers: {
              'x-base-url': apiUrl + endpoint,
            },
          }
        : options,
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },

  resolveCredentialConfigs: function (
    credentials: IYagiOrderCancellableCredentials,
  ): Partial<YagiConfigType> | boolean {
    const {apiBaseUrl, shopDomain} = credentials;
    if (!apiBaseUrl || !shopDomain) return false;
    return {
      apiBaseUrl,
      shopDomain,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'shopDomain'];
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: YagiCancellableEditor,
});
