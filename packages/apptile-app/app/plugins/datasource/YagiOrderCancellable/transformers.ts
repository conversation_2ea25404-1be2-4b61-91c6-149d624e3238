import {camelCase, isArray, transform, isObject} from 'lodash';

interface OrderCancellableCheckQueryData {
  preference: any;
}

const camelize = (obj: Record<string, unknown>) =>
  transform(obj, (result: Record<string, unknown>, value: unknown, key: string, target) => {
    const camelKey = isArray(target) ? key : camelCase(key);
    result[camelKey] = isObject(value) ? camelize(value as Record<string, unknown>) : value;
  });

export const transformGetOrderCancellableCheckQueryData = (queryResponse: OrderCancellableCheckQueryData) => {
  let {preference} = queryResponse;
  preference = camelize(preference);
  return {data: preference};
};
