import {select} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IYotpoCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import _ from 'lodash';
import {TransformAggregatedData, TransformGetReviews, TransformPostReview} from './transformers';

export type YotpoPluginConfigType = DatasourcePluginConfig &
  IYotpoCredentials & {
    queryRunner: any;
  };

type IEditableParams = Record<string, any>;

export type YotpoQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {
  data: any;
  hasNextPage?: boolean;
  paginationMeta?: any;
};

const baseYotpoQuerySpec: Partial<YotpoQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};
const YotpoApiRecords: Record<string, YotpoQueryDetails> = {
  //https://api-cdn.yotpo.com/product/{app_key}/{product_id}/bottomline/bottomline
  getAggregatedData: {
    ...baseYotpoQuerySpec,
    queryType: 'get',
    endpoint: '/widget/',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const after = 1;
      return `${endpoint}${inputVariables.appKey}/products/${inputVariables.productId}/reviews.json?${
        after ? `&page=${after}` : ''
      }${inputVariables.per_page ? `&per_page=${inputVariables.per_page}` : ''}${
        inputVariables.star ? `&star=${inputVariables.star}` : ''
      }${inputVariables.direction ? `&direction=${inputVariables.direction}` : ''}${
        inputVariables.sort ? `&sort=${inputVariables.sort}` : ''
      }`;
    },
    editableInputParams: {
      appKey: '',
      productId: '',
      per_page: 10,
      star: '',
      sort: 'date',
      direction: 'asc',
    },
    transformer: TransformAggregatedData,
    inputResolver: (inputVariables, inputParams) => {
      const {per_page, star, sort, direction, appKey, productId} = inputVariables;
      return {
        per_page: per_page,
        star: star,
        sort: sort,
        direction: direction,
        appKey: appKey,
        productId: productId,
      };
    },
  },
  listProductReviews: {
    ...baseYotpoQuerySpec,
    queryType: 'get',
    endpoint: '/widget/',
    endpointResolver: (endpoint: string, inputVariables: any, _paginationMeta: any) => {
      const {after = 1} = _paginationMeta ?? {};
      return `${endpoint}${inputVariables.appKey}/products/${inputVariables.productId}/reviews.json?${
        after ? `&page=${after}` : ''
      }${inputVariables.per_page ? `&per_page=${inputVariables.per_page}` : ''}${
        inputVariables.star ? `&star=${inputVariables.star}` : ''
      }${inputVariables.direction ? `&direction=${inputVariables.direction}` : ''}${
        inputVariables.sort ? `&sort=${inputVariables.sort}` : ''
      }`;
    },
    editableInputParams: {
      appKey: '',
      productId: '',
      per_page: 10,
      star: '',
      sort: 'date',
      direction: 'asc',
    },
    transformer: TransformGetReviews,
    inputResolver: (inputVariables, inputParams) => {
      const {per_page, star, sort, direction, appKey, productId} = inputVariables;
      return {
        per_page: per_page,
        star: star,
        sort: sort,
        direction: direction,
        appKey: appKey,
        productId: productId,
      };
    },
    isPaginated: true,
  },
  postCustomerReview: {
    ...baseYotpoQuerySpec,
    queryType: 'post',
    endpoint: '/widget/reviews',
    endpointResolver: (endpoint: string) => {
      return `${endpoint}`;
    },
    transformer: TransformPostReview,
    inputResolver: (inputVariables, inputParams) => {
      const {review_score, review_title, review_content, email, display_name, product_url, product_title, sku, appkey} =
        inputVariables;
      return {
        review_score: review_score,
        review_title: review_title,
        review_content: review_content,
        email: email,
        display_name: display_name,
        product_url: product_url,
        product_title: product_title,
        sku: sku,
        appkey: appkey,
      };
    },
    editableInputParams: {
      review_score: '',
      review_title: '',
      review_content: '',
      email: '',
      display_name: '',
      product_url: '',
      product_title: '',
      sku: '',
      appkey: '',
    },
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'yotpoReviews',
  type: 'datasource',
  name: 'Yotpo Reviews',
  description: '',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const YotpoEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api-cdn.yotpo.com/v1',
      },
    },
    {
      type: 'codeInput',
      name: 'appKey',
      props: {
        label: 'Yotpo App Key',
        placeholder: '',
      },
    },
  ],
};
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<YotpoPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, YotpoPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<YotpoQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(
          queryDetails.queryType,
          endpoint,
          {...typedDataVariables},
          {
            ...options,
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = YotpoApiRecords[queryName];
  const queryDetails = YotpoApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'yotpoReviews',
  config: {
    apiBaseUrl: 'https://',
    appKey: '',
    queryRunner: 'queryrunner',
  } as YotpoPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<YotpoPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    let queryRunner = dsModelValues?.get('queryRunner');

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return YotpoApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = YotpoApiRecords && YotpoApiRecords[queryName] ? YotpoApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IYotpoCredentials): Partial<YotpoPluginConfigType> | boolean {
    const {apiBaseUrl, appKey} = credentials;
    if (!(apiBaseUrl && appKey)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      appKey: appKey,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'appKey'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'yotpoReviews';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = YotpoApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    return response;
  },
  options: {
    pluginListing,
  },
  editors: YotpoEditors,
});
