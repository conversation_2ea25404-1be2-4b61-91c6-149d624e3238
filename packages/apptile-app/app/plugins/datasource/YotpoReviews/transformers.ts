export const TransformAggregatedData = (data: any) => {
  return {
    data: data,
  };
};

export const TransformGetReviews = (data: any) => {
  const {reviews} = data.response;
  const {page} = data.response.pagination;
  const hasNextPage = !(reviews.length === 0);
  const paginationMeta = {after: page + 1};

  return {
    data: reviews,
    hasNextPage,
    paginationMeta,
  };
};
export const TransformPostReview = (reponse: any) => {
  return {
    data: reponse,
  };
};
