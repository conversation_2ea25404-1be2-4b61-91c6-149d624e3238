import {select} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IZapietCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import _ from 'lodash';
import {TransformGetAvailableDates, TransformGetAvailableDeliveryLocations} from './transformers';

export type ZapietPluginConfigType = DatasourcePluginConfig &
  IZapietCredentials & {
    queryRunner: any;
  };

type IEditableParams = Record<string, any>;

export type ZapietQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseZapietQuerySpec: Partial<ZapietQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
    apiKey: '',
    shop: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};
const ZapietApiRecords: Record<string, ZapietQueryDetails> = {
  //Store pickup apis
  getAvailableStorePickupDeliveryLocations: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/pickup/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {shop} = inputVariables;
      return `${endpoint}?shop=${shop}`;
    },
    editableInputParams: {
      shoppingCart: '',
      geoSearchQuery: '',
    },
    transformer: TransformGetAvailableDeliveryLocations,
  },
  getStorePickupDates: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/pickup/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {locationId, shop} = inputVariables;
      return `${endpoint}/${locationId}/calendar?shop=${shop}`;
    },
    editableInputParams: {
      locationId: '',
      shoppingCart: '',
    },
    transformer: TransformGetAvailableDates,
  },
  getStorePickupTimes: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/pickup/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {locationId, shop, date} = inputVariables;
      return `${endpoint}/${locationId}/calendar/${date}?shop=${shop}`;
    },
    editableInputParams: {
      locationId: '',
      date: '',
    },
    transformer: TransformGetAvailableDates,
  },

  //Local delivery
  getAvailableLocalDeliveryLocations: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/delivery/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {shop} = inputVariables;
      return `${endpoint}?shop=${shop}`;
    },
    editableInputParams: {
      shoppingCart: '',
      geoSearchQuery: '',
    },
    transformer: TransformGetAvailableDeliveryLocations,
  },
  getLocalDeliveryDates: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/delivery/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {locationId, shop} = inputVariables;
      return `${endpoint}/${locationId}/calendar?shop=${shop}`;
    },
    editableInputParams: {
      locationId: '',
      shoppingCart: '',
    },
    transformer: TransformGetAvailableDates,
  },
  getLocalDeliveryTimes: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/delivery/locations',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {locationId, shop, date} = inputVariables;
      return `${endpoint}/${locationId}/calendar/${date}?shop=${shop}`;
    },
    editableInputParams: {
      locationId: '',
      date: '',
    },
    transformer: TransformGetAvailableDates,
  },

  //shipping
  getShippingDates: {
    ...baseZapietQuerySpec,
    queryType: 'post',
    endpoint: '/shipping/calendar',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {shop} = inputVariables;
      return `${endpoint}?shop=${shop}`;
    },
    editableInputParams: {
      shoppingCart: '',
    },
    transformer: TransformGetAvailableDates,
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'zapiet',
  type: 'datasource',
  name: 'Zapiet',
  description: '',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const ZapietEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API base url',
        placeholder: 'https://api-us.zapiet.com/v1.0',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'Zapiet api key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'shop',
      props: {
        label: 'Shopify shop',
        placeholder: '',
      },
    },
  ],
};
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<ZapietPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ZapietPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<ZapietQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (isReadyToRun) {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {...typedDataVariables},
        {
          ...options,
        },
      );
    } else {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = ZapietApiRecords[queryName];
  const queryDetails = ZapietApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'zapiet',
  config: {
    apiBaseUrl: 'https://api-us.zapiet.com/v1.0',
    apiKey: '',
    shop: '',
    queryRunner: 'queryrunner',
  } as ZapietPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ZapietPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    let queryRunner = dsModelValues?.get('queryRunner');

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ZapietApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = ZapietApiRecords && ZapietApiRecords[queryName] ? ZapietApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IZapietCredentials): Partial<ZapietPluginConfigType> | boolean {
    const {apiBaseUrl, apiKey, shop} = credentials;
    if (!(apiKey && shop)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      apiKey: apiKey,
      shop,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'apiKey'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'zapiet';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = ZapietApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    return response;
  },
  options: {
    pluginListing,
  },
  editors: ZapietEditors,
});
