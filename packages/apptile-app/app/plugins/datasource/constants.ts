//Datasource standardization
export const IntegrationCodesMappingWithDataSource: any = {
  rechargePayments: 'Payment Recharge',
  firebaseAnalytics: 'firebaseAnalytics',
  flits: 'flitsWishlist',
  gorgias: 'gorgiasHelpdesk',
  productFilterSearch: 'productFilterSearch',
  stamped: 'Stamped',
  ApptileMFAuth: 'ApptileMFAuth',
  apptileMFAuthentication: 'apptileMFAuthentication',
  stampedReviews: 'stampedReviews',
  stampedRewards: 'stampedRewards',
  localWishlist: 'LocalWishlist',
  apptileSkinCare: 'apptileSkinCare',
  judgeMe: 'judgeMe',
  joleneAuth: 'joleneAuth',
  instagramMedia: 'instagramMedia',
  meragi: 'meragi',
  storifyMe: 'storifyMe',
  yagiOrderCancellable: 'yagiOrderCancellable',
  simplyOtp: 'simplyOTPLogin',
  nectorRewards: 'nectorRewards',
  wizzySearch: 'wizzySearch',
  searchanize: 'searchanize',
  orderLimiter: 'orderLimiter',
  lively: 'lively',
  livelyShoppable: 'livelyShoppable',
  okendoReviews: 'okendoReviews',
  yotpoReviews: 'yotpoReviews',
  reviewsIoReviews: 'reviewsIoReviews',
  cloudsearch: 'Cloud Search',
  zapiet: 'zapiet',
  nosto: 'Nosto',
  easyAppointment: 'easyAppointment',
  loyaltyLion: 'loyaltyLion',
  joyLoyalty: 'joyLoyalty',
  wishlistPlus: 'wishlistPlus',
  appstleSubs: 'Appstle Subscriptions',
  riseAI: 'riseAI',
  swatchKing: 'swatchKing',
  apptileCartUpsell: 'ApptileCartUpsell',
  aitrillion: 'Aitrillion',
  omegaEstimate: 'OmegaEstimate',
  preOrderWod: 'preOrderWod',
  apptileCartDiscounts: 'apptileCartDiscounts',
  apptilePinCodeChecker: 'apptilePinCodeChecker',
  discourse: 'discourse',
  cartAssist: 'cartAssist',
  smile: 'smile',
  fera: 'fera',
  rivo: 'rivo',
  recurpay: 'Recurpay',
  fastSimon: 'fastSimon',
  notificationCenter: 'RestApi',
};
