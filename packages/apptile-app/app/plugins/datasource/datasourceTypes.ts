export interface IShopifyDatasourceQueryReturnValue<T> {
  data: T;
  rawData: any;
  hasNextPage?: boolean;
  paginationMeta?: any;
  errors: any;
  hasError: boolean;
}

export type IQueryDataType<T> =
  | (T & {
      _raw: any;
    })
  | Array<T & {_raw: any}>;

export type IntegrationPlatformType =
  | 'shopify'
  | 'flits'
  | 'firebaseAnalytics'
  | 'rechargePayments'
  | 'stamped'
  | 'productFilterSearch'
  | 'gorgias'
  | 'ApptileMFAuth'
  | 'apptileMFAuthentication'
  | 'stampedReviews'
  | 'stampedRewards'
  | 'localWishlist'
  | 'apptileSkinCare'
  | 'storifyMe'
  | 'shopFlo'
  | 'orderLimiter'
  | 'judgeMe'
  | 'yagiOrderCancellable'
  | 'shopifyMultipass'
  | 'msg91'
  | 'moEngage'
  | 'searchanize'
  | 'judgeMe'
  | 'aitrillion'
  | 'omegaEstimate'
  | 'instagramMedia'
  | 'snackShackPartner'
  | 'joleneAuth'
  | 'googleMaps'
  | 'simplyOtp'
  | 'lively'
  | 'livelyShoppable'
  | 'discourse'
  | 'wizzySearch'
  | 'nectorRewards'
  | 'vaccinationTracker'
  | 'HRTech'
  | 'okendoReviews'
  | 'yotpoReviews'
  | 'reviewsIoReviews'
  | 'cloudsearch'
  | 'zapiet'
  | 'nosto'
  | 'easyAppointment'
  | 'loyaltyLion'
  | 'joyLoyalty'
  | 'wishlistPlus'
  | 'appstleSubs'
  | 'riseAI'
  | 'swatchKing'
  | 'apptileCartUpsell'
  | 'preOrderWod'
  | 'apptileCartDiscounts'
  | 'preOrderWod'
  | 'apptilePinCodeChecker'
  | 'apptilePreOrder'
  | 'smile'
  | 'cartAssist'
  | 'fera'
  | 'redo'
  | 'rivo'
  | 'advanceOrderLimiter'
  | 'smile'
  | 'recurpay'
  | 'tryWithMirra'
  | 'tokiRewards'
  | 'fastSimon';

export interface DatasourcePluginConfig {
  secretsConfigured: boolean;
}

export interface IBaseCredentials {
  appId: string;
  proxyUrl?: string;
  platformType: string;
}

export interface IRechargePaymentsCredentials extends IBaseCredentials {
  apiVersion: string;
}

export interface IShopifyCredentials extends IBaseCredentials {
  storeName: string;
  storefrontAccessToken: string;
  shopManagerApiUrl: string;
  appId: string;
}

export interface IShopifyCustomerAPICredentials extends IBaseCredentials {}

export interface IProductFilterAndSearchCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopUrl: string;
}

export interface IStampedCredentials extends IBaseCredentials {
  storeUrl: string;
  publicKey: string;
  storeHash: string;
  apiBaseUrl: string;
  privateKey: string;
}

export interface IFlitsCredentials extends IBaseCredentials {
  userId: string;
}

export interface IGorgiasCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  apiKey: string;
  username: string;
}

export interface IApptileMFAuthCredentials extends IBaseCredentials {
  apiBasePath: string;
  sourceEntityId: string;
}

export interface IStorifyMeCredentials extends IBaseCredentials {
  accountId: string;
  apiKey: string;
  env: string;
}

export interface IOrderLimiterCredentials {
  maximumOrderValue: string;
  maximumOrderItems: string;
  maximumItemQuantity: string;
}

export interface IShopFloCredentials extends IBaseCredentials {
  merchantId: string;
}

export interface ISearchanizeCredentials extends IBaseCredentials {
  apiKey: string;
}
export interface IJudgeMeCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopDomain: string;
  apiToken: string;
}

export interface ILoyaltyLionCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  username: string;
  password: string;
}

export interface IJoyLoyaltyCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  joyLoyaltyAppId: string;
  secretKey: string;
}

export interface IWishlistPlusCredentials extends IBaseCredentials {
  apiEndpoint: string;
  apiKey: string;
  pid: string;
}

export interface ISmileCredentials extends IBaseCredentials {
  apiBaseUrl: string;
}

export interface IApptileSkinCareCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  apiAccessKey: string;
}

export interface IYagiOrderCancellableCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopDomain: string;
}

export interface ISimplyOTPLoginCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  apiVersion: string;
  proxyUrl: string;
  shopDomain: string;
}

export interface IDiscourseCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  appId: string;
  communityHelperEndpoint: string;
}

export interface ILivelyCredentials {
  brandId: string;
}

export interface IHRTechCredentials {
  apiBaseUrl: string;
  apiKey: string;
}

export interface IOkendoCredentials {
  apiBaseUrl: string;
  okendoUserId: string;
}
export interface IYotpoCredentials {
  apiBaseUrl: string;
  appKey: string;
}
export interface IReviewsIoReviewsCredentials {
  apiBaseUrl: string;
  appKey: string;
}

export interface IZapietCredentials {
  apiBaseUrl: string;
  apiKey: string;
  shop: string;
}

export interface ICloudSearchCredentials {
  apiBaseUrl: string;
  shopDomain: string;
  platformType: string;
}

export interface INostoCredentials {
  apiBaseUrl: string;
  searchBaseUrl: string;
  apiToken: string;
  accountId: string;
}

export interface IFastSimonCredentials extends IBaseCredentials {
  proxyUrl: string;
  appId: string;
}

export interface INectorRewardsCredentials {
  proxyUrl: string;
  appId: string;
}

export interface IEasyAppointmentCredentials {
  apiBaseUrl: string;
}

export interface IAppstleSubscriptionsCredentials extends IBaseCredentials {
  apiKey: string;
  apiBaseUrl: string;
}

export interface IAitrillionCredentials {
  apiBaseUrl: string;
  storeApiAuthenticationKey: string;
  serverApiAuthenticationKey: string;
}

export interface IOmegaEstimateCredentials {
  apiBaseUrl: string;
  apiKey: string;
}

export interface IApptileCartDiscountsCredentials {
  apiBaseUrl: string;
}

export interface IRecurpayCredentials extends IBaseCredentials {
  appId: string;
}

export interface IApptilePinCodeCheckerCredentials {}

export type IDatasourceCredentialTypes =
  | IRechargePaymentsCredentials
  | IShopifyCredentials
  | IProductFilterAndSearchCredentials
  | IFlitsCredentials
  | IStampedCredentials
  | IGorgiasCredentials
  | IStorifyMeCredentials
  | IShopFloCredentials
  | IJudgeMeCredentials
  | ILoyaltyLionCredentials
  | IWishlistPlusCredentials
  | IJoyLoyaltyCredentials
  | IApptileSkinCareCredentials
  | IOrderLimiterCredentials
  | IYagiOrderCancellableCredentials
  | ISimplyOTPLoginCredentials
  | IDiscourseCredentials
  | IHRTechCredentials
  | IOkendoCredentials
  | IYotpoCredentials
  | IReviewsIoReviewsCredentials
  | ICloudSearchCredentials
  | IZapietCredentials
  | INostoCredentials
  | IFastSimonCredentials
  | IEasyAppointmentCredentials
  | IAppstleSubscriptionsCredentials
  | IAitrillionCredentials
  | IOmegaEstimateCredentials
  | IApptileCartDiscountsCredentials
  | IRecurpayCredentials
  | ISmileCredentials;
