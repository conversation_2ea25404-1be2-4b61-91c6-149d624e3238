import {connectConfig, makeActionSelectorConfig, registerPlugin} from 'apptile-core';
import BakulMembership from './BakulMembership';
import Findify from './Findify';
import ProductFilter from './ProductFilter';
import RechargePayments from './RechargePayments';
import RestApi from './RestApi';
import Stamped from './Stamped';
import Wishlist from './Wishlist';
import GorgiasHelpdesk from './GorgiasHelpdesk';
import ShopifyV_22_10 from './ShopifyV_22_10';
import ApptileMFAuth from './ApptileMFAuth';
import ApptileQuiz from './ApptileQuiz';
import LocalWishlist from './LocalWishlist';
import ApptileMFAuthentication from './ApptileMFAuthentication';
import StampedReviews from './StampedReviews';
import StampedRewards from './StampedRewards';
import ApptileSkinCare from './ApptileSkinCare';
import StorifyMe from './StorifyMe';
import ShopFlo from './ShopFlo';
import OrderLimiter from './OrderLimiter';
import Searchanize from './Searchanize';
import JudgeMe from './JudgeMe';
import YagiOrderCancellable from './YagiOrderCancellable';
import Aitrillion from './Aitrillion';
import FlitsWishlist from './FlitsWishlist';
import JoleneAuth from './JoleneOtpLogin';
import InstagramIntegration from './InstagramIntegration';
import SnackShackPartner from './SnackShackPartner';
import GoogleMaps from './GoogleMaps';
////////////////////
import Meragi from './Meragi';
import SimplyOtpLogin from './SimplyOtpLogin';
import Discourse from './Discourse';
import WizzySearch from './WizzySearch';
import NectorRewards from './NectorRewards';
import RforRabbitVaccinationTracker from './RforRabbitVaccinationTracker';
import HRTech from './HRTech';
import Lively from './Lively';
import CloudSearch from './CloudSearch';
import OkendoReviews from './OkendoReviews';
import YotpoReviews from './YotpoReviews';
import ReviewsIoReviews from './ReviewsIoReviews';
import Zapiet from './Zapiet';
import Nosto from './Nosto';
import EasyAppointment from './EasyAppointment';
import LoyaltyLion from './LoyaltyLion';
import WishlistPlus from './WishlistPlus';
import JoyLoyalty from './JoyLoyalty';
import FastSimon from './FastSimon';
import Pazo from './Pazo';
import AppstleSubscriptions from './AppstleSubscriptions';
import RiseAI from './RiseAI/LoyaltyAndRewards';
import SwatchKing from './SwatchKing';
import ApptileCartUpsell from './ApptileCartUpsell';
import PreOrderWod from './PreOrderWod';
import ApptileCartDiscounts from './ApptileCartDiscounts';
import ApptilePinCodeChecker from './ApptilePinCodeChecker';
import Smile from './Smile';
import ApptileCartAssist from './ApptileCartAssist';
import OmegaEstimate from './OmegaEstimate';
import Fera from './Fera';
import Redo from './Redo';
import Rivo from './Rivo';
import AdvanceOrderLimiter from './AdvanceOrderLimiter';
import Recurpay from './Recurpay';
import TryWithMirra from './TryWithMirra';
import LivelyShoppable from './LivelyShoppable';
import TokiRewards from './TokiRewards';

export function registerDatasource(dsName: string, dsModel: any) {
  const actions = makeActionSelectorConfig(dsModel?.options?.propertySettings);
  registerPlugin(
    dsName,
    dsModel,
    connectConfig(dsModel.config),
    dsModel.onPluginUpdate,
    dsModel.editors, // Fill editors.
    dsModel.options,
    actions,
  );
}

// Please also add the datasource
// to IntegrationCodesMappingWithDataSource in constants folder to ensure future self serve
const datasourceToLoad = [
  Findify,
  BakulMembership,
  RestApi,
  ProductFilter,
  Stamped,
  RechargePayments,
  Wishlist,
  LocalWishlist,
  GorgiasHelpdesk,
  ShopifyV_22_10,
  ApptileCartAssist,
  ApptileMFAuth,
  ApptileQuiz,
  ApptileMFAuthentication,
  StampedReviews,
  StampedRewards,
  ApptileSkinCare,
  StorifyMe,
  ShopFlo,
  OrderLimiter,
  Searchanize,
  JudgeMe,
  YagiOrderCancellable,
  Aitrillion,
  OmegaEstimate,
  FlitsWishlist,
  JoleneAuth,
  InstagramIntegration,
  SnackShackPartner,
  GoogleMaps,
  Meragi,
  SimplyOtpLogin,
  Discourse,
  WizzySearch,
  NectorRewards,
  RforRabbitVaccinationTracker,
  LoyaltyLion,
  WishlistPlus,
  JoyLoyalty,
  FastSimon,
  Lively,
  LivelyShoppable,
  HRTech,
  OkendoReviews,
  CloudSearch,
  YotpoReviews,
  ReviewsIoReviews,
  Zapiet,
  Nosto,
  EasyAppointment,
  Pazo,
  AppstleSubscriptions,
  RiseAI,
  SwatchKing,
  ApptileCartUpsell,
  PreOrderWod,
  ApptileCartDiscounts,
  ApptilePinCodeChecker,
  Smile,
  Fera,
  Redo,
  Rivo,
  AdvanceOrderLimiter,
  Recurpay,
  TryWithMirra,
  TokiRewards,
];
export function loadDatasourcePlugins() {
  datasourceToLoad.forEach(v => {
    registerDatasource(v.name, v);
  });
  return datasourceToLoad;
}

export default loadDatasourcePlugins;
