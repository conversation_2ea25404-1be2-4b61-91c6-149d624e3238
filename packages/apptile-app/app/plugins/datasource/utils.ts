import {isArray, isObject} from 'lodash';
import {ImmutableMapType} from 'apptile-core';
const handleGlobalQueryErrorsAndTransform = (
  queryData: any,
  queryDetails: any,
  rawData: any,
  shopConfig: any,
  model: ImmutableMapType<any>,
) => {
  if (queryData?.errors) {
    return {
      errors: [queryData.errors],
      hasError: true,
      data: null,
    };
  } else {
    const countryCode = model?.get('countryCode');
    const languageCode = model?.get('languageCode');
    const enhancedShopConfig = {
      ...shopConfig,
      countryCode,
      languageCode,
    };
    return queryDetails.transformer(rawData, enhancedShopConfig, model);
  }
};

export function processShopifyGraphqlQueryResponse(
  queryResponse: any,
  queryDetails: any,
  shopConfig: any,
  model: ImmutableMapType<any>,
) {
  const queryData = queryResponse;
  const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
  let transformedData = rawData;
  let transformedError;
  let transformedHasError;
  let queryHasNextPage, paginationDetails;
  if (queryDetails && queryDetails.transformer) {
    const {
      data,
      hasNextPage,
      errors = null,
      hasError = false,
      paginationMeta: pageData,
    } = handleGlobalQueryErrorsAndTransform(queryData, queryDetails, rawData, shopConfig, model);

    transformedData = data;
    transformedError = errors;
    transformedHasError = hasError;
    queryHasNextPage = hasNextPage;
    paginationDetails = pageData;
  }
  return {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails};
}

export function fetchInputFieldsForUIEditor(queryInputs: any) {
  const queryInputEditor =
    queryInputs &&
    Object.entries(queryInputs).map(([queryInput, placeholder]) => {
      if (typeof placeholder === 'object' && isArray(placeholder))
        return {
          type: 'dropDown',
          name: queryInput,
          props: {
            label: queryInput,
            options: placeholder,
          },
        };
      if (typeof placeholder === 'boolean')
        return {
          type: 'checkbox',
          name: queryInput,
          props: {
            label: queryInput,
            defaultValue: placeholder,
          },
        };
      return {
        type: 'codeInput',
        name: queryInput,
        props: {
          label: queryInput,
          placeholder: isObject(placeholder) ? `${JSON.stringify(placeholder)}` : `${placeholder}`,
        },
      };
    });
  return queryInputEditor;
}
