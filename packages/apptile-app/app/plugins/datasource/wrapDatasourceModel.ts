import * as Immutable from 'immutable';
import {call, put, select} from 'redux-saga/effects';
import {pluginUpdateConfigAction} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {selectPluginStageModel} from 'apptile-core';
import {RootState} from 'apptile-core';
import {AppPageTriggerOptions, GetRegisteredConfig} from 'apptile-core';
import {DatasourceQueryReturnValue} from '../query';
import {IDatasourceCredentialTypes, IntegrationPlatformType} from './datasourceTypes';
import {fetchInputFieldsForUIEditor} from './utils';

export default function wrapDatasourceModel(datasourceModel: any) {
  const config = Immutable.fromJS({
    ...datasourceModel.config,
  });

  return {
    name: datasourceModel.name,

    config: Immutable.Map(config),

    runQuery: function* (
      dsModel,
      dsConfig,
      dsModelValues,
      queryName: string,
      inputVariables: any,
      options?: AppPageTriggerOptions,
    ): DatasourceQueryReturnValue {
      return yield call(datasourceModel.runQuery, dsModel, dsConfig, dsModelValues, queryName, inputVariables, options);
    },
    getQueries: function (): string[] {
      return datasourceModel.getQueries();
    },
    getQueryInputParams: function (queryName: string) {
      const queryInputs = datasourceModel.getQueryInputParams(queryName);
      return fetchInputFieldsForUIEditor(queryInputs);
    },
    resolveCredentialConfigs: function (credentials: IDatasourceCredentialTypes): any {
      return datasourceModel.resolveCredentialConfigs(credentials);
    },
    resolveClearCredentialConfigs: function (): any {
      return datasourceModel.resolveClearCredentialConfigs();
    },
    getPlatformIdentifier: function (): IntegrationPlatformType {
      return datasourceModel?.getPlatformIdentifier ? datasourceModel.getPlatformIdentifier() : null;
    },

    onPluginUpdate: function* (
      state: RootState,
      pluginId: string,
      pageKey: string,
      instance: number | null,
      userTriggered: boolean,
      pageLoad: boolean,
      options: AppPageTriggerOptions,
    ) {
      if (pageKey) throw 'Datasource cannot be in a page';
      if (datasourceModel.onPluginUpdate) {
        return yield datasourceModel.onPluginUpdate(
          state,
          pluginId,
          pageKey,
          instance,
          userTriggered,
          pageLoad,
          options,
        );
      } else {
        const dsConfig = yield select(selectPluginConfig, pageKey, pluginId);
        const dsModelValues = yield select(selectPluginStageModel, pageKey, pluginId);
        const configGen = GetRegisteredConfig(dsConfig.get('subtype'));
        const mergedConfig = configGen(dsConfig.config);
        const mergedPluginConfig = dsConfig.set('config', mergedConfig);
        if (pageLoad) {
          yield put(pluginUpdateConfigAction([pluginId], mergedPluginConfig));
          return yield call(datasourceModel.initDatasource, datasourceModel, mergedPluginConfig, dsModelValues);
        } else {
          if (datasourceModel.onModelUpdate) {
            yield* datasourceModel.onModelUpdate(datasourceModel, mergedPluginConfig, dsModelValues);
          }
        }
      }
    },
    options: datasourceModel.options,
    editors: datasourceModel.editors,
  };
}
