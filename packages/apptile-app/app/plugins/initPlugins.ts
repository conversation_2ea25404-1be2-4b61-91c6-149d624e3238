/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * Generated with the TypeScript template
 * https://github.com/react-native-community/react-native-template-typescript
 *
 * @format
 */
import {ApptileGlobalPlugin} from 'apptile-core';
import loadDatasourcePlugins from './datasource';
import ContainerWidget from './widgets/Container';
import ListViewWidget from './widgets/ListViewWidget';
import ImageWidget from './widgets/ImageWidget';
import TextWidget from './widgets/TextWidget';
import QueryPlugin from './query/index';
import TextInputWidget from './widgets/TextInputWidget';
import CheckBoxWidget from './widgets/CheckBoxWidget';
import ButtonWidget from './widgets/ButtonWidget';
import RichTextWidget from './widgets/RichTextWidget';
import BadgeWidget from './widgets/badgeWidget';
import PillWidget from './widgets/PillWidget';
import ModalWidget from './widgets/ModalWidget';
import IconWidget from './widgets/IconWidget';
import WebViewWidget from './widgets/WebViewWidget';
import GorgiasWebViewWidget from './widgets/GorgiasWidget';
import ImageSliderWidget from './widgets/ImageSliderWidget';
import RatingWidget from './widgets/RatingWidget';
import RangeSliderWidget from './widgets/RangeSliderWidget';
import AccordionWidget from './widgets/Accordion';
import CountdownWidget from './widgets/CountdownWidget';
import ChatWidget from './widgets/ChatWidget';
import BottomSheetWidget from './widgets/BottomSheetWidget';
import ProgressBarWidget from './widgets/ProgressBarWidget';

import VideoPlayerWidget from './widgets/VideoPlayerWidget';
import CameraWidget from './widgets/CameraWidget';
import RadioGroupWidget from './widgets/RadioGroupWidget';
import ScrollBubblesWidget from './widgets/ScrollBubblesWidget/ScrollBubbles';
import SwatchWidget from './widgets/SwatchWidget';
import ImageSliderWidgetV2 from './widgets/ImageSliderV2';
import {ModuleProperty} from 'apptile-core';
import {ModuleOutput} from 'apptile-core';
import {ModuleInstance} from 'apptile-core';

import StatePlugin from './state/StatePlugin';
import LocalStoragePlugin from './state/LocalStoragePlugin';
import TimerPlugin from './state/Timer';
import PollPlugin from './state/Poll';
import LogicalExpressionPlugin from './state/LogicalExpression';
import {CustomList} from 'apptile-core';
import {ShopifyPDP_22_10} from './state/ShopifyPDP';
import {ShopifyPLP_22_10} from './state/ShopifyPLP';
import {SearchanizeHelper} from './state/SearchanizeHelper';
import {SearchFiltersPlugin} from './state/SearchFilters';
import {CloudSearchHelper} from './state/CloudSearchHelper';

import DisplayImageList from './state/DisplayImageList';
import MFAuthQuery from './mfAuthQuery';
import MFAuthenticationQuery from './mfAuthenticationQuery';
import {ShopifyCollectionsTree_22_10} from './state/ShopifyCollectionsTree';
// import StorifyMeWidget from './widgets/StorifyMeWidget';
import ShopFloCheckoutWidget from './widgets/ShopFloCheckoutWidget';
import AutoLinkTextWidget from './widgets/AutoLinkTextWidget';
import ApptileColorSwatch from './state/ApptileColorSwatch';
import GoKwikCheckoutWidget from './widgets/GoKwikCheckoutWidget';
import ShopifyWebCheckoutWidget from './widgets/ShopifyWebCheckoutWidget';
import ProfileWidget from './widgets/ProfileWidget';
import AssistantAI from './widgets/AssistantAI';
import LivelyWidget from './widgets/LivelyWidget';
import LivelyStoriesWidget from './widgets/LivelyStoriesWidget';
import CalendarWidget from './widgets/CalendarWidget';
import KimiricaFAQWidget from './widgets/KimiricaFAQWidget';
import {NostoHelper} from './state/NostoHelper';
import ZapietHelper from './state/ZapietHelper';
import PazoReport from './widgets/PazoReport';
import AnimatedProgressBarWidget from './widgets/AnimatedProgressBarWidget';
import LivelyLiveSellingWidget from './widgets/LivelyLiveSellingWidget';
import VideoPlayerV2 from './widgets/VideoPlayerV2';
import VideoSeeker from './widgets/VideoSeeker';
import Comparator from './state/Comparator';
import FireworkGlobal from './state/FireworkGlobalPlugin';
import FireworkWidget from './widgets/ApptileFireworkPlugin';
import FireworkSBWidget from './widgets/ApptileFireworkStoryblockPlugin';
import LivelyLiveSellingWidgetV2 from './widgets/LivelyLiveSellingWidgetV2';
import {ShopifyWishlist_22_10} from './state/ShopifyWishlist';
import TawkToWebViewWidget from './widgets/TawkToWebViewWidget';
import ShopifyCustomerLoginWidget from './widgets/ShopifyCustomerLoginWidget';
import KippunHaruWebViewWidget from './widgets/KippunHaruWebViewWidget';
import WebViewWidgetV2 from './widgets/WebViewWidgetV2';
import WebViewWidgetV3 from './widgets/WebViewWidgetV3';
import ResetPasswordWebViewWidget from './widgets/ResetPasswordWebViewWidget';
import LivelyShoppableFeedV2 from './widgets/LivelyShoppableFeedV2';
import HeaderWidget from './widgets/HeaderWidget';
import LivelyAuctionWidget from './widgets/LivelyAuctionWidget';

export const initPlugins = () => {
  const pluginsLoaded = [
    ApptileGlobalPlugin,
    ContainerWidget,
    ListViewWidget,
    ImageWidget,
    BadgeWidget,
    TextInputWidget,
    PillWidget,
    CheckBoxWidget,
    ButtonWidget,
    TextWidget,
    RichTextWidget,
    ModalWidget,
    IconWidget,
    QueryPlugin,
    RadioGroupWidget,
    SwatchWidget,
    AccordionWidget,
    CountdownWidget,
    ChatWidget,
    BottomSheetWidget,
    AutoLinkTextWidget,
    ProgressBarWidget,
    ProfileWidget,
    AssistantAI,
    CloudSearchHelper,
    ////////////
    ModuleProperty,
    ModuleOutput,
    ModuleInstance,
    ////////////
    StatePlugin,
    LocalStoragePlugin,
    WebViewWidget,
    WebViewWidgetV2,
    WebViewWidgetV3,
    ResetPasswordWebViewWidget,
    ImageSliderWidget,
    RatingWidget,
    RangeSliderWidget,
    TimerPlugin,
    PollPlugin,
    VideoPlayerWidget,
    ScrollBubblesWidget,
    CameraWidget,
    ImageSliderWidgetV2,
    // StorifyMeWidget,
    ShopifyCustomerLoginWidget,
    ShopFloCheckoutWidget,
    GoKwikCheckoutWidget,
    ShopifyWebCheckoutWidget,
    LivelyWidget,
    LivelyStoriesWidget,
    LivelyLiveSellingWidget,
    LivelyLiveSellingWidgetV2,
    /////////////////
    DisplayImageList,
    LogicalExpressionPlugin,
    Comparator,
    MFAuthQuery,
    CustomList,
    ShopifyPDP_22_10,
    ShopifyPLP_22_10,
    ShopifyCollectionsTree_22_10,
    SearchanizeHelper,
    MFAuthenticationQuery,
    ApptileColorSwatch,
    CalendarWidget,
    KimiricaFAQWidget,
    AnimatedProgressBarWidget,
    NostoHelper,
    PazoReport,
    ZapietHelper,
    VideoPlayerV2,
    VideoSeeker,
    SearchFiltersPlugin,
    FireworkGlobal,
    FireworkWidget,
    FireworkSBWidget,
    ShopifyWishlist_22_10,
    GorgiasWebViewWidget,
    TawkToWebViewWidget,
    KippunHaruWebViewWidget,
    LivelyShoppableFeedV2,
    HeaderWidget,
    LivelyAuctionWidget,
  ];
  const datasourcePlugins = loadDatasourcePlugins();
  return [...pluginsLoaded, ...datasourcePlugins];
};
