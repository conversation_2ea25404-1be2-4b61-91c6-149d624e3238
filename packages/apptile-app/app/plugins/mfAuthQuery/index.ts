import Immutable from 'immutable';
import {call, put, select} from 'redux-saga/effects';
import {modelUpdateAction} from 'apptile-core';
import {triggerPageEvent} from 'apptile-core';
import {ModelChange} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import {getAppDispatch} from 'apptile-core';
import {modelUpdateSaga} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {RootState} from 'apptile-core';
import {
  AppPageTriggerOptions,
  connectPlugin,
  EventTriggerIdentifier,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
} from 'apptile-core';
import {defaultQueryConfig} from '../queryTypes';

type QueryInputvariables = Record<string, any>;

interface IMFAuthQueryConfig {
  datasource: string;
  actionName: string;
  params: QueryInputvariables;
  loading: boolean;
}

type IMFAuthQueryEventConfig = {
  data?: any;
  onSuccess: string;
  onError: string;
  onSignupRequired: string;
  onOtpGenerated: string;
  onPasswordRequire: string;
  onExistingUser: string;
  executeQuery: string;
};

const defaultQueryEventConfig: IMFAuthQueryEventConfig = {
  onSuccess: '',
  onError: '',
  onSignupRequired: '',
  onOtpGenerated: '',
  onPasswordRequire: '',
  onExistingUser: '',
  executeQuery: '',
};

const defaultQueryPluginConfig: IMFAuthQueryConfig = {
  datasource: '',
  actionName: '',
  params: {},
  loading: false,
};

const MFAuthQueryConfig = Immutable.fromJS({
  ...defaultQueryConfig,
  ...defaultQueryPluginConfig,
  ...defaultQueryEventConfig,
  loading: false,
});

const propertySettings: PluginPropertySettings = {
  data: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  params: {
    updatesProps: ['data'],
  },
  executeQuery: {
    updatesProps: ['onSignupRequired', 'onOtpGenerated', 'onPasswordRequire', 'onError', 'onSuccess', 'onExistingUser'],
  },
  onSignupRequired: {
    type: EventTriggerIdentifier,
  },
  onOtpGenerated: {
    type: EventTriggerIdentifier,
  },
  onPasswordRequire: {
    type: EventTriggerIdentifier,
  },
  onError: {
    type: EventTriggerIdentifier,
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
  onExistingUser: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<IMFAuthQueryConfig> = {
  basic: [
    {
      type: 'mfAuthQueryEditor',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'runWhenModelUpdates',
      props: {
        label: 'Run when model updates',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runWhenPageLoads',
      props: {
        label: 'Run when page loads',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runOnPageFocus',
      props: {
        label: 'Run on page focus',
        checkedValue: false,
      },
    },
  ],
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'MFAuthQuery',
  type: 'query',
  name: 'MFAuthQuery',
  description: 'OTP Login Query',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'query',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  let modelUpdates = {
    modelUpdates: [] as ModelChange[],
  };

  let modelSelector = pageKey ? [pageKey, 'plugins', pluginId] : [pluginId];

  try {
    const pluginModel: any = state.stageModel.getPluginModel(pageKey, pluginId);
    const pageId = state.stageModel.getPageId(pageKey);
    if (!pageId) return;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);

    const pluginSelector = pageKey ? [pageKey, 'plugins', pluginId] : [pluginId];
    var appModel = state.stageModel.getModelValue([]);

    const runWhenModelUpdates = pluginModel?.get('runWhenModelUpdates');
    const runWhenPageLoads = pluginModel?.get('runWhenPageLoads');
    const runOnPageFocus = pluginModel?.get('runOnPageFocus');
    const loading = pluginModel?.get('loading');

    if (options?.focusTrigger && !runOnPageFocus) {
      return modelUpdates;
    }
    if (
      !userTriggered &&
      !runWhenModelUpdates &&
      !(pageLoad && runWhenPageLoads) &&
      !(options?.focusTrigger && runOnPageFocus)
    ) {
      return modelUpdates;
    }

    if (loading) return modelUpdates;

    yield put(
      modelUpdateAction([
        {
          selector: pluginSelector.concat('loading'),
          newValue: true,
        },
      ]),
    );

    const datasourceId = pluginConfig?.config.get('datasource', null);
    if (!datasourceId) {
      logger.error(`Could not find datasourceId`);
      return modelUpdates;
    }

    const dsModelValues = appModel.get(datasourceId);
    if (!dsModelValues) {
      logger.error(`Could not find dsModelValues`);
      return modelUpdates;
    }

    const params = pluginModel.get('params').toJS();
    const actionName = pluginModel.get('actionName');

    const actionHandler = dsModelValues.get(actionName);
    const dispatch = getAppDispatch();

    if (!actionHandler) {
      logger.error(`Could not find actionHandler`);
      return modelUpdates;
    }

    let {modelUpdates: actionModelUpdates, eventToBeTriggered} = yield call(
      actionHandler,
      dispatch,
      pluginConfig,
      dsModelValues,
      pluginSelector,
      params,
    );

    if (!eventToBeTriggered) {
      logger.error(`Could not find an event`, eventToBeTriggered);
      return;
    }
    yield call(modelUpdateSaga, actionModelUpdates, null, true);
    yield put(triggerPageEvent(pageKey, pluginId, instance, eventToBeTriggered));
    return;
  } catch (error) {
    yield put(
      modelUpdateAction([
        {
          selector: modelSelector.concat('loading'),
          newValue: false,
        },
      ]),
    );
    yield put(triggerPageEvent(pageKey, pluginId, instance, 'onError'));
    return;
  }
};

export default connectPlugin('MFAuthQuery', null, MFAuthQueryConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
