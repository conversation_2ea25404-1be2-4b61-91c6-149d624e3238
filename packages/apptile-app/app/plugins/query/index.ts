import Immutable from 'immutable';
import _, {isArray} from 'lodash';
import {AppConfig} from 'react-native';
import {call, delay, put, putResolve, select} from 'redux-saga/effects';
import {modelUpdateAction} from 'apptile-core';
import {triggerPageEvent} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import {modelUpdateSaga} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {
  connectPlugin,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  AppPageTriggerOptions,
  PluginModelChange,
  CachePolicy,
} from 'apptile-core';
import {defaultQueryConfig} from '../queryTypes';
import docs from './docs';

export type QueryInputvariables = Record<string, any>;
export type DatasourceQueryDetail = {
  editableInputParams?: Record<string, any>;
  isPaginated?: boolean;
};

export interface DatasourceQueryReturnValue {
  data: any;
  rawData: any;
  hasNextPage?: boolean;
  paginationMeta?: any;
  errors: any;
  hasError: boolean;
}

interface QueryModelConfigParams {
  datasource: string;
  queryName: string;
  inputVariables: QueryInputvariables;
  loading: boolean;
  isPaginated: boolean;
  hasNextPage: boolean;
  pageinationMeta: any;
  prevRunVariables: any;
  cachePolicy: CachePolicy;
}

const defaultQueryPluginConfig: QueryModelConfigParams = {
  datasource: '',
  queryName: '',
  inputVariables: {},
  loading: false,
  isPaginated: false,
  hasNextPage: false,
  pageinationMeta: null,
  prevRunVariables: null,
  cachePolicy: 'no-cache',
};

type QueryModelEventsConfig = {
  executeQuery: string;
  onSuccess: string;
  onError: string;
};

const defaultQueryEventConfig: QueryModelEventsConfig = {
  executeQuery: '',
  onSuccess: '',
  onError: '',
};

const propertySettings: PluginPropertySettings = {
  data: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  inputVariables: {
    updatesProps: ['data', 'rawData'],
  },
  executeQuery: {
    updatesProps: ['onSuccess', 'onError'],
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
  onError: {
    type: EventTriggerIdentifier,
  },
};

const queryPluginConfig = Immutable.fromJS({
  ...defaultQueryConfig,
  ...defaultQueryPluginConfig,
  ...defaultQueryEventConfig,
  loading: false,
});

function* onPluginUpdate(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number | null,
  userTriggered: boolean,
  pageLoad: boolean,
  options: AppPageTriggerOptions,
) {
  let modelUpdates = {
    modelUpdates: [] as PluginModelChange[],
  };
  let modelSelector = pageKey ? [pageKey, 'plugins', pluginId] : [pluginId];
  try {
    // var pageModels = yield select(selectPageModels);
    var pageModels = state.stageModel.getModelValue([]);
    const querySchema = pageModels.getIn(modelSelector) ? pageModels.getIn(modelSelector).toJS() : null;
    if (!querySchema) {
      return modelUpdates;
    }

    const {
      datasource,
      queryName,
      inputVariables,
      transformers,
      runWhenModelUpdates,
      runOnPageFocus,
      runWhenPageLoads,
      loading,
      prevRunVariables,
      paginationMeta: paginationDetails,
      cachePolicy,
    } = querySchema;
    const {getNextPage, inputOptionsVariables} = options ? options : {};

    // logger.info(`Check Query Trigger conditions.`)
    //userTriggered will be true for userTriggered event or direct dipatch action(triggerPageQuery)
    if (options?.focusTrigger && !runOnPageFocus) {
      return modelUpdates;
    }
    if (
      !userTriggered &&
      !runWhenModelUpdates &&
      !(pageLoad && runWhenPageLoads) &&
      !(options?.focusTrigger && runOnPageFocus)
    ) {
      return modelUpdates;
    }
    // logger.info(`User Trigger & Model Trigger conditions passsed.`);
    if (getNextPage && !querySchema.hasNextPage) return modelUpdates;
    // logger.info(`Pagination conditions Passed.`);
    if (loading) return modelUpdates;
    // logger.info(`Loading State conditions Passed.`);

    const appConfig: AppConfig = yield select(selectAppConfig);
    const dsModelValues = pageModels.get(datasource);
    const dsConfig = appConfig.getPlugin(datasource);
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    // logger.info(`[QUERY] Loading ${pluginId}`);
    yield put(
      modelUpdateAction([
        {
          selector: modelSelector.concat('loading'),
          newValue: true,
        },
        {
          selector: modelSelector.concat('errors'),
          newValue: [],
        },
      ]),
    );
    // yield delay(50);
    // logger.info(`[QUERY] Running ${pluginId}`);
    const mergedInputVariables = _.mergeWith({}, inputOptionsVariables, inputVariables, (objValue, srcValue) => {
      if ((typeof objValue === 'boolean') || (typeof objValue === 'number')) {
        return objValue;
      } else if (objValue === undefined) {
        return _.isEmpty(srcValue) ? undefined : srcValue;
      } else {
        return _.isEmpty(objValue) ? srcValue : objValue;
      }
    });
    logger.info("Running query: " + queryName);
    const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
      dsModel.runQuery,
      dsModel,
      dsConfig,
      dsModelValues,
      queryName,
      getNextPage ? prevRunVariables : mergedInputVariables,
      {
        transformers: transformers,
        getNextPage: !!getNextPage,
        paginationMeta: getNextPage ? paginationDetails : undefined,
        cachePolicy: cachePolicy ?? null,
      },
    );
    // logger.info(`[QUERY] Returned ${pluginId}`);
    let dataToUpdate = data;
    if (getNextPage && _.isArray(querySchema.data) && isArray(data)) {
      dataToUpdate = querySchema.data.concat(data);
    }
    modelUpdates.modelUpdates = [
      {
        selector: modelSelector.concat(['data']),
        newValue: dataToUpdate,
      },
      {
        selector: modelSelector.concat(['rawData']),
        newValue: rawData,
      },
      {
        selector: modelSelector.concat(['hasNextPage']),
        newValue: !!hasNextPage,
      },
      {
        selector: modelSelector.concat(['paginationMeta']),
        newValue: paginationMeta,
      },
      {
        selector: modelSelector.concat(['prevRunVariables']),
        newValue: getNextPage ? prevRunVariables : mergedInputVariables,
      },
      {
        selector: modelSelector.concat(['errors']),
        newValue: errors,
      },
      {
        selector: modelSelector.concat(['hasError']),
        newValue: hasError,
      },
      {
        selector: modelSelector.concat('loading'),
        newValue: false,
      },
    ];

    yield call(modelUpdateSaga, modelUpdates.modelUpdates, null);
    // yield put(modelUpdateAction(modelUpdates.modelUpdates));

    if (hasError) {
      yield put(triggerPageEvent(pageKey, pluginId, instance, 'onError'));
    } else {
      yield put(triggerPageEvent(pageKey, pluginId, instance, 'onSuccess'));
    }
    // logger.info(`[QUERY] Finished ${pluginId}`);
    return;
  } catch (error) {
    console.warn(`Query:: ${pluginId} failed with error: ${error}`);
    yield put(
      modelUpdateAction([
        {
          selector: modelSelector.concat('loading'),
          newValue: false,
        },
        {
          selector: modelSelector.concat('errors'),
          newValue: [error],
        },
      ]),
    );
    yield put(triggerPageEvent(pageKey, pluginId, instance, 'onError'));
    return;
  }
}

const editors: PluginEditorsConfig<QueryModelConfigParams> = {
  query: [
    {
      type: 'queryBuilder',
      name: 'query',
      props: {
        label: 'Query Editor',
        editors: [],
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'runWhenModelUpdates',
      props: {
        label: 'Run when model updates',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runWhenPageLoads',
      props: {
        label: 'Run when page loads',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runOnPageFocus',
      props: {
        label: 'Run on page focus',
        checkedValue: false,
      },
    },
    {
      type: 'radioGroup',
      name: 'cachePolicy',
      defaultValue: 'no-cache',
      props: {
        label: 'Cache Policy',
        options: ['cache-first', 'network-only', 'cache-only', 'no-cache'],
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'query',
  type: 'query',
  name: 'Query',
  description: 'Retrieve data from datasources and provide access to this data to other page components.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'query',
};

export default connectPlugin('QueryPlugin', null, Immutable.Map(queryPluginConfig), onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
