import {connectPlugin, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import docs from './docs';
import _ from 'lodash';

interface ApptileColorSwatchConfigParams {
  value: any;
  colorsConfig: any;
  getColorOption: (colorKey: string) => any;
}

export const ApptileColorSwatchConfig: ApptileColorSwatchConfigParams = {
  value: null,
  colorsConfig: '',
  getColorOption: 'function',
};

const fuzzySearchColor = (colorKey: string, colorMapping: any) => {
  let colorHex = colorMapping[colorKey.toLowerCase()] ? colorMapping[colorKey.toLowerCase()] : null;

  if (!colorHex) {
    const derivedColorKey = colorKey.toLowerCase().replace('/', ' ').split(' ').splice(-1).pop();
    if (derivedColorKey) {
      colorHex = _.get(colorMapping, derivedColorKey);
    }
  }

  if (!colorHex) {
    const possibleMatches = colorKey.toLowerCase().replace('/', ' ').split(' ');
    for (let possibleMatch of possibleMatches) {
      colorHex = _.get(colorMapping, possibleMatch);
      if (colorHex) break;
    }
  }
  return colorHex;
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  getColorOption: {
    getValue(model, renderedValue, selector) {
      const colorsConfig = model?.colorsConfig;
      return (colorKey: string) => {
        return fuzzySearchColor(colorKey, colorsConfig);
      };
    },
  },
};

export const shopifyEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'editorSectionHeader',
      name: '',
      props: {
        label: 'Color Swatches',
      },
      isVisibleInV2: true,
      category: 'appSettings',
    },
    {
      type: 'customData',
      name: 'colorsConfig',
      isVisibleInV2: true,
      category: 'appSettings',
      preCompileValue: true,
      compiler: (value: any) => {
        if (typeof value === 'string' && value.startsWith('{{')) {
          value = value.replace('{{', '').trim();
          value = value.slice(0, value.lastIndexOf('}}'));
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.error('Invalid JSON in colorsConfig:', e);
          }
        }
        if (typeof value === 'object' && !Array.isArray(value)) {
          try {
            return Object.keys(value).map(colorName => ({
              colorName,
              colorHex: value[colorName]?.colorHex,
            }));
          } catch (e) {
            console.error('Invalid JSON in colorsConfig:', e);
            return [];
          }
        }
        return value || [];
      },
      props: {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              colorName: {
                type: 'string',
              },
              colorHex: {
                type: 'string',
              },
            },
          },
        },
        label: 'List of Colors',
      },
      postCompileValue: true,
      postCompiler: (value: any) => {
        if (Array.isArray(value)) {
          value = value.reduce((acc, item) => {
            if (item.colorName && item.colorHex) {
              acc[item.colorName.toLowerCase()] = {colorHex: item.colorHex};
            }
            return acc;
          }, {});
        }
        return value;
      },
      reloadOnChange: true,
    },
  ],
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'apptileColorSwatch',
  type: 'datasource',
  name: 'Apptile Color Swatch',
  description: 'Manage color swatches for Product.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'state',
};
const emptyOnupdate = null;

export default connectPlugin('ApptileColorSwatch', null, ApptileColorSwatchConfig, emptyOnupdate, shopifyEditors, {
  propertySettings,
  pluginListing: pluginListing,
  docs,
});
