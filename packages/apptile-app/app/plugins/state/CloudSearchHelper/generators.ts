import {modelUpdateAction} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import {AppConfig} from 'react-native';
import {select, put, call} from 'redux-saga/effects';
import {GetRegisteredPlugin} from 'apptile-core';
import {getPluginModelUpdate} from './actions';
import {getDisplayFilterOptions} from './utils';

export function* fetchProductsGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const searchTerm = model?.get('searchTerm');
  const modelHasNextPage = model?.get('hasNextPage', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;

  const modelPaginationMeta = model?.get('paginationMeta', false);
  const currentFilters = model?.get('selectedFilters', {});
  const sortBy = model?.get('sortBy', 'relevance');
  const modelProducts = model.get('products', []);

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
    suggesting: false,
    ...(!bGetNextPage ? {products: []} : {}),
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'SearchProducts',
    {
      q: searchTerm,
      facet: true,
      offset: 0,
      limits: {products: 24},
      filters: {...currentFilters},
      sort_by: sortBy,
    },
    {
      transformers: undefined,
      getNextPage: bGetNextPage,
      paginationMeta: bGetNextPage ? modelPaginationMeta : undefined,
      cachePolicy: null,
    },
  );

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: bGetNextPage ? modelProducts.concat(data.products) : data.products ?? [],
    previousSuggestedTerm: searchTerm,
    loading: false,
    suggesting: false,
    hasNextPage,
    paginationMeta,
  });
  yield put(modelUpdateAction(modelUpdates, undefined, true));
}

export function* fetchProductFiltersGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const searchTerm = model?.get('searchTerm');
  const currentFilters = model?.get('selectedFilters', {});

  const sortBy = model?.get('sortBy', 'relevance');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
    suggesting: false,
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetProductFilters',
    {
      q: searchTerm,
      facet: true,
      filters: {...currentFilters},
      sort_by: sortBy,
    },
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    filters: data ?? [],
    ...getDisplayFilterOptions(data, []),
  });
  yield put(modelUpdateAction(modelUpdates, undefined, true));
}

export function* fetchSuggestionsGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const value = model?.get('value');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetSuggestions',
    {q: value, limits: {products: 5}},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    // suggestedTerms: data?.suggestions,
    suggestedProducts: data?.products,
    // suggestedCollections: data?.collections,
  });
  yield put(modelUpdateAction(modelUpdates, undefined, true));
}
