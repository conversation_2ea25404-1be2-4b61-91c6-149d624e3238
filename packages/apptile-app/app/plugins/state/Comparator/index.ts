import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  connectPlugin,
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  PluginModelChange,
  AppPageTriggerOptions,
} from 'apptile-core';
import {triggerPageEvent} from 'apptile-core';
import {put} from 'redux-saga/effects';
import _ from 'lodash';
interface comparatorConfigParams {
  onValueSame: () => void;
  onValueChange: () => void;
  equalityCheckMethod: string;
  value: string;
  previousValue: any;
  runWhenModelUpdates: boolean;
  runWhenPageLoads: boolean;
  runOnPageFocus: boolean;
}

const comparatorConfig: comparatorConfigParams = {
  onValueSame: _.identity,
  onValueChange: _.identity,
  value: '',
  equalityCheckMethod: 'Referencial',
  previousValue: '',
  runWhenModelUpdates: false,
  runWhenPageLoads: false,
  runOnPageFocus: false,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue;
    },
  },
  previousValue: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue;
    },
    updatesProps: ['value'],
  },
  onValueChange: {
    type: EventTriggerIdentifier,
  },
  onValueSame: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<comparatorConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
    {
      type: 'radioGroup',
      name: 'equalityCheckMethod',
      defaultValue: 'Referencial',
      props: {
        label: 'Equality Check Method',
        options: ['Referencial', 'Lodash', 'Immutable'],
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'runWhenModelUpdates',
      props: {
        label: 'Run when model updates',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runWhenPageLoads',
      props: {
        label: 'Run when page loads',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runOnPageFocus',
      props: {
        label: 'Run on page focus',
        checkedValue: false,
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'comparator',
  type: 'state',
  name: 'Comparator',
  description: 'Compares previous and current value and triggers events based on the comparison.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'expression',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  const value = model?.get('value');
  const equalityCheckMethod = model?.get('equalityCheckMethod');
  const previousValue = model?.get('previousValue');
  const runWhenModelUpdates = model?.get('runWhenModelUpdates');
  const runWhenPageLoads = model?.get('runWhenPageLoads');
  const runOnPageFocus = model?.get('runOnPageFocus');

  if (options?.focusTrigger && !runOnPageFocus) {
    return;
  }
  if (
    !userTriggered &&
    !runWhenModelUpdates &&
    !(pageLoad && runWhenPageLoads) &&
    !(options?.focusTrigger && runOnPageFocus)
  ) {
    // logger.info(
    //   `Skipping onPluginUpdate ${pluginId} runOnPageFocus: ${runOnPageFocus} focusTrigger: ${options?.focusTrigger}`,
    // );
    return;
  }
  switch (equalityCheckMethod) {
    case 'Referencial':
      if (value === previousValue) {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueSame'));
      } else {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueChange'));
      }
      break;
    case 'Lodash':
      if (_.isEqual(value, previousValue)) {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueSame'));
      } else {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueChange'));
      }
      break;
    case 'Immutable':
      if (value?.equals(previousValue) || value === previousValue) {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueSame'));
      } else {
        yield put(triggerPageEvent(pageKey, pluginId, instance, 'onValueChange'));
      }
      break;
  }
  if (!_.isEqual(value, previousValue)) {
    let pluginUpdateReturn = {
      modelUpdates: [
        {
          selector: modelSelector.concat(['previousValue']),
          newValue: value,
        },
      ] as PluginModelChange[],
    };
    return pluginUpdateReturn;
  }
};
export default connectPlugin('comparatorPlugin', null, comparatorConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
