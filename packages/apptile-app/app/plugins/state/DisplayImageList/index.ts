import Immutable from 'immutable';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {connectPlugin, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import docs from './docs';

interface DisplayImageListModelConfigParams {
  value: any;
}

const DisplayImageListConfig: DisplayImageListModelConfigParams = {
  value: [],
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
};

const editors: PluginEditorsConfig<DisplayImageListModelConfigParams> = {
  basic: [
    {
      type: 'listEditor',
      name: 'value',
      props: {
        label: 'Image List',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'list',
  type: 'state',
  name: 'Display Image-List',
  description: 'Create custom lists for sliders and other display elements.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'image-list',
};
const emptyOnupdate = null;

export default connectPlugin('DisplayImageList', null, DisplayImageListConfig, emptyOnupdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
