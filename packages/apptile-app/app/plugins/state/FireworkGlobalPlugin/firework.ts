import React from "react";
import {Text} from "react-native";

export const FireworkSDK = {
  getInstance: () => ({
    onSDKInit: () => { logger.info('Firework sdk not enabled') },
    markInitCalled: () => { logger.info('Firework sdk not enabled') },
    shopping: {
    },
    navigator: {
      popNativeContainer: () => { logger.info('Firework sdk not enabled') },
      startFloatingPlayer: () => { logger.info('Firework sdk not enabled'); return Promise.resolve(true); },
    }
  })
};

export function VideoFeed() {
    return React.createElement(Text, {}, "Firework sdk not initialized");
}

export function StoryBlock() {
    return React.createElement(Text, {}, "Firework sdk not initialized");
}
