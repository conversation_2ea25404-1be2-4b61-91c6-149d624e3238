export const FireworkSDK = {
  getInstance: () => ({
    onSDKInit: () => { console.log('FireworkSingleton.onSDKInit called'); },
    markInitCalled: () => { console.log('FireworkSingleton.markInitCalled called'); },
    shopping: {
    },
    navigator: {
      popNativeContainer: () => { console.log('FireworkSingleton.markInitCalled called'); },
      startFloatingPlayer: () => { console.log('FireworkSingleton.markInitCalled called'); },
    }
  })
};
