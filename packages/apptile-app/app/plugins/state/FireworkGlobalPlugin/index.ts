import { FireworkSDK } from './firework';
import { 
  connectPlugin, 
  PluginListingSettings, 
  getAppDispatch, 
  navigateToScreen,
  EventTriggerIdentifier,
  RootState,
  triggerPageEvent,
  modelUpdateAction,
  selectStageModel
} from 'apptile-core';
import {put, select} from 'redux-saga/effects';
import _ from 'lodash';

const fireworkSingleton = FireworkSDK.getInstance();
fireworkSingleton.onSDKInit = (event) => {
  logger.info('[FIREWORK] onInitSDK', event);
};

fireworkSingleton.markInitCalled();
async function handleCTAClick(event){
  const dispatch = getAppDispatch();
  
  
  // Navigate to the RN webview of the host app.
  const productId = event.productId;
  const url = event.url;
  const start = url.indexOf('/products/') + '/products/'.length; 
  let end = url.indexOf('?', start);
  if (end < 0) {
    end = url.length;
  }
  const productHandle = url.substring(start, end);
  console.log("clicked product product", {productId, productHandle});
  // const navigateAction = navigateToScreen("Cart", {});
  // const navigateAction = navigateToScreen("variantSelector", {productId, productHandle});
  dispatch(
    modelUpdateAction(
      [
        {
          selector: ['firework', 'fireworkPayload'],
          newValue: { url, productId, productHandle },
        },
      ],
      undefined,
      true,
    )
  );

  return {
    res: 'success',
  };
}

// fireworkSingleton.shopping.onShoppingCTA = handleCTAClick;
// onCustomTapProductCard
// onClickProduct
fireworkSingleton.shopping.onCustomTapProductCard = handleCTAClick;

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number
) {
  const stageModel: any = yield select(selectStageModel);
  const fireworkPayload = stageModel.getModelValue(['firework'])?.get('fireworkPayload') ?? null;
  if (fireworkPayload) {
    // start floating player
    fireworkSingleton.navigator.startFloatingPlayer()
    .then((result: any) =>{;
      if (!result) {
        /* when the result is false, the current fullscreen player may not
         * enable the floating player. In that case, we could call the 
         * following method to close the fullscreen player.
         */
        fireworkSingleton.navigator.popNativeContainer();
      }
    })
    yield put(triggerPageEvent(pageKey, pluginId, instance, 'onShoppingCTAClick'));
  }
}

const FireworkGlobalPluginConfig = {
  value: '',
  fireworkPayload: '',
  onShoppingCTAClick: _.identity
};

const propertySettings = {
  value: {
    getValue: (model, renderedValue) => renderedValue
  },
  fireworkPayload: {
    getValue: (model, renderedValue) => renderedValue,
  },
  onShoppingCTAClick: {
    type: EventTriggerIdentifier
  }
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'firework',
  type: 'datasource',
  name: 'Firework Global Plugin',
  description: 'Firework singleton instance access',
  section: 'Integrations',
  icon: 'datasource',
};

export default connectPlugin('Firework', null, FireworkGlobalPluginConfig, onPluginUpdate, null, {
  propertySettings,
  pluginListing,
  docs: {},
});
