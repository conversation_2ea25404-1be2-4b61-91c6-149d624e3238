import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginEditorsConfig} from 'apptile-core';
import {connectPlugin, onPluginUpdateFn, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import { LocalStorage } from 'apptile-core';

interface LocalStorageModelConfigParams {
  key: string;
  value: any;
}

const defaultLocalStoragePluginConfig: LocalStorageModelConfigParams = {
  key: '',
  value: '',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue === '' ? model?.value : renderedValue;
    },
  },
};

const LocalStoragePluginConfig = Immutable.fromJS({
  ...defaultLocalStoragePluginConfig,
});

const editors: PluginEditorsConfig<LocalStorageModelConfigParams> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'key',
      props: {
        label: 'Key',
        placeholder: '@LocalStorageKey',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'localStorage',
  type: 'state',
  name: 'LocalStorage',
  description: 'Store data as part in Local Storage.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'local-storage',
};

const onPluginUpdate: onPluginUpdateFn = function* (state, pluginId, pageKey, instance, userTriggered, pageLoad) {
  // logger.info(`LocalStoragePlugin::OnPluginUpdate(pluginId: ${pluginId}, pageKey: ${pageKey}, pageLoad: ${pageLoad}`);
  const pageId = state.stageModel.pageKeysToId.get(pageKey);
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  const key = model?.get('key');
  const value = model?.get('value');
  if (pageLoad) {
    const storageValue = yield call(LocalStorage.getValue, key);
    return {
      modelUpdates: [
        {
          selector: [pluginId, 'value'],
          newValue: storageValue,
        },
      ],
    };
  } else {
    yield call(LocalStorage.setValue, key, value);
    return null;
  }
};

export default connectPlugin('LocalStoragePlugin', null, LocalStoragePluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
