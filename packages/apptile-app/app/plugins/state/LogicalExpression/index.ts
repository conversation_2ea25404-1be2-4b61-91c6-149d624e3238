import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  connectPlugin,
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  PluginModelChange,
  AppPageTriggerOptions,
} from 'apptile-core';
import {triggerPageEvent} from 'apptile-core';
import {put, delay} from 'redux-saga/effects';
import _ from 'lodash';
interface LogicalExpressionConfigParams {
  onReturnFalse: () => void;
  onReturnTrue: () => void;
  value: string;
  previousValue: any;
  runWhenModelUpdates: boolean;
  runWhenPageLoads: boolean;
  runOnPageFocus: boolean;
}

const LogicalExpressionConfig: LogicalExpressionConfigParams = {
  onReturnFalse: _.identity,
  onReturnTrue: _.identity,
  value: '',
  previousValue: '',
  runWhenModelUpdates: false,
  runWhenPageLoads: false,
  runOnPageFocus: false,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue === 'false' ||
        renderedValue === '0' ||
        (_.isObjectLike(renderedValue) && _.isEmpty(renderedValue))
        ? false
        : !!renderedValue;
    },
  },
  previousValue: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue === 'false' ||
        renderedValue === '0' ||
        (_.isObjectLike(renderedValue) && _.isEmpty(renderedValue))
        ? false
        : !!renderedValue;
    },
    updatesProps: ['value'],
  },
  onReturnFalse: {
    type: EventTriggerIdentifier,
  },
  onReturnTrue: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<LogicalExpressionConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'runWhenModelUpdates',
      props: {
        label: 'Run when model updates',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runWhenPageLoads',
      props: {
        label: 'Run when page loads',
        checkedValue: false,
      },
    },
    {
      type: 'checkbox',
      name: 'runOnPageFocus',
      props: {
        label: 'Run on page focus',
        checkedValue: false,
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'logicalExpression',
  type: 'state',
  name: 'Expression',
  description: 'Functional Expression plugin',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'expression',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  const value = model?.get('value');
  const previousValue = model?.get('previousValue');
  const runWhenModelUpdates = model?.get('runWhenModelUpdates');
  const runWhenPageLoads = model?.get('runWhenPageLoads');
  const runOnPageFocus = model?.get('runOnPageFocus');

  if (options?.focusTrigger && !runOnPageFocus) {
    return;
  }
  if (
    !userTriggered &&
    !runWhenModelUpdates &&
    !(pageLoad && runWhenPageLoads) &&
    !(options?.focusTrigger && runOnPageFocus)
  ) {
    // logger.info(
    //   `Skipping onPluginUpdate ${pluginId} runOnPageFocus: ${runOnPageFocus} focusTrigger: ${options?.focusTrigger}`,
    // );
    return;
  }
  if (value) {
    if (value !== previousValue || (options?.focusTrigger && runOnPageFocus)) yield put(triggerPageEvent(pageKey, pluginId, instance, 'onReturnTrue'));
  } else {
    if (value !== previousValue || (options?.focusTrigger && runOnPageFocus)) yield put(triggerPageEvent(pageKey, pluginId, instance, 'onReturnFalse'));
  }
  if (value !== previousValue) {
    let pluginUpdateReturn = {
      modelUpdates: [
        {
          selector: modelSelector.concat(['previousValue']),
          newValue: value,
        },
      ] as PluginModelChange[],
    };
    return pluginUpdateReturn;
  }
};
export default connectPlugin('LogicalExpressionPlugin', null, LogicalExpressionConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
