import {modelUpdateAction} from 'apptile-core';
import {RootState} from 'apptile-core';
import _, {filter} from 'lodash';
import {put, spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from 'apptile-core';
import {
  AppPageTriggerOptions,
  connectPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {
  onChooseFilterCategory,
  changeFetchType,
  clearSearch,
  resetFilters,
  applyFilters,
  searchQuery,
  getNextPageForSearch,
  setSortOptions,
  queryWithoutFiltersAndSort,
  queryCollectionWithoutFiltersAndSort,
  onChooseFilterValue,
  queryRandomRecommendations,
} from './actions';

import {fetchSearchResults} from './generators';

interface NostoModelConfigParams {
  //   datasource: string;
  //   value: any;
  //   collectionHandle: string;
  //   collection: any;
  //   filterNames: string[];
  //   products: Record<string, any>[];
  //   selectedFilters: Record<string, string>;
  //   displayFilters: Record<string, string[]>;
}
const NostoProductsSortOptions = [
  {sortFilterName: 'Featured', sortKey: '', sortOrder: ''},
  {sortFilterName: 'Price: Low to High', sortKey: 'price', sortOrder: 'asc'},
  {sortFilterName: 'Price: High to Low', sortKey: 'price', sortOrder: 'desc'},
  {sortFilterName: 'Alphabetically, A-Z', sortKey: 'name', sortOrder: 'asc'},
  {sortFilterName: 'Alphabetical, Z-A', sortKey: 'name', sortOrder: 'desc'},
  {sortFilterName: 'Newest', sortKey: 'datePublished', sortOrder: 'desc'},
  {sortFilterName: 'Oldest', sortKey: 'datePublished', sortOrder: 'asc'},
];

const ShopifyPLPPluginConfig: NostoModelConfigParams = {
  datasource: 'nosto',
  numItems: 24,
  initialNumItems: 8,
  loading: false,
  filters: [],
  filterNames: [],
  selectedFilters: [],
  displayFilterName: '',
  displayFilterValues: [],
  randomNostoProducts: [],
  displayFilterType: '',
  products: [],
  sortOptions: [],
  query: '',
  queryType: '',
  collectionHandle: '',
  collectionId: null,
  _doFetch: false,
  _doNextPage: false,
  fetchType: 'COLLECTION',
  paginationMeta: null,
  hasNextPage: false,
  selectFilter: 'action',
  searchQuery: 'action',
  getNextPageForSearch: 'action',
  selectedSortName: 'Featured',
  sortBy: '',
  sortOrder: '',
  setSortOptions: 'action',
  queryWithoutFiltersAndSort: 'action',
  queryCollectionWithoutFiltersAndSort: 'action',
  onChooseFilterCategory: 'action',
  queryRandomRecommendations: 'action',
  onChooseFilterValue: 'action',
  resetFilters: 'action',
  applyFilters: 'action',
  checkFilters: 'function',
  changeFetchType: 'function',
  clearSearch: 'action',
  getSelectedMinMaxFilter: 'function',
};

const propertySettings: PluginPropertySettings = {
  selectedFilters: {
    updatesProps: ['checkFilters'],
  },
  searchQuery: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return searchQuery;
    },
    actionMetadata: {
      editableInputParams: {
        query: '',
      },
    },
  },
  queryCollectionWithoutFiltersAndSort: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return queryCollectionWithoutFiltersAndSort;
    },
    actionMetadata: {
      editableInputParams: {
        collectionHandle: '',
      },
    },
  },
  queryRandomRecommendations: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return queryRandomRecommendations;
    },
    actionMetadata: {
      editableInputParams: {
        minProducts: 1,
        maxProducts: 10,
      },
    },
  },
  queryWithoutFiltersAndSort: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return queryWithoutFiltersAndSort;
    },
    actionMetadata: {
      editableInputParams: {
        query: '',
        fetchType: '',
      },
    },
  },
  getNextPageForSearch: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return getNextPageForSearch;
    },
  },
  setSortOptions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSortOptions;
    },
    actionMetadata: {
      editableInputParams: {
        sortObj: '',
      },
    },
  },
  onChooseFilterCategory: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return onChooseFilterCategory;
    },
    actionMetadata: {
      editableInputParams: {
        filterName: '',
      },
    },
  },
  onChooseFilterValue: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return onChooseFilterValue;
    },
    actionMetadata: {
      editableInputParams: {
        filterName: '',
        value: '',
        selectedMin: '',
        selectedMax: '',
      },
    },
  },
  resetFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return resetFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },

  applyFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return applyFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },

  changeFetchType: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return changeFetchType;
    },
    actionMetadata: {
      editableInputParams: {
        fetchType: 'SEARCH',
      },
    },
  },

  clearSearch: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return clearSearch;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },

  checkFilters: {
    getValue(model, renderedValue, selector) {
      return currFilterObj => {
        const selectedFilters = model?.selectedFilters;
        const filterValue = currFilterObj.value;
        const filterFieldName = currFilterObj.field;
        const existingFilter = selectedFilters.find(
          selectedFilter => selectedFilter.field === filterFieldName && selectedFilter.value.includes(filterValue),
        );
        if (existingFilter) return true;
        else return false;
      };
    },
  },
  getSelectedMinMaxFilter: {
    getValue(model, renderedValue, selector) {
      return (rangeType: 'min' | 'max') => {
        const selectedFilters = model?.selectedFilters;
        const filters = model?.filters;
        const displayFilterName = model?.displayFilterName;
        const currFilterObj = filters[displayFilterName];
        const filterFieldName = currFilterObj.field;
        const existingFilter = selectedFilters.find(selectedFilter => selectedFilter.field === filterFieldName);

        if (!existingFilter) return '';
        else {
          return existingFilter[rangeType];
        }
      };
    },
  },
};

const editors: PluginEditorsConfig<NostoModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'datasource',
      props: {
        label: 'Nosto Datasource Id',
        placeholder: 'Nosto',
      },
    },
    // {
    //   type: 'codeInput',
    //   name: 'collectionHandle',
    //   props: {
    //     label: 'Collection Handle',
    //     placeholder: '{{currentPage.params?.collectionHandle}}',
    //   },
    // },
    {
      type: 'codeInput',
      name: 'numItems',
      props: {
        label: 'Number of Items',
        placeholder: '24',
      },
    },
    {
      type: 'codeInput',
      name: 'initialNumItems',
      props: {
        label: 'Initial Number of Items to load',
        placeholder: '8',
      },
    },

    {
      type: 'codeInput',
      name: 'collectionHandle',
      props: {
        label: 'Collection Handle',
        placeholder: '{{currentPage.params.collectionHandle}}',
      },
    },
    // {
    //   type: 'dropDown',
    //   name: 'sortBy',
    //   props: {
    //     label: 'Collection sort order',
    //     options: ['BEST_SELLING', 'COLLECTION_DEFAULT', 'CREATED', 'MANUAL', 'PRICE', 'TITLE'],
    //   },
    // },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'NostoHelper',
  type: 'state',
  name: 'Nosto Helper',
  description: 'Nosto Helper For PLP,FILTER,SORT,SEARCH',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};
const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  if (options?.focusTrigger) return;
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [] as PluginModelChange[],
  };
  const collectionHandle = model?.get('collectionHandle');
  const doFetch = model?.get('_doFetch') ?? false;

  if (pageLoad) {
    const fetchType = collectionHandle && collectionHandle.trim() !== '' ? 'COLLECTION' : 'SEARCH_SUGGESTIONS';

    if (fetchType === 'COLLECTION') {
      yield spawn(fetchSearchResults, pluginSelector, state, pluginId, pageKey, fetchType, collectionHandle, pageLoad);
    }
    return {
      modelUpdates: [
        {
          selector: modelSelector.concat(['loading']),
          newValue: fetchType === 'COLLECTION' ? true : false,
        },
        {
          selector: modelSelector.concat(['sortOptions']),
          newValue: NostoProductsSortOptions,
        },
        {
          selector: modelSelector.concat(['selectedSortName']),
          newValue: 'Featured',
        },
        {
          selector: modelSelector.concat(['fetchType']),
          newValue: fetchType,
        },
        {
          selector: modelSelector.concat(['selectedFilters']),
          newValue: [],
        },
      ],
    };
  }

  if (doFetch) {
    const fetchType = model?.fetchType ?? 'SEARCH';
    yield spawn(fetchSearchResults, pluginSelector, state, pluginId, pageKey, fetchType, collectionHandle, pageLoad);
  }

  return pluginUpdateReturn;
};

export default connectPlugin('NostoHelper', null, ShopifyPLPPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs: null,
});
