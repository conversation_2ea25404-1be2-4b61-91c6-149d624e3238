import {ModelChange, PluginConfigType, PluginModelType, Selector, modelUpdateAction} from 'apptile-core';
import _ from 'lodash';

export async function resetFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        selectedFilters: [],
        _doFetch: true,
        _doNextPage: false,
        fetchType: 'SEARCH',
        paginationMeta: null,
        hasNextPage: false,
      }),
      undefined,
      true,
    ),
  );
}

export async function applyFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        _doFetch: true,
        _doNextPage: false,
        fetchType: 'SEARCH',
        paginationMeta: null,
        hasNextPage: false,
      }),
      undefined,
      true,
    ),
  );
}

export async function onChooseFilterCategory(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {filterName} = params;
  const filters = model?.get('filters');

  const displayFilterName = filterName;
  const filterObj = filters[displayFilterName];

  const displayFilterType = filterObj.type;

  const displayFilterValues = getFilterValues(filterObj);
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        displayFilterName,
        displayFilterValues,
        displayFilterType,
      }),
      undefined,
      true,
    ),
  );
}

export async function onChooseFilterValue(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {filterName, value} = params;

  let selectedFilters = model?.get('selectedFilters');
  let displayFilterType = model?.get('displayFilterType');

  if (displayFilterType === 'terms') {
    const existingFilter = selectedFilters.find((filter: any) => filter.field === filterName);

    if (existingFilter) {
      if (existingFilter.value.includes(value))
        existingFilter.value = existingFilter.value.filter(filterValue => filterValue !== value);
      else existingFilter.value.push(value);
    } else {
      selectedFilters.push({field: filterName, value: [value]});
    }
    selectedFilters = selectedFilters.filter(filter => {
      if (!filter?.value) return true;
      if (filter.value?.length !== 0) return true;
      return false;
    });
  }

  if (displayFilterType === 'stats') {
    const {selectedMin, selectedMax} = params;
    if (!selectedMin && !selectedMax) return;
    const rangeObj = {};
    if (selectedMin) rangeObj.gte = selectedMin.toString();
    if (selectedMax) rangeObj.lte = selectedMax.toString();
    const existingFilter = selectedFilters.find((filter: any) => filter.field === filterName);
    if (existingFilter) {
      existingFilter.range = [rangeObj];
    } else {
      selectedFilters.push({field: filterName, range: [rangeObj]});
    }
  }

  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        selectedFilters,
      }),
      undefined,
      true,
    ),
  );
}

export async function queryWithoutFiltersAndSort(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {query, fetchType = 'SEARCH'} = params;

  //!Scope for optimisation when selected Filters is already null
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        query,
        selectedFilters: [],
        _doFetch: true,
        _doNextPage: false,
        fetchType,
        sortBy: '',
        sortOrder: '',
        selectedSortName: 'Featured',
        paginationMeta: null,
        hasNextPage: false,
      }),
      undefined,
      true,
    ),
  );
}

export async function queryRandomRecommendations(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  const {minProducts, maxProducts} = params;
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        minRandomProducts: minProducts,
        maxRandomProducts: maxProducts,
      }),
      undefined,
      true,
    ),
  );
}

export async function queryCollectionWithoutFiltersAndSort(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {collectionHandle} = params;
  //!Scope for optimisation when selected Filters is already null
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        query: null,
        collectionHandle,
        selectedFilters: [],
        _doFetch: true,
        _doNextPage: false,
        fetchType: 'COLLECTION',
        sortBy: '',
        sortOrder: '',
        selectedSortName: 'Featured',
        paginationMeta: null,
        hasNextPage: false,
      }),
      undefined,
      true,
    ),
  );
}

export async function getNextPageForSearch(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  dispatch(modelUpdateAction(getPluginModelUpdate(selector, {_doFetch: true, _doNextPage: true}), undefined, true));
}

export async function setSortOptions(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {sortObj} = params;
  const {sortFilterName, sortKey = '', sortOrder = ''} = sortObj;
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        sortOrder,
        sortBy: sortKey,
        selectedSortName: sortFilterName,
        _doFetch: true,
        _doNextPage: false,
      }),
      undefined,
      true,
    ),
  );
}

export async function querySearch(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {query} = params;

  //!Scope for optimisation when selected Filters is already same
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {query, _doFetch: true, paginationMeta: null, hasNextPage: false}),
      undefined,
      true,
    ),
  );
}

export async function searchQuery(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {query} = params;
  const _doFetch = true;

  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {query, _doFetch, paginationMeta: null, hasNextPage: false}),
      undefined,
      true,
    ),
  );
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}

export function getFilterValues(filterObj) {
  if (!filterObj) return [];

  const field = filterObj.field;

  const fieldKeys = filterObj.fieldKeys;

  const type = filterObj.type;

  if (type === 'stats') return {min: filterObj.min, max: filterObj.max};

  return fieldKeys.map(fieldKey => ({...fieldKey, field}));
}

export async function changeFetchType(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {fetchType} = params;

  //!Scope for optimisation when selected Filters is already same
  dispatch(modelUpdateAction(getPluginModelUpdate(selector, {fetchType}), undefined, true));
}

export async function clearSearch(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {query} = params;

  //!Scope for optimisation when selected Filters is already same
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        query: null,
        _doFetch: false,
        _doNextPage: false,
        products: [],
        selectedFilters: [],
        filterNames: [],
        filters: {},
        displayFilterValues: [],
        sortBy: '',
        paginationMeta: null,
        hasNextPage: false,
        fetchType: 'SEARCH_SUGGESTIONS',
      }),
      undefined,
      true,
    ),
  );
}
