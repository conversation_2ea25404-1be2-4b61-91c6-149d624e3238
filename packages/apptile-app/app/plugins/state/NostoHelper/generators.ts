import {call, put, select, delay, retry, spawn} from 'redux-saga/effects';
import {GetRegisteredPlugin} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {getFilterValues} from './actions';
import _ from 'lodash';

export function* fetchSearchResults(
  pluginSelector: any,
  state: RootState,
  pluginId: string,
  pageKey: string,
  fetchType: 'SEARCH' | 'COLLECTION',
  collectionHandle,
  pageLoad,
) {
  let collectionId = null;

  if (fetchType === 'COLLECTION' && pageLoad)
    collectionId = yield call(queryShopifyDataSource, pluginSelector, pluginId, pageKey, collectionHandle);

  yield call(queryNostoDataSource, pluginSelector, pluginId, pageKey, pageLoad, fetchType, collectionId);
}

export function* queryShopifyDataSource(pluginSelector, pluginId, pageKey, collectionHandle) {
  const state = yield select();
  const datasource = 'shopify';
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(datasource);
  const appConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin('shopify');
  const dsType = dsModelValues.get('pluginType');

  const dsModel = GetRegisteredPlugin(dsType);

  const NostoHelperModel = state.stageModel.getModelValue([pluginId]);

  const {
    data: transformedData,
    errors,
    hasError,
  } = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetCollectionDetailsByHandle',
    {collectionHandle},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );

  const idSplitArray = transformedData.id.split('/');
  const collectionId = idSplitArray[idSplitArray.length - 1];

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    collectionId,
  });

  yield put(modelUpdateAction(modelUpdates));

  return collectionId;
}

export function* queryNostoDataSource(pluginSelector, pluginId, pageKey, pageload, fetchType, collectionId) {
  const state = yield select();
  const datasource = 'nosto';
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(datasource);
  const appConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin('Nosto');
  const dsType = dsModelValues.get('pluginType');

  const loadingModelUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
  });

  yield put(modelUpdateAction(loadingModelUpdates));
  const dsModel = GetRegisteredPlugin(dsType);

  const NostoHelperModel = state.stageModel.getPluginModel(pageKey, pluginId);

  const searchPageItems = NostoHelperModel?.get('numItems', 24);
  const suggestionPageItems = NostoHelperModel?.get('initialNumItems');
  const doNextPage = NostoHelperModel?.get('_doNextPage', false);
  const sortBy = NostoHelperModel?.get('sortBy', '');
  const sortOrder = NostoHelperModel?.get('sortOrder', '');
  const size = fetchType === 'SEARCH' ? searchPageItems : suggestionPageItems;

  const filters = NostoHelperModel?.get('selectedFilters');
  const existingProducts = NostoHelperModel?.get('products', []);
  const paginationMeta = doNextPage ? NostoHelperModel?.get('paginationMeta', null) : null;
  const hasNextPage = doNextPage ? NostoHelperModel?.get('hasNextPage', null) : false;
  const accountId = dsModelValues?.get('accountId');
  const query = fetchType !== 'COLLECTION' ? NostoHelperModel?.get('query') : null;

  collectionId = pageload ? collectionId : NostoHelperModel?.get('collectionId');

  const {
    data: transformedData,
    errors,
    hasError,
    hasNextPage: isThereANextPage,
    paginationMeta: nextPageMeta,
  } = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'Search',
    {query, accountId, size, sortBy, sortOrder, collectionId, filter: filters},
    {
      transformers: undefined,
      getNextPage: hasNextPage,
      paginationMeta,
      cachePolicy: null,
    },
  );

  if (_.isEmpty(transformedData.products)) {
    yield call(queryNostoRandomRecommendations, pluginSelector, pluginId, pageKey);
  }

  const filterNames = Object.keys(transformedData.filters);

  let displayFilterValues = [];
  let displayFilterName = '';
  let displayFilterType = '';

  if (filterNames.length) {
    displayFilterName = filterNames[0];
    displayFilterValues = getFilterValues(transformedData.filters[displayFilterName]);
    displayFilterType = transformedData.filters[displayFilterName].type;
  }
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: doNextPage ? [...existingProducts, ...transformedData.products] : transformedData.products,
    filters: transformedData.filters,
    filterNames,
    loading: false,
    displayFilterName,
    displayFilterValues,
    hasNextPage: isThereANextPage,
    paginationMeta: nextPageMeta,
    _doFetch: false,
    _doNextPage: false,
    displayFilterType,
  });

  yield put(modelUpdateAction(modelUpdates));
}

//For recommendations
export function* queryProductsFromShopifyDatasource(pluginSelector, productIds: string[]) {
  const state = yield select();
  const datasource = 'shopify';
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(datasource);
  const appConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin('shopify');
  const dsType = dsModelValues.get('pluginType');

  const dsModel = GetRegisteredPlugin(dsType);

  const {
    data: transformedData,
    errors,
    hasError,
  } = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetProductByIds',
    {productIds},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    randomNostoProducts: transformedData,
  });

  yield put(modelUpdateAction(modelUpdates));
}
export function* queryNostoRandomRecommendations(pluginSelector, pluginId, pageKey) {
  const state = yield select();
  const datasource = 'nosto';
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(datasource);
  const appConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin('nosto');
  const dsType = dsModelValues.get('pluginType');

  const loadingModelUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
  });

  yield put(modelUpdateAction(loadingModelUpdates));
  const dsModel = GetRegisteredPlugin(dsType);

  const NostoHelperModel = state.stageModel.getPluginModel(pageKey, pluginId);

  const minRandomProducts = NostoHelperModel?.get('minRandomProducts', 1);
  const maxRandomProducts = NostoHelperModel?.get('maxRandomProducts', 10);

  const {
    data: transformedData,
    errors,
    hasError,
    hasNextPage: isThereANextPage,
    paginationMeta: nextPageMeta,
  } = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'RandomRecommendations',
    {minProducts: minRandomProducts, maxProducts: maxRandomProducts},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: null,
      cachePolicy: null,
    },
  );

  const convertedProductIds = transformedData.map(productObj => {
    return `gid://shopify/Product/${productObj?.productId}`;
  });

  yield call(queryProductsFromShopifyDatasource, pluginSelector, convertedProductIds);
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): any {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}
