import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {connectPlugin, PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {triggerPageEvent, triggerPageQuery} from 'apptile-core';
import _ from 'lodash';
import { store } from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {getPluginSelector} from 'apptile-core';

interface StateModelConfigParams {
  enabled: boolean;
  value: number;
  onPollTrigger: string;
}

const TimerPluginConfig: StateModelConfigParams = {
  enabled: false,
  value: 5000,
  onPollTrigger: '',
};

const propertySettings: PluginPropertySettings = {
  onPollTrigger: {
    type: EventTriggerIdentifier,
  },
  enabled: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue === 'false' ||
        renderedValue === '0' ||
        (_.isObjectLike(renderedValue) && _.isEmpty(renderedValue))
        ? false
        : !!renderedValue;
    },
  },
  value: {
    getValue: (model, val, sel) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
};

const editors: PluginEditorsConfig<StateModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'enabled',
      props: {
        label: 'Enabled',
        placeholder: 'false',
      },
    },
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: 5000,
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'poll',
  type: 'state',
  name: 'Poll',
  description: 'Poll plugin',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'timer',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  const enabled = model?.get('enabled');
  const value = model?.get('value');
  var currentPoll = model?.get('currentPoll');
  const pluginSelector = getPluginSelector(pageKey, pluginId).concat(instance ? [instance] : []);
  const dispatch = store.dispatch;
  logger.info('Poll::onPluginUpdate', enabled, value, currentPoll);
  if (enabled && !currentPoll) {
    currentPoll = setInterval(() => {
      dispatch(triggerPageEvent(pageKey, pluginId, instance, 'onPollTrigger'));
    }, value);
    return {
      modelUpdates: [
        {
          selector: [pluginId, 'currentPoll'],
          newValue: currentPoll,
        },
      ],
    };
  }else if(!enabled && currentPoll){
    clearInterval(currentPoll);
  }
};
export default connectPlugin('PollPlugin', null, TimerPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
