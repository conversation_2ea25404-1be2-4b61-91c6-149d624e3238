import {modelUpdateAction} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import _ from 'lodash';
import {put, spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  connectPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {
  applyFilters,
  deselectFilter,
  getNextPage,
  getPluginModelUpdate,
  getSearchProducts,
  resetFilters,
  selectFilter,
  selectFilterCategory,
  setFilterPriceMax,
  setFilterPriceMin,
  setSortOptions,
} from './actions';
import docs from './docs';
import {
  fetchCollectionDetailsGenerator,
  fetchProductFiltersGenerator,
  fetchProductsGenerator,
  fetchSearchProductFiltersGenerator,
  fetchSearchProductsGenerator,
} from './generators';

interface SearchFilterPLPModelConfigParams {
  datasource: string;
  value: any;
  collectionHandle: string;
  collection: any;
  filterNames: string[];
  products: Record<string, any>[];
  selectedFilters: Record<string, string>;
  displayFilters: Record<string, string[]>;
}
const SearchFilterProductsSortOptions = [
  {
    label: 'Relavance',
    sortBy: 'RELEVANCE',
    sortReverse: false,
  },
  {
    label: 'Best Selling',
    sortBy: 'BEST_SELLING',
    sortReverse: false,
  },
  {
    label: 'Price - Low to High',
    sortBy: 'PRICE',
    sortReverse: false,
  },
  {
    label: 'Price - High to Low',
    sortBy: 'PRICE',
    sortReverse: true,
  },
];

const SearchFiltersPluginConfig: SearchFilterPLPModelConfigParams = {
  datasource: 'shopify',
  numItems: 24,
  initialNumItems: 8,
  value: '',
  collectionHandle: '',
  collection: '',
  products: [],
  _doFetch: '',
  doNextPage: '',
  loading: '',
  filtersLoading: '',
  sortBy: 'RELEVANCE',
  sortReverse: '',
  sortOptions: '',
  filters: [],
  filterNames: [],
  selectedFilters: [],
  filtersById: '',
  filterValuesById: '',
  selectedFilterId: '',
  selectedFilterIdValues: [],
  filterPriceMin: '',
  filterPriceMax: '',
  displayFilters: '',
  hasNextPage: '',
  paginationMeta: '',
  searchedProducts: '',
  getNextPage: 'action',
  selectFilter: 'action',
  deselectFilter: 'action',
  getSearchProducts: 'action',
  setFilterPriceMin: 'action',
  setFilterPriceMax: 'action',
  selectFilterCategory: 'action',
  applyFilters: 'action',
  resetFilters: 'action',
  setSortOptions: 'action',
};

const ShopifyProductsSortOptions = [
  {
    label: 'Best selling',
    sortBy: 'BEST_SELLING',
    sortReverse: false,
  },
  {
    label: 'Price - Low to High',
    sortBy: 'PRICE',
    sortReverse: false,
  },
  {
    label: 'Price - High to Low',
    sortBy: 'PRICE',
    sortReverse: true,
  },
  {
    label: 'Newly Added',
    sortBy: 'CREATED',
    sortReverse: true,
  },
];

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  numItems: {
    getValue: (model, val) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
  sortOptions: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? ShopifyProductsSortOptions : renderedValue;
    },
  },
  selectedFilters: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? [] : renderedValue;
    },
  },
  getNextPage: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return getNextPage;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  selectFilterCategory: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilterCategory;
    },
    actionMetadata: {
      editableInputParams: {
        filterId: '',
      },
    },
  },
  selectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
      },
    },
  },
  deselectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return deselectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
      },
    },
  },
  getSearchProducts: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return getSearchProducts;
    },
    actionMetadata: {
      editableInputParams: {
        query: 'frontpage',
        sortKey: 'BEST_SELLING',
        reverse: false,
        first: 10,
        after: 0,
      },
    },
  },
  setFilterPriceMin: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setFilterPriceMin;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
        value: '',
      },
    },
  },
  setFilterPriceMax: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setFilterPriceMax;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
        value: '',
      },
    },
  },
  applyFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return applyFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  resetFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return resetFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  setSortOptions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSortOptions;
    },
    actionMetadata: {
      editableInputParams: {
        sortBy: '',
        sortReverse: '',
      },
    },
  },
};

const editors: PluginEditorsConfig<SearchFilterPLPModelConfigParams> = {
  basic: [
    {
      type: 'checkbox',
      name: 'unavailableProducts',
      props: {
        label: 'Sort by unavailable products',
        placeholder: '',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'SearchFilters',
  type: 'state',
  name: 'Search Filters Helper',
  description: 'Search Filters helper plugin.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};
const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  if (options?.focusTrigger) return;
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  let pluginUpdateReturn = {
    modelUpdates: [] as PluginModelChange[],
  };
  const bDoFetch = model?.get('_doFetch', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const modelHasNextPage = model?.get('hasNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;
  if (bDoFetch) {
    yield spawn(fetchSearchProductsGenerator, state, pluginId, pageKey, instance);
    yield spawn(fetchSearchProductFiltersGenerator, state, pluginId, pageKey, instance);
  }
  return pluginUpdateReturn;
};

export default connectPlugin('SearchFiltersPlugin', null, SearchFiltersPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
