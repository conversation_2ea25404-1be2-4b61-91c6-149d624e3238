import {modelUpdateAction, setActiveBundle} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import {AppConfig, InteractionManager} from 'react-native';
import {select, put, call} from 'redux-saga/effects';
import {GetRegisteredPlugin} from 'apptile-core';
import {getPluginModelUpdate} from './actions';
import {getFilterRestrictions, getDisplayFilterOptions} from './utils';
import {getNavigationContext} from 'apptile-core';

/* export function* fetchProductsGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  numItems: number = 8,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const modelHasNextPage = model?.get('hasNextPage', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;

  const modelPaginationMeta = model?.get('paginationMeta', false);
  const currentFilters = model?.get('selectedFilters', []);
  const allFilterValues = model?.get('filterValuesById', {});
  const sortBy = model?.get('sortBy', 'relevance');
  const sortReverse = model?.get('sortReverse', false);
  const filterPriceMin = model?.get('filterPriceMin', 0);
  const filterPriceMax = model?.get('filterPriceMax', '');
  const modelProducts = model.get('products', []);
  const filterParams = getFilterRestrictions(
    allFilterValues,
    currentFilters,
    sortBy,
    sortReverse,
    filterPriceMin,
    filterPriceMax,
  );

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
    ...(!bGetNextPage ? {products: []} : {}),
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetCollectionProductsByHandle',
    {
      collectionHandle,
      first: numItems || 24,
      ...filterParams,
    },
    {
      transformers: undefined,
      getNextPage: bGetNextPage,
      paginationMeta: bGetNextPage ? modelPaginationMeta : undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: bGetNextPage ? modelProducts.concat(data) : data ?? [],
    loading: false,
    hasNextPage,
    paginationMeta,
  });
  yield put(modelUpdateAction(modelUpdates));
}

export function* fetchCollectionDetailsGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    collection: {},
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetCollectionDetailsByHandle',
    {collectionHandle},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {collection: data});
  const currentNavContext = getNavigationContext();
  if (currentNavContext?.getState()?.type !== 'tab') currentNavContext?.setOptions({title: data?.title});
  yield put(modelUpdateAction(modelUpdates));
}

export function* fetchProductFiltersGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  yield call(InteractionManager.runAfterInteractions);
  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    filtersLoading: true,
    suggesting: false,
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetProductFilters',
    {
      collectionHandle,
    },
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    filters: data ?? [],
    filtersLoading: false,
    ...getDisplayFilterOptions(data, []),
  });
  yield put(modelUpdateAction(modelUpdates));
} */

export function* fetchSearchProductsGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  numItems: number = 8,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);
  const modelHasNextPage = model?.get('hasNextPage', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;

  const modelPaginationMeta = model?.get('paginationMeta', false);
  const currentFilters = model?.get('selectedFilters', []);
  const allFilterValues = model?.get('filterValuesById', {});
  const sortReverse = model?.get('sortReverse', false);
  const searchQuery = model?.get('query', '');
  const sortKey = model?.get('sortKey', 'RELEVANCE');
  const first = model?.get('first', 10);
  const after = model?.get('after', '');
  const filterPriceMin = model?.get('filterPriceMin', 0);
  const filterPriceMax = model?.get('filterPriceMax', '');
  const modelProducts = model.get('products', []);
  const unavailableProducts = model?.get('unavailableProducts', false);
  const filterParams = getFilterRestrictions(
    allFilterValues,
    currentFilters,
    sortKey,
    sortReverse,
    filterPriceMin,
    filterPriceMax,
  );

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(
    pluginSelector,
    {
      loading: true,
      ...(!bGetNextPage ? {products: []} : {}),
      _doFetch: false,
      doNextPage: false,
    },
  );
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'SearchProductsWithFilters',
    {
      query: searchQuery,
      sortKey: sortKey,
      reverse: sortReverse,
      first: first,
      after: after,
      productFilters: filterParams.filters,
      unavailableProducts: unavailableProducts ? 'LAST' : 'SHOW',
      // ...filterParams,
    },
    {
      transformers: undefined,
      getNextPage: bGetNextPage,
      paginationMeta: bGetNextPage ? modelPaginationMeta : undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: bGetNextPage ? modelProducts.concat(data) : data ?? [],
    loading: false,
    hasNextPage,
    paginationMeta,
  });
  yield put(modelUpdateAction(modelUpdates));
}

export function* fetchSearchProductFiltersGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  yield call(InteractionManager.runAfterInteractions);
  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);
  let searchQuery = model?.get('query', '');
  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    filtersLoading: true,
    suggesting: false,
  });

  // const modelPaginationMeta = model?.get('paginationMeta', false);
  const currentFilters = model?.get('selectedFilters', []);
  const allFilterValues = model?.get('filterValuesById', {});
  // const sortBy = model?.get('sortBy', 'RELEVANCE');
  const sortReverse = model?.get('sortReverse', false);
  const sortKey = model?.get('sortKey', 'RELEVANCE');
  // const after = model?.get('after', '');
  const filterPriceMin = model?.get('filterPriceMin', 0);
  const filterPriceMax = model?.get('filterPriceMax', '');
  // const modelProducts = model.get('products', []);
  const filterParams = getFilterRestrictions(
    allFilterValues,
    currentFilters,
    sortKey,
    sortReverse,
    filterPriceMin,
    filterPriceMax,
  );

  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetSearchFilters',
    {
      query: searchQuery,
      first: 100,
      productFilters: [],
    },
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    filters: data?.search.productFilters ?? [],
    filtersLoading: false,
    ...getDisplayFilterOptions(data?.search.productFilters, []),
  });
  yield put(modelUpdateAction(modelUpdates));
}
