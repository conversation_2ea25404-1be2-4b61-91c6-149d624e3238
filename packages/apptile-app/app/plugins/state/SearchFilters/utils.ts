import _ from 'lodash';

export function getDisplayFilterOptions(filters: Array<Record<string, any>>, blacklistedAttrs: string[]) {
  const displayFilters = _.omitBy(filters, (val, key) => blacklistedAttrs?.includes(val?.id));
  const filtersById = _.keyBy(displayFilters, val => val?.id);
  let filterValuesById = {};
  _.forIn(filtersById, (filterCat, filterId) => {
    _.forEach(filterCat?.values, (filterVal, key) => {
      try {
        _.set(filterValuesById, [filterVal?.id], JSON.parse(filterVal?.input));
      } catch (e) {
        logger.info(e);
      }
    });
  });
  const selectedFilterId = displayFilters[0]?.id;
  const selectedFilterIdValues = filtersById[selectedFilterId]?.values;
  return {displayFilters, filtersById, selectedFilterId, selectedFilterIdValues, filterValuesById};
}

export function getFilterRestrictions(
  allFilters: Record<string, any>,
  selectedFilters: Array<string>,
  sortBy: string,
  sortReverse: boolean,
  priceMin: string | number,
  priceMax: string | number,
): Record<string, any> {
  let filterValues: any[] = [];
  _.forEach(selectedFilters, val => {
    const filterVal = allFilters[val];
    if (!_.isEmpty(filterVal)) {
      if (filterVal.hasOwnProperty('price')) {
        filterVal.price.min = priceMin ? _.toSafeInteger(priceMin) : 0;
        filterVal.price.max = priceMax ? _.toSafeInteger(priceMax) : _.toSafeInteger(Number.MAX_SAFE_INTEGER);
      }
      filterValues.push(filterVal);
    }
  });
  return {filters: filterValues, sortKey: sortBy, reverse: !!sortReverse};
}
