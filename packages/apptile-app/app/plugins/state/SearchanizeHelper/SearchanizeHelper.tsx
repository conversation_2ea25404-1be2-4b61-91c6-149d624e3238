import {modelUpdateAction} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import _ from 'lodash';
import {put, spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  connectPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {
  applyFilters,
  deselectFilter,
  getNextPage,
  getPluginModelUpdate,
  resetFilters,
  searchProducts,
  selectFilter,
  selectFilterAttribute,
  setSortOptions,
} from './actions';
import docs from './docs';
import {fetchProductFiltersGenerator, fetchProductsGenerator, fetchSuggestionsGenerator} from './generators';

interface SearchanizeHelperModelConfigParams {
  datasource: string;
  value: string;
  previousSuggestedTerm: string;
  searchTerm: string;
  doSearch: boolean;
  loading: boolean;
  suggesting: boolean;
  products: Array<Record<string, any>>;
  suggestedTerms: string[];
  suggestedProducts: Array<Record<string, any>>;
  suggestedCollections: Array<Record<string, any>>;
  searchProducts: string;
}

const SearchanizeSortOptions = [
  {
    label: 'Relevance',
    sortBy: 'relevance',
    sortOrder: 'asc',
  },
  {
    label: 'Price - Low to High',
    sortBy: 'price',
    sortOrder: 'asc',
  },
  {
    label: 'Price - High to Low',
    sortBy: 'price',
    sortOrder: 'desc',
  },
  {
    label: 'Best selling',
    sortBy: 'sales_amount',
    sortOrder: 'desc',
  },
];

const SearchanizeHelperPluginConfig: SearchanizeHelperModelConfigParams = {
  datasource: 'searchanize',
  value: '',
  previousSuggestedTerm: '',
  searchTerm: '',
  doSearch: '',
  doNextPage: '',
  loading: '',
  suggesting: '',
  sortBy: 'relevance',
  sortOrder: 'asc',
  sortOptions: '',
  products: [],
  suggestedTerms: [],
  suggestedProducts: [],
  suggestedCollections: [],
  blacklistedFilters: [],
  selectedFilters: '',
  filters: [],
  displayFilters: [],
  filtersByAttribute: '',
  selectedFilterAttribute: '',
  selectedFilterAttributeValues: [],
  hasNextPage: '',
  paginationMeta: '',
  getNextPage: 'action',
  searchProducts: 'action',
  selectFilter: 'action',
  deselectFilter: 'action',
  selectFilterAttribute: 'action',
  applyFilters: 'action',
  resetFilters: 'action',
  setSortOptions: 'action',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  selectedFilters: {
    getValue: (model, renderedValue) => {
      return typeof renderedValue !== 'object' ? {} : renderedValue;
    },
  },
  sortOptions: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? SearchanizeSortOptions : renderedValue;
    },
  },
  searchProducts: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return searchProducts;
    },
    actionMetadata: {
      editableInputParams: {
        term: '',
      },
    },
  },
  getNextPage: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return getNextPage;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  selectFilterAttribute: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilterAttribute;
    },
    actionMetadata: {
      editableInputParams: {
        attribute: '',
      },
    },
  },
  selectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        attribute: '',
        value: '',
      },
    },
  },
  deselectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return deselectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        attribute: '',
        value: '',
      },
    },
  },
  applyFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return applyFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  resetFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return resetFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  setSortOptions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSortOptions;
    },
    actionMetadata: {
      editableInputParams: {
        sortBy: '',
        sortOrder: '',
      },
    },
  },
};

const editors: PluginEditorsConfig<SearchanizeHelperModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{query1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'datasource',
      props: {
        label: 'Searchanize Datasource Id',
        placeholder: 'searchanize',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'SearchanizeHelper',
  type: 'state',
  name: 'Searchanize Helper',
  description: 'Searchanize helper plugin.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [
      // {
      //   selector: modelSelector.concat(['previousSuggestedTerm']),
      //   newValue: value,
      // },
    ] as PluginModelChange[],
  };
  const value = model?.get('value');
  const previousValue = model?.get('previousSuggestedTerm');
  const searchTerm = model?.get('searchTerm');
  const bDoSearch = model?.get('doSearch', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const isLoadingSearchResults = model?.get('loading', false);
  if ((pageLoad || value !== previousValue) && !bDoSearch && !isLoadingSearchResults) {
    yield spawn(fetchSuggestionsGenerator, state, pluginId, pageKey, instance);
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['doSearch']),
      newValue: false,
    });
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['loading']),
      newValue: false,
    });
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['suggesting']),
      newValue: true,
    });
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['previousSuggestedTerm']),
      newValue: value,
    });
  }
  const modelHasNextPage = model?.get('hasNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;
  if (bDoSearch && searchTerm) {
    yield spawn(fetchProductsGenerator, state, pluginId, pageKey, instance);
    if (!bGetNextPage) {
      yield spawn(fetchProductFiltersGenerator, state, pluginId, pageKey, instance);
    }
    const modelUpdates = getPluginModelUpdate(pluginSelector, {
      doSearch: false,
      doNextPage: false,
      loading: true,
      suggesting: false,
    });
    yield put(modelUpdateAction(modelUpdates));
  }
  return pluginUpdateReturn;
};

export default connectPlugin('SearchanizeHelper', null, SearchanizeHelperPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
