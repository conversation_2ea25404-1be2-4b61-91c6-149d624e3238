import {modelUpdateAction} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import {ModelChange, Selector} from 'apptile-core';
import _ from 'lodash';
import {PluginModelType} from 'apptile-core';

export interface SearchanizeSearchProductActionParams {
  term: string;
}

export async function searchProducts(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SearchanizeSearchProductActionParams,
) {
  const {term} = params;
  dispatch(modelUpdateAction(getPluginModelUpdate(selector, {searchTerm: term, doSearch: true}), undefined, true));
}

export async function getNextPage(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  dispatch(modelUpdateAction(getPluginModelUpdate(selector, {doSearch: true, doNextPage: true}), undefined, true));
}

export async function applyFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  dispatch(modelUpdateAction(getPluginModelUpdate(selector, {doSearch: true, doNextPage: false}), undefined, true));
}
export async function resetFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {doSearch: true, doNextPage: false, selectedFilters: {}}),
      undefined,
      true,
    ),
  );
}
export async function setSortOptions(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: {sortBy: string; sortOrder: string},
) {
  const {sortBy, sortOrder} = params;
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {doSearch: true, doNextPage: false, sortBy, sortOrder}),
      undefined,
      true,
    ),
  );
}

export interface SearchanizeSelectFilterAttributeActionParams {
  attribute: string;
}
export async function selectFilterAttribute(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SearchanizeSelectFilterAttributeActionParams,
) {
  const {attribute} = params;
  if (attribute !== undefined) {
    const filtersByAttribute = model.get('filtersByAttribute');
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {
          selectedFilterAttributeValues: filtersByAttribute[attribute]?.values,
          selectedFilterAttribute: attribute,
        }),
        undefined,
        true,
      ),
    );
  }
}

function arrayMerge(objValue, srcValue) {
  if (_.isArray(objValue)) {
    return _.uniq(objValue.concat(srcValue));
  }
}
export interface SearchanizeSelectFilterActionParams {
  attribute: string;
  value: string;
}
export async function selectFilter(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SearchanizeSelectFilterActionParams,
) {
  const {attribute, value} = params;
  if (attribute !== undefined && value !== undefined) {
    var selectedFilters = model.get('selectedFilters', {});
    const newSelectedFilters = _.mergeWith({...selectedFilters}, {[attribute]: [value]}, arrayMerge);
    dispatch(
      modelUpdateAction(getPluginModelUpdate(selector, {selectedFilters: {...newSelectedFilters}}), undefined, true),
    );
  }
}
export async function deselectFilter(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SearchanizeSelectFilterActionParams,
) {
  const {attribute, value} = params;
  if (attribute !== undefined && value !== undefined) {
    var selectedFilters = model.get('selectedFilters', {});
    if (_.isArray(selectedFilters[attribute])) {
      _.remove(selectedFilters[attribute], v => v == value);
    }
    dispatch(
      modelUpdateAction(getPluginModelUpdate(selector, {selectedFilters: {...selectedFilters}}), undefined, true),
    );
  }
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}
