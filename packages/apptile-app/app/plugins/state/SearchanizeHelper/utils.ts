import _ from 'lodash';

export function getDisplayFilterOptions(filters: Array<Record<string, any>>, blacklistedAttrs: string[]) {
  const displayFilters = _.omitBy(filters, (val, key) => blacklistedAttrs?.includes(val?.attribute));
  const filtersByAttribute = _.keyBy(displayFilters, val => val?.attribute);
  const selectedFilterAttribute = displayFilters[0]?.attribute;
  const selectedFilterAttributeValues = filtersByAttribute[selectedFilterAttribute]?.values;
  return {displayFilters, filtersByAttribute, selectedFilterAttribute, selectedFilterAttributeValues};
}

export function getFilterRestrictions(
  selectedFilters: Record<string, Array<string>>,
  sortBy: string,
  sortOrder: string,
): Record<string, string> {
  let filterParams = _.transform(
    selectedFilters,
    (result: Record<string, string>, val, key) => {
      if (_.isArray(val) && !_.isEmpty(val)) {
        result[`restrictBy[${key}]`] = val?.join('|');
      }
    },
    {},
  );
  return {...filterParams, sortBy, sortOrder};
}
