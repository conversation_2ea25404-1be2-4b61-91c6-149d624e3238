import {AppConfig} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import _ from 'lodash';
import {call, select} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  connectPlugin,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {selectMenuChildCollectionAction} from './actions';

import docs from './docs';

interface ShopifyCollectionsTreeModelConfigParams {
  datasource: string;
  collectionHandle: string;
  shopifyMenuHandle: string;
  value: any;
  menuCollections: any[];
  selectedCollectionChilds: any[];
  selectMenuChildCollectionAction: string;
  selectedChildCollectionHandle: string;
  activeChildCollection: string;
  metafieldsIdentifiers: any[];
  cachePolicy: string;
}

const ShopifyCollectionsTreePluginConfig: ShopifyCollectionsTreeModelConfigParams = {
  datasource: 'shopify',
  collectionHandle: '',
  shopifyMenuHandle: '',
  value: '',
  menuCollections: [],
  selectedCollectionChilds: [],
  selectMenuChildCollectionAction: 'action',
  selectedChildCollectionHandle: '',
  activeChildCollection: '',
  metafieldsIdentifiers: [],
  cachePolicy: '',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  selectMenuChildCollectionAction: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectMenuChildCollectionAction;
    },
    actionMetadata: {
      editableInputParams: {
        childCollectionHandle: '',
      },
    },
  },
};

const editors: PluginEditorsConfig<ShopifyCollectionsTreeModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{query1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'datasource',
      props: {
        label: 'Shopify Datasource Id',
        placeholder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'collectionHandle',
      props: {
        label: 'Collection Handle',
        placeholder: '{{currentPage.params?.collectionHandle}}',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyMenuHandle',
      props: {
        label: 'Shopify Menu Handle',
        placeholder: '{{shopifyMenuHandle}}',
      },
    },
    {
      type: 'codeInput',
      name: 'metafieldsIdentifiers',
      props: {
        label: 'Shopify App Icon MetaField Identifiers',
        placeholder: "{{[{key: 'apptile_app_icon', namespace: 'custom'}]}}",
      },
    },
    {
      type: 'radioGroup',
      name: 'cachePolicy',
      defaultValue: 'no-cache',
      props: {
        label: 'Cache Policy',
        options: ['cache-first', 'network-only', 'cache-only', 'no-cache'],
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyCollectionsTree',
  type: 'state',
  name: 'Shopify Collections Tree Helper',
  description: 'Shopify Collections Tree helper.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};

const shopifyQueryExecuter = function* (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryName: string,
  inputVariables: any,
  cachePolicy: string | null,
): any {
  return yield call(dsModel.runQuery, dsModel, dsConfig, dsModelValues, queryName, inputVariables, {
    transformers: undefined,
    getNextPage: false,
    paginationMeta: undefined,
    cachePolicy: cachePolicy ? cachePolicy : null,
  });
};

const recursivelyGetCollectionIds = (data: any, accList = []) => {
  data.map((v: any) => {
    accList.push(v?.collectionId);
    if (v?.items) recursivelyGetCollectionIds(v.items, accList);
  });
  return accList;
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [] as PluginModelChange[],
  };
  if (pageLoad) {
    var pageModels = state.stageModel.getModelValue([]);
    const shopifyMenuHandle = model?.get('shopifyMenuHandle');
    const collectionHandle = model?.get('collectionHandle');
    const metafieldsIdentifiers = model?.get('metafieldsIdentifiers');
    const datasource = model?.get('datasource');
    const dsModelValues = pageModels.get(datasource);

    const appConfig: AppConfig = yield select(selectAppConfig);
    const dsConfig = appConfig.getPlugin(datasource);
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const cachePolicy = model?.get('cachePolicy');

    const {data: menuItems} = yield call(
      shopifyQueryExecuter,
      dsModel,
      dsConfig,
      dsModelValues,
      'GetMenu',
      {
        menuHandle: shopifyMenuHandle,
      },
      cachePolicy,
    );

    let selectedCollectionChilds = [];
    let firstLevelCollectionInMenu = [];

    if (!_.isEmpty(menuItems?.items)) {
      const rawCollectionIds = recursivelyGetCollectionIds(menuItems.items);
      const collectionIds = _.filter(
        rawCollectionIds,
        collectionId => !_.isEmpty(collectionId) && _.startsWith(collectionId, 'gid://shopify/Collection/'),
      );
      const {data: collections} = yield call(
        shopifyQueryExecuter,
        dsModel,
        dsConfig,
        dsModelValues,
        'GetCollectionsByIdsWithMetaField',
        {
          collectionIds,
          identifiers: _.isEmpty(metafieldsIdentifiers) ? [] : metafieldsIdentifiers,
        },
        cachePolicy,
      );

      const collectionIndexedByIds = _.keyBy(collections, 'id');

      // const firstLevelMenuCollectionIds = _.map(menuItems?.items, item => item.collectionId);
      firstLevelCollectionInMenu = _.map(menuItems?.items, item => {
        return {...collectionIndexedByIds[item.collectionId], items: item?.items};
      });

      const selectedCollection = _.find(collections, col => {
        return col.handle === collectionHandle;
      });

      if (selectedCollection) {
        const selectedMenu = _.find(menuItems?.items, item => item.collectionId === selectedCollection?.id);

        selectedCollectionChilds = _.map(selectedMenu?.items, colItem => {
          return {...collectionIndexedByIds[colItem.collectionId], items: colItem?.items};
        });
      }
    }

    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['selectedCollectionChilds']),
      newValue: selectedCollectionChilds,
    });

    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['menuCollections']),
      newValue: firstLevelCollectionInMenu,
    });
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['menuItems']),
      newValue: menuItems.items,
    });

    if (!model.get('selectedChildCollectionHandle')) {
      pluginUpdateReturn.modelUpdates.push({
        selector: modelSelector.concat(['selectedChildCollectionHandle']),
        newValue: _.first(selectedCollectionChilds)?.handle,
      });

      pluginUpdateReturn.modelUpdates.push({
        selector: modelSelector.concat(['activeChildCollection']),
        newValue: _.first(selectedCollectionChilds),
      });
    }
  }
  return pluginUpdateReturn;
};

export default connectPlugin(
  'ShopifyCollectionsTree_22_10',
  null,
  ShopifyCollectionsTreePluginConfig,
  onPluginUpdate,
  editors,
  {
    propertySettings,
    pluginListing,
    docs,
  },
);
