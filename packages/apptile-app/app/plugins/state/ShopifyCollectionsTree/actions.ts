import {modelUpdateAction} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import {Selector} from 'apptile-core';
import _ from 'lodash';
import {ModelChange} from 'apptile-core';
import {PluginModelType} from 'apptile-core';

type selectMenuChildCollectionParams = {
  childCollectionHandle: string;
};

type ChangeParentCollectionParmas = {
  collectionHandle: string;
};

export async function selectMenuChildCollectionAction(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: selectMenuChildCollectionParams,
) {
  const {childCollectionHandle} = params;
  const selectedCollectionChilds = model.get('selectedCollectionChilds');

  const activeChildCollection = _.find(selectedCollectionChilds, v => v.handle === childCollectionHandle);

  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        selectedChildCollectionHandle: childCollectionHandle,
        activeChildCollection: activeChildCollection,
      }),
    ),
  );
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}
