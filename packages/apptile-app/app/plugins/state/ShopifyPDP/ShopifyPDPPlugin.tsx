import {modelUpdateAction} from 'apptile-core';
import {AppConfig} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import Immutable from 'immutable';
import _ from 'lodash';
import {call, put, select, spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  connectPlugin,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {
  getPluginModelUpdate,
  getProductDefaultVariantOptions,
  getProductDerivedData,
  selectMultipleVariantOptionsAction,
  selectVariantOptionAction,
  setActiveSellingPlan,
  setOrderType,
} from './actions';
import docs from './docs';

interface ShopifyPDPModelConfigParams {
  datasource: string;
  value: any;
  product: any;
  variantCount: number;
  productOptions: any;
  optionNames: string[];
  selectedOptions: Record<string, string>;
  displayOptions: Record<string, string[]>;
  activeVariant: any;
  variantImages: string[];
  variantMedias: string[];
  productHandle: string;
  selectVariantOption: string;
  selectMultipleVariantOption: string;
  variantSelectionStrategy: string;
  variantImageSelectionStrategy: string;
  variantImageFilterByOptionName: string;
  orderType: string;
  activeSellingPlan: any;
  setOrderType: string;
  setActiveSellingPlan: string;
  loading: boolean;
}

const ShopifyPDPPluginConfig: ShopifyPDPModelConfigParams = {
  datasource: 'shopify',
  value: '',
  product: '',
  variantCount: 1,
  productOptions: '',
  optionNames: [],
  selectedOptions: {},
  displayOptions: {},
  activeVariant: {},
  variantImages: [],
  variantMedias: [],
  productHandle: '',
  selectVariantOption: 'action',
  selectMultipleVariantOption: 'action',
  variantSelectionStrategy: 'first-product',
  variantImageSelectionStrategy: '',
  variantImageFilterByOptionName: '',
  orderType: 'buy-once',
  activeSellingPlan: {},
  setOrderType: 'action',
  setActiveSellingPlan: 'action',
  loading: true,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  selectVariantOption: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectVariantOptionAction;
    },
    actionMetadata: {
      editableInputParams: {
        optionName: '',
        optionValue: '',
      },
    },
  },
  selectMultipleVariantOption: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectMultipleVariantOptionsAction;
    },
    actionMetadata: {
      editableInputParams: {
        options: '',
      },
    },
  },
  setOrderType: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setOrderType;
    },
    actionMetadata: {
      editableInputParams: {
        orderType: '',
      },
    },
  },
  setActiveSellingPlan: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setActiveSellingPlan;
    },
    actionMetadata: {
      editableInputParams: {
        planValue: '',
      },
    },
  },
};

const editors: PluginEditorsConfig<ShopifyPDPModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{query1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'datasource',
      props: {
        label: 'Shopify Datasource Id',
        placeholder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'productHandle',
      props: {
        label: 'Product Handle',
        placeholder: '{{currentPage.params?.productHandle}}',
      },
    },
    {
      type: 'radioGroup',
      name: 'variantSelectionStrategy',
      defaultValue: 'first-product',
      props: {
        label: 'Variant Selection Strategy',
        options: ['first-product', 'first-isInStock-product', 'first-availableForSale-product'],
      },
    },
    {
      type: 'radioGroup',
      name: 'variantImageSelectionStrategy',
      defaultValue: 'default',
      props: {
        label: 'Variant Image Selection Strategy',
        options: ['default', 'match-one-option-show-all-images', 'match-one-option'],
      },
    },
    {
      type: 'codeInput',
      name: 'variantImageFilterByOptionName',
      props: {
        label: 'Variant Image Filter By OptionName',
        placeholder: 'Color',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyPDP',
  type: 'state',
  name: 'Shopify PDP Helper',
  description: 'Shopify PDP helper plugin.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};
const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [
      // {
      //   selector: modelSelector.concat(['previousValue']),
      //   newValue: value,
      // },
    ] as PluginModelChange[],
  };
  const productHandle = model?.get('productHandle');
  const currentHandle = model?.get('product')?.handle;
  if (pageLoad || productHandle !== currentHandle) {
    var pageModels = state.stageModel.getModelValue([]);
    const datasource = model?.get('datasource');
    const dsModelValues = pageModels.get(datasource);
    const productsCache: Record<string, any> = dsModelValues?.get('productCacheByHandle');

    yield spawn(function* () {
      const appConfig: AppConfig = yield select(selectAppConfig);
      const dsConfig = appConfig.getPlugin(datasource);
      const dsType = dsModelValues.get('pluginType');
      const dsModel = GetRegisteredPlugin(dsType);
      const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
      const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
        dsModel.runQuery,
        dsModel,
        dsConfig,
        dsModelValues,
        'GetProductByHandle',
        {productHandle},
        {
          transformers: undefined,
          getNextPage: false,
          paginationMeta: undefined,
          cachePolicy: null,
        },
      );
      const derivedData = getProductDerivedData(data);
      const defaultOptionsData = getProductDefaultVariantOptions(data, model);
      const modelUpdates = getPluginModelUpdate(pluginSelector, {
        ...derivedData,
        ...defaultOptionsData,
        product: data,
        loading: false,
      });
      yield put(modelUpdateAction(modelUpdates));
    });

    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['product']),
      newValue: _.get(productsCache, productHandle, null),
    });
  }
  return pluginUpdateReturn;
};

export default connectPlugin('ShopifyPDP_22_10', null, ShopifyPDPPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
