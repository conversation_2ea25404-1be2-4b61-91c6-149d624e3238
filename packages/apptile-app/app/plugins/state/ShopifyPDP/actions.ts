import {modelUpdateAction} from 'apptile-core';
import {<PERSON><PERSON><PERSON><PERSON>, Selector} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import _ from 'lodash';
import {IProductDetail, IProductVariant} from '../../datasource/ShopifyV_22_10/types';
import {PluginModelType} from 'apptile-core';
import {getVariantImagesBySelectedOption, getVariantImagesOnLaunch} from './variantSelectionStrategies';

export interface IVariantMedias {
  mediaContentType: string;
  host?: string;
  embeddedUrl?: string;
  sources?: Array<{
    format: string;
    url: string;
    width: string;
    height: string;
  }>;
  previewImage: {
    url: string;
  };
}

export function getProductDerivedData(product: IProductDetail) {
  const variantCount = product?.variants?.length;
  const productOptions = product?.productOptions;
  const optionNames = productOptions?.map(option => option.name) ?? [];

  return {variantCount, productOptions, optionNames};
}

function getVariantMedias(variantImages, product) {
  //First convert the variantImages to variantMedias
  let variantMedias: IVariantMedias[] = variantImages.map(image => {
    return {
      mediaContentType: 'IMAGE',
      previewImage: {
        url: image,
      },
    };
  });
  //Then add product media videos to the variantMedias
  product?.media?.forEach(media => {
    if (media.mediaContentType === 'EXTERNAL_VIDEO') {
      if (media.host === 'VIMEO') {
        variantMedias.push({
          mediaContentType: 'EXTERNAL_VIDEO',
          host: media.host,
          embeddedUrl: media.embeddedUrl,
          previewImage: {
            url: media.previewImage.url,
          },
        });
      }
    }
    if (media.mediaContentType === 'VIDEO') {
      variantMedias.push({
        mediaContentType: 'VIDEO',
        sources: media.sources,
        previewImage: {
          url: media.previewImage.url,
        },
      });
    }
  });

  return variantMedias;
}

export function getProductDefaultVariantOptions(product: IProductDetail, model: any) {
  if (!product) return null;
  // const defaultVariant = product.variants[0];

  const variantSelectionStrategy = model?.get('variantSelectionStrategy') ?? 'first-product';

  let defaultVariant: IProductVariant;

  switch (variantSelectionStrategy) {
    case 'first-product':
      defaultVariant = product.variants[0];
      break;

    case 'first-isInStock-product':
      defaultVariant = product.variants.find(variant => variant.isInStock) || product.variants[0];
      break;

    case 'first-availableForSale-product':
      defaultVariant = product.variants.find(variant => variant.availableForSale) || product.variants[0];
      break;

    default:
      defaultVariant = product.variants[0];
  }

  let selectedOptions = {};
  if (product.variants?.length > 1) {
    const defaultOptions = {...defaultVariant.variantOptions};
    selectedOptions = _.keyBy(defaultOptions, option => option?.name);
  }
  const displayOptions = getDisplayOptions(product, selectedOptions);
  // const variantImages = getActiveVariantImages(product, defaultVariant);

  const variantImageSelectionStrategy = model?.get('variantImageSelectionStrategy') ?? 'default';
  const filterOptions = Object.values(selectedOptions);
  const variantImageFilterByOptionName = model?.get('variantImageFilterByOptionName');

  let variantImages: string[] = getVariantImagesOnLaunch(
    variantImageSelectionStrategy,
    defaultVariant,
    product,
    filterOptions,
    variantImageFilterByOptionName,
  );

  const variantMedias = getVariantMedias(variantImages, product);

  return {selectedOptions, displayOptions, activeVariant: defaultVariant, variantImages, variantMedias};
}

export function getDisplayOptions(
  product: IProductDetail,
  selectedOptions: Record<string, IProductVariant['variantOptions'][number]>,
) {
  const productOptions = product?.productOptions;
  let displayOptions: Record<string, string[]> = {};
  productOptions?.forEach(option => {
    displayOptions[option.name] = [];
  });
  let filterOptions: IProductVariant['variantOptions'][number][] = [];
  productOptions?.forEach((option, optionIndex) => {
    if (optionIndex === 0) {
      option.values.forEach(value => displayOptions[option.name].push(value));
    } else {
      const filteredVariants = filterVariantsByOptions(product, filterOptions);
      const filteredVariantOptionValues = filteredVariants?.map(variant => {
        const variantCurrentOption = variant?.variantOptions?.find(
          variantOption => variantOption.name === option?.name,
        );
        return variantCurrentOption?.value;
      });
      option.values.forEach(value => {
        if (filteredVariantOptionValues.includes(value)) {
          displayOptions[option.name].push(value);
        }
      });
    }
    filterOptions.push({name: option.name, value: selectedOptions[option.name]?.value});
  });

  return displayOptions;
}

function filterVariantsByOptions(
  product: IProductDetail,
  filterOptions: IProductVariant['variantOptions'],
): IProductVariant[] {
  const productVariants = product?.variants;
  const filteredVariants = productVariants?.filter(variant => {
    let bMatched = true;
    filterOptions.forEach(filterOption => {
      variant.variantOptions.forEach(variantOption => {
        if (variantOption.name === filterOption.name) {
          if (variantOption.value !== filterOption.value) bMatched = false;
        }
      });
    });
    return bMatched;
  });
  return filteredVariants;
}

export function getActiveVariantImages(product: IProductDetail, activeVariant: IProductVariant): string[] {
  const images = [];
  images.push(activeVariant?.featuredImage);
  product.images?.forEach(image => images.push(image.src));
  return images;
}

type SelectVariantOptionParams = {
  optionName: string;
  optionValue: string;
};
type SelectMultipleVariantOptionParams = {
  options: {[key: string]: string};
};

/**
 * To Select first option in Child Level Option selection as default when parent Option changed.
 * @param model
 * @param product
 * @param selectedOptions
 * @param params
 * @returns
 */
const selectFirstOptionOnParentOptionChange = (
  model: any,
  product: IProductDetail,
  selectedOptions: any,
  params: SelectVariantOptionParams,
) => {
  const {optionName, optionValue} = params;
  if (!optionValue) {
    return selectedOptions;
  }

  const optionNames = model?.get('optionNames');
  const productVariants = product?.variants;

  if (optionNames.length < 2) return selectedOptions;

  const currentOptionIndex = _.findIndex(optionNames, name => name == optionName);
  const nextOptionName = _.get(optionNames, currentOptionIndex + 1);
  const nextOptionVariant = _.find(productVariants, variant => {
    let match = false;
    if (_.find(variant?.variantOptions, vOpt => vOpt?.name == optionName && vOpt?.value == optionValue)) {
      match = true;
    }
    return match;
  });

  // To Select first option in Child Level Option selection as default when parent Option changed.
  const nextOptionValue = _.find(nextOptionVariant?.variantOptions, (v, k) => v?.name == nextOptionName)?.value;
  if (nextOptionName && nextOptionVariant && nextOptionValue) {
    selectedOptions[nextOptionName].value = nextOptionValue;
  }
  return selectedOptions;
};

export async function selectVariantOptionAction(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {optionName, optionValue} = params;
  if (!optionValue) {
    return;
  }
  //We don't need to deep clone product we are only mutating selectedOptions
  const product = model?.get('product');

  // Deep Clone added as spread operator only does shallow clones and structedClone are still in expermental phase.
  // done deep clone for selectedOptions instead of entire product.
  let selectedOptions = _.cloneDeep(model?.get('selectedOptions'));
  const variantImageSelectionStrategy = model?.get('variantImageSelectionStrategy') ?? 'default';
  const variantImageFilterByOptionName = model?.get('variantImageFilterByOptionName');

  selectedOptions[optionName].value = optionValue;
  // To Select first option in Child Level Option selection as default when parent Option changed.
  selectedOptions = selectFirstOptionOnParentOptionChange(model, product, selectedOptions, params);

  const displayOptions = getDisplayOptions(product, selectedOptions);
  const filterOptions = Object.values(selectedOptions);
  const activeVariant = _.first(filterVariantsByOptions(product, filterOptions));

  let variantImages = getVariantImagesBySelectedOption(
    variantImageSelectionStrategy,
    activeVariant,
    product,
    filterOptions,
    variantImageFilterByOptionName,
  );

  const variantMedias = getVariantMedias(variantImages, product);

  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {selectedOptions, displayOptions, activeVariant, variantImages, variantMedias}),
    ),
  );
}

export async function selectMultipleVariantOptionsAction(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectMultipleVariantOptionParams,
) {
  const {options} = params;
  if (Object.keys(options).length == 0) {
    return;
  }
  //We don't need to deep clone product we are only mutating selectedOptions
  const product = model?.get('product');

  // Deep Clone added as spread operator only does shallow clones and structedClone are still in expermental phase.
  // done deep clone for selectedOptions instead of entire product.
  let selectedOptions = _.cloneDeep(model?.get('selectedOptions'));
  const variantImageSelectionStrategy = model?.get('variantImageSelectionStrategy') ?? 'default';
  const variantImageFilterByOptionName = model?.get('variantImageFilterByOptionName');

  Object.keys(options).map(e => {
    if (selectedOptions[e]) {
      selectedOptions[e].value = options[e];
    }
  });
  // To Select first option in Child Level Option selection as default when parent Option changed.
  selectedOptions = selectFirstOptionOnParentOptionChange(model, product, selectedOptions, params);

  const displayOptions = getDisplayOptions(product, selectedOptions);
  const filterOptions = Object.values(selectedOptions);
  const activeVariant = _.first(filterVariantsByOptions(product, filterOptions));

  let variantImages = getVariantImagesBySelectedOption(
    variantImageSelectionStrategy,
    activeVariant,
    product,
    filterOptions,
    variantImageFilterByOptionName,
  );

  const variantMedias = getVariantMedias(variantImages, product);

  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {selectedOptions, displayOptions, activeVariant, variantImages, variantMedias}),
    ),
  );
}

type SetOrderTypeParams = {
  orderType: string;
};

export function setOrderType(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SetOrderTypeParams,
) {
  const {orderType} = params;
  if (orderType !== undefined) {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {orderType: orderType}), undefined, true));
  }
}

type setActiveSellingPlanParams = {
  planValue: any;
};

export function setActiveSellingPlan(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: setActiveSellingPlanParams,
) {
  const {planValue} = params;
  if (Object.keys(planValue).length !== 0) {
    console.log('inner', planValue);
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {activeSellingPlan: planValue}), undefined, true));
  }
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}
