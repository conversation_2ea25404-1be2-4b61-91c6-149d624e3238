import _ from 'lodash';
import {IProductDetail, IProductVariant} from '../../../datasource/ShopifyV_22_10/types';
import {getVariantImagesMatchOneOption} from './matchOneOption';
import {getVariantImagesMatchOneOptionFilterImages} from './matchOneOptionFilterImages';
import {getActiveVariantImages} from '../actions';

export function getProductImages(product: IProductDetail): string[] {
  const images: string[] = [];
  product.images?.forEach(image => images.push(image.src));
  return images;
}

export const getVariantImagesBySelectedOption = (
  variantImageSelectionStrategy: string,
  activeVariant: any,
  product: IProductDetail,
  filterOptions: IProductVariant['variantOptions'],
  variantImageFilterByOptionName: string,
) => {
  let variantImages: string[] = [];

  switch (variantImageSelectionStrategy) {
    case 'match-one-option':
      variantImages = getVariantImagesMatchOneOptionFilterImages(
        product,
        filterOptions,
        variantImageFilterByOptionName,
      );
      break;
    case 'match-one-option-show-all-images':
      variantImages = getVariantImagesMatchOneOption(product, filterOptions, variantImageFilterByOptionName);
      break;
    default:
      variantImages = getActiveVariantImages(product, activeVariant);
  }
  return variantImages;
};

export const getVariantImagesOnLaunch = (
  variantImageSelectionStrategy: string,
  activeVariant: any,
  product: IProductDetail,
  filterOptions: IProductVariant['variantOptions'],
  variantImageFilterByOptionName: string,
) => {
  let variantImages: string[] = [];
  switch (variantImageSelectionStrategy) {
    case 'match-one-option':
      variantImages = getVariantImagesMatchOneOptionFilterImages(
        product,
        filterOptions,
        variantImageFilterByOptionName,
      );
      break;
    case 'match-one-option-show-all-images':
      variantImages = getVariantImagesMatchOneOption(product, filterOptions, variantImageFilterByOptionName);

      // variantImages = getProductImages(product);
      break;
    default:
      variantImages = getProductImages(product);
  }
  return variantImages;
};

export default {getVariantImagesBySelectedOption, getVariantImagesOnLaunch, getProductImages};
