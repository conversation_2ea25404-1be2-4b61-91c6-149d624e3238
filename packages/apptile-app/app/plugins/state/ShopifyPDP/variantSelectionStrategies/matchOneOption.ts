import _ from 'lodash';
import {IProductDetail} from '../../../datasource/ShopifyV_22_10/types';

export function getVariantImagesMatchOneOption(
  product: IProductDetail,
  filterOptions: any[],
  variantImageFilterByOptionName: string,
): string[] {
  let filterOptionToMatch = variantImageFilterByOptionName
    ? _.find(filterOptions, opt => opt.name === variantImageFilterByOptionName)
    : null;

  if (!filterOptionToMatch) {
    filterOptionToMatch = _.first(filterOptions);
  }

  const productVariants = product?.variants;
  const filteredVariant: any = productVariants?.find(variant => {
    let matchFound = false;
    variant.variantOptions.forEach(variantOption => {
      if (variantOption.name === filterOptionToMatch?.name && variantOption.value === filterOptionToMatch.value) {
        matchFound = true;
      }
    });
    return matchFound;
  });

  const producImages = [...product?.images];

  let rearrangedImages: any[] = producImages;
  const activeImageIndex = _.findIndex(producImages, v => v.id === filteredVariant?.image?.id);

  if (activeImageIndex > -1) {
    rearrangedImages = [...producImages.splice(activeImageIndex), ...producImages.splice(0, activeImageIndex)];
  }

  const images = rearrangedImages.map(img => {
    return img?.src;
  });
  return images;
}
