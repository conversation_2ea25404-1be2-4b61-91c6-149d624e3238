import _ from 'lodash';
import {IProductDetail} from '../../../datasource/ShopifyV_22_10/types';

export function getVariantImagesMatchOneOptionFilterImages(
  product: IProductDetail,
  filterOptions: any[],
  variantImageFilterByOptionName: string,
): string[] {
  let filterOptionToMatch = variantImageFilterByOptionName
    ? _.find(filterOptions, opt => opt.name === variantImageFilterByOptionName)
    : null;

  if (!filterOptionToMatch) {
    filterOptionToMatch = _.first(filterOptions);
  }

  const productVariants = product?.variants;
  const filteredVariant: any = productVariants?.find(variant => {
    let matchFound = false;
    variant.variantOptions.forEach(variantOption => {
      if (variantOption.name === filterOptionToMatch?.name && variantOption.value === filterOptionToMatch.value) {
        matchFound = true;
      }
    });
    return matchFound;
  });

  const producImages = _.reverse(_.cloneDeep(product.images ?? []));

  const variantFeaturedImages = _.uniq(_.map(productVariants, pv => pv.image.id));
  const relatedImages: Record<string, any> = {};
  let lastImageIndex = 0;

  for (let i = 0; i < producImages.length; i++) {
    const pvImageId = producImages[i].id;
    if (pvImageId && _.includes(variantFeaturedImages, pvImageId)) {
      relatedImages[pvImageId] = _.reverse(producImages?.slice(lastImageIndex, i + 1));
      lastImageIndex = i + 1;
    }
  }

  let rearrangedImages: any[] = producImages;
  if (filteredVariant) {
    rearrangedImages = relatedImages[filteredVariant?.image?.id];
  }

  // const activeImageIndex = _.findIndex(producImages, v => v.id === filteredVariant?.image?.id);
  // let rearrangedImages = producImages;

  // if (activeImageIndex > -1) {
  //   rearrangedImages = [...producImages.splice(activeImageIndex), ...producImages.splice(0, activeImageIndex)];
  // }

  const images = rearrangedImages.map(img => {
    return img?.src;
  });
  return images;
}
