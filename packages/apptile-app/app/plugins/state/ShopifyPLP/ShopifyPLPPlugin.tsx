import {modelUpdateAction} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import _ from 'lodash';
import {put, spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  AppPageTriggerOptions,
  connectPlugin,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {
  applyFilters,
  deselectFilter,
  getNextPage,
  getPluginModelUpdate,
  resetFilters,
  selectFilter,
  selectFilterCategory,
  setFilterPriceMax,
  setFilterPriceMin,
  setSortOptions,
} from './actions';
import docs from './docs';
import {fetchCollectionDetailsGenerator, fetchProductFiltersGenerator, fetchProductsGenerator} from './generators';

interface ShopifyPLPModelConfigParams {
  datasource: string;
  value: any;
  collectionHandle: string;
  collection: any;
  filterNames: string[];
  products: Record<string, any>[];
  selectedFilters: Record<string, string>;
  displayFilters: Record<string, string[]>;
}
const ShopifyProductsSortOptions = [
  {
    label: 'Best selling',
    sortBy: 'BEST_SELLING',
    sortReverse: false,
  },
  {
    label: 'Price - Low to High',
    sortBy: 'PRICE',
    sortReverse: false,
  },
  {
    label: 'Price - High to Low',
    sortBy: 'PRICE',
    sortReverse: true,
  },
  {
    label: 'Newly Added',
    sortBy: 'CREATED',
    sortReverse: true,
  },
];

const ShopifyPLPPluginConfig: ShopifyPLPModelConfigParams = {
  datasource: 'shopify',
  numItems: 24,
  initialNumItems: 8,
  value: '',
  collectionHandle: '',
  collection: '',
  products: [],
  _doFetch: '',
  doNextPage: '',
  loading: '',
  filtersLoading: '',
  sortBy: 'COLLECTION_DEFAULT',
  isVariantFlattened: false,
  sortReverse: '',
  sortOptions: '',
  filters: [],
  filterNames: [],
  selectedFilters: [],
  filtersById: '',
  filterValuesById: '',
  selectedFilterId: '',
  selectedFilterIdValues: [],
  filterPriceMin: '',
  filterPriceMax: '',
  displayFilters: '',
  hasNextPage: '',
  paginationMeta: '',
  getNextPage: 'action',
  selectFilter: 'action',
  deselectFilter: 'action',
  setFilterPriceMin: 'action',
  setFilterPriceMax: 'action',
  selectFilterCategory: 'action',
  applyFilters: 'action',
  resetFilters: 'action',
  setSortOptions: 'action',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  numItems: {
    getValue: (model, val) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
  sortOptions: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? ShopifyProductsSortOptions : renderedValue;
    },
  },
  selectedFilters: {
    getValue: (model, renderedValue) => {
      return !_.isArray(renderedValue) ? [] : renderedValue;
    },
  },
  getNextPage: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return getNextPage;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  selectFilterCategory: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilterCategory;
    },
    actionMetadata: {
      editableInputParams: {
        filterId: '',
      },
    },
  },
  selectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return selectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
      },
    },
  },
  deselectFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return deselectFilter;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
      },
    },
  },
  setFilterPriceMin: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setFilterPriceMin;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
        value: '',
      },
    },
  },
  setFilterPriceMax: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setFilterPriceMax;
    },
    actionMetadata: {
      editableInputParams: {
        id: '',
        value: '',
      },
    },
  },
  applyFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return applyFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  resetFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return resetFilters;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  setSortOptions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSortOptions;
    },
    actionMetadata: {
      editableInputParams: {
        sortBy: '',
        sortReverse: '',
      },
    },
  },
};

const editors: PluginEditorsConfig<ShopifyPLPModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'datasource',
      props: {
        label: 'Shopify Datasource Id',
        placeholder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'collectionHandle',
      props: {
        label: 'Collection Handle',
        placeholder: '{{currentPage.params?.collectionHandle}}',
      },
    },
    {
      type: 'codeInput',
      name: 'numItems',
      props: {
        label: 'Number of Items',
        placeholder: '24',
      },
    },
    {
      type: 'codeInput',
      name: 'initialNumItems',
      props: {
        label: 'Initial Number of Items to load',
        placeholder: '8',
      },
    },
    {
      type: 'dropDown',
      name: 'sortBy',
      props: {
        label: 'Collection sort order',
        options: ['BEST_SELLING', 'COLLECTION_DEFAULT', 'CREATED', 'MANUAL', 'PRICE', 'TITLE'],
      },
    },
    {
      type: 'checkbox',
      name: 'isVariantFlattened',
      props: {
        label: 'Flatten Variants',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyPLP',
  type: 'state',
  name: 'Shopify PLP Helper',
  description: 'Shopify PLP helper plugin.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};
const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  if (options?.focusTrigger) return;
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [] as PluginModelChange[],
  };
  const collectionHandle = model?.get('collectionHandle');
  const numItems = model?.get('numItems');
  const initialNumItems = model?.get('initialNumItems');

  const currentHandle = model?.get('collection')?.handle;
  const bDoFetch = model?.get('_doFetch', false);
  const bDoNextPage = model?.get('doNextPage', false);
  if (
    pageLoad ||
    (collectionHandle && currentHandle && collectionHandle !== currentHandle) ||
    (collectionHandle && !currentHandle)
  ) {
    yield spawn(fetchCollectionDetailsGenerator, state, pluginId, pageKey, instance);
    yield spawn(fetchProductsGenerator, state, pluginId, pageKey, instance, initialNumItems);
    yield spawn(fetchProductFiltersGenerator, state, pluginId, pageKey, instance);
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['_doFetch']),
      newValue: false,
    });
    pluginUpdateReturn.modelUpdates.push({
      selector: modelSelector.concat(['loading']),
      newValue: true,
    });
  }
  const modelHasNextPage = model?.get('hasNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;
  if (bDoFetch) {
    yield spawn(fetchProductsGenerator, state, pluginId, pageKey, instance, numItems);
    if (!bGetNextPage) {
      yield spawn(fetchProductFiltersGenerator, state, pluginId, pageKey, instance);
    }
    const modelUpdates = getPluginModelUpdate(pluginSelector, {
      _doFetch: false,
      doNextPage: false,
    });
    yield put(modelUpdateAction(modelUpdates));
  }
  return pluginUpdateReturn;
};

export default connectPlugin('ShopifyPLP_22_10', null, ShopifyPLPPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
