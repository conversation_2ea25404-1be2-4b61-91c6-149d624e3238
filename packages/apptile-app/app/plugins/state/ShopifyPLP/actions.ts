import {modelUpdateAction} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import {ModelChange, Selector} from 'apptile-core';
import _ from 'lodash';
import {PluginModelType} from 'apptile-core';

export async function getNextPage(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  const action = getPluginModelUpdate(selector, {_doFetch: true, doNextPage: true});
  dispatch(modelUpdateAction(action, undefined, true));
}

export async function applyFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {_doFetch: true, doNextPage: false, paginationMeta: {}}),
      undefined,
      true,
    ),
  );
}
export async function resetFilters(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: any,
) {
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {
        _doFetch: true,
        doNextPage: false,
        selectedFilters: [],
        filterPriceMin: '',
        filterPriceMax: '',
      }),
      undefined,
      true,
    ),
  );
}
export async function setSortOptions(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: {sortBy: string; sortReverse: string},
) {
  const {sortBy, sortReverse} = params;
  dispatch(
    modelUpdateAction(
      getPluginModelUpdate(selector, {_doFetch: true, doNextPage: false, sortBy, sortReverse}),
      undefined,
      true,
    ),
  );
}

export interface ShopifySelectFilterCategoryIdActionParams {
  filterId: string;
}
export async function selectFilterCategory(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterCategoryIdActionParams,
) {
  const {filterId} = params;
  if (filterId !== undefined) {
    const filtersByAttribute = model.get('filtersById');
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {
          selectedFilterIdValues: filtersByAttribute[filterId]?.values,
          selectedFilterId: filterId,
        }),
        undefined,
        true,
      ),
    );
  }
}

function arrayMerge(objValue, srcValue) {
  if (_.isArray(objValue)) {
    return _.uniq(objValue.concat(srcValue));
  }
}
export interface ShopifySelectFilterActionParams {
  id: string;
}
export async function selectFilter(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {id} = params;
  if (id !== undefined) {
    var selectedFilters = model.get('selectedFilters', []);
    const newSelectedFilters = _.uniq(selectedFilters.concat([id]));
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {selectedFilters: newSelectedFilters}), undefined, true));
  }
}
export async function deselectFilter(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySelectFilterActionParams,
) {
  const {id} = params;
  if (id !== undefined) {
    var selectedFilters = model.get('selectedFilters', []);
    if (_.isArray(selectedFilters)) {
      _.remove(selectedFilters, v => v == id);
    }
    dispatch(
      modelUpdateAction(getPluginModelUpdate(selector, {selectedFilters: _.uniq(selectedFilters)}), undefined, true),
    );
  }
}

export interface ShopifySetFilterPriceParams extends ShopifySelectFilterActionParams {
  value: string;
}

export async function setFilterPriceMin(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySetFilterPriceParams,
) {
  const {id, value} = params;
  if (id !== undefined) {
    var val = _.toNumber(value);
    var selectedFilters = model.get('selectedFilters', []);
    const newSelectedFilters = _.uniq(selectedFilters.concat([id]));
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {selectedFilters: newSelectedFilters, filterPriceMin: val}),
        undefined,
        true,
      ),
    );
  }
}
export async function setFilterPriceMax(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: ShopifySetFilterPriceParams,
) {
  const {id, value} = params;
  if (id !== undefined) {
    var val = _.toNumber(value);
    var selectedFilters = model.get('selectedFilters', []);
    const newSelectedFilters = _.uniq(selectedFilters.concat([id]));
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {selectedFilters: newSelectedFilters, filterPriceMax: val}),
        undefined,
        true,
      ),
    );
  }
}

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}
