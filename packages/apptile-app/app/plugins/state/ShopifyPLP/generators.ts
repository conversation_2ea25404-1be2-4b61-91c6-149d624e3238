import {modelUpdateAction} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import {AppConfig, InteractionManager} from 'react-native';
import {select, put, call} from 'redux-saga/effects';
import {GetRegisteredPlugin} from 'apptile-core';
import {getPluginModelUpdate} from './actions';
import {getFilterRestrictions, getDisplayFilterOptions} from './utils';
import {getNavigationContext} from 'apptile-core';

export function* fetchProductsGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  numItems: number = 8,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const modelHasNextPage = model?.get('hasNextPage', false);
  const bDoNextPage = model?.get('doNextPage', false);
  const bGetNextPage = bDoNextPage && modelHasNextPage;

  const modelPaginationMeta = model?.get('paginationMeta', false);
  const currentFilters = model?.get('selectedFilters', []);
  const allFilterValues = model?.get('filterValuesById', {});
  const sortBy = model?.get('sortBy', 'relevance');
  const sortReverse = model?.get('sortReverse', false);
  const filterPriceMin = model?.get('filterPriceMin', 0);
  const filterPriceMax = model?.get('filterPriceMax', '');
  const modelProducts = model.get('products', []);
  const isVariantFlattened = model?.get('isVariantFlattened', false);
  const filterParams = getFilterRestrictions(
    allFilterValues,
    currentFilters,
    sortBy,
    sortReverse,
    filterPriceMin,
    filterPriceMax,
  );

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
    ...(!bGetNextPage ? {products: []} : {}),
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetCollectionProductsByHandle',
    {
      collectionHandle,
      first: numItems || 24,
      ...filterParams,
    },
    {
      transformers: undefined,
      getNextPage: bGetNextPage,
      paginationMeta: bGetNextPage ? modelPaginationMeta : undefined,
      cachePolicy: null,
    },
  );

  let productsData = data;

  if (isVariantFlattened) {
    productsData = data?.flatMap(product =>
      product?.variants?.map(variant => ({
        ...product,
        variants: [{...variant}],
        variantName: variant?.variantOptions[0]?.name,
        variantValue: variant?.variantOptions[0]?.value,
        currentVariant: variant,
        maxPrice: variant?.price,
        minPrice: variant?.price,
        featuredImage: variant?.featuredImage,
        minSalePrice: variant?.salePrice,
        maxSalePrice: variant?.salePrice,
        displayMinSalePrice: variant?.displaySalePrice?.ok,
        displayMaxSalePrice: variant?.displaySalePrice,
        displayMaxPrice: variant?.displayPrice,
        displayMinPrice: variant?.displayPrice,
        availableForSale: variant?.availableForSale,
      })),
    );
  }

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: bGetNextPage ? modelProducts.concat(productsData) : productsData ?? [],
    loading: false,
    hasNextPage,
    paginationMeta,
  });
  yield put(modelUpdateAction(modelUpdates));
}

export function* fetchCollectionDetailsGenerator(
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    collection: {},
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetCollectionDetailsByHandle',
    {collectionHandle},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {collection: data});
  const currentNavContext = getNavigationContext();
  if (currentNavContext?.getState()?.type !== 'tab') currentNavContext?.setOptions({title: data?.title});
  yield put(modelUpdateAction(modelUpdates));
}

export function* fetchProductFiltersGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);
  yield call(InteractionManager.runAfterInteractions);
  var pageModels = state.stageModel.getModelValue([]);
  const datasource = model?.get('datasource');
  const dsModelValues = pageModels.get(datasource);

  const collectionHandle = model?.get('collectionHandle');

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    filtersLoading: true,
    suggesting: false,
  });
  yield put(modelUpdateAction(loadingUpdates));
  const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetProductFilters',
    {
      collectionHandle,
    },
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    filters: data ?? [],
    filtersLoading: false,
    ...getDisplayFilterOptions(data, []),
  });
  yield put(modelUpdateAction(modelUpdates));
}
