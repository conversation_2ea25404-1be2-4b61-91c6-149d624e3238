import {RootState} from '@/root/app/store/RootReducer';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelChange,
  PluginPropertySettings,
  connectPlugin,
} from 'apptile-core';
import {spawn} from 'redux-saga/effects';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import docs from './docs';
import {fetchProductsGenerator} from './generators';

interface ShopifyWishlistModelConfigParams {
  shopifyDatasource: string;
  localWishlistProductIds: string;
  value: any;
  products: Record<string, any>[];
  loading: boolean;
}

const ShopifyWishlistPluginConfig: ShopifyWishlistModelConfigParams = {
  shopifyDatasource: 'shopify',
  localWishlistProductIds: '',
  value: '',
  products: [],
  loading: false,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
};

const editors: PluginEditorsConfig<ShopifyWishlistModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'shopifyDatasource',
      props: {
        label: 'Shopify Datasource Id',
        placeholder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'localWishlistProductIds',
      props: {
        label: 'LocalWishlist Product Ids',
        placeholder: 'LocalWishlist Product Ids',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyWishlist',
  type: 'state',
  name: 'Shopify Wishlist Helper',
  description: 'Shopify Wishlist helper plugin.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  if (options?.focusTrigger) return;
  let modelSelector = [pluginId];
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  let pluginUpdateReturn = {
    modelUpdates: [] as PluginModelChange[],
  };

  yield spawn(fetchProductsGenerator, state, pluginId, pageKey, instance);
  pluginUpdateReturn.modelUpdates.push({
    selector: modelSelector.concat(['loading']),
    newValue: true,
  });
  return pluginUpdateReturn;
};

export default connectPlugin('ShopifyWishlist_22_10', null, ShopifyWishlistPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
