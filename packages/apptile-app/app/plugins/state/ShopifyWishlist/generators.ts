import {RootState} from '@/root/app/store/RootReducer';
import {GetRegisteredPlugin, ModelChange, Selector, modelUpdateAction, selectAppConfig} from 'apptile-core';
import _ from 'lodash';
import {AppConfig} from 'react-native';
import {call, put, select} from 'redux-saga/effects';
import {productIdMapper, resolveGid} from '../../datasource/LocalWishlist/helpers';

function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}

export function* fetchProductsGenerator(state: RootState, pluginId: string, pageKey: string, instance: number) {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  let modelSelector = [pluginId];
  const pluginSelector = [pageKey, 'plugins'].concat(modelSelector);
  if (instance !== undefined && instance !== null) modelSelector = modelSelector.concat([instance + '']);

  var pageModels = state.stageModel.getModelValue([]);

  const productIds = model?.get('localWishlistProductIds');

  const datasource = model?.get('shopifyDatasource');
  const dsModelValues = pageModels.get(datasource);

  const appConfig: AppConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin(datasource);
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const loadingUpdates = getPluginModelUpdate(pluginSelector, {
    loading: true,
  });
  yield put(modelUpdateAction(loadingUpdates));

  const productGids = _.map(productIds, pid => resolveGid(pid));

  const {
    data: tData,
    errors,
    hasError,
  } = yield call(
    dsModel.runQuery,
    dsModel,
    dsConfig,
    dsModelValues,
    'GetProductByIds',
    {productIds: productGids},
    {
      transformers: undefined,
      getNextPage: false,
      paginationMeta: undefined,
      cachePolicy: null,
    },
  );
  const data = _.map(productGids, productGid => {
    const remoteProductDetails = _.find(tData, sProudct => {
      return productGid === sProudct?.id;
    });
    return !_.isEmpty(remoteProductDetails) ? remoteProductDetails : {id: productGid};
  });

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: data,
    loading: false,
  });
  yield put(modelUpdateAction(modelUpdates));
}
