import Immutable from 'immutable';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {connectPlugin, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import docs from './docs';

interface StateModelConfigParams {
  value: any;
}

const StatePluginConfig: StateModelConfigParams = {
  value: null,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
};

const editors: PluginEditorsConfig<StateModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{query1.value}}',
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'state',
  type: 'state',
  name: 'State',
  description: 'Store data as part of the application state.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};
const emptyOnupdate = null;

export default connectPlugin('StatePlugin', null, StatePluginConfig, emptyOnupdate, editors, {
  propertySettings,
  pluginListing,
  docs,
});
