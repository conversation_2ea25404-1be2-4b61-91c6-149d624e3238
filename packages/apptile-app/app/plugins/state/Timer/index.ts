import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {connectPlugin, PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {triggerPageEvent, triggerPageQuery} from 'apptile-core';
import _ from 'lodash';
import { store } from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {getPluginSelector} from 'apptile-core';

interface StateModelConfigParams {
  enabled: boolean;
  value: number;
  onTimerEnd: string;
}

const TimerPluginConfig: StateModelConfigParams = {
  enabled: false,
  value: 5000,
  onTimerEnd: '',
};

const propertySettings: PluginPropertySettings = {
  onTimerEnd: {
    type: EventTriggerIdentifier,
  },
  enabled: {
    getValue: (model, renderedValue, sel) => {
      return renderedValue === 'false' ||
        renderedValue === '0' ||
        (_.isObjectLike(renderedValue) && _.isEmpty(renderedValue))
        ? false
        : !!renderedValue;
    },
  },
  value: {
    getValue: (model, val, sel) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
};

const editors: PluginEditorsConfig<StateModelConfigParams> = {
  basic: [
    {
      type: 'codeInput',
      name: 'enabled',
      props: {
        label: 'Enabled',
        placeholder: 'false',
      },
    },
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: 5000,
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'timer',
  type: 'state',
  name: 'Timer',
  description: 'Timer plugin',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'timer',
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number,
  userTriggered: boolean,
  pageLoad: boolean,
): any {
  const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  const enabled = model?.get('enabled');
  const value = model?.get('value');
  var currentTimer = model?.get('currentTimer');
  const pluginSelector = getPluginSelector(pageKey, pluginId).concat(instance ? [instance] : []);
  // FIXME: Dispatch is not available without a component.
  // Fix with a way to pass dispatch around to be used by methods out of context.
  const dispatch = store.dispatch;
  logger.info('Timer::onPluginUpdate', enabled, value, currentTimer);
  if (enabled && !currentTimer) {
    // Set timer back again on plugin update, only if it is enabled.
    currentTimer = setTimeout(() => {
      dispatch(triggerPageEvent(pageKey, pluginId, instance, 'onTimerEnd'));
      // dispatch(triggerPageQuery({selector: [pageKey, 'plugins', pluginId]}));
      dispatch(
        modelUpdateAction([
          {
            selector: pluginSelector.concat(['currentTimer']),
            newValue: null,
          },
        ]),
      );
    }, value);
    return {
      modelUpdates: [
        {
          selector: [pluginId, 'currentTimer'],
          newValue: currentTimer,
        },
      ],
    };
  }
};
export default connectPlugin('TimerPlugin', null, TimerPluginConfig, onPluginUpdate, editors, {
  propertySettings,
  pluginListing,
});
