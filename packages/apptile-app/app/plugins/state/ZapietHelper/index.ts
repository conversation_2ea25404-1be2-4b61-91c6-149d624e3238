import {
  connectPlugin,
  ModelChange,
  modelUpdateAction,
  PluginConfig,
  PluginConfigType,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  Selector,
  TriggerActionIdentifier,
} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import docs from './docs';
import _ from 'lodash';

interface ZapietConfigParams {
  value: any;
  optedDeliveryMethod: string;
  setOptedDeliveryMethod: string;
  selectedPickupLocation: string;
  availableDeliveryMethods: any[];
  setSelectedPickupLocation: string;
  selectedLocalDeliveryLocation: string;
  setSelectedLocalDeliveryLocation: any;
  selectedTime: string;
  selectedHours: string;
  setSelectedHours: any;
  getLocalDeliveryTimes: string;
  setSelectedTime: any;
  storePickupTimes: string;
  storepickUpLocations: string;
  getStorePickupLocations: string;
  getStorePickupTimes: string;
  storePickupDates: string;
  getStorePickupDates: string;
  getShippingDates: string;
  shippingDates: string;
  locationSeachFailed: boolean;
  getLocalDeliveryLocations: string;
  getLocalDeliveryDates: string;
}

export const ZapietConfig: ZapietConfigParams = {
  value: null,
  optedDeliveryMethod: '', // this is the state
  setOptedDeliveryMethod: 'action', // this is the action/function that manipulates the state
  selectedPickupLocation: '',
  setSelectedPickupLocation: 'action',
  selectedTime: '',
  selectedHours: '',
  shippingDates: '',
  setSelectedHours: 'action',
  setSelectedTime: 'action',
  availableDeliveryMethods: [
    {
      name: 'Store Pickup',
      value: 'storePickup',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/f4253ecd-4686-4e24-a807-f7fbf59bed8a/original.png',
    },
    {
      name: 'Local Delivery',
      value: 'localDelivery',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/9741e62b-8e2f-414b-9e6d-8f3f5184943e/original.png',
    },
    {
      name: 'Shipping',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/bfa4bae3-930e-4c05-8bb0-436accd24d1f/original.png',
      value: 'shipping',
    },
  ],
  selectedLocalDeliveryLocation: '',
  setSelectedLocalDeliveryLocation: 'action',
  getLocalDeliveryTimes: 'action',
  storePickupTimes: '',
  getStorePickupTimes: 'action',
  storepickUpLocations: '',
  getStorePickupLocations: 'action',
  storePickupDates: '',
  getStorePickupDates: 'action',
  getShippingDates: 'action',
  locationSeachFailed: false,
  getLocalDeliveryLocations: 'action',
  getLocalDeliveryDates: 'action',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },

  setOptedDeliveryMethod: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setOptedDeliveryMethod;
    },
    actionMetadata: {
      editableInputParams: {
        optedDeliveryMethod: '',
      },
    },
  },
  setSelectedPickupLocation: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSelectedPickupLocation;
    },
    actionMetadata: {
      editableInputParams: {
        selectedPickupLocation: '',
      },
    },
  },
  getStorePickupTimes: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getStorePickupTimes;
    },
    actionMetadata: {
      editableInputParams: {
        date: '',
        locationId: '',
      },
    },
  },
  getLocalDeliveryLocations: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getLocalDeliveryLocations;
    },
    actionMetadata: {
      editableInputParams: {
        shoppingCart: '',
        geoSearchQuery: '',
      },
    },
  },
  getStorePickupLocations: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getStorePickupLocations;
    },
    actionMetadata: {
      editableInputParams: {
        shoppingCart: '',
        geoSearchQuery: '',
      },
    },
  },
  getShippingDates: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getShippingDates;
    },
    actionMetadata: {
      editableInputParams: {
        shoppingCart: '',
      },
    },
  },
  getStorePickupDates: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getStorePickupDates;
    },
    actionMetadata: {
      editableInputParams: {
        shoppingCart: '',
        locationId: '',
      },
    },
  },
  getLocalDeliveryTimes: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return zapietHelperActions.getLocalDeliveryTimes;
    },
    actionMetadata: {
      editableInputParams: {
        date: '',
        locationId: '',
      },
    },
  },
  setSelectedLocalDeliveryLocation: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSelectedLocalDeliveryLocation;
    },
    actionMetadata: {
      editableInputParams: {
        selectedLocalDeliveryLocation: '',
      },
    },
  },
  setSelectedHours: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSelectedHours;
    },
    actionMetadata: {
      editableInputParams: {
        selectedHours: '',
      },
    },
  },
  setSelectedTime: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setSelectedTime;
    },
    actionMetadata: {
      editableInputParams: {
        selectedTime: '',
      },
    },
  },
};

async function setOptedDeliveryMethod(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {optedDeliveryMethod} = params;
  if (optedDeliveryMethod !== undefined) {
    dispatch(
      modelUpdateAction(getPluginModelUpdate(selector, {optedDeliveryMethod: optedDeliveryMethod}), undefined, true),
    );
  }
}

async function setSelectedHours(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {selectedHours} = params;
  if (selectedHours !== undefined) {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {selectedHours: selectedHours}), undefined, true));
  }
}

async function setSelectedTime(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {selectedTime} = params;
  if (selectedTime !== undefined) {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {selectedTime: selectedTime}), undefined, true));
  } else {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {selectedTime: ''}), undefined, true));
  }
}

async function setSelectedPickupLocation(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {selectedPickupLocation} = params;
  if (selectedPickupLocation !== undefined) {
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {selectedPickupLocation: selectedPickupLocation}),
        undefined,
        true,
      ),
    );
  }
}

async function setSelectedLocalDeliveryLocation(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: SelectVariantOptionParams,
) {
  const {selectedLocalDeliveryLocation} = params;
  if (selectedLocalDeliveryLocation !== undefined) {
    dispatch(
      modelUpdateAction(
        getPluginModelUpdate(selector, {selectedLocalDeliveryLocation: selectedLocalDeliveryLocation}),
        undefined,
        true,
      ),
    );
  }
}

class ZapietHelperActions {
  private static async queryExecutor(appModel: any, queryType: string, endpoint: string, params: any) {
    const zapietDsModel = appModel.getModelValue(['zapiet']);
    const queryRunner = zapietDsModel.get('queryRunner');
    const shopUrl = zapietDsModel.get('shop');
    const queryResponse = await queryRunner.runQuery(queryType, `${endpoint}?shop=${shopUrl}`, {...params}, {});
    return queryResponse;
  }

  getStorePickupTimes = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {getStorePickupTimesLoading: true}), undefined, true));
    const {date, locationId} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(
      appModel,
      'post',
      `/pickup/locations/${locationId}/calendar/${date}`,
      {
        date,
        locationId,
      },
    );
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {storePickupTimes: errors}), undefined, true));
    } else {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {storePickupTimes: data}), undefined, true));
    }
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {getStorePickupTimesLoading: false}), undefined, true));
  };
  getStorePickupLocations = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    // const {shoppingCart, geoSearchQuery} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(appModel, 'post', `/pickup/locations/`, {
      ...params,
    });
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {storepickUpLocations: errors}), undefined, true));
    } else {
      dispatch(
        modelUpdateAction(getPluginModelUpdate(selector, {storepickUpLocations: data.locations}), undefined, true),
      );
    }
  };
  getShippingDates = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {shoppingCart} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(appModel, 'post', `/shipping/calendar`, {
      shoppingCart,
    });
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {shippingDates: errors}), undefined, true));
    } else {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {shippingDates: data}), undefined, true));
    }
  };

  getStorePickupDates = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {getStorePickupDatesLoading: true}), undefined, true));
    const {locationId, shoppingCart} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(
      appModel,
      'post',
      `/pickup/locations/${locationId}/calendar`,
      {
        locationId,
        shoppingCart,
      },
    );
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(
        modelUpdateAction(
          getPluginModelUpdate(selector, {storePickupDates: errors, getStorePickupDatesLoading: false}),
          undefined,
          true,
        ),
      );
    } else {
      dispatch(
        modelUpdateAction(
          getPluginModelUpdate(selector, {getStorePickupDatesLoading: false, storePickupDates: data}),
          undefined,
          true,
        ),
      )
        .then(() => {
          // This block will execute immediately after the dispatch has completed
          let selectedPickupLocation = model.get('selectedPickupLocation');
          this.getStorePickupTimes(
            dispatch,
            config,
            model,
            selector,
            {
              date: data?.minDate,
              locationId: selectedPickupLocation?.id,
            },
            appConfig,
            appModel,
          );
        })
        .catch((error: any) => {
          // Handle any errors that occurred during the dispatch
          console.error('Error occurred during dispatch:', error);
        });
    }
  };
  getLocalDeliveryDates = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryDatesLoading: true}), undefined, true));
    const {locationId, shoppingCart} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(
      appModel,
      'post',
      `/delivery/locations/${locationId}/calendar`,
      {
        locationId,
        shoppingCart,
      },
    );
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryDates: errors}), undefined, true));
    } else {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryDates: data}), undefined, true));
      this.getLocalDeliveryTimes(
        dispatch,
        config,
        model,
        selector,
        {
          date: data.minDate,
          locationId: locationId,
        },
        appConfig,
        appModel,
      );
    }
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryDatesLoading: false}), undefined, true));
  };
  getLocalDeliveryLocations = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {shoppingCart, geoSearchQuery} = params;
    const {data} = await ZapietHelperActions.queryExecutor(appModel, 'post', `/delivery/locations`, {
      shoppingCart,
      geoSearchQuery,
    });
    const errors = data?.error;
    // set the value of storePickupTimes here
    if (errors) {
      setSelectedLocalDeliveryLocation(dispatch, config, model, selector, {
        selectedLocalDeliveryLocation: '',
      });
      dispatch(
        modelUpdateAction(
          getPluginModelUpdate(selector, {
            localDeliveryLocations: errors,
            locationSeachFailed: true,
          }),
          undefined,
          true,
        ),
      );
    } else {
      dispatch(
        modelUpdateAction(
          getPluginModelUpdate(selector, {
            localDeliveryLocations: data,
            locationSeachFailed: false,
          }),
          undefined,
          true,
        ),
      );
      setSelectedLocalDeliveryLocation(dispatch, config, model, selector, {
        selectedLocalDeliveryLocation: data,
      });
      this.getLocalDeliveryDates(
        dispatch,
        config,
        model,
        selector,
        {
          locationId: data.id,
          shoppingCart,
        },
        appConfig,
        appModel,
      );
    }
  };
  getLocalDeliveryTimes = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {getLocalDeliveryTimesLoading: true}), undefined, true));

    const {date, locationId} = params;
    const {data, errors} = await ZapietHelperActions.queryExecutor(
      appModel,
      'post',
      `/delivery/locations/${locationId}/calendar/${date}`,
      {
        date,
        locationId,
      },
    );
    // set the value of storePickupTimes here
    if (errors && errors.length > 0) {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryTimes: errors}), undefined, true));
    } else {
      dispatch(modelUpdateAction(getPluginModelUpdate(selector, {localDeliveryTimes: data}), undefined, true));
    }
    dispatch(modelUpdateAction(getPluginModelUpdate(selector, {getLocalDeliveryTimesLoading: false}), undefined, true));
  };
}

export const shopifyEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'optedDeliveryMethod',
      props: {
        label: 'Default Delivery Method',
        placeholder: 'storePickup',
      },
    },
    {
      type: 'codeInput',
      name: 'availableDeliveryMethods',
      props: {
        label: 'Available Delivery Methods',
        placeholder: `{{[
    {
      name: 'Store Pickup',
      value: 'storePickup',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/f4253ecd-4686-4e24-a807-f7fbf59bed8a/original.png',
    },
    {
      name: 'Local Delivery',
      value: 'localDelivery',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/9741e62b-8e2f-414b-9e6d-8f3f5184943e/original.png',
    },
    {
      name: 'Shipping',
      image:
        'https://cdn.apptile.io/572c9e61-25cd-447d-a86a-de18e053b14b/bfa4bae3-930e-4c05-8bb0-436accd24d1f/original.png',
      value: 'shipping',
    },
  ]}}`,
      },
    },
  ],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'zapietHelper',
  type: 'datasource',
  name: 'Zapiet Helper',
  description: 'Zapiet Helper',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Data',
  icon: 'state',
};

export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): ModelChange[] {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}

const zapietHelperActions = new ZapietHelperActions();
export {zapietHelperActions};

export default connectPlugin('ZapietHelper', null, ZapietConfig, null, shopifyEditors, {
  propertySettings,
  pluginListing: pluginListing,
  docs,
});
