import React, {useState} from 'react';
import {View, Text, StyleSheet, Pressable, Animated} from 'react-native';

import {MaterialCommunityIcons} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {mergeWithDefaultStyles} from 'apptile-core';

import docs from './docs';
import _ from 'lodash';

type AccordionWidgetConfigType = {
  collapsedIcon: string;
  expandedIcon: string;
  value: any;
};
const AccordionWidgetConfig: AccordionWidgetConfigType = {
  collapsedIcon: 'plus',
  expandedIcon: 'minus',
  value: '[]',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'accordion',
  type: 'widget',
  name: 'Accordion',
  description: 'Create sections of collapsable data',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'accordion',
};

export const accordionWidgetStyleConfig: any = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    defaultValue: 'tile.accordion.backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'expandedTitleColor',
    defaultValue: 'tile.accordion.expandedTitleColor',
    props: {
      label: 'Expanded Title Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'collapsedTitleColor',
    defaultValue: 'tile.accordion.collapsedTitleColor',
    props: {
      label: 'Collapsed Title Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'contentColor',
    defaultValue: 'tile.accordion.contentColor',
    props: {
      label: 'Content Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'titleTypography',
    defaultValue: 'tile.accordion.titleTypography',
    props: {
      label: 'Title Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'contentTypography',
    defaultValue: 'tile.accordion.contentTypography',
    props: {
      label: 'Content Typography',
      placeholder: 'typography.body',
    },
  },
];

function Accordion({config, modelStyles, expandedIcon, collapsedIcon, ...i}) {
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {borderRadius, margin, padding, elevation, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding, elevation});
  const {contentTypography, titleTypography, ...restModelPlatformStyles} = modelPlatformStyles;
  const {expandedTitleColor, contentColor, backgroundColor, collapsedTitleColor} = restModelPlatformStyles;

  const [isActive, setIsActive] = useState(i.isExpanded);

  return (
    <View style={[layoutStyles, modelPlatformStyles, {backgroundColor: backgroundColor}]}>
      <Pressable
        style={styles.titleContainer}
        onPress={() => {
          setIsActive(!isActive);
        }}>
        <Text style={[titleTypography, {color: isActive ? expandedTitleColor : collapsedTitleColor, maxWidth: '95%'}]}>
          {i.title}
        </Text>
        <MaterialCommunityIcons
          color={isActive ? expandedTitleColor : collapsedTitleColor}
          name={isActive ? expandedIcon : collapsedIcon}
          size={24}
        />
      </Pressable>
      <Animated.View style={[{overflow: 'hidden'}, {maxHeight: isActive ? 1000 : 0}]}>
        <Text style={[styles.content, contentTypography, {color: contentColor}]}>{i.data}</Text>
      </Animated.View>
    </View>
  );
}

const AccordionWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config} = props;
  const {value, expandedIcon, collapsedIcon} = model.toJS();

  if (_.isEmpty(value)) {
    return <></>;
  }
  return (
    <View ref={ref}>
      {value &&
        JSON.parse(value).map(i => (
          <Accordion key={i.title} {...i} {...{config, modelStyles, expandedIcon, collapsedIcon}} />
        ))}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'expandedIcon',
      defaultValue: '',
      props: {
        label: 'Expanded icon name',
        placeholder: 'minus',
      },
    },
    {
      type: 'codeInput',
      name: 'collapsedIcon',
      defaultValue: '',
      props: {
        label: 'Collapsed icon name',
        placeholder: 'plus',
      },
    },
    {
      type: 'jsonInput',
      name: 'value',
      defaultValue: '[]',
      props: {
        label: 'JSON input',
        placeholder: '[]',
        schema: [
          {key: 'title', type: 'codeInput'},
          {key: 'data', type: 'codeInput'},
        ],
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? JSON.parse(renderedValue) : [];
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('AccordionWidget', AccordionWidget, AccordionWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(accordionWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'accordion'],
});

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  content: {
    paddingVertical: 8,
  },
});
