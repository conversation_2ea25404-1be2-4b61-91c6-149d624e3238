/* eslint-disable react-native/no-inline-styles */
import {mergeWithDefaultStyles} from 'apptile-core';
import React from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {WidgetProps, connectWidget} from 'apptile-core';
import docs from './docs';
import ProgressBar from './BarComponent';

const AnimatedProgressBarWidgetConfig = {
  progress: '',
  progressDuration: '',
  animated: '',
  indeterminate: '',
  indeterminateDuration: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'AnimatedProgressBar',
  type: 'widget',
  name: 'Animated Progress Bar',
  description: 'Progress bar with animation',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'container',
};

export const AnimatedProgressBarWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'progressBarColor',
    props: {
      placeholder: '#000000',
      label: 'Progress Bar Color',
    },
  },
  {
    type: 'numericInput',
    name: 'trackHeight',
    props: {
      placeholder: '3',
      label: 'Progress Bar Height',
      noUnit: true,
    },
  },
];

const AnimatedProgressBarWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config} = props;

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {});
  const progress = model.get('progress') || 0;
  const animated = model.get('animated');
  const indeterminate = model.get('indeterminate');

  const indeterminateDuration = model.get('indeterminateDuration') || 1100;
  const progressDuration = model.get('progressDuration') || 1100;

  const {progressBarColor, trackHeight, borderRadius, ...modelPlatformStyles} = modelStyles
    ? getPlatformStyles(modelStyles)
    : {};

  return (
    <ProgressBar
      progress={parseInt(progress)}
      animated={animated}
      indeterminate={indeterminate}
      containerStyles={[layoutStyles, modelStyles]}
      borderRadius={borderRadius || 0}
      backgroundColor={progressBarColor || 'red'}
      indeterminateDuration={parseInt(indeterminateDuration)}
      progressDuration={parseInt(progressDuration)}
      trackHeight={trackHeight}
    />
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'progress',
      props: {
        label: 'progress',
        placeholder: '50',
      },
    },
    {
      type: 'codeInput',
      name: 'progressDuration',
      props: {
        label: 'progressDuration',
        placeholder: '1000',
      },
    },
    {
      type: 'checkbox',
      name: 'animated',
      props: {
        label: 'Animated',
      },
    },
    {
      type: 'checkbox',
      name: 'indeterminate',
      props: {
        label: 'Indeterminate',
      },
    },
    {
      type: 'codeInput',
      name: 'indeterminateDuration',
      props: {
        label: 'indeterminateDuration',
        placeholder: '1000',
      },
    },
  ],
  // advanced: defaultEditors.advanced,
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  progress: {
    getValue: (model, val) => {
      return val;
    },
  },
  progressDuration: {
    getValue: (model, val) => {
      return val;
    },
  },
  animated: {
    getValue: (model, val) => {
      return val;
    },
  },
  indeterminate: {
    getValue: (model, val) => {
      return val;
    },
  },
  indeterminateDuration: {
    getValue: (model, val) => {
      return val;
    },
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'AnimatedProgressBarWidget',
  AnimatedProgressBarWidget,
  AnimatedProgressBarWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(AnimatedProgressBarWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'animatedProgressBarWidget'],
  },
);
