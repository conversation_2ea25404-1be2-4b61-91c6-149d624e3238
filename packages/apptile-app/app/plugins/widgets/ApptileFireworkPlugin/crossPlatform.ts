import {
  PluginPropertySettings, 
  TriggerActionIdentifier, 
  WidgetStyleEditorOptions, 
  PluginEditorsConfig,
  defaultEditors
} from 'apptile-core';
import {refreshFeed} from './fireworkFeedActions';
type FireworkVideoFeedConfig = {
  source: ''|'discover'|'channel'|'playlist'|'playlistGroup'|'dynamicContent'|'hashtagPlaylist'|'sku'|'singleContent';
  mode: ''|'row'|'column'|'grid';
  streamId: string;
  channel: string;
  playlist: string;
  playlistGroup: string;
  dynamicContentParameters: ''|Record<string, string[]>;
  hashtagFilterExpression: string;
  productIds: ''|string[];
  contentId: string;
  enablePictureInPicture: ''|boolean;
  enableThumbnailAutoplay: any;
  hidePlayIcon: ''|boolean;
  hideTitle: ''|boolean;
  refresh: string;
  loading: ''|boolean;
}; 

export const FireworkWidgetConfig: FireworkVideoFeedConfig = {
  source: '',
  mode: '',
  streamId: '',
  channel: '',
  playlist: '',
  playlistGroup: '',
  dynamicContentParameters: '',
  hashtagFilterExpression: '',
  productIds: '',
  contentId: '',
  enablePictureInPicture: '',
  enableThumbnailAutoplay: { ios: true, android: false },
  hidePlayIcon: '',
  hideTitle: '',
  refresh: 'action',
  loading: ''
};

const identity = {
  getValue: (model, val, sel) => val
};

const toBool = {
  getValue: (model, val, sel) => !!val
}

export const propertySettings: PluginPropertySettings = {
  streamId: identity,
  mode: identity,
  channel: identity,
  playlist: identity,
  playlistGroup: identity,
  dynamicContentParameters: identity,
  hashtagFilterExpression: identity,
  productIds: identity,
  contentId: identity ,
  enablePictureInPicture: identity,
  hidePlayIcon: toBool,
  hideTitle: toBool,
  enableThumbnailAutoplay: {
    getValue(model, val, sel) {
      if (typeof val !== 'object') {
        return {ios: true, android: false};
      } else {
        return { ios: val?.ios ?? true, android: val?.android ?? false }
      }
    }
  },
  refresh: {
    type: TriggerActionIdentifier,
    getValue() {
      return refreshFeed
    }
  }
};

export const fireworkWidgetStyleConfig: WidgetStyleEditorOptions = [
    {
      type: 'numericInput',
      name: 'containerHeight',
      props: {
        label: 'Container height for grid',
        placeholder: '400',
        noUnit: true
      }
    },
    {
      type: 'numericInput',
      name: 'thumbnailHeight',
      props: {
        label: 'Thumbnail height',
        placeholder: '200',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailWidth',
      props: {
        label: 'Thumbnail width',
        placeholder: '265',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailBorderRadius',
      props: {
        label: 'Thumbnail border radius',
        placeholder: '10',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailGap',
      props: {
        label: 'Thumbnail Gap',
        placeholder: '10',
        noUnit: true
      },
    },
    {
      type: 'colorInput',
      name: 'feedBackground',
      props: {
        label: 'Video feed backgroun color'
      }
    },
    {
      type: 'radioGroup',
      name: 'titlePosition',
      props: {
        label: 'Title position',
        options: ['nested', 'stacked'],
      },
      // hidden: config => config.get('type') !== 'navigator',
    },
    {
      type: 'trblValuesEditor',
      name: 'titlePadding',
      props: {
        label: 'Thumbnail title padding',
        options: [
          'thumbnailTitleTopPadding', 
          'thumbnailTitleRightPadding', 
          'thumbnailTitleBottomPadding', 
          'thumbnailTitleLeftPadding'
        ]
      }
    },
    {
      type: 'numericInput',
      name: 'playIconSize',
      props: {
        label: 'Play icon size',
        placeholder: '36',
        noUnit: true
      }
    },
    {
      type: 'colorInput',
      name: 'shadowColor',
      props: {
        label: 'Shadow color'
      }
    },
    {
      type: 'numericInput',
      name: 'shadowOpacity',
      props: {
        label: 'Shadow opacity',
        placeholder: '1',
        noUnit: true
      }
    },
    {
      type: 'trblValuesEditor',
      name: 'contentPadding',
      props: {
        label: 'Thumbnail title padding',
        options: [
          'contentPaddingTop', 
          'contentPaddingRight', 
          'contentPaddingBottom', 
          'contentPaddingLeft'
        ]
      }
    },
    {
      type: 'colorInput',
      name: 'titleBgColor',
      props: {
        label: 'Title background color'
      }
    },
    {
      type: 'numericInput',
      name: 'titleFontSize',
      props: {
        label: 'Title font size',
        placeholder: '12',
        noUnit: true
      }
    },
    
    {
      type: 'numericInput',
      name: 'titleLines',
      props: {
        label: 'Number of lines in title',
        placeholder: '2',
        noUnit: true
      }
    },
    {
      type: 'colorInput',
      name: 'titleTextColor',
      props: {
        label: 'Title text color'
      }
    }
  ];

export const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'radioGroup',
      name: 'source',
      props: {
        label: 'Source',
        options: [
          'discover', 
          'channel', 
          'playlist', 
          'playlistGroup', 
          'dynamicContent', 
          'hashtagPlaylist', 
          'sku', 
          'singleContent'
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'mode',
      props: {
        label: 'Mode',
        options: ['row', 'column', 'grid']
      }
    },
    {
      type: 'codeInput',
      name: 'streamId',
      props: {
        label: 'Stream id',
        placeholder: '{{streamid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'channel',
      props: {
        label: 'Channel id',
        placeholder: '{{channelid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlist',
      props: {
        label: 'Playlist id',
        placeholder: '{{playlistid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlistGroup',
      props: {
        label: '(Exclusively for ios only apps) Playlist group id',
        placeholder: '{{playlistGroupId}}'
      }
    },
    {
      type: 'codeInput',
      name: 'dynamicContentParameters',
      props: {
        label: 'Dynamic content parameters',
        placeholder: '{{ ({ [cohortkey]: [cohortval1, cohortval2] }) }}'
      }
    },
    {
      type: 'codeInput',
      name: 'hashtagFilterExpression',
      props: {
        label: 'Hashtag filter expression',
        placeholder: 'yourtag-*$'
      }
    },
    {
      type: 'codeInput',
      name: 'productIds',
      props: {
        label: 'Product ids',
        placeholder: '{{[prod1, prod2]}}'
      }
    },
    {
      type: 'codeInput',
      name: 'contentId',
      props: {
        label: 'Content id',
        placeholder: '{{contentid}}'
      }
    },
    {
      type: 'checkbox',
      name: 'enablePictureInPicture',
      props: {
        label: 'Enable picture in picture',
      }
    },
    {
      type: 'codeInput',
      name: 'enableThumbnailAutoplay',
      props: {
        label: 'Enable autoplay (impacts android perf)',
        placeholder: '{{({ios: true, android: false})}}'
      }
    },
    {
      type: 'checkbox',
      name: 'hidePlayIcon',
      props: {
        label: 'Hide play icon'
      }
    },
    {
      type: 'checkbox',
      name: 'hideTitle',
      props: {
        label: 'Hide title'
      }
    },
  ],
};
