import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {WidgetRefRegistry} from 'apptile-core';
export function refreshFeed(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.refresh();
}
