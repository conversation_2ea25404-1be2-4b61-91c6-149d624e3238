/* eslint-disable react-native/no-inline-styles */
import React, {useRef, useEffect, useContext} from 'react';
import _ from 'lodash';
import {Text, Platform} from 'react-native';
import { VideoFeed } from '../../state/FireworkGlobalPlugin/firework';
import {
  FireworkWidgetConfig,
  propertySettings,
  fireworkWidgetStyleConfig,
  widgetEditors
} from './crossPlatform';

import {
  PluginListingSettings, 
  connectWidget,
  WidgetRefContext,
} from 'apptile-core';

import docs from './docs';

const pluginListing: PluginListingSettings = {
  labelPrefix: 'fireworkwidget',
  type: 'widget',
  name: 'FireworkWidget',
  description: 'Receive a firework stream',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'video-player',
  defaultHeight: 0,
  defaultWidth: 0,
};

function parseTrblValue(val: number|string, singleVal: number|string, fallback: number) {
  if (val === undefined) {
    val = singleVal;
  }
  let res = parseInt(val);
  if (isNaN(res)) {
    res = fallback;
  }
  return res;
}

function FireworkWidget(props) {
  const {model, modelUpdate, modelStyles, config, triggerEvent, id, instance, pageKey} = props;
  const widgetRefContext = useContext(WidgetRefContext);
  const source = model.get('source') || '';
  const mode = model.get('mode') || '';
  const streamId = model.get('streamId');
  const channel = model.get('channel');
  const playlist = model.get('playlist');
  const playlistGroup = model.get('playlistGroup');
  const dynamicContentParameters = model.get('dynamicContentParameters');
  const hashtagFilterExpression = model.get('hashtagFilterExpression');
  const productIds = model.get('productIds');
  const contentId = model.get('contentId');
  const enablePictureInPicture = model.get('enablePictureInPicture');
  const enableThumbnailAutoplay = model.get('enableThumbnailAutoplay') || {ios: true, android: false};
  const hidePlayIcon = model.get('hidePlayIcon');
  const hideTitle = model.get('hideTitle');
  const feedRef = useRef();
  const refreshRequests = useRef(0);
  
  useEffect(() => {
    widgetRefContext.registerWidgetRef(
      {
        current: {
          refresh() {
            if (feedRef.current) {
              // The load finished callback is only called once so this goes unnoticed
              // const updates = [
              //   {
              //     selector: ['loading'],
              //     newValue: true
              //   }
              // ];
              // modelUpdate(updates);
              logger.info("calling refresh");
              if ((Platform.OS == 'android' && refreshRequests.current > 1) || (Platform.OS == 'ios')) {
                feedRef.current.refresh();
              } 

              if (refreshRequests.current < 1) {
                refreshRequests.current++;
              }
              // console.log("Number of refresh requests: ", refreshRequests.current);
            } else {
              logger.info("no feed to refresh");
            }
          },
        },
      },
      pageKey,
      id,
      instance
    );

    return () => {
      widgetRefContext.unRegisterWidgetRef(pageKey, id, instance);
    }
  }, [feedRef.current, refreshRequests.current]);

  modelStyles.thumbnailTitleTopPadding = parseTrblValue(
    modelStyles.thumbnailTitleTopPadding, modelStyles.titlePadding, 8);
  modelStyles.thumbnailTitleRightPadding = parseTrblValue(
    modelStyles.thumbnailTitleRightPadding, modelStyles.titlePadding, 8);
  modelStyles.thumbnailTitleBottomPadding = parseTrblValue(
    modelStyles.thumbnailTitleBottomPadding, modelStyles.titlePadding, 8);
  modelStyles.thumbnailTitleLeftPadding = parseTrblValue(
    modelStyles.thumbnailTitleLeftPadding, modelStyles.titlePadding, 8);

  modelStyles.contentPaddingTop = parseTrblValue(
    modelStyles.contentPaddingTop, modelStyles.contentPadding, 10); 
  modelStyles.contentPaddingRight = parseTrblValue(
    modelStyles.contentPaddingRight, modelStyles.contentPadding, 10); 
  modelStyles.contentPaddingBottom = parseTrblValue(
    modelStyles.contentPaddingBottom, modelStyles.contentPadding, 10); 
  modelStyles.contentPaddingLeft = parseTrblValue(
    modelStyles.contentPaddingLeft, modelStyles.contentPadding, 10);

  const styleDefaults = {
    containerHeight: modelStyles.containerHeight,
    thumbnailHeight: modelStyles.thumbnailHeight || 200,
    thumbnailWidth: modelStyles.thumbnailWidth || 120,
    thumbnailBorderRadius: modelStyles.thumbnailBorderRadius || 10,
    thumbnailGap: modelStyles.thumbnailGap || 10,
    feedBackground: modelStyles.feedBackground || '#ffffff',
    titlePosition: modelStyles.titlePosition || 'nested',
    titlePadding: {
      top: modelStyles.thumbnailTitleTopPadding || 8,
      right: modelStyles.thumbnailTitleRightPadding || 8,
      bottom: modelStyles.thumbnailTitleBottomPadding || 8,
      left: modelStyles.thumbnailTitleLeftPadding || 8
    },
    contentPadding: {
      top: modelStyles.contentPaddingTop || 10,
      right: modelStyles.contentPaddingRight || 10,
      bottom: modelStyles.contentPaddingBottom || 10,
      left: modelStyles.contentPaddingLeft || 10
    },
    playIconSize: modelStyles.playIconSize || 36,
    shadowOpacity: modelStyles?.shadowOpacity ?? 0,
    shadowColor: modelStyles.shadowColor || '#ffffff',
    titleBgColor: modelStyles.titleBgColor || '#00000088',
    titleFontSize: modelStyles.titleFontSize || 12,
    titleLines: modelStyles.titleLines || 2,
    titleTextColor: modelStyles.titleTextColor || '#ffffff'
  };

  let style; 

  if (mode !== 'row') {
    style = {flex: 1};
    // if (styleDefaults.containerHeight) {
    //   style.height = styleDefaults.containerHeight;
    // } else {
    //   style.flexGrow = 1;
    // }
  } else {
    style = { height: styleDefaults.thumbnailHeight };
  }

  const commonProps = {
    ref: feedRef,
    style,
    mode,
    source,
    videoFeedConfiguration: {
      aspectRatio: mode === "row" ? styleDefaults.thumbnailWidth/styleDefaults.thumbnailHeight : (2/3),
      backgroundColor: styleDefaults.feedBackground,
      contentPadding: styleDefaults.contentPadding,
      cornerRadius: styleDefaults.thumbnailBorderRadius,
      enableAutoplay: Platform.select(enableThumbnailAutoplay),
    //   gridColumns: 2,
      itemSpacing: styleDefaults.thumbnailGap,
      playIcon: { 
        hidden: hidePlayIcon, 
        iconWidth: styleDefaults.playIconSize 
      },
    //   // replayBadge: {}, // docs didn't have the structure
      shadow: {
        opacity: styleDefaults.shadowOpacity,
        color: styleDefaults.shadowColor
      },  // docs didn't have the structure
    //   showAdBadge: false,
     title: {
       // androidFontInfo: {
       //   isCustom: false,
       //   typefaceName: "DEFAULT"
       // },
       backgroundColor: styleDefaults.titleBgColor,
       fontSize: styleDefaults.titleFontSize,
       hidden: hideTitle,
       numberOfLines: styleDefaults.titleLines,
       textColor: styleDefaults.titleTextColor
     },
      titlePadding: styleDefaults.titlePadding,
      titlePosition: styleDefaults.titlePosition,
    },
    // videoPlayerConfiguration: {
    //   aspectRatio: 2/3,
    //   backgroundColor: "transparent",
    //   contentPadding: {top: 10, right: 10, bottom: 10, left: 10},
    //   cornerRadius: 10,
    //   enableAutoplay: false,
    //   gridColumns: 2, 
    //   itemSpacing: 10,
    //   playIcon: {
    //     hidden: false,
    //     iconWidth: 40
    //   },
    //   // replayBadge: // docs didn't have structure
    //   // shadow?
    //   showAdBadge: false,
    //   title: {
    //     // androidFontInfo?
    //     backgroundColor: "transparent",
    //     fontSize: 12,
    //     // gradientDrawable: ?
    //     hidden: false,
    //     iOSFontInfo: {
    //       fontName: "Helvetica"
    //     },
    //     numberOfLines: 2,
    //     textColor: '#ffffff'
    //   },
    //   titlePadding: { top: 8, bottom: 8, right: 8, left: 8},
    //   titlePosition: "nested" // "stacked"
    // },
    onVideoFeedLoadFinished: (err: any) => {
      logger.info("Video feed load finsihed for " + streamId, err);
      const updates = [
        {
          selector: ['loading'],
          newValue: false
        }
      ];
      modelUpdate(updates);
    },
    enablePictureInPicture
  };

  let renderResult = null;
  if (source.length > 0) {
    switch(source) {
      case 'discover':
        renderResult = <VideoFeed {...commonProps} />
        break;
      case 'channel':
        renderResult = (<VideoFeed {...commonProps}
            channel={channel}
          />);
          break;
      case 'playlist':
        if (playlist.length > 0 && channel.length > 0) {
          renderResult = (<VideoFeed {...commonProps}
            playlist={playlist}
            channel={channel}
          />);
        } else {
          renderResult = <Text>Invalid playlistid: {playlist} or channelid: {channel}</Text>
        }
        break;
      case 'playlistGroup':
        renderResult = (<VideoFeed {...commonProps} 
          playlistGroup={playlistGroup}
        />);
        break;
      case 'dynamicContent':
        renderResult = (<VideoFeed {...commonProps}
          channel={channel}
          dynamicContentParameters={dynamicContentParameters}
        />);
        break;
      case 'hashtagPlaylist':
        renderResult = (<VideoFeed {...commonProps}
          channel={channel}
          hashtagFilterExpression={hashtagFilterExpression}
        />);
        break;
      case 'sku':
        renderResult = (<VideoFeed {...commonProps}
          channel={channel}
          productIds={productIds}
        />);
        break;
      case 'singleContent':
        renderResult = (<VideoFeed {...commonProps}
          contentId={contentId}
        />);
        break;
    }
  } 

  // return (
  //   <VideoFeed
  //     style={{height: 800}}
  //     source="channel"
  //     mode="grid"
  //     channel="eA0L4WY"
  //     enablePictureInPicture={true}
  //   />
  // );
  return renderResult;
}


const emptyOnupdate = null;

export default connectWidget('FireworkWidget', FireworkWidget, FireworkWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: fireworkWidgetStyleConfig,
  pluginListing,
  docs,
  themeProfileSel: [],
});
