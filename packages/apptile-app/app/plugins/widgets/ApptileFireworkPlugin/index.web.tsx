/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useContext} from 'react';
import _ from 'lodash';
import {
  FireworkWidgetConfig,
  propertySettings,
  fireworkWidgetStyleConfig,
  widgetEditors
} from './crossPlatform';

import {
  PluginListingSettings, 
  connectWidget,
  WidgetRefContext,
} from 'apptile-core';

import docs from './docs';

const pluginListing: PluginListingSettings = {
  labelPrefix: 'fireworkwidget',
  type: 'widget',
  name: 'FireworkWidget',
  description: 'Receive a firework stream',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'video-player',
  defaultHeight: 0,
  defaultWidth: 0,
};

function FireworkWidget(props) {
  const {model, modelUpdate, modelStyles, config, triggerEvent, pageKey, instance, id} = props;
  const widgetRefContext = useContext(WidgetRefContext);
  const source = model.get('source') || '';
  const mode = model.get('mode') || '';
  const streamId = model.get('streamId');
  // const channel = model.get('channel');
  const playlist = model.get('playlist');
  // const playlistGroup = model.get('playlistGroup');
  const dynamicContentParameters = model.get('dynamicContentParameters');
  const hashtagFilterExpression = model.get('hashtagFilterExpression');
  const productIds = model.get('productIds');
  // const contentId = model.get('contentId');
  // const enablePictureInPicture = model.get('enablePictureInPicture');
 
  const styleDefaults = {
    thumbnailHeight: modelStyles.thumbnailHeight || 200,
    thumbnailWidth: modelStyles.thumbnailWidth || 120,
    thumbnailBorderRadius: modelStyles.thumbnailBorderRadius || 10,
    thumbnailGap: modelStyles.thumbnailGap || 10,
  };
  
  const containerRef = useRef<HTMLDivElement>();

  useEffect(() => {
    widgetRefContext.registerWidgetRef(
      {
        current: {
          refresh() {
              console.log("calling refresh");
          },
        },
      },
      pageKey,
      id,
      instance
    );

    return () => {
      widgetRefContext.unRegisterWidgetRef(pageKey, id, instance);
    }
  }, [containerRef.current]);
  
  useEffect(() => {
    const widgetId = (pageKey + id).replace(/[\s\-\:]/g, '')
    if (containerRef.current && source) {
      let styles = `
          #${widgetId} {
            --fw-thumbnail-height: ${styleDefaults.thumbnailHeight}px;
            --fw-thumbnail-width: ${styleDefaults.thumbnailWidth}px;
            --fw-thumbnail-border-radius: ${styleDefaults.thumbnailBorderRadius}px;
            --fw-thumbnail-gap: ${styleDefaults.thumbnailGap}px;
          }
        `;
      

      let renderedHtml = "Loading firework plugin";

      const commonAttrs = `
      id=${widgetId}
      channel="apptile_demo_account"
      open_id="_blank"
      autoplay="false"
      mode="${mode}"
      `;

      switch(source) {
        case 'singleContent':
        case 'discover':
        case 'playlistGroup':
        case 'channel':
        renderedHtml = `
          <fw-embed-feed
            ${commonAttrs}
          ></fw-embed-feed>
          `;
        break;
        case 'playlist':
        renderedHtml = `
          <fw-embed-feed
            ${commonAttrs}
            playlist="${playlist}"
          ></fw-embed-feed>
          `;
        break;
        case 'dynamicContent':
          renderedHtml = `
            <fw-embed-feed
              ${commonAttrs}
              dynamicContentParameters="${dynamicContentParameters}"
            ></fw-embed-feed>
            `;
        break;
        case 'hashtagPlaylist':
          renderedHtml = `
            <fw-embed-feed
              ${commonAttrs}
              hashtag="${hashtagFilterExpression}"
            ></fw-embed-feed>
            `;
        break;
        case 'sku':
          renderedHtml = `
            <fw-embed-feed
              ${commonAttrs}
              skus="${productIds}"
            ></fw-embed-feed>
            `;
        break;
      }

      // if (customElements.get('fw-storyblock')) {
      if (customElements.get('fw-embed-feed')) {
        containerRef.current.innerHTML = renderedHtml;
      } else {
        const script = document.createElement("script");
        // script.src = "https://asset.fwcdn3.com/js/storyblock.js";
        script.src = "https://asset.fwcdn3.com/js/embed-feed.js";

        document.head.appendChild(script);
        script.onload = () => {
          containerRef.current.innerHTML = renderedHtml;
        }
        script.onerror = () => {
          containerRef.current.innerText = 'Could not load firework plugin';
        }
      }

      let fireworkStyles = document.querySelector('style#' + widgetId);
      if (!fireworkStyles) {
        fireworkStyles = document.createElement('style');
        fireworkStyles.setAttribute('id', widgetId);
        document.head.appendChild(fireworkStyles);
      }
      fireworkStyles.innerHTML = styles;
    } else {
      logger.info("[FIREWORKWIDGET] Skipping because container was not ready");
    }
  }, [streamId, containerRef.current, mode, styleDefaults]);

  return (
    <div ref={containerRef}>Loading firework plugin</div>
  );
}

const emptyOnupdate = null;

export default connectWidget('FireworkWidget', FireworkWidget, FireworkWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: fireworkWidgetStyleConfig,
  pluginListing,
  docs,
  themeProfileSel: [],
});
