/* eslint-disable react-native/no-inline-styles */
import React, {useRef} from 'react';
import _ from 'lodash';
import {Text} from 'react-native';
import { StoryBlock } from '../../state/FireworkGlobalPlugin/firework';

import {
  PluginEditorsConfig, 
  WidgetStyleEditorOptions,
  PluginListingSettings, 
  PluginPropertySettings,
  defaultEditors,
  connectWidget,
  mergeWithDefaultStyles,
} from 'apptile-core';

import docs from './docs';

type FireworkStoryBlockConfig = {
  source: ''|'discover'|'channel'|'playlist'|'playlistGroup'|'dynamicContent'|'hashtagPlaylist'|'sku'|'singleContent';
  mode: ''|'row'|'column'|'grid';
  streamId: string;
  channel: string;
  playlist: string;
  playlistGroup: string;
  dynamicContentParameters: ''|Record<string, string[]>;
  hashtagFilterExpression: string;
  productIds: ''|string[];
  contentId: string;
}; 

const FireworkWidgetConfig: FireworkStoryBlockConfig = {
  source: '',
  mode: '',
  streamId: '',
  channel: '',
  playlist: '',
  playlistGroup: '',
  dynamicContentParameters: '',
  hashtagFilterExpression: '',
  productIds: '',
  contentId: ''
};

const identity = {
  getValue: (model, val, sel) => val
};

const propertySettings: PluginPropertySettings = {
  streamId: identity,
  mode: identity,
  channel: identity,
  playlist: identity,
  playlistGroup: identity,
  dynamicContentParameters: identity,
  hashtagFilterExpression: identity,
  productIds: identity,
  contentId: identity 
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'radioGroup',
      name: 'source',
      props: {
        label: 'Source',
        options: [
          'discover', 
          'channel', 
          'playlist', 
          'playlistGroup', 
          'dynamicContent', 
          'hashtagPlaylist', 
          'sku', 
          'singleContent'
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'mode',
      props: {
        label: 'Mode',
        options: ['row', 'column', 'grid']
      }
    },
    {
      type: 'codeInput',
      name: 'streamId',
      props: {
        label: 'Stream id',
        placeholder: '{{streamid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'channel',
      props: {
        label: 'Channel id',
        placeholder: '{{channelid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlist',
      props: {
        label: 'Playlist id',
        placeholder: '{{playlistid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlistGroup',
      props: {
        label: '(Exclusively for ios only apps) Playlist group id',
        placeholder: '{{playlistGroupId}}'
      }
    },
    {
      type: 'codeInput',
      name: 'dynamicContentParameters',
      props: {
        label: 'Dynamic content parameters',
        placeholder: '{{ ({ [cohortkey]: [cohortval1, cohortval2] }) }}'
      }
    },
    {
      type: 'codeInput',
      name: 'hashtagFilterExpression',
      props: {
        label: 'Hashtag filter expression',
        placeholder: 'yourtag-*$'
      }
    },
    {
      type: 'codeInput',
      name: 'productIds',
      props: {
        label: 'Product ids',
        placeholder: '{{[prod1, prod2]}}'
      }
    },
    {
      type: 'codeInput',
      name: 'contentId',
      props: {
        label: 'Content id',
        placeholder: '{{contentid}}'
      }
    },
    {
      type: 'checkbox',
      name: 'enablePictureInPicture',
      props: {
        label: 'Enable picture in picture',
      }
    }
  ],
  layout: [...defaultEditors.layout],
};


const pluginListing: PluginListingSettings = {
  labelPrefix: 'fireworkwidget',
  type: 'widget',
  name: 'FireworkSBWidget',
  description: 'Receive a firework stream',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'video-player',
  defaultHeight: 0,
  defaultWidth: 0,
};

const fireworkWidgetStyleConfig: WidgetStyleEditorOptions = [];

function FireworkSBWidget(props) {
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;
  const source = model.get('source') || '';
  const mode = model.get('mode') || '';
  const streamId = model.get('streamId');
  const channel = model.get('channel');
  const playlist = model.get('playlist');
  const playlistGroup = model.get('playlistGroup');
  const dynamicContentParameters = model.get('dynamicContentParameters');
  const hashtagFilterExpression = model.get('hashtagFilterExpression');
  const productIds = model.get('productIds');
  const contentId = model.get('contentId');
  const enablePictureInPicture = model.get('enablePictureInPicture');

  const feedRef = useRef();
  // const handleRefresh = useCallback(() => {
  //   if (feedRef.current) {
  //     console.log("calling refresh");
  //     feedRef.current.refresh();
  //   } else {
  //     console.log("no feed to refresh");
  //   }
  // }, [feedRef.current]);
  
  const styleDefaults = {
    containerHeight: modelStyles.containerHeight,
    thumbnailHeight: modelStyles.thumbnailHeight || 200,
    thumbnailWidth: modelStyles.thumbnailWidth || 120,
    thumbnailBorderRadius: modelStyles.thumbnailBorderRadius || 10,
    thumbnailGap: modelStyles.thumbnailGap || 10,
  };

  let style; 

  if (mode !== 'row') {
    style = {flex: 1};
    // if (styleDefaults.containerHeight) {
    //   style.height = styleDefaults.containerHeight;
    // } else {
    //   style.flexGrow = 1;
    // }
  } else {
    style = { flex: 1 };
  }

  const commonProps = {
    style,
    source,
    storyBlockConfiguration: {
      height: '100%',
      countdownTimerConfiguration: {
        isHidden: false,
        appearance: "light", // "dark"
      },
      playerStyle: "full",
    },
    enablePictureInPicture: true,
    onStoryBlockLoadFinished: (err: any) => console.log("Video feed load finsihed for " + streamId, err),
  };

  let renderResult = null;
  if (source.length > 0) {
    switch(source) {
      case 'discover':
        renderResult = <StoryBlock {...commonProps} />
        break;
      case 'channel':
        renderResult = (<StoryBlock {...commonProps}
            channel={channel}
          />);
          break;
      case 'playlist':
        if (playlist.length > 0 && channel.length > 0) {
          renderResult = (<StoryBlock {...commonProps}
            playlist={playlist}
            channel={channel}
          />);
        } else {
          renderResult = <Text>Invalid playlistid: {playlist} or channelid: {channel}</Text>
        }
        break;
      case 'playlistGroup':
        renderResult = (<StoryBlock {...commonProps} 
          playlistGroup={playlistGroup}
        />);
        break;
      case 'dynamicContent':
        renderResult = (<StoryBlock {...commonProps}
          channel={channel}
          dynamicContentParameters={dynamicContentParameters}
        />);
        break;
      case 'hashtagPlaylist':
        renderResult = (<StoryBlock {...commonProps}
          channel={channel}
          hashtagFilterExpression={hashtagFilterExpression}
        />);
        break;
      case 'sku':
        renderResult = (<StoryBlock {...commonProps}
          channel={channel}
          productIds={productIds}
        />);
        break;
      case 'singleContent':
        renderResult = (<StoryBlock {...commonProps}
          contentId={contentId}
        />);
        break;
    }
  } 

  // return (
  //   <StoryBlock
  //     style={{height: 800}}
  //     source="channel"
  //     mode="grid"
  //     channel="eA0L4WY"
  //     enablePictureInPicture={true}
  //   />
  // );
  return renderResult;
}


const emptyOnupdate = null;

export default connectWidget('FireworkSBWidget', FireworkSBWidget, FireworkWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(fireworkWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: [],
});
