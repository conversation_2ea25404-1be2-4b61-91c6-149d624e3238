/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef} from 'react';
import _ from 'lodash';

import {
  PluginEditorsConfig, 
  WidgetStyleEditorOptions,
  PluginListingSettings, 
  PluginPropertySettings,
  defaultEditors,
  connectWidget,
  mergeWithDefaultStyles,
} from 'apptile-core';

import docs from './docs';

type FireworkVideoFeedConfig = {
  source: ''|'discover'|'channel'|'playlist'|'playlistGroup'|'dynamicContent'|'hashtagPlaylist'|'sku'|'singleContent';
  mode: ''|'row'|'column'|'grid';
  streamId: string;
  channel: string;
  playlist: string;
  playlistGroup: string;
  dynamicContentParameters: ''|Record<string, string[]>;
  hashtagFilterExpression: string;
  productIds: ''|string[];
  contentId: string;
};

const FireworkWidgetConfig: FireworkVideoFeedConfig = {
  source: '',
  mode: '',
  streamId: '',
  channel: '',
  playlist: '',
  playlistGroup: '',
  dynamicContentParameters: '',
  hashtagFilterExpression: '',
  productIds: '',
  contentId: ''
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'radioGroup',
      name: 'source',
      props: {
        label: 'Source',
        options: [
          'discover', 
          'channel', 
          'playlist', 
          'playlistGroup', 
          'dynamicContent', 
          'hashtagPlaylist', 
          'sku', 
          'singleContent'
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'mode',
      props: {
        label: 'Mode',
        options: ['row', 'column', 'grid']
      }
    },
    {
      type: 'codeInput',
      name: 'streamId',
      props: {
        label: 'Stream id',
        placeholder: '{{streamid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'channel',
      props: {
        label: 'Channel id',
        placeholder: '{{channelid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlist',
      props: {
        label: 'Playlist id',
        placeholder: '{{playlistid}}'
      }
    },
    {
      type: 'codeInput',
      name: 'playlistGroup',
      props: {
        label: '(Exclusively for ios only apps) Playlist group id',
        placeholder: '{{playlistGroupId}}'
      }
    },
    {
      type: 'codeInput',
      name: 'dynamicContentParameters',
      props: {
        label: 'Dynamic content parameters',
        placeholder: '{{ ({ [cohortkey]: [cohortval1, cohortval2] }) }}'
      }
    },
    {
      type: 'codeInput',
      name: 'hashtagFilterExpression',
      props: {
        label: 'Hashtag filter expression',
        placeholder: 'yourtag-*$'
      }
    },
    {
      type: 'codeInput',
      name: 'productIds',
      props: {
        label: 'Product ids',
        placeholder: '{{[prod1, prod2]}}'
      }
    },
    {
      type: 'codeInput',
      name: 'contentId',
      props: {
        label: 'Content id',
        placeholder: '{{contentid}}'
      }
    },
    {
      type: 'checkbox',
      name: 'enablePictureInPicture',
      props: {
        label: 'Enable picture in picture',
      }
    }
  ],
  layout: [...defaultEditors.layout],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'fireworksbwidget',
  type: 'widget',
  name: 'FireworkSBWidget',
  description: 'Firework storyblock widget',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'video-player',
  defaultHeight: 0,
  defaultWidth: 0,
};

const fireworkWidgetStyleConfig: WidgetStyleEditorOptions = [
    {
      type: 'numericInput',
      name: 'containerHeight',
      props: {
        label: 'Container height for grid',
        placeholder: '400',
      }
    },
    {
      type: 'numericInput',
      name: 'thumbnailHeight',
      props: {
        label: 'Thumbnail height',
        placeholder: '200',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailWidth',
      props: {
        label: 'Thumbnail width',
        placeholder: '265',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailBorderRadius',
      props: {
        label: 'Thumbnail border radius',
        placeholder: '10',
        noUnit: true
      },
    },
    {
      type: 'numericInput',
      name: 'thumbnailGap',
      props: {
        label: 'Thumbnail Gap',
        placeholder: '10',
        noUnit: true
      },
    },
  ];

function FireworkSBWidget(props) {
  const {model, modelUpdate, modelStyles, config, triggerEvent, pageKey, id} = props;
  const source = model.get('source') || '';
  const mode = model.get('mode') || '';
  const streamId = model.get('streamId');
  // const channel = model.get('channel');
  const playlist = model.get('playlist');
  // const playlistGroup = model.get('playlistGroup');
  const dynamicContentParameters = model.get('dynamicContentParameters');
  const hashtagFilterExpression = model.get('hashtagFilterExpression');
  const productIds = model.get('productIds');
  // const contentId = model.get('contentId');
  // const enablePictureInPicture = model.get('enablePictureInPicture');
 
  const styleDefaults = {
    thumbnailHeight: modelStyles.thumbnailHeight || 200,
    thumbnailWidth: modelStyles.thumbnailWidth || 120,
    thumbnailBorderRadius: modelStyles.thumbnailBorderRadius || 10,
    thumbnailGap: modelStyles.thumbnailGap || 10,
  };
  
  const containerRef = useRef<HTMLDivElement>();
  
  // const feed = streamId => `
  //   <fw-storyblock
  //     channel="apptile_demo_account"
  //     video="${streamId}"
  //     max_videos="3"
  //     autoplay="true"
  //   ></fw-storyblock>
  // `
  
  useEffect(() => {
    const widgetId = (pageKey + id).replace(/[\s\-\:]/g, '')
    if (containerRef.current && source) {
      let styles = `
          #${widgetId} {
            --fw-thumbnail-height: ${styleDefaults.thumbnailHeight}px;
            --fw-thumbnail-width: ${styleDefaults.thumbnailWidth}px;
            --fw-thumbnail-border-radius: ${styleDefaults.thumbnailBorderRadius}px;
            --fw-thumbnail-gap: ${styleDefaults.thumbnailGap}px;
          }
        `;
      

      let renderedHtml = "Loading firework plugin";

      const commonAttrs = `
      id=${widgetId}
      channel="apptile_demo_account"
      open_id="_blank"
      autoplay="false"
      mode="${mode}"
      `;

      switch(source) {
        case 'singleContent':
        case 'discover':
        case 'playlistGroup':
        case 'channel':
        renderedHtml = `
          <fw-storyblock
            ${commonAttrs}
          ></fw-storyblock>
          `;
        break;
        case 'playlist':
        renderedHtml = `
          <fw-storyblock
            ${commonAttrs}
            playlist="${playlist}"
          ></fw-storyblock>
          `;
        break;
        case 'dynamicContent':
          renderedHtml = `
            <fw-storyblock
              ${commonAttrs}
              dynamicContentParameters="${dynamicContentParameters}"
            ></fw-storyblock>
            `;
        break;
        case 'hashtagPlaylist':
          renderedHtml = `
            <fw-storyblock
              ${commonAttrs}
              hashtag="${hashtagFilterExpression}"
            ></fw-storyblock>
            `;
        break;
        case 'sku':
          renderedHtml = `
            <fw-storyblock
              ${commonAttrs}
              skus="${productIds}"
            ></fw-storyblock>
            `;
        break;
      }

      if (customElements.get('fw-storyblock')) {
        containerRef.current.innerHTML = renderedHtml;
      } else {
        const script = document.createElement("script");
        script.src = "https://asset.fwcdn3.com/js/storyblock.js";

        document.head.appendChild(script);
        script.onload = () => {
          containerRef.current.innerHTML = renderedHtml;
        }
        script.onerror = () => {
          containerRef.current.innerText = 'Could not load firework plugin';
        }
      }

      let fireworkStyles = document.querySelector('style#' + widgetId);
      if (!fireworkStyles) {
        fireworkStyles = document.createElement('style');
        fireworkStyles.setAttribute('id', widgetId);
        document.head.appendChild(fireworkStyles);
      }
      fireworkStyles.innerHTML = styles;
    } else {
      logger.info("[FIREWORKWIDGET] Skipping because container was not ready");
    }
  }, [streamId, containerRef.current, mode, styleDefaults]);

  return (
    <div ref={containerRef}>Loading firework plugin</div>
  );
}


const propertySettings: PluginPropertySettings = {
  streamId: {
    getValue: (model, val, sel) => {
      return val;
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('FireworkSBWidget', FireworkSBWidget, FireworkWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(fireworkWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: [],
});
