import React, {useCallback, useEffect, useRef} from 'react';
import {Text, View, StyleSheet, FlatList} from 'react-native';

import docs from './docs';
import {PluginEditorsConfig} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {defaultEditors} from 'apptile-core';

const AssistantAIWidgetConfig = {
  value: '',
  messages: [],
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'assistantAI',
  type: 'widget',
  name: 'AssistantA<PERSON>',
  description: 'AssistantAI UI',
  layout: {},
  defaultHeight: 20,
  defaultWidth: 'auto',
  section: 'Inputs',
  icon: '',
};

const AssistantAIWidget = React.forwardRef((props, forwardedRef) => {
  const flatListRef = useRef<FlatList>(null);

  const {model, modelUpdate, config, modelStyles, triggerEvent} = props;
  // const layout = config.get('layout');
  // const layoutStyles = layout ? layout.getFlexProperties() : {};
  // const {typography, color, disabledColor, disabledBackgroundColor, backgroundColor, ...restStyles} = modelStyles || {};

  let messages = model.get('messages');
  if (!Array.isArray(messages)) messages = [];
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      flatListRef.current.scrollToOffset({offset: 0, animated: true});
    }
  }, [messages.length]);

  const setRef = useCallback(
    ref => {
      if (forwardedRef !== null) {
        if (typeof forwardedRef === 'function') {
          forwardedRef(ref);
        } else {
          if (!forwardedRef.hasOwnProperty('current')) {
            logger.error(
              'Unexpected ref object provided for %s. ' + 'Use either a ref-setter function or React.createRef().',
            );
          }
          forwardedRef.current = ref;
        }
      }
    },
    [forwardedRef],
  );

  return (
    <View ref={setRef} style={styles.container}>
      <FlatList
        ref={flatListRef}
        style={styles.chatArea}
        contentContainerStyle={styles.chatAreaContent}
        data={messages}
        inverted
        renderItem={({item}) => (
          <View style={item.role === 'user' ? styles.sentMessage : styles.receivedMessage}>
            <Text style={item.role === 'user' ? styles.sentMessageText : styles.receivedMessageText}>
              {item.content[0].text.value}
            </Text>
          </View>
        )}
        keyExtractor={item => item.id.toString()}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f2',
  },
  chatArea: {
    flex: 1,
  },
  chatAreaContent: {
    padding: 12,
  },
  sentMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#1B1B1B',
    borderRadius: 16,
    borderBottomRightRadius: 0,
    padding: 12,
    marginBottom: 16,
    maxWidth: '70%',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#e6e6e6',
    borderRadius: 16,
    borderBottomLeftRadius: 0,
    padding: 12,
    marginBottom: 16,
    maxWidth: '70%',
  },
  sentMessageText: {
    color: '#ffffff',
  },
  receivedMessageText: {
    color: '#000000',
  },
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (_model, renderedValue, _selector) => {
      return renderedValue;
    },
  },
  messages: {
    getValue: (_model, renderedValue, _selector) => {
      return Array.isArray(renderedValue) ? renderedValue : [];
    },
  },
  onMessage: {
    type: EventTriggerIdentifier,
  },
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'messages',
      props: {
        label: 'Messages',
      },
    },
  ],
  layout: defaultEditors.layout,
};

export const assistantAIWidgetStyleConfig: WidgetStyleEditorOptions = [];

const emptyOnupdate = null;

export default connectWidget(
  'AssistantAIWidget',
  AssistantAIWidget,
  AssistantAIWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(assistantAIWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile'],
  },
);
