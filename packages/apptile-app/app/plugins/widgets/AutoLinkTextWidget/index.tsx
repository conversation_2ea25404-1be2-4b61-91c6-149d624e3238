/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget, WidgetProps} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import {makeBoolean} from 'apptile-core';
import {usePlaceHolder} from 'apptile-core';
import Autolink from 'react-native-autolink';
import {View, Text, Linking, Pressable} from 'react-native';

const AutoLinkTextWidgetConfig = {
  value: 'Text',
  link: '',
  adjustsFontSizeToFit: false,
  isAutoDetectEnabled: true,
  minFontScale: 1,
  numLines: 1,
  isLoading: false,
  detectEmail: true,
  detectUrl: true,
  detectPhone: true,
};

/**
 * Supported Url
 * mailto
 * tel
 * sms
 * https/https
 */

const handleLinking = async (url: string) => {
  const supported = await Linking.canOpenURL(url);
  if (supported) {
    await Linking.openURL(url);
  }
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'autoLinkText',
  type: 'widget',
  name: 'AutoLink Text',
  description: 'Display Auto link (Phone, email, url) Text on screen',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'text',
};

export const textWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
    },
  },
  {
    type: 'colorInput',
    name: 'linkColor',
    props: {
      label: 'Link Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
    },
  },
];

const AutoLinkTextWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config} = props;
  const Placeholder = usePlaceHolder();
  const value = model.get('value')?.toString();

  const layout = config.get('layout');
  const isLoading = !!model.get('isLoading');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});

  const {shadowColor, shadowOffset, shadowRadius, ...modelPlatformStyles} = modelStyles
    ? getPlatformStyles(modelStyles)
    : {};

  const {typography, linkColor, ...restModelPlatformStyles} = modelPlatformStyles;
  const horizontalAlign = _.isEmpty(model.get('horizontalAlign', 'auto'))
    ? 'auto'
    : model.get('horizontalAlign', 'auto');
  const verticalAlign = _.isEmpty(model.get('verticalAlign', 'auto')) ? 'auto' : model.get('verticalAlign', 'auto');

  const overflowType = model.get('overflowType') ? model.get('overflowType') : 'hidden';
  const adjustsFontSizeToFit = model.get('adjustsFontSizeToFit', false);
  const sizeAdjustProps = adjustsFontSizeToFit
    ? {
        adjustsFontSizeToFit,
        minimumFontScale: _.clamp(_.toNumber(model.get('minFontScale', 1)), 0, 1),
        numberOfLines: _.clamp(_.toNumber(model.get('numLines', 1)), 1, Number.MAX_SAFE_INTEGER),
      }
    : {};

  const isAutoDetectEnabled = model.get('isAutoDetectEnabled', true);
  const detectEmail = model.get('detectEmail', true);
  const detectUrl = model.get('detectUrl', true);
  const detectPhone = model.get('detectPhone', true);
  const link = model.get('link', '');

  return isLoading ? (
    <Placeholder
      layoutStyles={{...layoutStyles, ...restModelPlatformStyles, height: typography?.fontSize, minWidth: '75%'}}
    />
  ) : isAutoDetectEnabled ? (
    <View ref={ref} style={[layoutStyles, {overflow: overflowType}]}>
      <Autolink
        text={value ? value : ''}
        {...sizeAdjustProps}
        style={[
          restModelPlatformStyles,
          typography,
          {
            textAlign: horizontalAlign,
            textAlignVertical: verticalAlign,
          },
          {
            textShadowColor: shadowColor,
            textShadowOffset: shadowOffset,
            textShadowRadius: shadowRadius,
          },
        ]}
        url={detectUrl}
        email={detectEmail}
        phone={detectPhone}
        linkStyle={{color: linkColor ?? 'blue'}}
      />
    </View>
  ) : (
    <Pressable ref={ref} onPress={!_.isEmpty(link) ? () => handleLinking(link) : undefined}>
      <Text
        {...sizeAdjustProps}
        style={[
          layoutStyles,
          restModelPlatformStyles,
          typography,
          {
            textAlign: horizontalAlign,
            textAlignVertical: verticalAlign,
          },
          {
            textShadowColor: shadowColor,
            textShadowOffset: shadowOffset,
            textShadowRadius: shadowRadius,
          },
        ]}>
        {value ? value : ''}
      </Text>
    </Pressable>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: defaultEditors.basic,
  advanced: [
    {
      type: 'checkbox',
      name: 'isAutoDetectEnabled',
      props: {
        label: 'Enable auto detect',
      },
    },
    {
      type: 'codeInput',
      name: 'link',
      props: {
        label: 'Link',
      },
      hidden: model => model.get('isAutoDetectEnabled'),
    },
    {
      type: 'checkbox',
      name: 'detectEmail',
      props: {
        label: 'Link Email',
      },
      hidden: model => !model.get('isAutoDetectEnabled'),
    },
    {
      type: 'checkbox',
      name: 'detectUrl',
      props: {
        label: 'Link Url',
      },
      hidden: model => !model.get('isAutoDetectEnabled'),
    },
    {
      type: 'checkbox',
      name: 'detectPhone',
      props: {
        label: 'Link Phone number',
      },
      hidden: model => !model.get('isAutoDetectEnabled'),
    },
    {
      type: 'checkbox',
      name: 'adjustsFontSizeToFit',
      props: {
        label: 'Adjust font size to fit',
      },
    },
    {
      type: 'codeInput',
      name: 'minFontScale',
      props: {
        label: 'Minimum font scale',
      },
      hidden: model => !model.get('adjustsFontSizeToFit'),
    },
    {
      type: 'codeInput',
      name: 'numLines',
      props: {
        label: 'Number of lines',
      },
      hidden: model => !model.get('adjustsFontSizeToFit'),
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      defaultValue: false,
      props: {
        label: 'Loading State',
      },
    },
  ],
  layout: [
    {
      type: 'radioGroup',
      name: 'horizontalAlign',
      props: {
        label: 'Horizontal Alignment',
        options: [
          {icon: 'alpha-a', value: 'auto'},
          {icon: 'format-align-left', value: 'left'},
          {icon: 'format-align-center', value: 'center'},
          {icon: 'format-align-right', value: 'right'},
          {icon: 'format-align-justify', value: 'justify'},
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'verticalAlign',
      props: {
        label: 'Vertical Alignment',
        options: [
          {icon: 'alpha-a', value: 'auto'},
          {icon: 'format-vertical-align-top', value: 'top'},
          {icon: 'format-vertical-align-center', value: 'center'},
          {icon: 'format-vertical-align-bottom', value: 'bottom'},
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'overflowType',
      props: {
        label: 'Overflow',
        options: ['scroll', 'hidden'],
      },
    },
    ...defaultEditors.layout,
  ],
  animations: defaultEditors.animations,
};

const propertySettings: PluginPropertySettings = {
  isLoading: {
    getValue: (model, val) => {
      return makeBoolean(val);
    },
  },
  minFontScale: {
    getValue: (model, val) => {
      return val;
    },
  },
  numLines: {
    getValue: (model, val) => {
      return val;
    },
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'AutoLinkTextWidget',
  AutoLinkTextWidget,
  AutoLinkTextWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(textWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'autoLinkText'],
  },
);
