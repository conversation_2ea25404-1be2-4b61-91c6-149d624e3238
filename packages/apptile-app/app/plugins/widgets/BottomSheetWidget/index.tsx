import React from 'react';
import {View} from 'react-native';
import {Portal} from '@gorhom/portal';

import docs from './docs';
import {WidgetProps, connectWidget} from 'apptile-core';
import {ApptileFlexbox} from 'apptile-core';
import {renderWidgetTreeNode} from 'apptile-core';
import {BottomSheet} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';

import {WidgetStyleEditorOptions} from '../../../styles/types';
import {
  EventTriggerIdentifier,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {BottomSheetWidgetConfig, BottomSheetWidgetProps} from './type';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';

const bottomSheetWidgetConfig: BottomSheetWidgetConfig = {
  show: 'Action_show',
  hide: 'Action_hide',
  value: true,
  snapPoint: '100%',
  borderRadius: 12,
  hideBackdrop: false,
  backdropColor: '#0c0b0b24',
  onExpand: 'Event_onExpand',
  onCollapse: 'Event_onCollapse',
  onBackdropPress: 'Event_onBackdropPress',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'bottomSheet',
  type: 'widget',
  name: 'Bottom Sheet',
  description: 'Bottom Sheet',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Layout',
  layout: {
    flex: 1,
    flexDirection: 'column',
  },
  icon: '',
};

export const BottomSheetWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#FFFFFF',
    },
  },
];

const BottomSheetWidget = React.forwardRef<View, BottomSheetWidgetProps>((props, ref) => {
  return (
    <Portal hostName={'root'}>
      {props.model.get('value') && (
        <BottomSheet
          snapPoint={props.model.get('snapPoint')}
          borderRadius={parseFloat(props.model.get('borderRadius') || '0')}
          hideBackdrop={props.model.get('hideBackdrop')}
          backdropColor={props.model.get('backdropColor') || '#0c0b0b24'}
          style={props.modelStyles}
          onExpand={() => props.triggerEvent('onExpand')}
          onCollapse={() => props.triggerEvent('onCollapse')}
          onBackdropPress={() => props.triggerEvent('onBackdropPress')}
          onCloseComplete={() => {
            props.modelUpdate([
              {
                selector: "value",
                newValue: false,
              },
            ])
          }}
        >
          <ApptileFlexbox
            key={props.id + '_' + props.instance}
            ref={ref}
            containerId={props.id}
            {...props}
            renderWidgetTreeNode={renderWidgetTreeNode({...props})}
            message="Start by dropping a container widget."
          />
        </BottomSheet>
      )}
    </Portal>
  );
});

const propertySettings: PluginPropertySettings = {
  show: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const changeSets = [{selector: selector.concat(['value']), newValue: true}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  hide: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const changeSets = [{selector: selector.concat(['value']), newValue: false}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  value: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  snapPoint: {
    getValue: (model, renderedValue, _selector) => renderedValue,
  },
  borderRadius: {
    getValue: (model, renderedValue, _selector) => renderedValue,
  },
  hideBackdrop: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  backdropColor: {
    getValue: (model, renderedValue, _selector) => renderedValue,
  },
  onExpand: {
    type: EventTriggerIdentifier,
  },
  onCollapse: {
    type: EventTriggerIdentifier,
  },
  onBackdropPress: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<BottomSheetWidgetConfig> = {
  basic: [
    {
      type: 'checkbox',
      name: 'value',
      props: {
        label: 'Show',
      },
    },
    {
      type: 'codeInput',
      name: 'snapPoint',
      props: {
        label: 'Snap Point',
        placeholder: '100%',
      },
    },
    {
      type: 'codeInput',
      name: 'borderRadius',
      props: {
        label: 'Border Radius',
        placeholder: '12',
      },
    },
    {
      type: 'checkbox',
      name: 'hideBackdrop',
      props: {
        label: 'Hide backdrop',
      },
    },
    {
      type: 'colorInput',
      name: 'backdropColor',
      props: {
        label: 'Backdrop color',
        placeholder: '#0c0b0b24',
      },
      hidden: config => config.get('hideBackdrop'),
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
};

export default connectWidget('BottomSheetWidget', BottomSheetWidget, bottomSheetWidgetConfig, null, editors, {
  propertySettings,
  widgetStyleConfig: BottomSheetWidgetStyleConfig,
  pluginListing,
  docs,
});
