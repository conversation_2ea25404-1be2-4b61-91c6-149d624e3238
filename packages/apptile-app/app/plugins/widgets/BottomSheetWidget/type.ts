import {<PERSON><PERSON><PERSON>, ViewStyle} from 'react-native';
import {PluginModelChange, PluginModelType} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';

export type BottomSheetWidgetConfig = {
  show: any;
  hide: any;
  value: boolean;
  snapPoint: string;
  borderRadius: number;
  hideBackdrop: boolean;
  backdropColor: string;
  onExpand: any;
  onCollapse: any;
  onBackdropPress: any;
};

export type BottomSheetWidgetProps = {
  id: string;
  instance: number;
  model: PluginModelType;
  modelStyles: StyleProp<ViewStyle>;
  config: PluginConfigType<BottomSheetWidgetConfig>;
  modelUpdate: (changes: PluginModelChange[]) => void;
  triggerEvent: (pluginId: string, event?: string) => void;
};
