import {ApptileAnalytics} from '@/root/app/common/ApptileAnalytics';
import {PluginEditorsConfig} from 'apptile-core';
import {makeBoolean} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import {WidgetStyleEditorOptions, useIsEditable} from 'apptile-core';
import {toInteger} from 'lodash';
import React, {useCallback} from 'react';
import {ActivityIndicator, Platform, Pressable, Text, View, Animated} from 'react-native';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {performHapticFeedback} from 'apptile-core';
import {getShadowStyle} from 'apptile-core';
import {defaultEditors, hapticEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import _ from 'lodash';

const ButtonWidgetConfig = {
  value: 'Button',
  onTap: '',
  loading: '',
  disabled: '',
  enableHaptics: '',
  hapticMethod: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'button',
  type: 'widget',
  name: 'Button',
  description: 'Display a Tappable button or link',
  defaultHeight: 20,
  defaultWidth: 'auto',
  section: 'Inputs',
  icon: 'button',
};
// FIXME:  debounce need to be added
const ButtonWidget = React.forwardRef((props, forwardedRef) => {
  const {model, config, modelStyles, triggerEvent, isAnimated, animations} = props;
  const value = model.get('value');
  const enableHaptics = model.get('enableHaptics');
  const hapticMethod = model.get('hapticMethod');
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {};
  const elevation = toInteger(config.getIn(['style', 'elevation'], 0));
  const shadowStyles = {...getShadowStyle(elevation), elevation};
  const isEditable = useIsEditable();
  const horizontalAlign = _.isEmpty(model.get('horizontalAlign', 'center'))
    ? 'center'
    : model.get('horizontalAlign', 'center');
  const verticalAlign = _.isEmpty(model.get('verticalAlign', 'auto')) ? 'auto' : model.get('verticalAlign', 'auto');

  const {
    typography,
    color,
    disabledColor,
    disabledBackgroundColor,
    backgroundColor,
    padding,
    paddingTop,
    paddingBottom,
    paddingLeft,
    paddingRight,
    ...restStyles
  } = modelStyles ? modelStyles : {};

  const disabled = model.get('disabled');
  const loading = model.get('loading');

  const currentBackgroundColor = disabled ? disabledBackgroundColor : backgroundColor;
  const currentColor = disabled ? disabledColor : color;

  const onButtonTap = useCallback(() => {
    if (!disabled && !loading) {
      if (enableHaptics) performHapticFeedback(hapticMethod);
      triggerEvent('onTap');
      if (model.get('analytics') && model.getIn(['analytics', 'enabled'], false)) {
        ApptileAnalytics.sendEvent(
          model.getIn(['analytics', 'type']),
          model.getIn(['analytics', 'name']),
          model.getIn(['analytics', 'params']),
        );
      }
    }
  }, [disabled, enableHaptics, hapticMethod, loading, model, triggerEvent]);

  const setRef = useCallback(
    ref => {
      if (forwardedRef !== null) {
        if (typeof forwardedRef === 'function') {
          forwardedRef(ref);
        } else {
          if (!forwardedRef.hasOwnProperty('current')) {
            logger.error(
              'Unexpected ref object provided for %s. ' + 'Use either a ref-setter function or React.createRef().',
            );
          }
          forwardedRef.current = ref;
        }
      }
    },
    [forwardedRef],
  );

  // After update to react-native0.73 padding styles only work on react-native-web if they are in a separarte object and not a part of
  // reststyles. It doesn't make sense but that is what I observed. Keeping all 5 things i.e. padding, paddingLeft, paddingRight,
  // paddingBottom, paddingTop still works. But I have changed the logic below slightly to ensure I can work with a fixed object
  // structure.

  // Making sure that the paddingStyles object is always created with the same structure and there are no
  // runtime modifications to its properties. This helps the JIT of v8.
  const paddingStyles = {
    paddingTop: padding,
    paddingBottom: padding,
    paddingLeft: padding,
    paddingRight: padding,
  };

  if (!_.isNil(paddingTop)) {
    paddingStyles.paddingTop = paddingTop;
  }

  if (!_.isNil(paddingLeft)) {
    paddingStyles.paddingLeft = paddingLeft;
  }

  if (!_.isNil(paddingRight)) {
    paddingStyles.paddingRight = paddingRight;
  }

  if (!_.isNil(paddingBottom)) {
    paddingStyles.paddingBottom = paddingBottom;
  }

  return (
    <Pressable
      ref={setRef}
      style={[
        {backgroundColor: currentBackgroundColor, alignItems: 'center', justifyContent: 'center'},
        layoutStyles,
        restStyles,
        shadowStyles,
        paddingStyles,
      ]}
      onPress={isEditable ? () => {} : onButtonTap}>
      {loading ? (
        <ActivityIndicator color={color} size={Math.max(typography?.fontSize, typography?.lineHeight)} />
      ) : (
        <Text
          sentry-label={'Button ' + value}
          style={[{textAlign: horizontalAlign, textAlignVertical: verticalAlign, color: currentColor, ...typography}]}>
          {value}
        </Text>
      )}
    </Pressable>
  );
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  loading: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  disabled: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  onTap: {
    type: EventTriggerIdentifier,
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: defaultEditors.basic,
  advanced: [
    {
      type: 'codeInput',
      name: 'disabled',
      props: {
        label: 'Disabled',
      },
    },
    {
      type: 'codeInput',
      name: 'loading',
      props: {
        label: 'Is Loading',
      },
    },
    ...hapticEditors.advanced,
  ],
  layout: [
    {
      type: 'radioGroup',
      name: 'horizontalAlign',
      props: {
        label: 'Horizontal Alignment',
        options: ['auto', 'left', 'center', 'right', 'justify'],
      },
    },
    {
      type: 'radioGroup',
      name: 'verticalAlign',
      props: {
        label: 'Vertical Alignment',
        options: ['auto', 'top', 'center', 'bottom'],
      },
    },
    {
      type: 'radioGroup',
      name: 'overflowType',
      props: {
        label: 'Overflow',
        options: ['scroll', 'hidden'],
      },
    },
    ...defaultEditors.layout,
  ],
  animations: defaultEditors.animations,
  analytics: [
    {
      type: 'analyticsEditor',
      name: 'analytics',
      props: {
        defaultType: 'track',
      },
    },
  ],
};

export const buttonWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledBackgroundColor',
    props: {
      label: 'Disabled Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledColor',
    props: {
      label: 'Disabled Text Color',
      placeholder: '#HexCode',
    },
  },

  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const emptyOnupdate = null;

export default connectWidget('ButtonWidget', ButtonWidget, ButtonWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(buttonWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'button'],
});
