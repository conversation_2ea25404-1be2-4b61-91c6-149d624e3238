import {identity} from '@/root/app/styles/theme/derived/IdentityMethods';

const button = {
  theme: {
    default: {
      backgroundColor: {
        dependencies: ['colors.bg.default'],
        getValue: identity,
      },
    },
    primary: {
      backgroundColor: {
        dependencies: ['colors.bg.primary'],
        getValue: identity,
      },
    },
  },
  size: {
    small: {
      padding: 2,
    },
    normal: {
      padding: 4,
    },
    large: {
      padding: 8,
    },
  },
};
