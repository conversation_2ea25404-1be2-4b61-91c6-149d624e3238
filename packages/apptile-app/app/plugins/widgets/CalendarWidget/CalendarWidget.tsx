import React, {useEffect} from 'react';
import _ from 'lodash';
import {Platform, StyleSheet, View} from 'react-native';
import {CurrentScreenContext, CustomModal, MaterialCommunityIcons, getPlatformStyles} from 'apptile-core';
import {Calendar, DateData} from 'react-native-calendars';
import {Portal} from '@gorhom/portal';
import moment from 'moment';

// FIXME:  debounce need to be added

const styles = StyleSheet.create({
  closeIcon: {
    fontSize: 20,
    color: 'black',
  },
  iconWrapper: {
    position: 'absolute',
    zIndex: 9,
    top: 0,
    right: 8,
    height: 28,
    width: 28,
    borderRadius: 28,
    backgroundColor: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const CalendarWidgetView = ({props}) => {
  const {model, modelUpdate, modelStyles, config} = props;

  const value = model.get('value') ?? new Date();
  const minDate = model.get('minDate') ?? null;
  const maxDate = model.get('maxDate') ?? null;
  const specificDates = Array.isArray(model.get('specificDates')) ? model.get('specificDates') : [];
  const specificDays = Array.isArray(model.get('specificDays')) ? model.get('specificDays') : [];
  const enableOrDisableSpecificDates = model.get('enableOrDisableSpecificDates') ?? 'disable';
  const enableOrDisableSpecificDays = model.get('enableOrDisableSpecificDays') ?? 'disable';
  const setOpen = !!model.get('setOpen');
  const BACKDROP_COLOR = 'rgba(0,0,0,0.8)';
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 5, flexDirection: 'row'});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const horizontalAlign = model.get('horizontalAlign', 'left');
  const verticalAlign = model.get('verticalAlign', 'auto');
  const flexDirection = model.get('flexDirection', 'row');
  const setOpenUpdates = {selector: ['setOpen'], newValue: false};

  const onDateChange = date => {
    const weekDay = moment(date?.dateString).format('dddd');

    if (date) {
      modelUpdate([
        {
          selector: ['value'],
          newValue: date?.dateString,
        },
        {selector: ['setOpen'], newValue: false},
        {selector: ['dateObject'], newValue: Object.assign(date, {weekDay})},
      ]);
      props.triggerEvent('onDateSelect');
    }
  };

  useEffect(() => {
    if (typeof value === 'string' && moment(value, 'YYYY-MM-DD', true).isValid()) {
      const weekDay = moment(value).format('dddd');
      modelUpdate([{selector: ['dateObject'], newValue: {dateString: value, weekDay}}]);
    }
  }, [value]);

  const closeModal = () => {
    modelUpdate([setOpenUpdates]);
  };

  const generateMarkedDates = () => {
    const startDate = new Date(minDate || new Date());
    const endDate = new Date(maxDate || new Date(startDate.getTime() + 60 * 24 * 60 * 60 * 1000));
    const markedDates = {};

    const formattedSpecificDates = new Set(specificDates.map(date => formatDate(new Date(date))));
    const specificDaysSet = new Set(specificDays);

    const isSpecificDateEnabled = enableOrDisableSpecificDates === 'enable';
    const isSpecificDayEnabled = enableOrDisableSpecificDays === 'enable';

    for (let currentDate = startDate; currentDate <= endDate; currentDate.setDate(currentDate.getDate() + 1)) {
      const dateString = formatDate(currentDate); // dateString = '2024-01-01'
      const dayOfWeek = currentDate.getDay(); // dayOfWeek = 0

      const isSpecificDate = formattedSpecificDates.has(dateString); // isSpecificDate = true
      const isSpecificDay = specificDaysSet.has(dayOfWeek); // isSpecificDay = true

      let isEnabled;

      if (isSpecificDateEnabled === isSpecificDayEnabled) {
        // Both specific dates and days are in the same mode (both enable or both disable)
        isEnabled = isSpecificDateEnabled ? isSpecificDate || isSpecificDay : !isSpecificDate && !isSpecificDay;
      } else if (isSpecificDateEnabled) {
        // Specific dates are enabled, specific days are disabled
        isEnabled = isSpecificDate && !isSpecificDay;
      } else {
        // Specific dates are disabled, specific days are enabled
        isEnabled = !isSpecificDate && isSpecificDay;
      }

      if (!isEnabled) {
        markedDates[dateString] = {disabled: true, disableTouchEvent: true};
      }
    }

    // Mark the selected date
    if (value) {
      const formattedValue = formatDate(new Date(value));
      markedDates[formattedValue] = {...markedDates[formattedValue], selected: true};
    }

    return markedDates;
  };
  // Helper function to format date as 'YYYY-MM-DD'
  function formatDate(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
      2,
      '0',
    )}`;
  }
  const markedDates = generateMarkedDates();

  return (
    <View
      style={[
        layoutStyles,
        modelPlatformStyles,
        {
          textAlign: horizontalAlign,
          textAlignVertical: verticalAlign,
          flexDirection: flexDirection,
        },
      ]}>
      {setOpen && (
        <CurrentScreenContext.Consumer>
          {screen => (
            <Portal hostName={screen?.isModal ? (model.get('pageKey') as string) : 'root'}>
              {setOpen && (
                <CustomModal
                  position="center"
                  onClose={closeModal}
                  outerComponents={
                    <View style={[styles.iconWrapper, {top: (Platform.OS === 'ios' && !screen?.isModal ? 54 : 0) + 4}]}>
                      <MaterialCommunityIcons name="close" style={[styles.closeIcon]} onPress={closeModal} />
                    </View>
                  }
                  backdropColor={BACKDROP_COLOR}>
                  <View style={{paddingHorizontal: 10}}>
                    <Calendar
                      markedDates={markedDates}
                      minDate={minDate}
                      maxDate={maxDate}
                      onDayPress={onDateChange}
                      enableSwipeMonths={true}
                    />
                  </View>
                </CustomModal>
              )}
            </Portal>
          )}
        </CurrentScreenContext.Consumer>
      )}
    </View>
  );
};

export default CalendarWidgetView;
