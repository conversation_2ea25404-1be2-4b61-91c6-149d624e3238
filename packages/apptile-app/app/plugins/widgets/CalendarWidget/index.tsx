import React from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';

import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {connectWidget} from 'apptile-core';
// import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import CalendarWidgetView from './CalendarWidget';

const CalendarWidgetConfig = {
  value: null,
  dateObject: null,
  maxDate: null,
  minDate: null,
  enableOrDisableSpecificDates: false,
  enableOrDisableSpecificDays: false,
  specificDates: [],
  specificDays: [],
  setOpen: false,
  readonly: false,
  onDateSelect: 'Event_onDateSelect',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'CalendarWidget',
  type: 'widget',
  name: 'Calendar',
  description: 'Display A Calendar',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: '',
  defaultHeight: 0,
  defaultWidth: 0,
};

// FIXME:  debounce need to be added
const CalendarWidget = React.forwardRef((props, ref) => {
  return <CalendarWidgetView props={props} />;
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, val, sel) => {
      return val;
    },
  },
  onDateSelect: {
    type: EventTriggerIdentifier,
  },
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'enableOrDisableSpecificDates',
      props: {
        label: 'enableOrDisableSpecificDates',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'enableOrDisableSpecificDays',
      props: {
        label: 'enableOrDisableSpecificDays',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'minDate',
      props: {
        label: 'minDate',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'maxDate',
      props: {
        label: 'maxDate',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'specificDates',
      props: {
        // Define the type of the input in the label name
        label: 'specificDates["YYYY-MM-DD"]',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'specificDays',
      props: {
        // Define the type of the input in the label name
        label: 'specificDays["index of the day"]',
        placeholder: '{{textInput1.value}}',
      },
    },
  ],
};

//TODO: Add appropriate styling for changing calendar looks since everything is customizable!
export const CalendarWidgetStyleConfig: WidgetStyleEditorOptions = [];

const emptyOnupdate = null;

export default connectWidget('CalendarWidget', CalendarWidget, CalendarWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(CalendarWidgetStyleConfig),
  pluginListing,
  themeProfileSel: ['tile', 'calendar'],
});
