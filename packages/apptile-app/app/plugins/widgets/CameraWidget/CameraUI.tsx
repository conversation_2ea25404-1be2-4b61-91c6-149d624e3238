import React, {useCallback, useEffect, useRef} from 'react';
import {Platform, StyleSheet} from 'react-native';

import {CameraProps} from './type';
import RNCamera from './RNCamera';

const styles = StyleSheet.create({
  cameraUI: {
    flex: 1,
  },
});

const Camera: React.FC<CameraProps> = ({...props}) => {
  const {type = 'back', flash, onCapture, captureId, ratio: inputRatio} = props;
  const [ratio, setRatio] = React.useState('');

  const ref = useRef<RNCamera>(null);

  const takePicture = useCallback(async () => {
    if (ref.current) {
      const data = await ref.current.takePictureAsync({
        quality: 0.8,
        base64: true,
        fixOrientation: true,
        mirrorImage: true,
      });

      if (data?.base64 && data?.uri) {
        onCapture?.(data.base64, data.uri);
      }
    }
  }, [onCapture]);

  const prepareRatio = async () => {
    if (Platform.OS === 'android' && ref.current && inputRatio) {
      const ratios = await ref.current.getSupportedRatiosAsync();

      // See if the current device has your desired ratio, otherwise get the maximum supported one
      // Usually the last element of "ratios" is the maximum supported ratio
      const availableRatio = ratios.find(entry => entry === inputRatio) || ratios[ratios.length - 1];
      setRatio(availableRatio);
    }
  };

  useEffect(() => {
    takePicture();
  }, [captureId, takePicture]);

  const dynamicProps = ratio ? {ratio: ratio} : {};

  return (
    <RNCamera
      ref={ref}
      style={styles.cameraUI}
      captureAudio={false}
      onCameraReady={prepareRatio}
      type={type === 'back' ? RNCamera.Constants.Type.back : RNCamera.Constants.Type.front}
      flashMode={flash ? RNCamera.Constants.FlashMode.on : RNCamera.Constants.FlashMode.off}
      androidCameraPermissionOptions={{
        title: 'Permission to use camera',
        message: 'We need your permission to use your camera',
        buttonPositive: 'Ok',
        buttonNegative: 'Cancel',
      }}
      {...dynamicProps}
    />
  );
};

export default Camera;
