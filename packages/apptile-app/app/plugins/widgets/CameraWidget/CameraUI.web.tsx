import React, {useCallback, useEffect, useRef} from 'react';
import Webcam from 'react-webcam';

import {CameraProps} from './type';

const Camera: React.FC<CameraProps> = ({...props}) => {
  const {width, height, type = 'back', onCapture, captureId} = props;

  const ref = useRef<Webcam>(null);

  const takePicture = useCallback(async () => {
    if (ref.current) {
      const data = ref.current.getScreenshot();
      onCapture?.(data, null);
    }
  }, [onCapture]);

  useEffect(() => {
    takePicture();
  }, [captureId, takePicture]);

  return (
    <Webcam
      ref={ref}
      style={{flex: 1}}
      videoConstraints={{width, height, facingMode: type === 'back' ? 'environment' : 'user'}}
    />
  );
};

export default Camera;
