import React, {useCallback, useEffect, useRef, useState} from 'react';
import {AppState, LayoutRectangle, View} from 'react-native';
import {useIsFocused} from '@react-navigation/native';

import docs from './docs';
import {connectWidget, WidgetProps} from 'apptile-core';
import {
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  PluginModelType,
  PluginModelChange,
  TriggerActionIdentifier,
} from 'apptile-core';
import {CameraWidgetConfig} from './type';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {PluginConfig, PluginConfigType} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {WidgetStyleEditorOptions} from '@/root/app/styles/types';
import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
import Camera<PERSON> from './CameraUI';

const cameraWidgetConfig: CameraWidgetConfig = {
  start: 'Action_start',
  stop: 'Action_reset',
  captureId: 'default',
  capture: 'Action_capture',
  switchCamera: 'Action_switchCamera',
  toggleFlash: 'Action_toggleFlash',
  value: '',
  ratio: '',
  path: '',
  isActive: false,
  type: 'back',
  flash: false,
  onCapture: 'Event_onCapture',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'camera',
  type: 'widget',
  name: 'Camera',
  description: 'Capture image',
  layout: {
    flex: 1,
  },
  defaultHeight: 'auto',
  defaultWidth: 'auto',
  section: 'Display',
  icon: '',
};

const cameraWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
];

interface IProps {
  model: PluginModelType;
  modelStyles: Record<string, any>;
  config: PluginConfigType<CameraWidgetConfig>;
  modelUpdate: (changes: PluginModelChange[]) => void;
  triggerEvent: (pluginId: string, event?: string) => void;
}

const CameraWidget = React.forwardRef<View, IProps>((props, ref) => {
  const {model, modelStyles, config, modelUpdate, triggerEvent} = props;

  const innerRef = React.useRef<View>(null);
  React.useEffect(() => {
    if (!ref) return;
    if (typeof ref === 'function') ref(innerRef.current);
    else ref.current = innerRef.current;
  }, [ref]);

  const [layoutRectangle, setLayoutRectangle] = useState<LayoutRectangle>();

  const isActive = !!model.get('isActive');
  const type = model.get('type');
  const ratio = model.get('ratio');
  const flash = !!model.get('flash');
  const captureId = model.get('captureId');

  const onCapture = useCallback(
    (base64: string | null, path: string | null) => {
      const updates = [{selector: ['value'], newValue: base64}];
      if (path) {
        updates.push({selector: ['path'], newValue: path});
      }

      modelUpdate(updates);
      triggerEvent('onCapture');
    },
    [modelUpdate, triggerEvent],
  );

  const [enableCamera, setEnableCamera] = useState(isActive);
  const isFocused = useIsFocused();
  useEffect(() => {
    setEnableCamera(isFocused && isActive);
  }, [isActive, isFocused, setEnableCamera]);

  const appState = useRef(AppState.currentState);
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        setEnableCamera(isActive && isFocused);
      } else setEnableCamera(false);
      appState.current = nextAppState;
    });
    return () => subscription.remove();
  }, [isActive, isFocused, setEnableCamera]);

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {borderRadius, margin, padding, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  return (
    <View
      ref={innerRef}
      onLayout={e => setLayoutRectangle(e.nativeEvent.layout)}
      style={[layoutStyles, modelPlatformStyles]}>
      {enableCamera && layoutRectangle && (
        <CameraUI
          width={layoutRectangle.width}
          height={layoutRectangle.height}
          type={type}
          flash={flash}
          ratio={ratio}
          onCapture={onCapture}
          captureId={captureId}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'checkbox',
      name: 'isActive',
      props: {
        label: 'Show Camera',
      },
    },
    {
      type: 'radioGroup',
      name: 'type',
      defaultValue: 'front',
      props: {
        label: 'Side',
        options: ['front', 'back'],
      },
    },
    {
      type: 'codeInput',
      name: 'ratio',
      props: {
        label: 'ratio',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  start: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const changeSets = [{selector: selector.concat(['isActive']), newValue: true}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  stop: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const changeSets = [{selector: selector.concat(['isActive']), newValue: false}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  capture: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const changeSets = [{selector: selector.concat(['captureId']), newValue: Math.random()}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  switchCamera: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const type = model.get('type');
        const changeSets = [{selector: selector.concat(['type']), newValue: type !== 'back' ? 'back' : 'front'}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },
  toggleFlash: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        const flash = model.get('flash');
        const changeSets = [{selector: selector.concat(['flash']), newValue: !flash}];
        dispatch(modelUpdateAction(changeSets, undefined, true));
      };
    },
  },

  value: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  isActive: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  type: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  flash: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  onCapture: {
    type: EventTriggerIdentifier,
  },
};

export default connectWidget('CameraWidget', CameraWidget, cameraWidgetConfig, null, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(cameraWidgetStyleConfig),
  pluginListing,
  docs,
});
