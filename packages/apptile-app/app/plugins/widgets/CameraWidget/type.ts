export type CameraWidgetConfig = {
  start: any;
  stop: any;
  captureId: string;
  capture: any;
  switchCamera: any;
  toggleFlash: any;
  value: string;
  path: string;
  ratio: string;
  isActive: boolean;
  type: string;
  flash: boolean;
  onCapture: any;
};

export type CameraProps = {
  width?: number;
  height?: number;
  type?: 'back' | 'front';
  captureId: string;
  onCapture: (uri: string | null, path: string | null) => void;
  flash?: boolean;
  ratio?: string;
};
