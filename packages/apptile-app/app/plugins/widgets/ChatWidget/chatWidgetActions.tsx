import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {WidgetRefRegistry} from 'apptile-core';

export const scrollToBottom = (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, undefined);
  if (!currentRef) return;
  currentRef?.scrollToEnd({
    offset: 0,
    animated: false,
  });
};
