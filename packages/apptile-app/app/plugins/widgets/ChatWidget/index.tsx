import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {mergeWithDefaultStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from '@/root/app/styles/types';
import React from 'react';
import {StyleSheet, Text, View, FlatList, Image, Platform} from 'react-native';
import {
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  TriggerActionIdentifier,
} from 'apptile-core';
import {defaultEditors, hapticEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {scrollToBottom} from './chatWidgetActions';
import {WidgetRefContext} from 'apptile-core';
import get from 'lodash/get';
import _ from 'lodash';

const ChatWidgetConfig = {
  value: '[{text:"", isRightSideMessage:false, id:""}]',
  leftAvatar: '',
  rightAvatar: '',
  onEndReached: '',
  scrollToBottom: 'action',
};

export const chatWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'typographyInput',
    name: 'rightRowTypography',
    defaultValue: 'tile.chatWidget.rightRowTypography',
    props: {
      label: 'Right Row Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'leftRowTypography',
    defaultValue: 'tile.chatWidget.leftRowTypography',
    props: {
      label: 'Left Row Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'colorInput',
    name: 'backgroundColor',
    defaultValue: 'tile.chatWidget.backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'rowPadding',
    defaultValue: 'tile.chatWidget.rowPadding',
    props: {
      label: 'Row Padding',
      placeholder: '8',
      options: ['rowPaddingTop', 'rowPaddingRight', 'rowPaddingBottom', 'rowPaddingLeft'],
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'leftRowBorderRadius',
    props: {
      label: 'Left Row Border Radius',
      placeholder: '0',
      options: [
        'leftRowBorderTopLeftRadius',
        'leftRowBorderTopRightRadius',
        'leftRowBorderBottomRightRadius',
        'leftRowBorderBottomLeftRadius',
      ],
      layout: 'square',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'rightRowBorderRadius',
    props: {
      label: 'Right Row Border Radius',
      placeholder: '0',
      options: [
        'rightRowBorderTopLeftRadius',
        'rightRowBorderTopRightRadius',
        'rightRowBorderBottomRightRadius',
        'rightRowBorderBottomLeftRadius',
      ],
      layout: 'square',
    },
  },
  {
    type: 'colorInput',
    name: 'rightRowBackgroundColor',
    defaultValue: 'tile.chatWidget.rightRowBackgroundColor',
    props: {
      label: 'Right Row Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'leftRowBackgroundColor',
    defaultValue: 'tile.chatWidget.leftRowBackgroundColor',
    props: {
      label: 'Left Row Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'rightTextColor',
    defaultValue: 'tile.chatWidget.rightTextColor',
    props: {
      label: 'Right Row Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'leftTextColor',
    defaultValue: 'tile.chatWidget.leftTextColor',
    props: {
      label: 'Left Row Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'rowBorderColor',
    defaultValue: 'tile.chatWidget.rowBorderColor',
    props: {
      label: 'Row Border Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'borderWidth',
    props: {
      label: 'Row Border',
      placeholder: '0',
      options: ['rowBorderTopWidth', 'rowBorderRightWidth', 'rowBorderBottomWidth', 'rowBorderLeftWidth'],
    },
  },
];

const pluginListing: PluginListingSettings = {
  labelPrefix: 'chatWidget',
  type: 'widget',
  name: 'Chat',
  description: 'Display chat window',
  section: 'Display',
  icon: 'button',
};

// FIXME:  debounce need to be added
const ChatWidget = React.forwardRef((props, ref) => {
  const {model, config, modelStyles, triggerEvent, pageKey, instance, id} = props;
  const refContext = React.useContext(WidgetRefContext);

  const {
    borderRadius,
    margin,
    padding,
    elevation: elevationStr,
    rowPadding,
    rowPaddingBottom: paddingBottom,
    rowPaddingLeft: paddingLeft,
    rowPaddingRight: paddingRight,
    rowPaddingTop: paddingTop,
    leftRowBorderTopLeftRadius,
    leftRowBorderTopRightRadius,
    leftRowBorderBottomRightRadius,
    leftRowBorderBottomLeftRadius,
    leftRowBorderRadius,
    rightRowBorderTopLeftRadius,
    rightRowBorderTopRightRadius,
    rightRowBorderBottomRightRadius,
    rightRowBorderBottomLeftRadius,
    rightRowBorderRadius,
    leftRowBackgroundColor,
    rightRowBackgroundColor,
    rowBorderColor,
    rowBorderTopWidth,
    rowBorderRightWidth,
    rowBorderBottomWidth,
    rowBorderLeftWidth,
    rowBorder,
    rightTextColor,
    leftTextColor,
    ...genericStyles
  } = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const value = model.get('value');
  const leftAvatar = model.get('leftAvatar');
  const rightAvatar = model.get('rightAvatar');
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {};

  const {
    backgroundColor,
    color,
    disabledBackgroundColor,
    disabledColor,
    leftRowTypography,
    rightRowTypography,
    ...restStyles
  } = modelStyles ? modelStyles : {};

  const onEndReached = () => {
    triggerEvent('onEndReached');
  };

  const textContainerStyles = {
    padding: rowPadding,
    paddingBottom,
    paddingLeft,
    paddingRight,
    paddingTop,
    borderColor: rowBorderColor,
    borderTopWidth: rowBorderTopWidth,
    borderRightWidth: rowBorderRightWidth,
    borderBottomWidth: rowBorderBottomWidth,
    borderLeftWidth: rowBorderLeftWidth,
    borderWidth: rowBorder,
  };

  const leftContainerStyles = {
    borderRadius: leftRowBorderRadius,
    borderTopLeftRadius: leftRowBorderTopLeftRadius,
    borderTopRightRadius: leftRowBorderTopRightRadius,
    borderBottomRightRadius: leftRowBorderBottomRightRadius,
    borderBottomLeftRadius: leftRowBorderBottomLeftRadius,
    backgroundColor: leftRowBackgroundColor,
  };

  const rightContainerStyles = {
    borderRadius: rightRowBorderRadius,
    borderTopLeftRadius: rightRowBorderTopLeftRadius,
    borderTopRightRadius: rightRowBorderTopRightRadius,
    borderBottomRightRadius: rightRowBorderBottomRightRadius,
    borderBottomLeftRadius: rightRowBorderBottomLeftRadius,
    backgroundColor: rightRowBackgroundColor,
  };

  const renderItem = ({item}: any) => {
    return (
      <View style={styles.row} key={item.id}>
        {item.isRightSideMessage ? (
          <View style={styles.rightContainer}>
            <View style={[styles.rightMsgContainer, textContainerStyles, rightContainerStyles]}>
              <Text style={{...rightRowTypography, color: rightTextColor}}>{item.text}</Text>
            </View>
            <Image style={styles.avatar} source={{uri: rightAvatar}} />
          </View>
        ) : (
          <View style={styles.leftContainer}>
            <Image style={styles.avatar} source={{uri: leftAvatar}} />
            <View style={[styles.leftMsgContainer, textContainerStyles, leftContainerStyles]}>
              <Text style={{...leftRowTypography, color: leftTextColor}}>{item.text}</Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  const setRef = React.useCallback(
    r => {
      if (ref) ref = r;
      refContext.registerWidgetRef(r, pageKey, id, instance);
    },
    [refContext, ref],
  );

  console.log(layoutStyles);
  console.log(modelPlatformStyles);
  return (
    <View style={[layoutStyles]}>
      {!_.isEmpty(value) ? (
        <FlatList
          ref={setRef}
          data={value}
          inverted
          onEndReached={onEndReached}
          onEndReachedThreshold={0.8}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          disableVirtualization={Platform.select({web: false, ios: true, android: false})}
        />
      ) : (
        <></>
      )}
    </View>
  );
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue : [];
    },
  },
  leftAvatar: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  rightAvatar: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  onEndReached: {
    type: EventTriggerIdentifier,
  },
  scrollToBottom: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return scrollToBottom;
    },
  },
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'leftAvatar',
      props: {
        label: 'Left Avatar',
        placeholder: 'Left side avatar',
      },
    },
    {
      type: 'codeInput',
      name: 'rightAvatar',
      props: {
        label: 'Right Avatar',
        placeholder: 'Right side avatar',
      },
    },
  ],
  advanced: [...hapticEditors.advanced],
  layout: [...defaultEditors.layout],
  analytics: [
    {
      type: 'analyticsEditor',
      name: 'analytics',
      props: {
        defaultType: 'track',
      },
    },
  ],
};

const emptyOnupdate = null;

export default connectWidget('ChatWidget', ChatWidget, ChatWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(chatWidgetStyleConfig),
  pluginListing,
  themeProfileSel: ['tile', 'chatWidget'],
});

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  avatar: {
    borderRadius: 20,
    width: 32,
    height: 32,
  },
  rightMsgContainer: {
    borderWidth: 1,
    marginHorizontal: 8,
    maxWidth: '70%',
  },
  leftMsgContainer: {
    borderWidth: 1,
    marginHorizontal: 8,
    maxWidth: '70%',
  },
  rightContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
