/* eslint-disable react-native/no-inline-styles */
import React, {useCallback, useEffect, useState} from 'react';
import {Text, View, Pressable, StyleSheet} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, hapticEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {MaterialCommunityIcons} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import {performHapticFeedback} from 'apptile-core';
import {makeBoolean} from 'apptile-core';

const checkBoxWidgetConfig = {
  label: '',
  value: false,
  onTap: '',
  enableHaptics: '',
  hapticMethod: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'checkbox',
  type: 'widget',
  name: 'Checkbox',
  description: 'Display checkbox with text',
  layout: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  section: 'Inputs',
  icon: 'checkbox',
};

export const checkBoxWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    defaultValue: 'tile.checkBox.backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'checkColor',
    defaultValue: 'tile.checkBox.checkColor',
    props: {
      label: 'Checkbox color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'tickColor',
    defaultValue: 'tile.checkBox.tickColor',
    props: {
      label: 'Tick color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'textColor',
    defaultValue: 'tile.checkBox.textColor',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    defaultValue: 'tile.checkBox.typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'codeInput',
    name: 'tickSize',
    defaultValue: 'tile.checkBox.tickSize',
    props: {
      label: 'Tick Size',
      placeholder: '0',
    },
  },
];

const CheckBoxWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, triggerEvent, config} = props;

  const label = model.get('label')?.toString();
  const enableHaptics = model.get('enableHaptics');
  const hapticMethod = model.get('hapticMethod');
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const {typography, ...restModelPlatformStyles} = modelPlatformStyles;

  const inputValue = !!model.get('value');
  const [isChecked, setIsChecked] = useState(inputValue);
  useEffect(() => setIsChecked(inputValue), [inputValue]);

  const handleClick = useCallback(
    (checkState: boolean) => {
      if (enableHaptics) performHapticFeedback(hapticMethod);
      setIsChecked(checkState);
      triggerEvent('onTap');
      modelUpdate([
        {
          selector: ['value'],
          newValue: checkState,
        },
      ]);
    },
    [enableHaptics, hapticMethod, modelUpdate, triggerEvent],
  );

  return (
    <View style={[layoutStyles, restModelPlatformStyles]} ref={ref}>
      <Pressable
        style={[
          styles.checkbox,
          {
            backgroundColor: isChecked ? modelPlatformStyles.checkColor : 'transparent',
            borderColor: isChecked ? 'transparent' : '#D1D5DB',
          },
        ]}
        onPress={() => handleClick(!isChecked)}>
        {isChecked && (
          <MaterialCommunityIcons
            name="check"
            size={modelPlatformStyles.tickSize}
            color={modelPlatformStyles.tickColor}
          />
        )}
      </Pressable>
      <Text
        style={[
          styles.text,
          typography,
          {
            color: modelPlatformStyles.textColor,
          },
        ]}>
        {label ? label : ''}
      </Text>
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'label',
      props: {
        label: 'label',
        placeholder: '{{textInput1.value}}',
      },
    },
  ],
  advanced: hapticEditors.advanced,
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  onTap: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  text: {
    marginHorizontal: 8,
  },
  checkbox: {
    minHeight: 22,
    minWidth: 22,
    borderWidth: 1,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});

export default connectWidget('CheckBoxWidget', CheckBoxWidget, checkBoxWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(checkBoxWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'checkBox'],
});
