import React from 'react';
import {useColorScheme} from 'react-native';
import {KeyboardAwareApptileFlexbox, PluginEditorsConfig} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, hapticEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import {makeBoolean} from 'apptile-core';
import {ApptileFlexbox} from 'apptile-core';
import {renderWidgetTreeNode} from 'apptile-core';

const ContainerWidgetConfig = {
  isTappable: false,
  keyboardAware: false,
  detectVisibility: false,
  enableHaptics: false,
  hapticMethod: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'container',
  type: 'widget',
  name: 'Container',
  description: 'Display Text on screen with varying styles.',
  defaultHeight: 20,
  defaultWidth: 0,
  section: 'Layout',
  layout: {
    flex: 1,
    flexDirection: 'column',
  },
  icon: 'container',
};
export const containerWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
];

const ContainerWidget = React.forwardRef((props, ref) => {
  const {model} = props;
  const keyboardAware = model?.get('keyboardAware') ?? false;
  if (keyboardAware) {
    return (
      <KeyboardAwareApptileFlexbox
        key={props?.id + '_' + props?.instance}
        {...props}
        containerId={props?.id}
        ref={ref}
        renderWidgetTreeNode={renderWidgetTreeNode({...props})}
      />
    );
  }
  return (
    <ApptileFlexbox
      key={props?.id + '_' + props?.instance}
      {...props}
      containerId={props?.id}
      ref={ref}
      renderWidgetTreeNode={renderWidgetTreeNode({...props})}
    />
  );
});

const propertySettings: PluginPropertySettings = {
  isTappable: {
    getValue: (model, renderedValue, sel) => {
      return makeBoolean(renderedValue);
    },
  },
  keyboardAware: {
    getValue: (model, renderedValue, sel) => {
      return makeBoolean(renderedValue);
    },
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  onTap: {
    type: EventTriggerIdentifier,
  },
  onVisible: {
    type: EventTriggerIdentifier,
  },
  onNotVisible: {
    type: EventTriggerIdentifier,
  },
};
const editors: PluginEditorsConfig<typeof ContainerWidgetConfig> = {
  basic: defaultEditors.basic,
  advanced: [
    {
      type: 'checkbox',
      name: 'isTappable',
      props: {
        label: 'Tappable',
      },
    },
    {
      type: 'checkbox',
      name: 'keyboardAware',
      props: {
        label: 'Keyboard Aware Container',
      },
    },
    {
      type: 'checkbox',
      name: 'detectVisibility',
      props: {
        label: 'Throw Visibility Events',
      },
    },
    ...hapticEditors.advanced,
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
  animations: defaultEditors.animations,
};

export default connectWidget('ContainerWidget', ContainerWidget, ContainerWidgetConfig, null, editors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(containerWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'container'],
});
