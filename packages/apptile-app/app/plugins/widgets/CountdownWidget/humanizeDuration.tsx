var LANGUAGES = {
  en: {
    y: function (c: number) {
      return 'year' + (c === 1 ? '' : 's');
    },
    mo: function (c: number) {
      return 'month' + (c === 1 ? '' : 's');
    },
    w: function (c: number) {
      return 'week' + (c === 1 ? '' : 's');
    },
    d: function (c: number) {
      return 'day' + (c === 1 ? '' : 's');
    },
    h: function (c: number) {
      return 'hour' + (c === 1 ? '' : 's');
    },
    m: function (c: number) {
      return 'minute' + (c === 1 ? '' : 's');
    },
    s: function (c: number) {
      return 'second' + (c === 1 ? '' : 's');
    },
    ms: function (c: number) {
      return 'millisecond' + (c === 1 ? '' : 's');
    },
    decimal: '.',
  },
};

// You can create a humanizer, which returns a function with default
// parameters.
function humanizer(passedOptions: any) {
  var result = function humanizer(ms: any, humanizerOptions: any) {
    var options = assign({}, result, humanizerOptions || {});
    return doHumanization(ms, options);
  };

  return assign(
    result,
    {
      language: 'en',
      spacer: ' ',
      conjunction: '',
      serialComma: true,
      units: ['y', 'mo', 'w', 'd', 'h', 'm', 's'],
      languages: {},
      round: false,
      unitMeasures: {
        y: 31557600000,
        mo: 2629800000,
        w: 604800000,
        d: 86400000,
        h: 3600000,
        m: 60000,
        s: 1000,
        ms: 1,
      },
    },
    passedOptions,
  );
}

// The main function is just a wrapper around a default humanizer.
var humanizeDuration = humanizer({});

// Build dictionary from options
function getDictionary(options: {language: any; fallbacks: string | any[]; languages: {[x: string]: any}}) {
  var languagesFromOptions = [options.language];

  if (has(options, 'fallbacks')) {
    if (isArray(options.fallbacks) && options.fallbacks.length) {
      languagesFromOptions = languagesFromOptions.concat(options.fallbacks);
    } else {
      throw new Error('fallbacks must be an array with at least one element');
    }
  }

  for (var i = 0; i < languagesFromOptions.length; i++) {
    var languageToTry = languagesFromOptions[i];
    if (has(options.languages, languageToTry)) {
      return options.languages[languageToTry];
    } else if (has(LANGUAGES, languageToTry)) {
      return LANGUAGES[languageToTry];
    }
  }

  throw new Error('No language found.');
}

// doHumanization does the bulk of the work.
function doHumanization(
  ms: number,
  options: {
    units: string | any[];
    unitMeasures: {[x: string]: number};
    maxDecimalPoints: number | undefined;
    round: any;
    largest: number;
    appendZero: any;
    includeZero: any;
    delimiter: any;
    conjunction: string | undefined;
    serialComma: any;
  },
) {
  var i, len, piece;

  // Make sure we have a positive number.
  // Has the nice sideffect of turning Number objects into primitives.
  ms = Math.abs(ms);

  var dictionary = getDictionary(options);
  var pieces = [];

  // Start at the top and keep removing units, bit by bit.
  var unitName, unitMS, unitCount;
  for (i = 0, len = options.units.length; i < len; i++) {
    unitName = options.units[i];
    unitMS = options.unitMeasures[unitName];

    // What's the number of full units we can fit?
    if (i + 1 === len) {
      if (has(options, 'maxDecimalPoints')) {
        // We need to use this expValue to avoid rounding functionality of toFixed call
        var expValue = Math.pow(10, options.maxDecimalPoints);
        var unitCountFloat = ms / unitMS;
        unitCount = parseFloat((Math.floor(expValue * unitCountFloat) / expValue).toFixed(options.maxDecimalPoints));
      } else {
        unitCount = ms / unitMS;
      }
    } else {
      unitCount = Math.floor(ms / unitMS);
    }

    // Add the string.
    pieces.push({
      unitCount: unitCount,
      unitName: unitName,
    });

    // Remove what we just figured out.
    ms -= unitCount * unitMS;
  }

  var firstOccupiedUnitIndex = 0;
  for (i = 0; i < pieces.length; i++) {
    if (pieces[i].unitCount) {
      firstOccupiedUnitIndex = i;
      break;
    }
  }

  if (options.round) {
    var ratioToLargerUnit, previousPiece;
    for (i = pieces.length - 1; i >= 0; i--) {
      piece = pieces[i];
      piece.unitCount = Math.round(piece.unitCount);

      if (i === 0) {
        break;
      }

      previousPiece = pieces[i - 1];

      ratioToLargerUnit = options.unitMeasures[previousPiece.unitName] / options.unitMeasures[piece.unitName];
      if (
        piece.unitCount % ratioToLargerUnit === 0 ||
        (options.largest && options.largest - 1 < i - firstOccupiedUnitIndex)
      ) {
        previousPiece.unitCount += piece.unitCount / ratioToLargerUnit;
        piece.unitCount = 0;
      }
    }
  }

  var result = [];
  for (i = 0, pieces.length; i < len; i++) {
    piece = pieces[i];
    if (options.includeZero) {
      result.push(render(piece.unitCount, piece.unitName, dictionary, options));
    } else {
      if (piece.unitCount) {
        result.push(render(piece.unitCount, piece.unitName, dictionary, options));
      }
    }

    if (result.length === options.largest) {
      break;
    }
  }

  if (result.length) {
    var delimiter;
    if (has(options, 'delimiter')) {
      delimiter = options.delimiter;
    } else if (has(dictionary, 'delimiter')) {
      delimiter = dictionary.delimiter;
    } else {
      delimiter = ', ';
    }

    if (!options.conjunction || result.length === 1) {
      return result.join(delimiter);
    } else if (result.length === 2) {
      return result.join(options.conjunction);
    } else if (result.length > 2) {
      return (
        result.slice(0, -1).join(delimiter) + (options.serialComma ? ',' : '') + options.conjunction + result.slice(-1)
      );
    }
  } else {
    return render(0, options.units[options.units.length - 1], dictionary, options);
  }
}

function render(
  count: number,
  type: string | number,
  dictionary: {[x: string]: any; decimal: any; _formatCount: (arg0: any, arg1: any) => any},
  options: {decimal: any; spacer: any; appendZero: any},
) {
  var decimal;
  if (has(options, 'decimal')) {
    decimal = options.decimal;
  } else if (has(dictionary, 'decimal')) {
    decimal = dictionary.decimal;
  } else {
    decimal = '.';
  }

  var countStr;
  if (typeof dictionary._formatCount === 'function') {
    countStr = dictionary._formatCount(count, decimal);
  } else {
    countStr = count.toString().replace('.', decimal);
  }

  var dictionaryValue = dictionary[type];
  var word;
  if (typeof dictionaryValue === 'function') {
    word = dictionaryValue(count);
  } else {
    word = dictionaryValue;
  }
  if (options.appendZero) countStr = ('00' + countStr).slice(-2);
  return countStr + options.spacer + word;
}

function assign(destination) {
  var source;
  for (var i = 1; i < arguments.length; i++) {
    source = arguments[i];
    for (var prop in source) {
      if (has(source, prop)) {
        destination[prop] = source[prop];
      }
    }
  }
  return destination;
}

// We need to make sure we support browsers that don't have
// `Array.isArray`, so we define a fallback here.
var isArray =
  Array.isArray ||
  function (arg) {
    return Object.prototype.toString.call(arg) === '[object Array]';
  };

function has(
  obj: {
    en: {
      y: (c: any) => string;
      mo: (c: any) => string;
      w: (c: any) => string;
      d: (c: any) => string;
      h: (c: any) => string;
      m: (c: any) => string;
      s: (c: any) => string;
      ms: (c: any) => string;
      decimal: string;
    };
  },
  key: PropertyKey,
) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

humanizeDuration.getSupportedLanguages = function getSupportedLanguages() {
  var result = [];
  for (var language in LANGUAGES) {
    if (has(LANGUAGES, language) && language !== 'gr') {
      result.push(language);
    }
  }
  return result;
};

humanizeDuration.humanizer = humanizer;

export default humanizeDuration;
