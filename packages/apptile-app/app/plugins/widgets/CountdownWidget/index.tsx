import React, {useEffect, useState} from 'react';
import {Text} from 'react-native';
import _ from 'lodash';
import humanizeDuration from './humanizeDuration';

import docs from './docs';
import {WidgetStyleEditorOptions} from '../../../styles/types';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {connectWidget, WidgetProps} from 'apptile-core';
import {
  EventTriggerIdentifier,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';

const CountdownWidgetConfig = {
  reset: 'Action_reset',
  key: 'default',
  hasEnded: false,
  isActive: false,
  appendZero: false,
  value: new Date().getTime(),
  interval: 1000,
  yFormat: 'Year,Years',
  moFormat: 'Month,Months',
  wFormat: 'Week,Weeks',
  dFormat: 'Day,Days',
  hFormat: 'Hour,Hours',
  mFormat: 'Minute,Minutes',
  sFormat: 'Second,Seconds',
  msFormat: 'Millisecond,Milliseconds',
  delimiter: ', ',
  units: 'Y,Mo,W,D,H,M,S',
  onCountdownEnd: 'Event_onCountdownEnd',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'countdown',
  type: 'widget',
  name: 'Countdown',
  description: 'Display Countdown',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
  icon: 'timer',
  layout: [],
};

export const countDownWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const CountdownWidget = React.forwardRef((props: WidgetProps, ref: React.LegacyRef<Text>) => {
  const {model, modelUpdate, modelStyles, triggerEvent} = props;
  const key = model.get('key') || 'default';
  const isActive = !!model.get('isActive');
  const [hasEnded, setHasEnded] = useState(!!model.get('hasEnded'));
  const appendZero = !!model.get('appendZero');
  const modelValue = model.get('value')
    ? typeof model.get('value') === 'string' && model.get('value').startsWith('{{')
      ? new Date().getTime() + 5000
      : model.get('value')
    : new Date().getTime() + 5000;
  const derivedValue = modelValue ? new Date(modelValue).getTime() - new Date().getTime() : 5000;
  const value = derivedValue < 0 ? 0 : derivedValue;
  const interval = model.get('interval') || 1000;
  let yFormat = model
    .get('yFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (yFormat.length == 0) yFormat = [''];
  let moFormat = model
    .get('moFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (moFormat.length == 0) moFormat = [''];
  let wFormat = model
    .get('wFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (wFormat.length == 0) wFormat = [''];
  let dFormat = model
    .get('dFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (dFormat.length == 0) dFormat = [''];
  let hFormat = model
    .get('hFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (hFormat.length == 0) hFormat = [''];
  let mFormat = model
    .get('mFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (mFormat.length == 0) mFormat = [''];
  let sFormat = model
    .get('sFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (sFormat.length == 0) sFormat = [''];
  let msFormat = model
    .get('msFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (msFormat.length == 0) msFormat = [''];
  const delimiter = model.get('delimiter');
  const acceptableUnits = ['Y', 'MS', 'W', 'D', 'H', 'M', 'S', 'MS'];
  const units = model
    .get('units')
    .toLowerCase()
    .split(',')
    .filter((e: string) => e.trim() && acceptableUnits.includes(e.toUpperCase()));

  const customHumanizer = humanizeDuration.humanizer({
    language: 'en',
    languages: {
      shortEn: {
        y: (e: number) => (e < 2 ? yFormat[0] : yFormat[yFormat.length - 1]),
        mo: (e: number) => (e < 2 ? moFormat[0] : moFormat[moFormat.length - 1]),
        w: (e: number) => (e < 2 ? wFormat[0] : wFormat[wFormat.length - 1]),
        d: (e: number) => (e < 2 ? dFormat[0] : dFormat[dFormat.length - 1]),
        h: (e: number) => (e < 2 ? hFormat[0] : hFormat[hFormat.length - 1]),
        m: (e: number) => (e < 2 ? mFormat[0] : mFormat[mFormat.length - 1]),
        s: (e: number) => (e < 2 ? sFormat[0] : sFormat[sFormat.length - 1]),
        ms: (e: number) => (e < 2 ? msFormat[0] : msFormat[msFormat.length - 1]),
      },
    },
  });

  const modelPlatformStyles = modelStyles ? getPlatformStyles(modelStyles) : {};
  const {typography, ...restModelPlatformStyles} = modelPlatformStyles;

  const [time, setTime] = useState<number>(value);
  useEffect(() => {
    let intervalId: NodeJS.Timer;
    if (isActive) {
      intervalId = setInterval(() => {
        setTime(t => {
          if (t <= interval) {
            clearInterval(intervalId);
            setHasEnded(true);
            triggerEvent('onCountdownEnd');
            return t;
          } else {
            setHasEnded(false);
          }
          return t - interval;
        });
      }, interval);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, interval, isActive]);

  useEffect(() => {
    if (time != value) {
      if (value > 0) {
        if (hasEnded) setHasEnded(false);
      } else {
        if (!hasEnded) setHasEnded(true);
      }
      setTime(value);
    }
  }, [key, hasEnded, modelUpdate, time, value]);

  useEffect(() => {
    modelUpdate([
      {
        selector: ['hasEnded'],
        newValue: hasEnded,
      },
    ]);
  }, [hasEnded, modelUpdate]);

  return (
    <Text ref={ref} style={[{textAlign: 'center'}, typography, restModelPlatformStyles]}>
      {customHumanizer(time, {
        units,
        maxDecimalPoints: 0,
        includeZero: true,
        appendZero,
        delimiter,
        language: 'shortEn',
      })}
    </Text>
  );
});

const propertySettings: PluginPropertySettings = {
  reset: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        dispatch(modelUpdateAction([{selector: selector.concat(['key']), newValue: Math.random()}], undefined, true));
      };
    },
  },
  key: {
    getValue: (_model, _renderedValue, _selector) => _renderedValue,
  },
  value: {
    getValue: (_model, _renderedValue, _selector) => {
      return _renderedValue;
    },
  },
  isActive: {
    getValue: (_model, _renderedValue, _selector) => !!_renderedValue,
  },
  interval: {
    getValue: (_model, _renderedValue, _selector) => _.toNumber(_renderedValue),
  },
  onCountdownEnd: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<typeof CountdownWidgetConfig> = {
  basic: [
    {
      type: 'checkbox',
      name: 'isActive',
      props: {
        label: 'isActive',
        placeholder: '{{false}}',
      },
    },
    {
      type: 'checkbox',
      name: 'appendZero',
      props: {
        label: 'appendZero',
        placeholder: '{{false}}',
      },
    },
    {
      type: 'dateAndTimeInput',
      name: 'value',
      props: {
        label: 'Date And Time',
        disableBinding: false,
      },
    },
    {
      type: 'codeInput',
      name: 'interval',
      props: {
        label: 'Interval (in ms)',
        placeholder: '{{1000}}',
      },
    },
    {
      type: 'codeInput',
      name: 'yFormat',
      props: {
        label: 'Years Format',
        placeholder: 'Year,Years',
      },
    },
    {
      type: 'codeInput',
      name: 'moFormat',
      props: {
        label: 'Months Format',
        placeholder: 'Month,Months',
      },
    },
    {
      type: 'codeInput',
      name: 'wFormat',
      props: {
        label: 'Weeks Format',
        placeholder: 'Week,Weeks',
      },
    },
    {
      type: 'codeInput',
      name: 'dFormat',
      props: {
        label: 'Days Format',
        placeholder: 'Day,Days',
      },
    },
    {
      type: 'codeInput',
      name: 'hFormat',
      props: {
        label: 'Hours Format',
        placeholder: 'Hour,Hours',
      },
    },
    {
      type: 'codeInput',
      name: 'mFormat',
      props: {
        label: 'Minutes Format',
        placeholder: 'Minute,Minutes',
      },
    },
    {
      type: 'codeInput',
      name: 'sFormat',
      props: {
        label: 'Seconds Format',
        placeholder: 'Second,Seconds',
      },
    },
    {
      type: 'codeInput',
      name: 'msFormat',
      props: {
        label: 'Milliseconds Format',
        placeholder: 'Millisecond,Milliseconds',
      },
    },
    {
      type: 'codeInput',
      name: 'delimiter',
      props: {
        label: 'Delimiter',
        placeholder: ',',
      },
    },
    {
      type: 'codeInput',
      name: 'units',
      props: {
        label: 'Units To Show',
        placeholder: 'Y,Mo,W,D,H,M,S,Ms',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
    },
  ],
};

export default connectWidget('CountdownWidget', CountdownWidget, CountdownWidgetConfig, () => {}, editors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(countDownWidgetStyleConfig),
  pluginListing,
  docs,
});
