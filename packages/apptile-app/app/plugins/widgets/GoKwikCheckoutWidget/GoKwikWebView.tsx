import React, {useCallback, useEffect, useState} from 'react';
import WebView from 'react-native-webview';
import {WebViewNavigationEvent} from 'react-native-webview/src/WebViewTypes';
import _ from 'lodash';
import {SHOPIFY_SUCCESS_PAGE_URL_REGEX} from './GoKwikWebViewTypes';
import {Text, Linking, StyleSheet, View} from 'react-native';
import {ImageComponent} from 'apptile-core';

const jsCode = `
  const gokwiktargetNode = document.body;
  const observerOptions = {
    subtree: true,
    childList: true,
  };
  window.gokwikIframe = null;

  function logChanges(records, observer) {
    if (window.gokwikIframe) {
      if (!document.body.contains(window.gokwikIframe)) {
        window.gokwikIframe = null;
        window.ReactNativeWebView.postMessage('cancel');
      }
    }
    for (const record of records) {
      for (const addedNode of record.addedNodes) {
        if (addedNode.id == "gokwik-iframe" && addedNode.tagName == "IFRAME") {
          window.gokwikIframe = addedNode;
          window.ReactNativeWebView.postMessage('loaded');
        }
      }
    }
  }
  window.gokwikobserver = new MutationObserver(logChanges);
  gokwikobserver.observe(gokwiktargetNode, observerOptions);

  function apptileInitGokwik() {
    window.setTimeout( function () {
      gokwikSdk.on('orderSuccess',(data)=>{ 
        window.ReactNativeWebView.postMessage('success');
      });
      gokwikSdk.on('openInBrowserTab',(data)=>{ 
        window.ReactNativeWebView.postMessage('link$$'+data?.link);
      });
      gokwikSdk.on('shopifyRedirect',(data)=>{ 
        window.ReactNativeWebView.postMessage('link$$'+data?.link);
      });
    }, 800);
  }
  var head = document.getElementsByTagName("head")[0];
  head.addEventListener("load", function(event) {
      if (event.target.nodeName === "SCRIPT" && event.target.id === "gokwikScript")
      {
        apptileInitGokwik()
      }
  }, true);
  apptileInitGokwik();
`;
const GoKwikWebView = props => {
  const {value, mid, environment, storeId, fbpixel, cartId, storefrontAccessToken, shopDomain, onCancel, onSuccess} =
    props;
  const [isLoading, setLoading] = useState(true);
  const onLoad = useCallback(
    (syntheticEvent: WebViewNavigationEvent) => {
      const {nativeEvent} = syntheticEvent;
      console.log(nativeEvent);
      if (nativeEvent?.loading === false) {
        const url = nativeEvent?.url;
        if (SHOPIFY_SUCCESS_PAGE_URL_REGEX.test(url)) {
          if (onSuccess) onSuccess();
        }
      }
    },
    [onSuccess],
  );
  const onMessage = useCallback(
    event => {
      console.log(event.nativeEvent.data);
      if (event.nativeEvent.data === 'loaded') setTimeout(() => setLoading(false), 100);
      if (event.nativeEvent.data === 'cancel') onCancel ? onCancel() : null;
      if (event.nativeEvent.data === 'success') onSuccess ? onSuccess() : null;
      if (event.nativeEvent.data && _.startsWith(event.nativeEvent.data, 'link$$')) {
        const urlToOpen = _.trimStart(event.nativeEvent.data, 'link$$');
        console.log('Opening URL: ' + urlToOpen);
        Linking.openURL(urlToOpen);
      }
    },
    [onCancel, onSuccess],
  );

  const jsDynamicCode = `
  let storeInfo = {
    "mid": "${mid}",
    "environment": "${environment}",
    "type": "merchantInfo",
    "storeId": "${storeId}",
    "fbpixel": "${fbpixel}",
    "storeData": {
      "cartId": "${cartId}",
      "storefrontAccessToken": "${storefrontAccessToken}",
      "shopDomain": "${shopDomain}"
    }
  }
  initKwikCheckout(storeInfo);
  `;
  const jsToLoad = jsCode + jsDynamicCode;
  console.log(jsToLoad);
  console.log(`URL: ${value}`);
  const [currentURI, setURI] = useState(value);
  useEffect(() => {
    if (value !== currentURI) setURI(value);
  }, [currentURI, value]);
  const newSource = {
    uri: currentURI,
  };
  console.log(`WebviewSource: ${JSON.stringify(newSource)}`);
  return (
    <>
      <WebView
        // FIXME: Disable this for performace reasons. If this causes crashes please revert
        // androidLayerType="software"
        key={value}
        onLoad={onLoad}
        originWhitelist={['*']}
        source={newSource}
        // Comment from https://github.com/react-native-webview/react-native-webview/pull/1119
        /* Must be populated in order for `messagingEnabled` to be `true` to activate the
         * JS injection user scripts, consistent with current behaviour. This is undesirable,
         * so I'll address it in a follow-up PR. */
        onMessage={onMessage}
        injectedJavaScriptForMainFrameOnly={true}
        injectedJavaScript={jsToLoad}
        onShouldStartLoadWithRequest={request => {
          // // If we're loading the current URI, allow it to load
          // if (request.url === currentURI) return true;
          // // We're loading a new URL -- change state first
          // setURI(request.url);
          // return false;
          console.log(`Loading URL: ${request.url}`);
          if (request.url.startsWith('http')) {
            // setURI(request.url);
            return true;
          } else {
            // Linking.canOpenURL(request.url)
            //   .then(canOpen => {
            //     console.log('Opening link:', request.url);
            // if (canOpen)
            Linking.openURL(request.url);
            //   else {
            //     console.log('CANNOT Open link:', request.url);
            //   }
            //   // else throw Error();
            // })
            // .catch(e => { });
            return false;
          }
        }}
      />
    </>
  );
};

export default GoKwikWebView;
