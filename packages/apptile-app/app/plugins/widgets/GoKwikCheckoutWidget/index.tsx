import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import _ from 'lodash';
import GoKwikWebView from './GoKwikWebView';

const GoKwikCheckoutWidgetConfig = {
  value: '',
  mid: '',
  environment: '',
  storeId: '',
  fbpixel: '',
  cartId: '',
  storefrontAccessToken: '',
  shopDomain: '',
  onCancel: '',
  onSuccess: '',
};
type GoKwikCheckoutWidgetConfigType = typeof GoKwikCheckoutWidgetConfig;

const pluginListing: PluginListingSettings = {
  labelPrefix: 'GoKwikCheckout',
  type: 'widget',
  name: 'GoKwik Checkout View',
  description: 'Load GoKwik Checkout in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const GoKwikCheckoutWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const mid = model.get('mid');
  const environment = model.get('environment');
  const storeId = model.get('storeId');
  const fbpixel = model.get('fbpixel');
  const cartId = model.get('cartId');
  const shopDomain = model.get('shopDomain');
  const storefrontAccessToken = model.get('storefrontAccessToken', '');

  const [isValid, setValid] = useState(false);

  useEffect(() => {
    if(value && cartId && shopDomain && storefrontAccessToken && storeId && mid && environment)
      setValid(true);
  }, [value, cartId, shopDomain, storefrontAccessToken,storeId, mid, environment]);
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onCancel = useCallback(() => {
    triggerEvent('onCancel');
  }, [triggerEvent]);
  const onSuccess = useCallback(() => {
    triggerEvent('onSuccess');
  }, [triggerEvent]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]} ref={ref}>
      {isValid ? (
        <GoKwikWebView
          value={value}
          mid={mid}
          environment={environment}
          storeId={storeId}
          fbpixel={fbpixel}
          cartId={cartId}
          storefrontAccessToken={storefrontAccessToken}
          shopDomain={shopDomain}
          onCancel={onCancel}
          onSuccess={onSuccess}
        />
      ) : (
        <></>
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'mid',
      props: {
        label: 'Gokwik merchant id',
      },
    },
    {
      type: 'codeInput',
      name: 'environment',
      props: {
        label: 'Gokwik environment',
      },
    },
    {
      type: 'codeInput',
      name: 'storeId',
      props: {
        label: 'Gokwik store id',
      },
    },
    {
      type: 'codeInput',
      name: 'fbpixel',
      props: {
        label: 'Gokwik FB pixel config',
      },
    },
    {
      type: 'codeInput',
      name: 'cartId',
      props: {
        label: 'Shopify Cart Id',
      },
    },
    {
      type: 'codeInput',
      name: 'shopDomain',
      props: {
        label: 'Shopify shop domain',
      },
    },
    {
      type: 'codeInput',
      name: 'storefrontAccessToken',
      props: {
        label: 'Shopify SF Access token',
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCancel: {
    type: EventTriggerIdentifier,
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'GoKwikCheckoutWidget',
  GoKwikCheckoutWidget,
  GoKwikCheckoutWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
