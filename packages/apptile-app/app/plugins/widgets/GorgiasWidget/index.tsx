import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {WebView} from 'react-native-webview';

import _ from 'lodash';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';

type WebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
};
const GorgiasWebViewWidgetConfig: WebViewWidgetConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'gorgiasWebView',
  type: 'widget',
  name: 'Gorgias Web View',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const GorgiasWebViewWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();

  const headers = model.get('headers')?.toString();
  const injectedJavaScript = model.get('injectedJavaScript').toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onError = () => {
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  // convert key-value pair input into an object
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};
  // let source = {uri: value};
  // requestHeaders && (source.headers = requestHeaders);
  const [webViewKey, setKey] = useState(1);
  const [source, setSource] = useState({uri: value, headers: requestHeaders});
  useEffect(() => {
    let newSource = {
      uri: value,
      headers: requestHeaders,
    };
    if (!_.isEqual(source, newSource)) {
      setSource(newSource);
      setKey(webViewKey + 1);
    }
  }, [requestHeaders, source, value, webViewKey]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      {source.uri && (
        <WebView
          // androidLayerType="software"
          incognito={false}
          key={webViewKey}
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          source={source}
          onError={onError}
          {...(injectedJavaScript ? {injectedJavaScriptBeforeContentLoaded: injectedJavaScript} : {})}
          javaScriptEnabled={true}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  loader: {
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
});

export default connectWidget(
  'GorgiasWebViewWidget',
  GorgiasWebViewWidget,
  GorgiasWebViewWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
