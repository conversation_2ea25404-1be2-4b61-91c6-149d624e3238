import React from 'react';

import docs from './docs';
import {mergeWithDefaultStyles, PluginEditorsConfig} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {TopTabHeader} from 'apptile-core';

const pluginListing: PluginListingSettings = {
  labelPrefix: 'Header',
  type: 'widget',
  name: 'Header',
  description: 'Display Header',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
};

interface IProps {
  model: any;
  modelStyles: any;
  config: any;
}

const HeaderWidget: React.FC<IProps> = React.forwardRef((props, ref: any) => {
  const {model} = props;

  const cartCount = model.get('cartCount') || 0;
  const wishlistCount = model.get('wishlistCount') || 0;
  const alertCount = model.get('alertCount');
  const rightIconAlert = model.get('rightIconAlert') || false;
  const isHidden = model.get('isHidden');
  if (!isHidden)
    return (
      <TopTabHeader
        ref={ref}
        cartCount={cartCount}
        wishlistCount={wishlistCount}
        alertCount={alertCount}
        rightIconAlert={rightIconAlert}
      />
    );
});
const HeaderConfig: any = [];

const propertySettings: PluginPropertySettings = {};

const editors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartCount',
      props: {
        label: 'cartCount',
        placeholder: ' ',
      },
    },
    {
      type: 'codeInput',
      name: 'wishlistCount',
      props: {
        label: 'wishlistCount',
        placeholder: ' ',
      },
    },
    {
      type: 'codeInput',
      name: 'alertCount',
      props: {
        label: 'alertCount',
        placeholder: ' ',
      },
    },
    {
      type: 'codeInput',
      name: 'rightIconAlert',
      props: {
        label: 'rightIconAlert',
        placeholder: ' ',
      },
    },
  ],
  layout: [
    {
      type: 'codeInput',
      name: 'isHidden',
      props: {
        label: 'Hidden',
        placeholder: ' ',
      },
    },
  ],
};

export default connectWidget('HeaderWidget', HeaderWidget, {value: 0}, null, editors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(HeaderConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'icon'],
});
