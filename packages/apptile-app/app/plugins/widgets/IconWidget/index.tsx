import React from 'react';

import docs from './docs';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {CustomIcon} from 'apptile-core';
import {Icon} from 'apptile-core';

const IconWidgetConfig = {
  iconType: 'Material Icon',
  value: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'Icon',
  type: 'widget',
  name: 'Icon',
  description: 'Display Icon',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
  layout: {
    flex: 1,
    flexDirection: 'column',
  },
  icon: 'icon',
};

const widgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Color',
      placeholder: '#000000',
    },
  },
  {
    type: 'codeInput',
    name: 'fontSize',
    props: {
      label: 'Size',
      placeholder: '18',
    },
  },
];

interface IProps {
  model: any;
  modelStyles: any;
  config: any;
}

const IconWidget: React.FC<IProps> = React.forwardRef((props, ref: any) => {
  const {model, modelStyles, config} = props;

  const iconType = model.get('iconType')?.toString() || 'Material Icons';
  const name = model.get('value')?.toString() || 'help';

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const modelPlatformStyles = {fontSize: 18, ...getPlatformStyles(modelStyles)};

  if (iconType === 'Custom Icon') return <CustomIcon name={name} style={[layoutStyles, modelPlatformStyles]} />;
  return <Icon ref={ref} iconType={iconType} name={name} style={[layoutStyles, modelPlatformStyles]} />;
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (_model, _renderedValue, _selector) => _renderedValue,
  },
};

const editors: PluginEditorsConfig<typeof IconWidgetConfig> = {
  basic: [
    {
      type: 'iconChooserInput',
      name: 'value',
      props: {
        label: 'Icon',
        placeholder: 'help',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
    },
  ],
};

export default connectWidget('IconWidget', IconWidget, IconWidgetConfig, null, editors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(widgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'icon'],
});
