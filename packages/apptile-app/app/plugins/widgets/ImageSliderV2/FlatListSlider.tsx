import React, {useState, useRef, useEffect, useCallback} from 'react';
import {View, Image, Dimensions, FlatList, StyleSheet, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import Indicator from './Indicator';

const InfiniteSlider = ({
  containerWidth = '100%',
  containerHeight,
  aspectRatio,
  data,
  onPress,
  resizeMode = 'cover',
  timer = 5000,
  autoscroll = true,
  sliderBackgroundColor = 'transparent',
  indicator = true,
  commonIndicatorStyles,
  initialDelay = 0,
}) => {
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  const [slideWidth, setSlideWidth] = useState(0);
  const containerRef = useRef(null);
  const flatListRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const indexRef = useRef(0);

  const [images, setImages] = useState([...data, ...data, ...data]);
  const [currentInitialDelay, setCurrentInitialDelay] = useState(initialDelay);
  const [isPaused, setIsPaused] = useState(false);
  const timeoutRef = useRef(null);

  useEffect(() => {
    setImages([...data, ...data, ...data]);
  }, [data]);

  useEffect(() => {
    const updateLayout = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    Dimensions.addEventListener('change', updateLayout);
    return () => {
      Dimensions.removeEventListener('change', updateLayout);
    };
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.measure((x, y, width) => {
        setSlideWidth(width);
      });
    }
  }, [screenWidth]);

  useEffect(() => {
    let intervalId;

    const startInterval = () => {
      if (autoscroll && data.length > 1 && !isPaused) {
        intervalId = setInterval(() => {
          const nextIndex = (indexRef.current + 1) % images.length;
          flatListRef.current?.scrollToIndex({index: nextIndex, animated: true});
        }, timer);
      }
    };

    const startAutoscroll = () => {
      if (currentInitialDelay > 0) {
        timeoutRef.current = setTimeout(() => {
          startInterval();
          setCurrentInitialDelay(0);
        }, currentInitialDelay);
      } else {
        startInterval();
      }
    };

    if (!isPaused) {
      startAutoscroll();
    }

    return () => {
      clearInterval(intervalId);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [autoscroll, images.length, timer, initialDelay, data.length, isPaused, currentInitialDelay]);

  const handleScrollBegin = () => {
    setIsPaused(true);
  };

  const handleScrollEnd = () => {
    setIsPaused(false);
  };

  const handleScroll = useCallback(
    event => {
      const scrollPosition = event.nativeEvent.contentOffset.x;
      const index = Math.round(scrollPosition / slideWidth);

      setCurrentIndex(index);
      indexRef.current = index;

      if (index === images.length - data.length) {
        setImages([...images, ...data]);
      } else if (index === data.length - 1) {
        setImages([...data, ...images]);
        flatListRef.current?.scrollToIndex({index: data.length * 2 - 1, animated: false});
      }
    },
    [data, images, slideWidth],
  );

  const renderItem = useCallback(
    ({item, index}) => {
      return (
        <TouchableOpacity
          style={[
            styles.slideContainer,
            {width: slideWidth},
            aspectRatio ? {aspectRatio: aspectRatio} : {height: containerHeight},
          ]}
          onPress={() => {
            if (item.navEntityType && item.navEntityId) {
              onPress(item.navEntityType, item.navEntityId);
            }
          }}
          activeOpacity={0.85}>
          <View style={[styles.imageContainer, {height: '100%'}]}>
            {item.image ? (
              <Image
                source={{uri: item.image}}
                style={styles.image}
                resizeMode={item.resizeMode || 'cover'}
                onError={e => console.error('Image Load Error:', e.nativeEvent.error)}
              />
            ) : (
              <Text style={{color: 'red'}}>Image Not Found</Text>
            )}
          </View>
        </TouchableOpacity>
      );
    },
    [onPress, resizeMode, slideWidth, containerHeight, aspectRatio],
  );

  return (
    <View
      ref={containerRef}
      style={[styles.container, {width: containerWidth, backgroundColor: sliderBackgroundColor || 'transparent'}]}
      onLayout={() => {
        containerRef.current?.measure((x, y, width) => {
          setSlideWidth(width);
        });
      }}>
      {images.length > 0 && slideWidth > 0 ? (
        <>
          <FlatList
            ref={flatListRef}
            data={images}
            renderItem={renderItem}
            keyExtractor={(item, index) => `${item.image}-${index}`}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            onScrollBeginDrag={handleScrollBegin}
            onScrollEndDrag={handleScrollEnd}
            onMomentumScrollEnd={handleScrollEnd}
            getItemLayout={(data, index) => ({
              length: slideWidth,
              offset: slideWidth * index,
              index,
            })}
            initialScrollIndex={data.length}
          />
          {indicator && (
            <Indicator
              data={data}
              currentIndex={currentIndex % data.length}
              commonIndicatorStyles={commonIndicatorStyles}
            />
          )}
        </>
      ) : (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={commonIndicatorStyles.indicatorActiveColor} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'red',
    position: 'relative',
  },
  slideContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#blue',
    borderRadius: 0,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default InfiniteSlider;
