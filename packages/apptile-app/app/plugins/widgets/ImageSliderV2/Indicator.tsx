import React from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';

const Indicator = ({data, currentIndex, commonIndicatorStyles}) => {
  return (
    <View style={[styles.wrapper, {top: commonIndicatorStyles.indicatorTopMargin}]}>
      <View style={[styles.indicatorContainer]}>
        {data.map((_, index) => {
          const isActive = currentIndex % data.length === index;
          return (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: isActive
                    ? commonIndicatorStyles.indicatorActiveColor
                    : commonIndicatorStyles.indicatorInactiveColor,
                  width: isActive
                    ? commonIndicatorStyles.indicatorActiveSize
                    : commonIndicatorStyles.indicatorInactiveSize,
                  height: isActive
                    ? commonIndicatorStyles.indicatorActiveSize
                    : commonIndicatorStyles.indicatorInactiveSize,
                  borderRadius: commonIndicatorStyles.indicatorBorderRadius,
                },
              ]}
            />
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    top: '90%',
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    height: 6,
    width: 6,
    borderRadius: 8,
    marginHorizontal: 3,
  },
});

export default Indicator;
