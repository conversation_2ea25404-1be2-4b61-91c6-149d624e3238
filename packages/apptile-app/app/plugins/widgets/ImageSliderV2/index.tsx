import React, {useCallback} from 'react';
import {
  defaultEditors,
  connectWidget,
  useCallbackRef,
  PluginListingSettings,
  PluginPropertySettings,
  getPlatformStyles,
  mergeWithDefaultStyles,
  navigateToScreen,
  WidgetStyleEditorOptions,
  selectAppConfig,
  getOptimalImageSize,
} from 'apptile-core';
import FlatListSlider from './FlatListSlider';
import docs from './docs';
// import {WidgetStyleEditorOptions} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import { Linking } from 'react-native';

const ImageSliderWidgetV2Config = {
  value: [],
  sliderHeight: 240,
  slideAutoplayToggle: true,
  slideIndicatorToggle: true,
  indicatorTopMargin: '90%',
  indicatorBorderRadius: 8,
  indicatorActiveSize: 18,
  indicatorInactiveSize: 16,
  sliderBackgroundColor: 'transparent',
};

const pluginListing = {
  labelPrefix: 'imageSlider',
  type: 'widget',
  name: 'Image Slider V2',
  description: 'Display a series of images on screen that slide across.',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'image-carousel',
};

export const imageSliderWidgetStyleConfigV2: WidgetStyleEditorOptions = [
  {
    type: 'numericInput',
    name: 'indicatorTopMargin',
    props: {
      label: 'Indicator Margin Top',
    },
  },
  {
    type: 'numericInput',
    name: 'indicatorInactiveSize',
    props: {
      label: 'Inactive Indicator Size',
      noUnit: true,
    },
  },
  {
    type: 'numericInput',
    name: 'indicatorActiveSize',
    props: {
      label: 'Active Indicator Size',
      noUnit: true,
    },
  },
  {
    type: 'colorInput',
    name: 'sliderBackgroundColor',
    props: {
      label: 'Slider Background Color',
    },
  },
  {
    type: 'colorInput',
    name: 'indicatorInactiveColor',
    props: {
      label: 'Inactive Indicator Color',
    },
  },
  {
    type: 'colorInput',
    name: 'indicatorActiveColor',
    props: {
      label: 'Active Indicator Color',
    },
  },
  {
    type: 'numericInput',
    name: `indicatorBorderRadius`,
    props: {
      label: `Indicator Border Radius`,
      noUnit: true,
    },
  },
];

const propertySettings = {};

const ImageSliderV2 = React.forwardRef((props, ref) => {
  const dispatch = useDispatch();
  const {model, modelUpdate, modelStyles, config} = props;
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});

  const modelPlatformStyles = modelStyles ? getPlatformStyles(modelStyles) : {};
  const {
    indicatorTopMargin,
    indicatorInactiveSize,
    indicatorInactiveColor,
    sliderBackgroundColor,
    indicatorBorderRadius,
    indicatorActiveSize,
    indicatorActiveColor,
    ...restModelPlatformStyles
  } = modelPlatformStyles;
  const commonIndicatorStyles = {
    indicatorTopMargin: indicatorTopMargin ?? '90%',
    indicatorInactiveSize: indicatorInactiveSize ?? 16,
    indicatorActiveSize: indicatorActiveSize ?? 18,
    indicatorBorderRadius: indicatorBorderRadius ?? 8,
    indicatorInactiveColor: indicatorInactiveColor ?? '#bdc3c7',
    indicatorActiveColor: indicatorActiveColor ?? '#3498db',
  };

  const appConfig = useSelector(selectAppConfig);

  const getDeviceImage = useCallback(
    (assetId: string) => {
      const imageRecord = appConfig.getImageId(assetId);

      const getOptimalImage = (layoutSize: string) => {
        return getOptimalImageSize(imageRecord, layoutSize);
      };
      return {imageRecord, getOptimalImage};
    },
    [appConfig],
  );

  const onPress = useCallbackRef((navEntityType, navEntityId) => {
    if (_.isEmpty(navEntityType)) return;

    switch (navEntityType.toLowerCase()) {
      case 'product':
        dispatch(navigateToScreen('Product', {productHandle: navEntityId}));
        break;
      case 'collection':
        dispatch(navigateToScreen('Collection', {collectionHandle: navEntityId}));
        break;
      case 'externallink': 
        Linking.openURL(navEntityId);
        break;
      default:
        dispatch(navigateToScreen(navEntityType, {}));
        break;
    }
  });

  const data = model.get('value') && Array.isArray(model.get('value')) ? model.get('value') : [];
  const timer = Number(model.get('timer')) || 5000;
  const initialDelay = Number(model.get('initialDelay')) || 0;
  const sliderHeight = Number(model.get('sliderHeight')) || 240;
  const aspectRatio = Number(model.get('aspectRatio')) || null;
  const slideIndicatorToggle = model.get('slideIndicatorToggle') || false;
  const slideAutoplayToggle = model.get('slideAutoplayToggle') || false;

  const transformData = data => {
    return data.map(item => {
      const {url, sourceType, assetId, navEntityId, navEntityType, resizeMode} = item;

      if (sourceType && sourceType?.toLowerCase() !== 'url' && assetId) {
        const assetSource = getDeviceImage(assetId);
        const imageRecord = assetSource.imageRecord?.fileUrl;

        return {
          image: imageRecord,
          navEntityType,
          navEntityId,
          resizeMode,
        };
      } else {
        return {
          image: url,
          navEntityType,
          navEntityId,
          resizeMode,
        };
      }
    });
  };

  const transformedData = transformData(data);

  return (
    <FlatListSlider
      containerWidth="100%"
      containerHeight={sliderHeight}
      aspectRatio={aspectRatio}
      styles={layoutStyles}
      data={transformedData}
      timer={timer}
      initialDelay={initialDelay}
      indicator={slideIndicatorToggle}
      sliderHeight={sliderHeight}
      autoscroll={slideAutoplayToggle}
      onPress={onPress}
      sliderBackgroundColor={sliderBackgroundColor ?? 'transparent'}
      commonIndicatorStyles={commonIndicatorStyles}
    />
  );
});

const basicEditors = [...defaultEditors.basic];
const valueIndex = basicEditors.findIndex(editor => editor.name === 'value');
basicEditors[valueIndex].defaultValue = "{{['']}}";

const widgetEditors = {
  basic: [
    {
      type: 'listEditor',
      name: 'value',
      props: {
        label: 'Image List',
      },
    },
    {
      type: 'numericInput',
      name: 'sliderHeight',
      props: {
        label: 'Slider Height',
        placeholder: '240',
        noUnit: true,
      },
    },
    {
      type: 'codeInput',
      name: 'aspectRatio',
      props: {
        label: 'Aspect Ratio',
        placeholder: 'null',
      },
    },
    {
      type: 'checkbox',
      name: 'slideIndicatorToggle',
      props: {
        label: 'Show Slide Indicator',
        checkedValue: true,
        defaultValue: true,
      },
    },
    {
      type: 'checkbox',
      name: 'slideAutoplayToggle',
      props: {
        label: 'Autoplay Slides',
        checkedValue: true,
        defaultValue: true,
      },
    },
    {
      type: 'codeInput',
      name: 'timer',
      props: {
        label: 'Timer',
        placeholder: '{{5000}}',
      },
      value: '{{5000}}',
    },
  ],
  advanced: [
    {
      type: 'codeInput',
      name: 'initialDelay',
      props: {
        label: 'Initial Delay before Autoscroll',
        placeholder: '{{5000}}',
      },
      value: '{{5000}}',
    },
  ],
  layout: [...defaultEditors.layout],
  style: defaultEditors,
};

export default connectWidget('ImageSliderWidgetV2', ImageSliderV2, ImageSliderWidgetV2Config, null, widgetEditors, {
  propertySettings,
  // widgetStyleConfig: mergeWithDefaultStyles(imageSliderWidgetStyleConfigV2),
  widgetStyleConfig: imageSliderWidgetStyleConfigV2,
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'imageSlider'],
});
