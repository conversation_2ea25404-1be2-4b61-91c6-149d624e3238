import React, {useCallback} from 'react';
import {Image, Pressable, StyleSheet} from 'react-native';
import {ImageComponent} from 'apptile-core';

export default ChildItem = ({item, style, onPress, resizeMode, index, imageKey, local, height}) => {
  const pressCallback = useCallback(() => {
    if (onPress) onPress(index);
  }, [index, onPress]);
  return (
    <Pressable style={[styles.container, {height}]} onPress={pressCallback}>
      <ImageComponent resizeMode={resizeMode} style={[style, {height: height}]} source={local ? item : {uri: item}} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, width: '100%'},
  image: {
    resizeMode: 'cover',
  },
});
