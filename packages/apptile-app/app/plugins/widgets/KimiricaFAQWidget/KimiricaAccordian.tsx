import React, {useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import RenderHtml from 'react-native-render-html';
import {RichTextDisplay} from '../RichTextWidget';
import {Icon} from 'apptile-core';

export const AccordionItem = ({title, content, layoutStyles, modelProps, textStyles}) => {
  const [expanded, setExpanded] = useState(false);
  const {openIconName, closeIconName, iconType, modelPlatformStyles, iconSize, iconColor} = modelProps;

  return (
    <View
      style={{
        marginBottom: 10,
        backgroundColor: layoutStyles.accordianTitleColor,
        borderWidth: layoutStyles.accordianBorderWidth,
        borderColor: layoutStyles.accordianBorderColor,
      }}>
      <TouchableOpacity onPress={() => setExpanded(!expanded)}>
        <View
          style={{
            padding: 10,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: layoutStyles.accordianTitleColor,
            borderWidth: layoutStyles.accordianBorderWidth,
            borderColor: layoutStyles.accordianBorderColor,
            borderRadius: layoutStyles.accordianBorderRadius,
          }}>
          <Text style={[textStyles]}>{title}</Text>
          {expanded ? (
            <Icon iconType={iconType} name={closeIconName} size={iconSize} color={iconColor} />
          ) : (
            <Icon iconType={iconType} name={openIconName} size={iconSize} color={iconColor} />
          )}
        </View>
      </TouchableOpacity>
      {expanded && <RichTextDisplay html={content} baseStyle={[{paddingLeft: 10, paddingRight: 10}, textStyles]} />}
    </View>
  );
};
