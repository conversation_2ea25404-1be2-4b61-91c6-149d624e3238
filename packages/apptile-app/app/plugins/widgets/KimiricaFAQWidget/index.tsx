/* eslint-disable react-native/no-inline-styles */
import {makeBoolean} from 'apptile-core';
import {usePlaceHolder} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {WidgetProps, connectWidget} from 'apptile-core';
import docs from './docs';
import {datasourceTypeModelSel} from 'apptile-core';
import {useSelector} from 'react-redux';
import * as MetaObjectGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/metaobject';
import {MetaObjectTransformer} from '../../datasource/ShopifyV_22_10/transformers/metaobjectTransformer';
import {AccordionItem} from './KimiricaAccordian';
import CodeInput from '@/root/web/components/codeEditor/codeInput';

const KimiricaFAQWidgetConfig = {
  value: 'Text',
  onLoggedInTap: '',
  onGuestInTap: '',
  metaObjectId: '',
  isLoading: '',
  closeIconName: '',
  openIconName: '',
  iconType: 'Material Icon',
  faqMap: '',
  faqKeys: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'kimiricaFAQWidget',
  type: 'widget',
  name: 'KimiricaFAQWidget',
  description: 'Show FAQ using meta objects for Kimirica',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'accordion',
};

export const KimiricaFAQWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    defaultValue: 'tile.checkBox.backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'textColor',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'accordianTitleColor',
    props: {
      label: 'Accordian Title Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'numericInput',
    name: 'iconSize',
    props: {
      label: 'Icon Size',
      placeholder: '20',
      unit: 'dp',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'accordianBorderRadius',
    props: {
      label: 'Border Radius',
      placeholder: '0',
      options: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
      layout: 'square',
    },
  },
  {
    type: 'numericInput',
    name: 'accordianBorderWidth',
    props: {
      label: 'Accordian Border Width',
      placeholder: '1',
    },
  },
  {
    type: 'colorInput',
    name: 'accordianBorderColor',
    props: {
      placeholder: '#000000',
    },
  },
  {
    type: 'colorInput',
    name: 'iconColor',
    props: {
      placeholder: '#000000',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    defaultValue: 'tile.checkBox.typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');

const KimricaFAQWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config} = props;
  const Placeholder = usePlaceHolder();
  // const modelValue = model.get('data');
  const faqMap = model.get('faqMap');
  const faqKeys = model.get('faqKeys');
  const metaObjectId = model.get('metaObjectId');
  const ShopifyDSModel = useSelector(shopifyModelSel);
  const [metaObject, setMetaObject] = useState<any>([]);

  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    async function fetchMetaObject() {
      let queryResponse = await queryRunner.runQuery('query', MetaObjectGqls.GET_SHOPIFY_METAOBJECT, {
        id: metaObjectId,
      });

      queryResponse = MetaObjectTransformer(queryResponse.data)?.data;

      const parsedResponse = FAQParser(faqKeys, faqMap, queryResponse.fields);
      const meta = Object.entries(parsedResponse).reduce((arr, [key, value]) => {
        if (value.headingValue && value.headingValue.length > 0) {
          arr.push({title: value.headingValue, data: value.data});
        }
        return arr;
      }, [] as any[]);
      setMetaObject(meta);
    }
    if (metaObjectId && metaObjectId.length > 0) {
      fetchMetaObject();
    }
    return () => {};
  }, [metaObjectId, ShopifyDSModel, faqKeys, faqMap]);

  const layout = config.get('layout');
  const isLoading = !!model.get('isLoading');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {});
  const iconType = model.get('iconType')?.toString() || 'Material Icons';
  const openIconName = model.get('openIconName') || 'expand-more';
  const closeIconName = model.get('closeIconName') || 'expand-less';

  const {
    shadowColor,
    shadowOffset,
    shadowRadius,
    iconSize,
    iconColor,
    typography: _typography,
    textColor,
    ...modelPlatformStyles
  } = modelStyles ? getPlatformStyles(modelStyles) : {};

  const {typography, subHeadingTypography, color, ...restModelPlatformStyles} = modelPlatformStyles;

  return isLoading || !metaObjectId ? (
    <Placeholder
      layoutStyles={{...layoutStyles, ...restModelPlatformStyles, height: typography?.fontSize, minWidth: '75%'}}
    />
  ) : (
    <View
      ref={ref}
      style={[
        layoutStyles,
        modelPlatformStyles,
        restModelPlatformStyles,
        {
          textShadowColor: shadowColor,
          textShadowOffset: shadowOffset,
          textShadowRadius: shadowRadius,
        },
      ]}>
      {metaObject.map((item, i) => (
        <AccordionItem
          key={i}
          title={item.title}
          content={item.data}
          layoutStyles={{...layoutStyles, ...restModelPlatformStyles}}
          modelProps={{
            openIconName,
            closeIconName,
            iconType,
            iconSize,
            iconColor,
            modelPlatformStyles,
          }}
          textStyles={{color: textColor}}
        />
      ))}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'iconChooserInput',
      name: 'iconName',
      props: {
        label: 'Icon Name',
        placeholder: 'help',
      },
    },
    {
      type: 'codeInput',
      name: 'faqKeys',
      props: {
        label: 'faqKeys',
        placeholder: '[key1 , key2 , key3]',
      },
    },
    {
      type: 'codeInput',
      name: 'faqMap',
      props: {
        label: 'faqMap',
        placeholder: '{key1 : value1}',
      },
    },
    {
      type: 'codeInput',
      name: 'metaObjectId',
      props: {
        label: 'metaObjectId',
        placeholder: '{key1 : value1}',
      },
    },
    {
      type: 'iconChooserInput',
      name: 'openIconName',
      props: {
        label: 'Open Icon Name',
        placeholder: 'help',
      },
    },
    {
      type: 'iconChooserInput',
      name: 'closeIconName',
      props: {
        label: 'Open Icon Name',
        placeholder: 'help',
      },
    },
  ],
  // advanced: defaultEditors.advanced,
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  isLoading: {
    getValue: (model, val) => {
      return makeBoolean(val);
    },
  },
  onLoggedInTap: {
    type: EventTriggerIdentifier,
  },
  onGuestInTap: {
    type: EventTriggerIdentifier,
  },
  iconName: {
    getValue: (model, val) => {
      return val;
    },
  },
  faqKeys: {
    getValue: (model, val) => {
      return val;
    },
  },
  faqMap: {
    getValue: (model, val) => {
      return val;
    },
  },
};
const emptyOnupdate = null;

function FAQParser(keys: string[], faqMap: any, data: any) {
  const res = data.filter((x: string) => keys.includes(x.key));

  _.entries(faqMap).map(x => {
    const [faqKey, faqValue] = x;
    const resKey = res.find((z: string) => z.key === faqKey)?.value || null;
    const resVal = res.find((z: string) => z.key === faqValue.value)?.value || null;
    if (resKey && resKey.length > 0) {
      faqMap[faqKey].headingValue = resKey;
      faqMap[faqKey].data = resVal;
    }
  });
  return faqMap;
}

export default connectWidget(
  'KimiricaFAQWidget',
  KimricaFAQWidget,
  KimiricaFAQWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(KimiricaFAQWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'text'],
  },
);
