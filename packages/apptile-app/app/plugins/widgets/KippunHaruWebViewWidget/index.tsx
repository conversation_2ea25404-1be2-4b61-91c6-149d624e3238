import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {WebView, WebViewMessageEvent} from 'react-native-webview';

import _ from 'lodash';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles, navigateToScreen} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';

type KippunHaruWebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
};
const KippunHaruWebViewWidgetConfig: KippunHaruWebViewWidgetConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'kippunHaruWebView',
  type: 'widget',
  name: 'Kippun Haru Web View',
  description: 'Load external uri in the app with some custom js injected already',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const REGEX = /\/products\/(.+)/;

const KippunHaruWebViewWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, config, triggerEvent, dispatch} = props;
  const value = model.get('value')?.toString();

  const headers = model.get('headers')?.toString();
  const injectedJavaScript = model.get('injectedJavaScript').toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onError = () => {
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  // convert key-value pair input into an object
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};
  // let source = {uri: value};
  // requestHeaders && (source.headers = requestHeaders);
  const [webViewKey, setKey] = useState(1);
  const [source, setSource] = useState({uri: value, headers: requestHeaders});
  useEffect(() => {
    let newSource = {
      uri: value,
      headers: requestHeaders,
    };
    if (!_.isEqual(source, newSource)) {
      setSource(newSource);
      setKey(webViewKey + 1);
    }
  }, [requestHeaders, source, value, webViewKey]);

  const selectorsToHide = useMemo(() => {
    return [
      '#shopify-section-header',
      '#shopify-section-footer',
      '#shopify-section-announcement-bar',
      '#shopify-chat',
      '#shopify-section-popup',
      '.samita-container',
      '.card-information__button',
      '.view-all',
      '.jdgm-widget',
      '.featured-product-section',
      '.multicolumn',
    ];
  }, []);

  const finalInjectedJavaScript = useMemo(() => {
    return `
    window.addEventListener('load', (event) => {
      const styleTag = document.createElement('style');
      styleTag.innerHTML = '${selectorsToHide.join(', ')} { display: none !important; }';
      document.head.appendChild(styleTag);
      window.ReactNativeWebView.postMessage(JSON.stringify({type: 'loadEnd'}));
    });

    ${injectedJavaScript}

    true;
    `;
  }, [selectorsToHide, injectedJavaScript]);

  const onMessage = useCallback((event: WebViewMessageEvent) => {
    const {type, handle} = JSON.parse(event.nativeEvent.data);
    switch (type) {
      case 'productClick':
        dispatch(navigateToScreen('Product', {productHandle: handle}));
        break;
      case 'loadEnd':
        onLoadEnd();
        break;
      default:
        break;
    }
  }, []);

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      {source.uri && (
        <WebView
          // androidLayerType="software"
          incognito={true}
          key={webViewKey}
          originWhitelist={['*']}
          source={source}
          onError={onError}
          injectedJavaScriptBeforeContentLoaded={finalInjectedJavaScript}
          javaScriptEnabled={true}
          onMessage={onMessage}
          onShouldStartLoadWithRequest={request => {
            const matches = request.url.match(REGEX);
            if (matches && matches[1]) {
              dispatch(navigateToScreen('Product', {productHandle: matches[1]}));
              return false;
            }

            return true;
          }}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'KippunHaruWebViewWidget',
  KippunHaruWebViewWidget,
  KippunHaruWebViewWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
