import _ from 'lodash';
import React from 'react';
import {View} from 'react-native';
import {encodeListViewPlugin, renderWidgetTreeNode, NativeEditableWidget} from 'apptile-core';
import { EditableHandlePositionContext } from '../common/components/EditableWidget';

export class _ApptileListItem extends React.Component {
  render() {
    let EditableWidget = NativeEditableWidget;
    if (global.EditableWidget) {
      // logger.info("Taking web version of editable widget in listItem");
      EditableWidget = global.EditableWidget;
    }

    const {children, isEditable, item, isHorizontal, numColumns, itemWidth, itemHeight} = this.props;
    const {i} = item;

    const sizingProps = isHorizontal
      ? {
          flexBasis: itemHeight,
          width: itemWidth,
          height: itemHeight,
          minHeight: itemHeight,
          maxHeight: itemHeight,
        }
      : {
          height: itemHeight,
          flexBasis: itemHeight,
          width: itemWidth,
          minWidth: itemWidth,
          maxWidth: itemWidth,
          // flexBasis: _.isNaN(itemHeight / numColumns) ? undefined : itemHeight / numColumns,
          // width: itemWidth / numColumns,
          // minWidth: itemWidth / numColumns,
        };
    // logger.info(`ListView RenderItem ${i}`);
    return (
      <View key={i} style={[{flex: 1, flexGrow: 0, flexShrink: 0, ...sizingProps}]}>
        {children.map(widget => {
          return isEditable ? (
            <EditableHandlePositionContext.Consumer key={encodeListViewPlugin(widget.id, i)}>
              {handlePosition => (
                <EditableHandlePositionContext.Provider value={handlePosition}>
                  <EditableWidget {...{widget, isEditable}}>
                    {renderWidgetTreeNode(this.props)(widget, i)}
                  </EditableWidget>
                </EditableHandlePositionContext.Provider>
              )}
            </EditableHandlePositionContext.Consumer>
          ) : (
            renderWidgetTreeNode(this.props)(widget, i)
          );
        })}
      </View>
    );
  }

  shouldComponentUpdate(nextProps, nextState) {
    return !(
      this.props.isEditable === nextProps.isEditable &&
      this.props.item === nextProps.item &&
      _.isEqual(this.props.children, nextProps.children) &&
      this.props.model === nextProps.model &&
      this.props.itemWidth === nextProps.itemWidth &&
      this.props.itemHeight === nextProps.itemHeight &&
      this.props.numColumns === nextProps.numColumns
    );
  }
}
