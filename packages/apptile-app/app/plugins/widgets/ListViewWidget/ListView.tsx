import {ApptileAnimationsContext} from 'apptile-core';
import {EmptyPlaceholder} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useContext, useImperativeHandle, useRef, useState} from 'react';
import {FlatList, LayoutChangeEvent, Platform} from 'react-native';
import {ApptileFlexbox} from 'apptile-core';
import {renderWidgetTreeNode} from 'apptile-core';
import {ContainerProps} from 'apptile-core';
import {WidgetLayout, WidgetTreeNode} from '../widgetLayout';
import {_ApptileListItem} from './ApptileListItem';

type ListViewWidgetLayoutType = Pick<WidgetLayout, 'id' | 'layout' | 'pageKey' | 'hidden'>;
type PluginInstanceIdToLayoutType = {
  [pluginId: string]: ListViewWidgetLayoutType;
};

type ApptileListViewProps = React.PropsWithRef<
  ContainerProps & {
    pluginInstanceIdToLayout: PluginInstanceIdToLayoutType;
    forwardedRef: React.Ref<any>;
  }
>;

const ApptileListView = React.forwardRef((props: ApptileListViewProps, ref) => {
  const {
    id,
    pageKey,
    model,
    namespace,
    layout,
    children,
    isEditable,
    containerId,
    isAnimated,
    animations,
    instance,
    triggerEvent,
    config,
  } = props;
  const [listViewWidth, setListViewWidth] = useState(0);
  const [listViewHeight, setListViewHeight] = useState(0);
  const [numItems, setNumItems] = useState('0');
  useEffect(() => {
    setNumItems(model?.get('isLoading', false) + '_' + (model?.get('instances', 0) ?? 0));
  }, [model]);
  const [currentOffset, setCurrentOffset] = useState(0);

  const data: any = model.get('data');
  const layoutStyles = layout ? layout.getNonContainerFlexProperties() : {flex: 1};
  // const layoutContainerStyles = layout ? layout.getContainerFlexProperties() : {flex: 1};
  const {width, height} = layoutStyles;
  const isHorizontal = model.get('horizontal', false);

  const pluginConfig = config.get('config');
  const numOfColumns = pluginConfig ? pluginConfig.get('numColumns', 1) : 1;
  const numColumns = isHorizontal ? 1 : numOfColumns;
  const itemWidth = _.toNumber(model.get('itemWidth')) || listViewWidth;
  const itemHeight = _.toNumber(model.get('itemHeight')) || listViewHeight;
  const showPlaceholder = isEditable && !children.length;
  const isSliderMode = model.get('isSliderMode', false);
  const scrollIndex = model.get('scrollIndex', 0);
  const isRepeaterMode = model.get('isRepeaterMode', false);
  const disableVirtualizationIos = pluginConfig ? pluginConfig.get('disableVirtualizationIos', false) : false;
  const disableVirtualizationAndroid = pluginConfig ? pluginConfig.get('disableVirtualizationAndroid', false) : false;
  const initialNumToRender = pluginConfig ? Number(pluginConfig.get('initialNumToRender', 10)) : 10;
  const windowSize = pluginConfig ? Number(pluginConfig.get('windowSize', 11)) : 11;
  const scrollEventThrottle = pluginConfig ? Number(pluginConfig.get('scrollEventThrottle', 16)) : 16;
  const maxToRenderPerBatch = pluginConfig ? Number(pluginConfig.get('maxToRenderPerBatch', 10)) : 10;

  const animProvider = useContext(ApptileAnimationsContext);
  
  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      if (e.nativeEvent.layout.width > 0 && listViewWidth !== e.nativeEvent.layout.width) {
        setListViewWidth(e.nativeEvent.layout.width);
      }
      if (e.nativeEvent.layout.height > 0 && listViewHeight !== e.nativeEvent.layout.height) {
        setListViewHeight(e.nativeEvent.layout.height);
      }
    },
    [listViewHeight, listViewWidth],
  );

  const onEndReached = useCallback(() => {
    logger.info('ListView End Reached !!!!');
    triggerEvent('onEndReached');
  }, [triggerEvent]);

  const adjustChildrenLayouts = useCallback((rootWidget: WidgetTreeNode, instance: number) => {
    const recurse = (widget: WidgetTreeNode) => {
      let adjustedWidget = {...widget};
      adjustedWidget.children = widget.children.map(child => recurse(child));
      return {...adjustedWidget, instance};
    };
    return recurse(rootWidget);
  }, []);

  const renderRepeaterWidget = useCallback(
    (widget: WidgetTreeNode) => {
      const i = widget.instance;
      return renderWidgetTreeNode(props)(widget, i);
    },
    [props],
  );

  const expandRepeaterChildren = useCallback(() => {
    const numRenderableInstances = model.get('data') ? model.get('data').length : 0;
    return _.flatMap(
      _.range(numRenderableInstances).map(i =>
        children.flatMap(child => {
          let widget = adjustChildrenLayouts(child, i);
          return widget;
        }),
      ),
      n => n,
    );
  }, [model, children, adjustChildrenLayouts]);

  const getItemLayout = useCallback(
    (data, index) => {
      const isHorizontal = model.get('horizontal', false);
      const numColumns = isHorizontal ? 1 : model.get('numColumns', 1);
      const itemWidth = model.get('itemWidth') || listViewWidth;
      const itemHeight = model.get('itemHeight') || listViewHeight;

      const layout = {
        length: (isHorizontal ? itemWidth : itemHeight) * 1,
        offset: (isHorizontal ? itemWidth : itemHeight) * Math.floor(index / numColumns),
        index,
      };
      // logger.info(`getItemLayout ${JSON.stringify(layout)}`);
      return layout;
    },
    [listViewHeight, listViewWidth, model],
  );

  const renderItem = useCallback(
    ({item, index}) => {
      const isHorizontal = model.get('horizontal', false);
      const numColumns = isHorizontal ? 1 : model.get('numColumns', 1);
      const itemWidth = _.toNumber(model.get('itemWidth')) || listViewWidth / numColumns;
      const itemHeight = isHorizontal ? 'auto' : _.toNumber(model.get('itemHeight')) || listViewHeight;
      const {i} = item;
      return React.cloneElement(<_ApptileListItem />, {
        ...props,
        ...{item, isHorizontal, numColumns, itemWidth, itemHeight, children},
      });
    },
    [model, listViewWidth, listViewHeight, children, props],
  );

  const keyExtractor = useCallback(item => item?.i, []);

  const flatListRef = useRef<FlatList>();
  const numOfItems = model?.get('instances', 0);


  const onViewableItemsChanged = useCallback(
    ({viewableItems, changed}: any) => {
      if (isSliderMode && viewableItems && viewableItems.length > 0) {
        const itemThatBecameVisible = changed.find(it => it.isViewable)?.index ?? -1;
        let currOffset;
        if (itemThatBecameVisible < 0) {
          currOffset = _.get(viewableItems, '0.key');
        } else {
          currOffset = itemThatBecameVisible;
        }
        setCurrentOffset(currOffset);

        let eventName: string;
        if (namespace) {
          const namespaceSelector = namespace.getNamespace().join('.');
          eventName = `${pageKey}.${namespaceSelector}::${id}.currentItem`;
        } else {
          eventName = `${pageKey}.${id}.currentItem`;
        }

        // console.log("eventName in listView: ", eventName, viewableItems, changed);
        animProvider.triggerEvent(eventName, {changed: [{ isViewable: true, index: currOffset }]});
      }
    },
    [isSliderMode],
  );

  //viewabilityConfigCallbackPairs object will persist for the full lifetime of the component.
  const viewabilityConfigCallbackPairs = useRef([
    {
      onViewableItemsChanged
    }
  ]);

  const onViewableItemsChangedFlatlist = useRef([
    {
      onViewableItemsChanged: ({viewableItems, changed}) => {
        let eventName: string;
        if (namespace) {
          const namespaceSelector = namespace.getNamespace().join('.');
          eventName = `${pageKey}.${namespaceSelector}::${id}.currentItem`;
        } else {
          eventName = `${pageKey}.${id}.currentItem`;
        }

        // console.log("eventName in listView: ", eventName, viewableItems, changed);
        animProvider.triggerEvent(eventName, {viewableItems, changed});
      },
      viewabilityConfig: {itemVisiblePercentThreshold: 50}
    }
  ]).current;

  const sliderProps = isSliderMode
    ? {
        snapToInterval: isHorizontal ? itemWidth : itemHeight,
        decelerationRate: 'fast',
        bounces: false,
        pagingEnabled: true,
        disableIntervalMomentum: true,
        viewabilityConfigCallbackPairs: viewabilityConfigCallbackPairs.current,
        viewabilityConfig: {itemVisiblePercentThreshold: 50},
      }
    : {
        viewabilityConfigCallbackPairs: onViewableItemsChangedFlatlist
      };

  // lets you customize the handle exposed as ref
  useImperativeHandle(ref, () => ({
    slideTo: (index: number, animated?: boolean = true) => {
      if (parseInt(numOfItems, 10) > currentOffset && flatListRef?.current) {
        flatListRef?.current?.scrollToIndex({
          index: index,
          animated: animated,
        });
      }
    },
    slideNext: (animated: boolean = true) => {
      if (parseInt(numOfItems, 10) > currentOffset + 1 && flatListRef?.current) {
        flatListRef?.current?.scrollToIndex({
          index: currentOffset + 1,
          animated: animated,
        });
      }
    },
    slidePrev: (animated: boolean = true) => {
      if (currentOffset > 0 && flatListRef?.current) {
        flatListRef?.current?.scrollToIndex({
          index: currentOffset - 1,
          animated: animated,
        });
      }
    },
  }));

  return isRepeaterMode ? (
    <ApptileFlexbox
      {...props}
      ref={flatListRef}
      renderWidgetTreeNode={renderRepeaterWidget}
      children={expandRepeaterChildren()}
    />
  ) : (
    <FlatList
      disableVirtualization={Platform.select({web: false, ios: disableVirtualizationIos, android: disableVirtualizationAndroid})}
      key={containerId + '_' + numColumns}
      ref={flatListRef}
      style={[layoutStyles]}
      scrollEnabled={true}
      horizontal={isHorizontal}
      initialScrollIndex={scrollIndex}
      numColumns={isHorizontal ? 1 : numColumns}
      height={height}
      width={width}
      data={data}
      extraData={numItems}
      getItemLayout={getItemLayout}
      ListEmptyComponent={showPlaceholder ? <EmptyPlaceholder /> : <></>}
      // overScrollMode="never"
      // onViewableItemsChanged={onViewableItemsChanged}
      onLayout={onLayout}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      onEndReachedThreshold={1}
      onEndReached={onEndReached}
      removeClippedSubviews={true}
      maxToRenderPerBatch={maxToRenderPerBatch}
      initialNumToRender={initialNumToRender}
      windowSize={windowSize}
      scrollEventThrottle={scrollEventThrottle}
      {...sliderProps}
    />
  );
});

export default ApptileListView;
