import {makeBoolean} from 'apptile-core';
import _ from 'lodash';
import React from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {
  EventTriggerIdentifier,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import ListView from './ListView';
import {scrollToIndex, slideNext, slidePrev} from './listViewActions';
import {Platform} from 'react-native';

const ListViewWidgetConfig = {
  instances: '',
  data: [],
  horizontal: false,
  itemWidth: '',
  itemHeight: '',
  numColumns: 1,
  onEndReachedThreshold: 0.5,
  onEndReached: '',
  isSliderMode: false,
  isRepeaterMode: false,
  currentOffset: 0,
  scrollToIndex: 'action',
  slideNext: 'action',
  slidePrev: 'action',
  isLoading: false,
  numPlaceholderItems: 3,
  disableVirtualizationIos: false,
  disableVirtualizationAndroid: false,
  initialNumToRender: 10,
  windowSize: 11,
  scrollEventThrottle: 16,
  maxToRenderPerBatch: 10,
};

const ListViewWidget = React.forwardRef((props, ref) => {
  return <ListView key={props?.id + props?.instance} {...props} containerId={props?.id} ref={ref} />;
});

const pluginListing: PluginListingSettings = {
  labelPrefix: 'listview',
  type: 'widget',
  name: 'List View',
  description: 'Display a list of items.',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
  section: 'Layout',
  icon: 'list-view',
};

const propertySettings: PluginPropertySettings = {
  data: {
    getValue: (model, val, sel) => {
      // const instances: number = model[0]?.get('instances', 0);
      const instances2 = model?.instances;
      const data = _.range(instances2).map(i => {
        return {i};
      });
      return data;
    },
    forceEvaluation: true,
  },
  instances: {
    getValue: (model, val, sel) => {
      // const isLoading = !!model[0]?.get('isLoading');
      const isLoading2 = !!model?.isLoading;

      let val2 = val;
      if (isLoading2) {
        // val = model[0]?.get('numPlaceholderItems');
        val2 = model?.numPlaceholderItems;
      }

      return _.isNaN(val2 * 1) ? val2 : _.toNumber(val2);
    },
    updatesProps: ['scrollToIndex', 'data'],
  },
  itemWidth: {
    getValue: (model, val, sel) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
  itemHeight: {
    getValue: (model, val, sel) => {
      return _.isNaN(val * 1) ? val : _.toNumber(val);
    },
  },
  numColumns: {
    getValue: (model, val, sel) => {
      return _.isNaN(_.toNumber(val)) ? 1 : _.toInteger(val);
    },
  },
  onEndReachedThreshold: {
    getValue: (model, val, sel) => {
      return _.isNaN(_.toNumber(val)) ? 0.5 : _.toInteger(val);
    },
  },
  onEndReached: {
    type: EventTriggerIdentifier,
  },
  currentOffset: {
    getValue: (model, val, sel) => {
      return _.isNaN(val) ? val : _.toNumber(val);
    },
  },
  isLoading: {
    getValue: (model, val, _) => {
      return makeBoolean(val);
    },
    updatesProps: ['instances'],
  },
  numPlaceholderItems: {
    getValue: (model, val, sel) => {
      return _.isNaN(val) ? val : _.toNumber(val);
    },
    updatesProps: ['instances'],
  },
  scrollToIndex: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return scrollToIndex;
    },
    actionMetadata: {
      editableInputParams: {
        scrollIndex: '',
        animated: '{{false}}',
      },
    },
  },
  slideNext: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return slideNext;
    },
    actionMetadata: {
      editableInputParams: {
        animated: '{{false}}',
      },
    },
  },
  slidePrev: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return slidePrev;
    },
    actionMetadata: {
      editableInputParams: {
        animated: '{{false}}',
      },
    },
  },
};

// const onPluginUpdate = (
//   state: RootState,
//   pluginId: string,
//   pageKey: string,
//   instance: number | null,
//   userTriggered: boolean,
//   pageLoad: boolean,
// ): any => {
//   const pageId = state.stageModel.pageKeysToId.get(pageKey);
//   const children = selectPageConfigForPage(state, pageId)?.plugins.filter(p => {
//     return p.layout && p.layout.container === pluginId;
//   });

//   const childrenValues = children?.map(c => state.stageModel.getPluginModel(pageKey, c.id));
//   const listViewPlugin: any = state.stageModel.getPluginModel(pageKey, pluginId);
//   const instances = listViewPlugin?.get('instances');
//   const currData = listViewPlugin?.get('data');

//   // TODO: do not update if length does not change.
//   if (currData?.length === instances) return null;

//   const data = _.range(instances).map(i => {
//     const rowData: any = childrenValues?.map(c => c?.getIn([i, 'value'])).toJS() ?? {};
//     rowData['i'] = i;
//     return rowData;
//   });
//   return {
//     modelUpdates: [
//       {
//         selector: [pluginId, 'data'],
//         newValue: data,
//       },
//     ],
//   };
// };

const onPluginUpdate = undefined;

const editorsConfig: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'instances',
      props: {
        label: 'Instances',
        placeholder: '{{Query1.data.length}}',
      },
    },
    {
      type: 'codeInput',
      name: 'numColumns',
      props: {
        label: 'Number of Columns',
        placeholder: '1',
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'isRepeaterMode',
      props: {
        label: 'Repeater Mode\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'checkbox',
      name: 'disableVirtualizationIos',
      props: {
        label: 'Disable Virtualization (iOS)\n(⚠️ DEV ONLY)',
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'checkbox',
      name: 'disableVirtualizationAndroid',
      props: {
        label: 'Disable Virtualization (Android)\n(⚠️ DEV ONLY)',
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'numericInput',
      name: 'initialNumToRender',
      props: {
        label: 'Initial No. to Render\n(⚠️ DEV ONLY)',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'numericInput',
      name: 'windowSize',
      props: {
        label: 'Window Size\n(⚠️ DEV ONLY)',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'numericInput',
      name: 'maxToRenderPerBatch',
      props: {
        label: 'Max to Render per Batch\n(⚠️ DEV ONLY)',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'numericInput',
      name: 'scrollEventThrottle',
      props: {
        label: 'Scroll Event Throttle\n(⚠️ DEV ONLY)',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'checkbox',
      name: 'horizontal',
      props: {
        label: 'Horizontal',
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'numericInput',
      name: 'itemHeight',
      props: {
        label: 'Item Height',
        placeholder: '240',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode') || config.get('horizontal'),
    },
    {
      type: 'numericInput',
      name: 'itemWidth',
      props: {
        label: 'Item Width',
        placeholder: '300',
        noUnit: true,
      },
      hidden: config => config.get('isRepeaterMode') || !config.get('horizontal'),
    },
    {
      type: 'checkbox',
      name: 'isSliderMode',
      props: {
        label: 'Slider Mode',
      },
      hidden: config => config.get('isRepeaterMode'),
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      props: {
        label: 'Loading State',
      },
    },
    {
      type: 'codeInput',
      name: 'numPlaceholderItems',
      props: {
        label: 'Placeholder Items when loading',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
      hidden: config => !config.get('isRepeaterMode'),
    },
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
      hidden: config => config.get('isRepeaterMode'),
    },
  ],
  animations: defaultEditors.animations,
};

export default connectWidget('ListViewWidget', ListViewWidget, ListViewWidgetConfig, onPluginUpdate, editorsConfig, {
  propertySettings,
  pluginListing,
  docs,
  animations: {
    animatedValues: [
      'scrollY',
      'scrollX',
      'itemSize',
      'currentItem',
      'width',
      'height',
      'contentWidth',
      'contentHeight',
    ],
  },
});
