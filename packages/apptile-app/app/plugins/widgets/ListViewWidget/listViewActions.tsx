import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {isNil} from 'lodash';
import {WidgetRefRegistry} from 'apptile-core';

export interface IScrollToIndex {
  scrollIndex: number;
  animated: boolean;
}

export const scrollToIndex = (dispatch, config: PluginConfig, model, selector: Selector, params: IScrollToIndex) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, undefined);
  const {scrollIndex, animated} = params;
  if (isNil(scrollIndex)) return;
  if (!currentRef) return;
  currentRef?.slideTo(scrollIndex, animated);
};

export const slideNext = (dispatch, config: PluginConfig, model, selector: Selector, params: IScrollToIndex) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, undefined);
  const {animated} = params;
  if (!currentRef) return;
  currentRef?.slideNext(animated);
};

export const slidePrev = (dispatch, config: PluginConfig, model, selector: Selector, params: IScrollToIndex) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, undefined);
  const {animated} = params;
  if (!currentRef) return;
  currentRef?.slidePrev(animated);
};
