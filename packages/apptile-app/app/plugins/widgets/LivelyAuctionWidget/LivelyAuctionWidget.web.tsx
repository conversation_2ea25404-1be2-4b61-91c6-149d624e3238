import {createDeepEqualSelector, datasourceTypeModelSel, getPlatformStyles, Icon, LayoutRecord, modelUpdateAction, RootState, useTheme} from 'apptile-core';
import {useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import AuctionButtons from './shared/AuctionButtons';
import AuctionInfo from './shared/AuctionInfo';
import CustomBidBS from './shared/CustomBidBS';
import {allLayoutEditors, layoutConts, styleConfigEditors} from './configs';
import { createSelector } from 'reselect';
import { useSelector } from 'react-redux';
import CommentsView from './shared/CommentsView';
import PollsView from '../LivelyLiveSellingWidgetV2/shared/PollsView';

const USER_TYPE = {
  USER: 'USER',
  MODERATOR: 'MODERATOR',
  HOST: 'HOST',
};

const shopifyDSSel = (state: RootState) => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyModelSel = createDeepEqualSelector(
  createSelector(shopifyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      formatCurrency: dsModel?.get('formatCurrency'),
      isloggedInUser: dsModel?.get('isloggedInUser'),
    } as any;
  }),
  (s: any): any => s,
);

const LivelyAuctionWidget = ({props, ref}: {props: any; ref: any}) => {
  const {model, modelStyles, config, pageKey, id, modelUpdate, triggerEvent} = props;
  const layoutContStyle: any = {};
  const stylesContStyle: any = {};

  /* Hooks */
  const { formatCurrency, isloggedInUser } = useSelector<RootState, any>(shopifyModelSel);
  const {themeEvaluator} = useTheme();
  const bodyTypo = themeEvaluator('typography.body');

  /* Layout and Styles */
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  Object.keys(layoutConts).map((e: string) => {
    if (!layoutContStyle[e]) layoutContStyle[e] = {};
    allLayoutEditors.map(f => {
      const data = model?.get(`${e}-${f.name}`);
      if (data) {
        layoutContStyle[e][f.name] = data;
      }
    });
  });
  Object.keys(layoutConts).map((e: string) => {
    if (!stylesContStyle[e]) stylesContStyle[e] = {};
    styleConfigEditors.map((f: any) => {
      const data = modelStyles[`${e}-${f.name}`];
      if (data) {
        stylesContStyle[e][f.name] = data;
      }
      if (f.type == 'trblValuesEditor') {
        f.props.options.map((s: any) => {
          const data = modelStyles[`${e}-${s}`];
          if (data) {
            stylesContStyle[e][s] = data;
          }
        });
      }
    });
  });
  const authButtonLayout = new LayoutRecord(layoutContStyle?.authButton ?? {});
  const authButtonLayoutStyles = authButtonLayout ? authButtonLayout.getFlexProperties() : {flex: 1};
  const authButtonModelStyles = stylesContStyle?.authButton ?? {};
  const headingTypo = themeEvaluator('typography.heading');

  /* Model */
  const streamConfig = model?.get('streamConfig');
  const showHostTag = model?.get('showHostTag');
  const authButtonText = model?.get('authButtonText');
  const showPolls = model?.get('showPolls');

  /* States */
  const [showCustomBidBS, setShowCustomBidBS] = useState(false);
  const [comment, setComment] = useState<string>('');
  const [comments, setComments] = useState<any[]>([
    {
      userName: 'Lonnie Cain',
      text: 'test',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Hugo Hatfield',
      text: 'test2',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Joe Boone',
      text: 'test3',
      source: USER_TYPE.HOST,
    },
    {
      userName: 'Les Roy',
      text: 'test223',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Devon Woodward',
      text: 'test3234',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Florentino Santiago',
      text: 'test452',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Jannie Lucero',
      text: 'test31345',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Virgie Lutz',
      text: 'test342',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Terrell Leblanc',
      text: 'test353',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Myra Nichols',
      text: 'test2234',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Loraine Hardin',
      text: 'test3 test3 test3 test3 test3 test3 test3 test3 test3 test3 test3 test3',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Stacy Ross',
      text: 'test2',
      source: USER_TYPE.HOST,
    },
    {
      userName: 'Gloria Carr',
      text: 'testing https://skandi-apparel.apptile.io/dresses test3 https://skandi-apparel.apptile.io/products/floral-bell-sleeve-off-shoulder-blouse',
      source: USER_TYPE.HOST,
    },
  ]);
  const [activePoll, setActivePoll] = useState({
    poll_uuid: "lvp-dd598c9df1",
    question: "Which color is your favorite?",
    options: [
      {
        optionId: 1,
        text: 'Red',
        votes: 0,
        _id: '67caca91988fe0fa4a56f700',
      },
      {
        optionId: 2,
        text: 'Blue',
        votes: 0,
        _id: '67caca91988fe0fa4a56f701',
      },
      {
        optionId: 3,
        text: 'Green',
        votes: 0,
        _id: '67caca91988fe0fa4a56f702',
      },
      {
        optionId: 4,
        text: 'Yellow',
        votes: 0,
        _id: '67caca91988fe0fa4a56f703',
      },
    ],
    total_votes: 0,
  });

  const openLink = (urlFromComment: string) => {
    modelUpdate([
      {
        selector: 'urlFromComment',
        newValue: urlFromComment,
      },
    ]);
    const params: any = {
      url: urlFromComment,
      type:
        urlFromComment.indexOf('/collections/') > 0
          ? 'collection'
          : urlFromComment.indexOf('/products/') > 0
          ? 'product'
          : 'url',
      handle: '',
    };
    params.handle =
      params.type == 'url'
        ? ''
        : urlFromComment.indexOf('?') > 0
        ? urlFromComment.split(`/${params.type}s/`).pop()?.slice(0, urlFromComment.indexOf('?'))
        : urlFromComment.split(`/${params.type}s/`).pop();
    triggerEvent(
      params.type == 'collection'
        ? 'onCollectionLinkOpen'
        : params.type == 'product'
        ? 'onProductLinkOpen'
        : 'onLinkOpen',
      params,
    );
  };
  const onPollModalClose = () => {
    modelUpdate([
      {
        selector: 'showPolls',
        newValue: false,
      },
    ]);
  }

  useEffect(() => {
    if (streamConfig) {
      const updates = [
        {
          selector: 'allProducts',
          newValue: streamConfig.product_info,
        },
        {
          selector: 'currentProduct',
          newValue: streamConfig.product_info[0],
        },
        {
          selector: 'viewersCount',
          newValue: 1,
        },
        {
          selector: 'viewersMultiplier',
          newValue: 1,
        },
        {
          selector: 'auctionedProducts',
          newValue: streamConfig?.product_info?.map((p: any) => ({
            auction_winners_count: p?.auction_winner?.length,
            product_id: p?.product_id,
            store_product_id: p?.store_product_id,
          })),
        },
      ];
      modelUpdate(updates);
    }
  }, [streamConfig]);

  return (
    <View style={[layoutStyles, modelStyles]} ref={ref}>
      <CommentsView
        comment={comment}
        setComment={setComment}
        comments={comments}
        USER_TYPE={USER_TYPE}
        layoutContStyle={layoutContStyle}
        stylesContStyle={stylesContStyle}
        modelStyles={modelStyles}
        openLink={openLink}
        showHostTag={showHostTag}
        sendComment={() => {}}
      />
      {isloggedInUser ? (
        <>
          {showCustomBidBS && <CustomBidBS onClose={() => setShowCustomBidBS(false)} modelStyles={modelStyles} model={model} formatCurrency={formatCurrency} />}
          <AuctionInfo model={model} modelStyles={modelStyles} stylesContStyle={stylesContStyle} />
          <AuctionButtons model={model} modelStyles={modelStyles} triggerEvent={triggerEvent} onCustomBidOpen={() => setShowCustomBidBS(true)} formatCurrency={formatCurrency} stylesContStyle={stylesContStyle} />
        </>
      ) : (
        <Pressable onPress={() => triggerEvent('onAuthRequest')} style={[authButtonLayoutStyles, authButtonModelStyles, {alignItems: 'center', justifyContent: 'center'}]}>
          <Text style={{...headingTypo, fontSize: 14}}>{authButtonText}</Text>
        </Pressable>
      )}
      {showPolls && (
        <PollsView
          activePoll={activePoll}
          onClose={onPollModalClose}
          handleVote={() => {}}
        />
      )}
    </View>
  );
};

export default LivelyAuctionWidget;

const styles = StyleSheet.create({
  commentInput: {
    color: 'white',
    minHeight: 40,
    height: 45,
    borderWidth: 1,
    borderColor: '#fff',
    borderRadius: 100,
    padding: 8,
    marginBottom: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
    paddingHorizontal: 10,
  },
  sendButton: {
    position: 'absolute',
    minHeight: 40,
    height: 45,
    right: 0,
    paddingRight: 14,
    padding: 4,
    justifyContent: 'center',
  },
  authButton: {
    backgroundColor: '#fff',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  }
});
