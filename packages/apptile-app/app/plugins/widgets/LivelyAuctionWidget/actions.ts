import { EventTriggerIdentifier, modelUpdateAction, PluginConfig, PluginPropertySettings, Selector, TriggerActionIdentifier, WidgetRefRegistry } from "apptile-core";

export const propertySettings: PluginPropertySettings = {
  onLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onProductLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onCollectionLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onProductChange: {
    type: EventTriggerIdentifier,
  },
  onAuthRequest: {
    type: EventTriggerIdentifier
  },
  onBidPlaced: {
    type: EventTriggerIdentifier,
  },
  onBidClosed: {
    type: EventTriggerIdentifier,
  },
  hideHeart: {
    type: EventTriggerIdentifier,
  },
  onWinnerAnnouncement: {
    type: EventTriggerIdentifier,
  },
  placeBid: {
    type: TriggerActionIdentifier,
    getValue(model, renderdValue, selector) {
      return placeBid;
    },
    actionMetadata: {
      editableInputParams: {
        bid: '',
        userOptedVariantId: '',
      },
    },
  },
  startPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderdValue, selector) {
      return startPip;
    },
  },
  endPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderdValue, selector) {
      return endPip;
    },
  },
  sendReactions: {
    type: TriggerActionIdentifier,
    getValue(model, renderdValue, selector) {
      return sendReactions;
    },
    actionMetadata: {
      editableInputParams: {
        reactionUrl: '',
      },
    },
  },
  onReactionLimitReached: {
    type: EventTriggerIdentifier,
  },
}

export function placeBid(dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance) as any;
  logger.info(`[STREAM UPDATE] placing Bid`);
  currentRef?.current?.placeBid(params?.bid ?? '', params?.userOptedVariantId ?? '');
}

export function startPip(dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance) as any;
  logger.info('currentRef', JSON.stringify(currentRef?.current));
  logger.info(`[STREAMUPDATE] starting pip activity`);
  currentRef?.current?.startPip();
}

export function endPip(dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance) as any;
  logger.info(`[STREAMUPDATE] ending pip activity`);
  currentRef?.current?.endPip();
}

export function sendReactions(dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance) as any;
  logger.info(`[STREAMUPDATE] SENDING HEART`);
  currentRef?.current?.sendReactions(params?.reactionUrl ?? 'reactionUrl');
}
