import { containerLayoutEditors, defaultEditors, defaultStyleEditors, EventTriggerIdentifier, layoutEditors, PluginEditorsConfig, PluginListingSettings, TriggerActionIdentifier, WidgetStyleEditorOptions } from "apptile-core";

export const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelyAuctionWidget',
  type: 'widget',
  name: 'Lively Auction Widget',
  description: 'Load Lively Auction Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

enum ZegoStreamResourceMode {
  /** Default mode. The SDK will automatically select the streaming resource according to the cdnConfig parameters set by the player config and the ready-made background configuration. */
  Default = 0,
  /** Playing stream only from CDN. */
  OnlyCDN = 1,
  /** Playing stream only from L3. */
  OnlyL3 = 2,
  /** Playing stream only from RTC. */
  OnlyRTC = 3,
  /** CDN Plus mode. The SDK will automatically select the streaming resource according to the network condition. */
  CDNPlus = 4,
}

export type LivelyAuctionWidgetConfigType = {
  streamConfig: any;
  showHostTag: boolean;
  muteSpeaker: boolean;
  authButtonText: string;
  placeBid: string;
  onReactionLimitReached: string;
  isInPip: boolean;
  zegoStreamResourceMode: ZegoStreamResourceMode;
  activePoll: any;
  showPolls: boolean;
  hideHeart: string;
  viewersCount: number;
  viewersMultipler: number;
  commentOnlyWhenLoggedIn: boolean;
  isBannedUser: boolean;

  reactionsAnimation: string;
  playWinningAnimation: boolean;
  playBiddingWarAnimation: boolean;
  playFinalCountDownAnimation: boolean;
  gradientTextBaseColor: string;
  gradientBreakPoints: number;
  countDownHaptics: boolean;
  winningHaptics: boolean;

  authRequest: typeof EventTriggerIdentifier;
  onLinkOpen: typeof EventTriggerIdentifier;
  onProductLinkOpen: typeof EventTriggerIdentifier;
  onCollectionLinkOpen: typeof EventTriggerIdentifier;
  onBidPlaced: typeof EventTriggerIdentifier;
  onBidClosed: typeof EventTriggerIdentifier;
  onWinnerAnnouncement: typeof EventTriggerIdentifier;
  hideCard: typeof EventTriggerIdentifier;

  startPip: typeof TriggerActionIdentifier;
  endPip: typeof TriggerActionIdentifier;
  sendReactions: typeof TriggerActionIdentifier;
};

export const LivelyAuctionWidgetConfig: LivelyAuctionWidgetConfigType = {
  streamConfig: '',
  showHostTag: true,
  muteSpeaker: false,
  authButtonText: '',
  hideHeart: '',
  activePoll: '',
  showPolls: false,
  zegoStreamResourceMode: ZegoStreamResourceMode.Default,
  isInPip: false,
  viewersCount: 0,
  viewersMultipler: 0,
  commentOnlyWhenLoggedIn: false,
  isBannedUser: false,

  reactionsAnimation: 'single',
  playWinningAnimation: true,
  playBiddingWarAnimation: false,
  playFinalCountDownAnimation: false,
  gradientTextBaseColor: '#F8DF7A',
  gradientBreakPoints: 11,
  countDownHaptics: true,
  winningHaptics: true,

  authRequest: EventTriggerIdentifier,
  onLinkOpen: EventTriggerIdentifier,
  onProductLinkOpen: EventTriggerIdentifier,
  onCollectionLinkOpen: EventTriggerIdentifier,
  onBidPlaced: EventTriggerIdentifier,
  onBidClosed: EventTriggerIdentifier,
  onWinnerAnnouncement: EventTriggerIdentifier,
  onReactionLimitReached: EventTriggerIdentifier,
  hideCard: EventTriggerIdentifier,

  startPip: TriggerActionIdentifier,
  endPip: TriggerActionIdentifier,
  sendReactions: TriggerActionIdentifier,
  placeBid: TriggerActionIdentifier,
};

export const layoutConts: any = {
  comments: 'Comments',
  commentItem: 'Comment Item',
  hostCommentItem: 'Host Comment Item',
  commentInput: 'Comment Input',
  auctionInfo: 'Auction Info',
  auctionButtons: 'Auction Buttons',
  authButton: 'Auth Button'
}

export const styleConfigEditors = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#Hexcode'
    }
  },
  ...defaultStyleEditors
]

export const allLayoutEditors = [...layoutEditors, ...containerLayoutEditors]


export const LivelyAuctionWidgetStyleConfig: WidgetStyleEditorOptions = [
  ...Object.keys(layoutConts).map(
    (e: string) => {
      return [
        {
          type: 'editorSectionHeader',
          name: '',
          props: { label: layoutConts[e] }
        },
        ...styleConfigEditors.map(f => {
          if(f.type == 'trblValuesEditor') {
            return {
              ...f,
              props: {
                ...f.props,
                options: f.props.options.map(s => `${e}-${s}`),
              },
              name: `${e}-${f.name}`,
            }
          }
          return {
            ...f,
            name: `${e}-${f.name}`
          }
        })
      ]
    }
  )
  .flat(),
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Comments'
    }
  },
  {
    type: 'colorInput',
    name: 'commentsColor',
    props: {
      label: 'Comments Text Color'
    }
  },
  {
    type: 'typographyInput',
    name: 'commentsTypography',
    props: {
      label: 'Comments Typography'
    }
  },
  {
    type: 'colorInput',
    name: 'commentsHeadingColor',
    props: {
      label: 'Comments Heading Text Color',
    }
  },
  {
    type: 'typographyInput',
    name: 'commentsHeadingTypography',
    props: {
      label: 'Comments Heading Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsColor',
    props: {
      label: 'Host Comments Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsTypography',
    props: {
      label: 'Host Comments Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsHeadingColor',
    props: {
      label: 'Host Comments Heading Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsHeadingTypography',
    props: {
      label: 'Host Comments Heading Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'commentInputColor',
    props: {
      label: 'Comment Input Text Color',
    }
  },
  {
    type: 'typographyInput',
    name: 'commentInputTypography',
    props: {
      label: 'Comment Input Typography',
    }
  },

  /* Auction Buttons */
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Auction Buttons'
    },
  },
  {
    type: 'typographyInput',
    name: 'auctionButtonsTypography',
    props: {
      label: 'Typography'
    }
  },
  {
    type: 'colorInput',
    name: 'auctionButtonsBackground',
    props: {
      label: 'Auction Buttons Background',
    }
  },
  {
    type: 'trblValuesEditor',
    name: 'auctionButtonsBorderWidth',
    props: {
      label: 'Auction Buttons Border Width',
      options: ['auctionButtonsBorderWidthTop', 'auctionButtonsBorderWidthRight', 'auctionButtonsBorderWidthBottom', 'auctionButtonsBorderWidthLeft']
    }
  },
  {
    type: 'colorInput',
    name: 'auctionButtonsBorder',
    props: {
      label: 'Auction Buttons Border',
    }
  },
  {
    type: 'borderRadiusEditor',
    name: 'auctionButtonsBorderRadius',
    props: {
      label: 'Auction Buttons Border Radius',
      options: ['auctionButtonsBorderTopLeftRadius', 'auctionButtonsBorderTopRightRadius', 'auctionButtonsBorderBottomLeftRadius', 'auctionButtonsBorderBottomRightRadius']
    },
  },
  /* Auction Buttons End */

  /* Custom Bid Button */
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Custom Bid Button'
    },
  },
  {
    type: 'colorInput',
    name: 'customBidButtonText',
    props: {
      label: 'Custom Bid Button Text',
    }
  },
  {
    type: 'trblValuesEditor',
    name: 'customBidButtonPadding',
    props: {
      label: 'Custom Bid Button Padding',
      options: ['customBidButtonPaddingTop', 'customBidButtonPaddingRight', 'customBidButtonPaddingBottom', 'customBidButtonPaddingLeft']
    },
  },
  /* Custom Bid Button End */

  /* Bid Slider Button */
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Bid Slider Button'
    },
  },
  {
    type: 'colorInput',
    name: 'bidSliderButtonText',
    props: {
      label: 'Bid Slider Button Text',
    }
  },
  {
    type: 'colorInput',
    name: 'bidSliderWinningText',
    props: {
      label: 'Bid Slider Winning Text',
    }
  },
  {
    type: 'colorInput',
    name: 'bidSliderButtonBackground',
    props: {
      label: 'Bid Slider Button Background',
    }
  },
  {
    type: 'colorInput',
    name: 'bidSliderButtonWinningBackground',
    props: {
      label: 'Bid Slider Button Winning Background',
    }
  },
  {
    type: 'trblValuesEditor',
    name: 'bidSliderOuterPadding',
    props: {
      label: 'Bid Slider Outer Padding',
      options: ['bidSliderOuterPaddingTop', 'bidSliderOuterPaddingRight', 'bidSliderOuterPaddingBottom', 'bidSliderOuterPaddingLeft']
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'bidSliderPadding',
    props: {
      label: 'Bid Slider Padding',
      options: ['bidSliderPaddingTop', 'bidSliderPaddingRight', 'bidSliderPaddingBottom', 'bidSliderPaddingLeft']
    },
  },
  {
    type: 'colorInput',
    name: 'bidSliderBorder',
    props: {
      label: 'Bid Slider Border',
    }
  },
  {
    type: 'trblValuesEditor',
    name: 'bidSliderBorderWidth',
    props: {
      label: 'Bid Slider Border Width',
      options: ['bidSliderBorderTopWidth', 'bidSliderBorderRightWidth', 'bidSliderBorderBottomWidth', 'bidSliderBorderLeftWidth']
    }
  },

  
  /* Auciton Buttons End */

  /* Auction Timer */
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Auction Timer'
    }
  },
  {
    type: 'colorInput',
    name: 'auctionTimerBackground',
    props: {
      label: 'Background',
    }
  },
  {
    type: 'colorInput',
    name: 'extendedBiddingBackground',
    props: {
      label: 'Extended Bidding Background',
    }
  },
  {
    type: 'colorInput',
    name: 'auctionTimerText',
    props: {
      label: 'Text',
    }
  },
  {
    type: 'colorInput',
    name: 'extendedBiddingText',
    props: {
      label: 'Extended Bidding Text',
    }
  },
  {
    type: 'typographyInput',
    name: 'auctionTimerTypography',
    props: {
      label: 'Typography',
    },
  },
  /* Auction Timer End */

  /* Default Styles */
  ...defaultStyleEditors,
];

export const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'streamConfig',
      props: {
        label: 'Stream Config',
      },
    },
    {
      type: 'codeInput',
      name: 'userName',
      props: {
        label: 'User Name',
      },
    },
    {
      type: 'codeInput',
      name: 'userid',
      props: {
        label: 'User Id',
      },
    },
    {
      type: 'checkbox',
      name: 'showHostTag',
      props: {
        label: 'Show Host Tag',
        disableBinding: false,
      },
    },
    {
      type: 'radioGroup',
      name: 'zegoStreamResourceMode',
      props: {
        label: 'Zego Stream Resource Mode',
        options: [
          {text: 'Default', value: ZegoStreamResourceMode.Default},
          {text: 'OnlyCDN', value: ZegoStreamResourceMode.OnlyCDN},
          {text: 'OnlyRTC', value: ZegoStreamResourceMode.OnlyRTC},
          {text: 'OnlyL3', value: ZegoStreamResourceMode.OnlyL3},
          {text: 'CDNPlus', value: ZegoStreamResourceMode.CDNPlus},
        ],
        disableBinding: true,
      },
    },
    {
      type: 'codeInput',
      name: 'authButtonText',
      props: {
        label: 'Auth Button Text',
      }
    },
    {
      type: 'checkbox',
      name: 'showTermsAndConditions',
      props: {
        label: 'Show Terms & Conditions'
      }
    },
    {
      type: 'checkbox',
      name: 'commentOnlyWhenLoggedIn',
      props: {
        label: 'Comment Only When Logged In'
      }
    },
    {
      type: 'codeInput',
      name: 'isBannedUser',
      props: {
        label: 'Is Banned User'
      }
    }
  ],
  advanced: [
    {
      type: 'radioGroup',
      name: 'reactionsAnimation',
      props: {
        label: 'Reactions Animation',
        options: [
          {text: 'Single', value: 'single'},
          {text: 'Multiple', value: 'multiple'},
        ],
        disableBinding: true,
      },
    },
    {
      type: 'checkbox',
      name: 'playWinningAnimation',
      props: {
        label: 'Winning Animation'
      }
    },
    {
      type: 'checkbox',
      name: 'playBiddingWarAnimation',
      props: {
        label: 'Bidding War Animation'
      }
    },
    {
      type: 'checkbox',
      name: 'playFinalCountDownAnimation',
      props: {
        label: 'Final Count Down Animation'
      }
    },
    {
      type: 'colorInput',
      name: 'gradientTextBaseColor',
      props: {
        label: 'Gradient Text Base Color'
      }
    },
    {
      type: 'codeInput',
      name: 'gradientBreakPoints',
      props: {
        label: 'Gradient Break Points',
      }
    },
    {
      type: 'checkbox',
      name: 'countDownHaptics',
      props: {
        label: 'Count Down Haptics'
      }
    },
    {
      type: 'checkbox',
      name: 'winningHaptics',
      props: {
        label: 'Winning Haptics'
      }
    },
    ...Object.keys(layoutConts)
      .map((e: string) => {
        if(e == 'auctionInfo' || e == 'auctionButtons') return [];
        return [
          {
            type: 'editorSectionHeader',
            name: '',
            props: {
              label: layoutConts[e],
            },
          },
          ...allLayoutEditors.map(f => ({
            ...f,
            name: `${e}-${f.name}`,
          })),
        ];
      })
      .flat(),
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
}
