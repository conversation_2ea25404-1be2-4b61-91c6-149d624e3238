import {
  PluginListingSettings,
  PluginPropertySettings,
  WidgetStyleEditorOptions,
  connectWidget
} from 'apptile-core';
import _ from 'lodash';
import React from 'react';
import { Text, View } from 'react-native';
const LivelyLiveSellingWidgetConfig = {
  value: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelyAuctionWidget',
  type: 'widget',
  name: 'Lively Auction Widget',
  description: 'Load Lively Auction Widget in the app',
  layout: {},
  section: 'Display',
  icon: 'web-view',
};
export const LivelyWidgetStyleConfig: WidgetStyleEditorOptions = [];
const Lively = React.forwardRef((props, ref) => {
  return (
    <View>
      <Text>Placeholder</Text>
    </View>
  );
});
const widgetEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
  ],
  advanced: [],
  layout: [],
};
const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return _.isNumber(renderedValue) ? renderedValue : renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;
export default connectWidget(
  'LivelyAuctionWidget',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: LivelyWidgetStyleConfig,
    pluginListing,
    docs: {},
    themeProfileSel: [],
  },
);
