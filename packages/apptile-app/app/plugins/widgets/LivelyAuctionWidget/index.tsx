import React from "react";
import LivelyAuctionWidget from "./LivelyAuctionWidget";
import { connectWidget } from "apptile-core";
import { LivelyAuctionWidgetConfig, LivelyAuctionWidgetStyleConfig, pluginListing, widgetEditors } from "./configs";
import { propertySettings } from "./actions";
import docs from "./docs";

const emptyOnUpdate = null;

const LivelyAuctionWidgetMain = React.forwardRef((props, ref) => {
  return <LivelyAuctionWidget props={props} ref={ref} />
});

export default connectWidget(
  'LivelyAuctionWidget',
  LivelyAuctionWidgetMain,
  LivelyAuctionWidgetConfig,
  emptyOnUpdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: LivelyAuctionWidgetStyleConfig,
    pluginListing,
    docs,
    themeProfileSel: [],
  },
)