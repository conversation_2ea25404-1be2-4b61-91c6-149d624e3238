import {Icon} from 'apptile-core';
import _ from 'lodash';
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable, Animated, PanResponder, Image, Platform} from 'react-native';

interface IAuctionButtonsProps {
  model: any;
  modelStyles: any;
  triggerEvent: any;
  onCustomBidOpen: any;
  formatCurrency?: any;
  nextBid?: any;
  isWinning?: boolean;
  stylesContStyle?: any;
}

const AuctionButtons = ({model, modelStyles, triggerEvent, onCustomBidOpen, formatCurrency, nextBid = 55, isWinning, stylesContStyle}: IAuctionButtonsProps) => {
  let {
    auctionButtonsTypography,

    customBidButtonText,
    bidSliderButtonText,
    bidSliderWinningText,

    auctionButtonsBackground,
    bidSliderButtonBackground,
    bidSliderButtonWinningBackground,

    auctionButtonsBorder,
    bidSliderBorder,

    customBidButtonPadding,
    customBidButtonPaddingTop,
    customBidButtonPaddingRight,
    customBidButtonPaddingBottom,
    customBidButtonPaddingLeft,

    bidSliderOuterPadding,
    bidSliderOuterPaddingTop,
    bidSliderOuterPaddingRight,
    bidSliderOuterPaddingBottom,
    bidSliderOuterPaddingLeft,

    bidSliderPadding,
    bidSliderPaddingTop,
    bidSliderPaddingRight,
    bidSliderPaddingBottom,
    bidSliderPaddingLeft,

    auctionButtonsBorderWidth,
    auctionButtonsBorderTopWidth,
    auctionButtonsBorderBottomWidth,
    auctionButtonsBorderLeftWidth,
    auctionButtonsBorderRightWidth,

    bidSliderBorderWidth,
    bidSliderBorderTopWidth,
    bidSliderBorderBottomWidth,
    bidSliderBorderLeftWidth,
    bidSliderBorderRightWidth,

    auctionButtonsBorderRadius,
    auctionButtonsBorderTopLeftRadius,
    auctionButtonsBorderTopRightRadius,
    auctionButtonsBorderBottomLeftRadius,
    auctionButtonsBorderBottomRightRadius,
  } = modelStyles;

  customBidButtonPadding = {
    padding: customBidButtonPadding,
    paddingTop: customBidButtonPaddingTop,
    paddingRight: customBidButtonPaddingRight,
    paddingBottom: customBidButtonPaddingBottom,
    paddingLeft: customBidButtonPaddingLeft,
  };

  bidSliderOuterPadding = {
    padding: bidSliderOuterPadding,
    paddingTop: bidSliderOuterPaddingTop,
    paddingRight: bidSliderOuterPaddingRight,
    paddingBottom: bidSliderOuterPaddingBottom,
    paddingLeft: bidSliderOuterPaddingLeft,
  };

  bidSliderPadding = {
    padding: bidSliderPadding,
    paddingTop: bidSliderPaddingTop,
    paddingRight: bidSliderPaddingRight,
    paddingBottom: bidSliderPaddingBottom,
    paddingLeft: bidSliderPaddingLeft,
  };

  auctionButtonsBorderWidth = {
    borderWidth: auctionButtonsBorderWidth,
    borderTopWidth: auctionButtonsBorderTopWidth,
    borderBottomWidth: auctionButtonsBorderBottomWidth,
    borderLeftWidth: auctionButtonsBorderLeftWidth,
    borderRightWidth: auctionButtonsBorderRightWidth,
  };

  bidSliderBorderWidth = {
    borderWidth: bidSliderBorderWidth,
    borderTopWidth: bidSliderBorderTopWidth,
    borderBottomWidth: bidSliderBorderBottomWidth,
    borderLeftWidth: bidSliderBorderLeftWidth,
    borderRightWidth: bidSliderBorderRightWidth,
  };

  auctionButtonsBorderRadius = {
    borderRadius: auctionButtonsBorderRadius,
    borderTopLeftRadius: auctionButtonsBorderTopLeftRadius,
    borderTopRightRadius: auctionButtonsBorderTopRightRadius,
    borderBottomLeftRadius: auctionButtonsBorderBottomLeftRadius,
    borderBottomRightRadius: auctionButtonsBorderBottomRightRadius,
  };
  const auctionButtonsModelStyles = stylesContStyle?.auctionButtons ?? {};

  /* Animations */
  const [containerDims, setContainerDims] = useState({width: 0, height: 0});
  const [sliderDims, setSliderDims] = useState({width: 0, height: 0});

  const panValue = useRef(new Animated.Value(0)).current;
  const distanceAvailableToSlide = useRef(0);

  const winningSlideAnimation = useRef(new Animated.Value(0)).current;
  const winningOpacity = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        const clamped = Math.max(0, Math.min(gestureState.dx, distanceAvailableToSlide.current));
        panValue.setValue(clamped);
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dx >= distanceAvailableToSlide.current) {
          panValue.setValue(0);
          triggerEvent('onBidPlaced', {});
        }
        else Animated.spring(panValue, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      },
    }),
  ).current;

  const startWinningAnimation = () => {
    winningSlideAnimation.setValue(0);
    winningOpacity.setValue(0);
    Animated.timing(winningSlideAnimation, {
      toValue: sliderDims.width - containerDims.height,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  };

  const textOpacity = winningSlideAnimation.interpolate({
    inputRange: [0, sliderDims.width * 0.4, sliderDims.width * 0.6],
    outputRange: [0, 0.5, 1],
    extrapolate: 'clamp',
  });

  useEffect(() => {
    if(isWinning) startWinningAnimation();
  }, [isWinning, sliderDims]);

  useEffect(() => {
    const paddingUsed =
      bidSliderOuterPadding?.padding ?? bidSliderOuterPadding.paddingLeft + bidSliderOuterPadding.paddingRight;
    const borderUsed =
      auctionButtonsBorderWidth?.borderWidth ??
      auctionButtonsBorderWidth.auctionButtonsBorderLeftWidth +
        auctionButtonsBorderWidth.auctionButtonsBorderRightWidth;

    distanceAvailableToSlide.current = containerDims.width - sliderDims.width - 2 * (paddingUsed + borderUsed);
  }, [containerDims, sliderDims]);

  return (
    <>
      <View style={[styles.wrapper, auctionButtonsModelStyles]}>
        <Pressable
          style={[
            {
              ...customBidButtonPadding,
              ...auctionButtonsBorderRadius,
              ...auctionButtonsBorderWidth,
              backgroundColor: auctionButtonsBackground,
              borderColor: auctionButtonsBorder,
              flex: 0.3,
              justifyContent: 'center',
              alignItems: 'center',
            },
          ]}
          onPress={onCustomBidOpen}
        >
          <Text style={[auctionButtonsTypography, {color: customBidButtonText}]}>Custom</Text>
        </Pressable>
        <View
          style={[
            {
              ...bidSliderOuterPadding,
              ...auctionButtonsBorderRadius,
              ...auctionButtonsBorderWidth,
              backgroundColor: auctionButtonsBackground,
              borderColor: auctionButtonsBorder,
              flexDirection: 'row',
              position: 'relative',
              flex: 0.7,
              marginLeft: 10,
            },
          ]}
          onLayout={e =>
            setContainerDims({
              width: e.nativeEvent.layout.width,
              height: e.nativeEvent.layout.height,
            })
          }>
          {!isWinning ? (
            <>
              <Animated.View
                style={[
                  {
                    ...bidSliderPadding,
                    ...auctionButtonsBorderRadius,
                    ...bidSliderBorderWidth,
                    backgroundColor: bidSliderButtonBackground,
                    borderColor: bidSliderBorder,
                    width: '70%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transform: [{translateX: panValue}],
                  },
                ]}
                onLayout={e =>
                  setSliderDims({
                    width: e.nativeEvent.layout.width,
                    height: e.nativeEvent.layout.height,
                  })
                }
                {...panResponder.panHandlers}
                >
                <Text
                  style={[
                    auctionButtonsTypography,
                    Platform.OS == 'web' && {pointerEvents: 'none', userSelect: 'none'},
                    {
                      color: bidSliderButtonText,
                    },
                  ]}>
                  Bid {formatCurrency?.(nextBid)}
                </Text>
              </Animated.View>

              <View style={{flexDirection: 'row', marginLeft: 10, alignSelf: 'center'}}>
                {Array(3)
                  .fill(0)
                  .map((_, index) => {
                    const opacity = 0.33 + index * 0.33;
                    return (
                      <Icon
                        key={`chevron-${index}`}
                        name="chevron-thin-right"
                        iconType="Entypo"
                        size={20}
                        color={bidSliderButtonBackground || '#00FF00'}
                        style={{
                          marginLeft: index > 0 ? -10 : 0,
                          opacity: opacity,
                        }}
                      />
                    );
                  })}
              </View>
            </>
          ) : (
            <>
              <Animated.View
                style={{
                  ...bidSliderPadding,
                  ...auctionButtonsBorderRadius,
                  ...bidSliderBorderWidth,
                  flex: 1,
                  backgroundColor: bidSliderButtonWinningBackground,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderColor: bidSliderBorder,
                  position: 'relative',
                  overflow: 'hidden',
                }}
                onLayout={e =>
                  setSliderDims({
                    width: e.nativeEvent.layout.width,
                    height: e.nativeEvent.layout.height,
                  })
                }>
                <Animated.Text
                  style={[
                    auctionButtonsTypography,
                    {
                      color: bidSliderWinningText,
                      textAlign: 'center',
                      opacity: textOpacity,
                      transform: [
                        {
                          scale: textOpacity.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0.85, 1],
                          }),
                        },
                      ],
                    },
                  ]}>
                  You are in the lead
                </Animated.Text>
              </Animated.View>
              <Animated.View
                style={{
                  width: containerDims.height + containerDims.height * 0.3,
                  aspectRatio: 1,
                  position: 'absolute',
                  transform: [{translateX: winningSlideAnimation}, {translateY: -12}],
                }}>
                <Image
                  source={{
                    uri: 'https://cdn.apptile.io/47d00157-6b8a-45bf-aa63-d9b90d16c93e/fc1086f9-435c-456c-9eef-673633d61f7a/original-720x720.png',
                  }}
                  resizeMode="contain"
                  style={{width: '100%', height: '100%'}}
                />
              </Animated.View>
            </>
          )}
        </View>
      </View>
    </>
  );
};

export default AuctionButtons;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
