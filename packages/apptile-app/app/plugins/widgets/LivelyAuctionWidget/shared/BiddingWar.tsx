import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';
import {processColor} from 'react-native';
import {View, Text, StyleSheet, useWindowDimensions, Image, Animated, Easing, Platform} from 'react-native';

export type BiddingWarRef = {
  startAnimation: () => void;
  stopAnimation: () => void;
  hasPlayed: boolean;
}

type BiddingWarProps = {
  gradientTextBaseColor: string;
  gradientBreakPoints: number;
}

const BiddingWar = forwardRef<BiddingWarRef, BiddingWarProps>(({
  gradientTextBaseColor,
  gradientBreakPoints,
}, ref) => {

  const {width, height} = useWindowDimensions();
  const gradientColorsMap = generateGradientColors(gradientTextBaseColor, gradientBreakPoints);
  const [hasPlayed, setHasPlayed] = useState<boolean>(false);

  // Refs
  const titleAnim = useRef(new Animated.Value(0)).current;
  const subtitleAnim = useRef(new Animated.Value(0)).current;
  const flareAnim = useRef(new Animated.Value(0)).current;
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);

  // Transforms
  const textTransform = Platform.select({
    ios: [{skewX: '-6deg'}],
    android: []
  })
  const titleTransform = Platform.select({
    ios: [
      {translateX: titleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [-width, 0],
      })},
      {scale: titleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0.8, 1],
      })},
    ],
    android: [
      {translateX: titleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [-width, 0],
      })},
      {scale: titleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0.8, 1],
      })},
    ],
  })
  const subtitleTransform = Platform.select({
    ios: [
      {translateY: subtitleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [-50, 0],
      })},
    ],
    android: [
      {translateY: subtitleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [-50, 0],
      })},
    ],
  })

  // Handlers
  const stopAnimation = useCallback(() => {
    if(animationRef.current) {
      animationRef.current.stop();
      animationRef.current = null;
    }
    titleAnim.setValue(0);
    subtitleAnim.setValue(0);
    flareAnim.setValue(0);
  }, [])
  const startAnimation = useCallback(() => {
    stopAnimation();
    setHasPlayed(false);
    const entranceSequence = Animated.sequence([
      Animated.timing(titleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(subtitleAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.out(Easing.back(1.2)),
      }),
      Animated.timing(flareAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
        easing: Easing.out(Easing.back(1)),
      }),
      Animated.delay(1000)
    ])
    
    const exitSequence = Animated.sequence([
      Animated.timing(flareAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
        easing: Easing.in(Easing.back(1)),
      }),
      Animated.timing(subtitleAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
        easing: Easing.in(Easing.back(1.2)),
      }),
      Animated.timing(titleAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }),
    ])

    animationRef.current = Animated.sequence([
      entranceSequence,
      exitSequence
    ])

    animationRef.current.start(({finished}) => {
      if(finished) {
        setHasPlayed(true);
        stopAnimation();
      }
    })
  }, [stopAnimation, setHasPlayed])

  useImperativeHandle(ref, () => ({
    startAnimation,
    stopAnimation,
    hasPlayed
  }))
  useEffect(() => {
    return () => {
      stopAnimation();
    }
  }, [])

  if(hasPlayed || !animationRef.current) return null;

  return (
    <View style={[styles.wrapper, {width, height}]}>
      <View style={{width: '100%', alignItems: 'center', position: 'relative'}}>
        <Animated.Image
          source={require('@/root/app/assets/gold-flare.png')}
          style={[styles.flareImg, {
            top: 0,
            right: 0,
            opacity: flareAnim,
            transform: [
              {scale: flareAnim}
            ]
          }]}
          resizeMode="contain"
        />
        <Animated.Image
          source={require('@/root/app/assets/gold-flare.png')}
          style={[styles.flareImg, {
            bottom: 20,
            left: 0,
            opacity: flareAnim,
            transform: [
              {scale: flareAnim}
            ]
          }]}
          resizeMode="contain"
        />
        <Animated.View style={[
          styles.shadow,
          {
            transform: [...(titleTransform || []), ...(textTransform || [])],
            opacity: titleAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 1]
            })
          }
        ]}>
          {Platform.OS == 'ios' ? (
            <MaskedView
              maskElement={
                <Text
                  style={[
                  styles.font,
                {fontSize: 36, backgroundColor: 'transparent'}
              ]}
            >
              BIDDING WAR
            </Text>
          }>
            <LinearGradient colors={gradientColorsMap} start={{x: 0, y: 0}} end={{x: 1, y: 0}}>
              <Text style={[styles.font, {fontSize: 36, opacity: 0}]}>BIDDING WAR</Text>
            </LinearGradient>
          </MaskedView>
          ) : (
            <Text style={[styles.font, {fontSize: 36, color: gradientTextBaseColor}]}>BIDDING WAR</Text>
          )}
        </Animated.View>
        <Animated.View
          style={{
            ...styles.gavelCtn,
            transform: subtitleTransform,
            opacity: subtitleAnim,
          }}>
          <Image
            source={require('@/root/app/assets/auction-gavel.png')}
            style={[styles.gavelImg, {transform: [{scaleX: -1}]}]}
            resizeMode="contain"
          />
          <Image
            source={require('@/root/app/assets/auction-gavel.png')}
            style={[styles.gavelImg]}
            resizeMode="contain"
          />
        </Animated.View>
        <Animated.Text
          style={[
            styles.font,
            styles.shadow,
            {
              fontSize: 24,
              color: 'white',
              bottom: 20,
              opacity: subtitleAnim,
              transform: textTransform
            },
          ]}>
          STARTS NOW!
        </Animated.Text>
      </View>
    </View>
  );
});

export default BiddingWar;

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    zIndex: 1,
    justifyContent: 'center',
    bottom: 100,
  },
  font: {
    fontFamily: 'Public Sans',
    fontWeight: '900'
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {width: 2, height: 2},
    shadowOpacity: 0.75,
    shadowRadius: 5,
    elevation: 5,
  },
  flareImg: {
    width: 130,
    height: 130,
    position: 'absolute',
  },
  gavelImg: {
    width: 80,
    height: 80,
  },
  gavelCtn: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'absolute',
    paddingHorizontal: 30,
  }
});

function generateGradientColors(baseColor: string, count: number = 10): string[] {
  const processedColor = processColor(baseColor);
  if (processedColor === null || processedColor === undefined) {
    return [baseColor];
  }

  const r = (processedColor as any) >> 16 & 255;
  const g = (processedColor as any) >> 8 & 255;
  const b = (processedColor as any) & 255;

  const hsl = rgbToHsl(r, g, b);
  const colors: string[] = [];

  for (let i = 0; i < count; i++) {
    const h = hsl[0];
    const s = Math.max(0.7, Math.min(1, hsl[1] * (0.9 + Math.random() * 0.2)));
    const l = 0.6 + (Math.random() * 0.3);
    
    const rgb = hslToRgb(h, s, l);
    const hex = `#${((1 << 24) + (rgb[0] << 16) + (rgb[1] << 8) + rgb[2]).toString(16).slice(1).toUpperCase()}`;
    colors.push(hex);
  }

  return colors;
}

function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255, g /= 255, b /= 255;
  const max = Math.max(r, g, b), min = Math.min(r, g, b);
  let h = 0, s, l = (max + min) / 2;

  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [h, s, l];
}

function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}