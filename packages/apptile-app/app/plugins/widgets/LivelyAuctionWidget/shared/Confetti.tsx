import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Animated, Dimensions, Easing, I18nManager, Platform, StyleSheet } from 'react-native';
import type {CompositeAnimation} from 'react-native/Libraries/Animated/AnimatedImplementation'
import type {EndResult} from 'react-native/Libraries/Animated/animations/Animation'

type Props = {
  count: number;
  origin: {
    x: number;
    y: number;
  };
  explosionSpeed?: number;
  fallSpeed?: number;
  colors?: Array<string>;
  fadeOut?: boolean;
  autoStart?: boolean;
  autoStartDelay?: number;
  onAnimationStart?: () => void;
  onAnimationResume?: () => void;
  onAnimationStop?: () => void;
  onAnimationEnd?: () => void;
  testID?: string;
};

type Item = {
  leftDelta: number;
  topDelta: number;
  swingDelta: number;
  speedDelta: {
    rotateX: number;
    rotateY: number;
    rotateZ: number;
  };
  color: string;
  width: number;
  height: number;
  isRounded: boolean;
};

export const TOP_MIN = 0.7;
export const DEFAULT_COLORS: Array<string> = [
  '#eb0004',
  '#fef308',
  '#0674cb',
  '#a50308',
];
export const DEFAULT_EXPLOSION_SPEED = 350;
export const DEFAULT_FALL_SPEED = 3000;

const randomValue = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

const randomColor = (colors: Array<string>): string => {
  return colors[Math.round(randomValue(0, colors.length - 1))]
};

const Explosion = (props: Props) => {
  const {
    count,
    origin,
    explosionSpeed = DEFAULT_EXPLOSION_SPEED,
    fallSpeed = DEFAULT_FALL_SPEED,
    colors = DEFAULT_COLORS,
    fadeOut = false,
    autoStart = true,
    autoStartDelay = 0,
    onAnimationStart,
    onAnimationResume,
    onAnimationStop,
    onAnimationEnd,
  } = props;

  const animation = useRef(new Animated.Value(0)).current;
  const sequenceRef = useRef<CompositeAnimation | null>(null);

  const getItems = useCallback((currentColors: Array<string>, prevColors: Array<string>, currentItems: Array<Item>): Array<Item> => {
    const difference = currentItems.length < count ? count - currentItems.length : 0;

    const newItems = Array(difference).fill(0).map((): Item => ({
      leftDelta: randomValue(0, 1),
      topDelta: randomValue(TOP_MIN, 1),
      swingDelta: randomValue(0.2, 1),
      speedDelta: {
        rotateX: randomValue(0.3, 1),
        rotateY: randomValue(0.3, 1),
        rotateZ: randomValue(0.3, 1)
      },
      color: randomColor(currentColors),
      width: randomValue(8, 16),
      height: randomValue(6, 12),
      isRounded: Math.round(randomValue(0, 1)) === 1,
    }));

    return currentItems
      .slice(0, count)
      .concat(newItems)
      .map(item => ({
        ...item,
        color: prevColors !== currentColors ? randomColor(currentColors) : item.color
      }));
  }, [count, randomValue, TOP_MIN, randomColor]);

  const [items, setItems] = useState<Array<Item>>(() => getItems(colors, DEFAULT_COLORS, []));

  const start = useCallback((resume: boolean = false) => {
    if (resume) {
      onAnimationResume && onAnimationResume();
    } else {
      sequenceRef.current = Animated.sequence([
        Animated.timing(animation, { toValue: 0, duration: 0, useNativeDriver: true }),
        Animated.timing(animation, {
          toValue: 1,
          duration: explosionSpeed,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true
        }),
        Animated.timing(animation, {
          toValue: 2,
          duration: fallSpeed,
          easing: Easing.quad,
          useNativeDriver: true
        }),
      ]);
      onAnimationStart && onAnimationStart();
    }

    sequenceRef.current && sequenceRef.current.start(({ finished }: EndResult) => {
      if (finished) {
        setTimeout(() => {
          onAnimationEnd && onAnimationEnd();
        }, 2000)
      }
    });
  }, [animation, explosionSpeed, fallSpeed, onAnimationStart, onAnimationResume, onAnimationEnd]);

  const resume = useCallback(() => start(true), [start]);

  const stop = useCallback(() => {
    onAnimationStop && onAnimationStop();
    sequenceRef.current && sequenceRef.current.stop();
  }, [onAnimationStop]);

  useEffect(() => {
    if (autoStart) {
      const timer = setTimeout(start, autoStartDelay);
      return () => clearTimeout(timer);
    }
  }, [autoStart, autoStartDelay, start]);

  const prevColorsRef = useRef(colors);
  useEffect(() => {
    if (count !== items.length || colors !== prevColorsRef.current) {
      setItems(prevItems => getItems(colors, prevColorsRef.current, prevItems));
      prevColorsRef.current = colors;
    }
  }, [count, colors, items.length, getItems]);


  const { height, width } = Dimensions.get('window');
  const directionalityFactor = I18nManager.isRTL ? -1 : 1;

  return (
    <>
      {items.map((item: Item, index: number) => {
        const left = animation.interpolate({
          inputRange: [0, 1, 2],
          outputRange: [
            directionalityFactor * origin.x,
            directionalityFactor * item.leftDelta * width,
            directionalityFactor * item.leftDelta * width
          ]
        });
        const top = animation.interpolate({
          inputRange: [0, 1, 1 + item.topDelta, 2],
          outputRange: [-origin.y, -item.topDelta * height, 0, 0]
        });
        const rotateX = animation.interpolate({
          inputRange: [0, 2],
          outputRange: ['0deg', `${item.speedDelta.rotateX * 360 * 10}deg`]
        });
        const rotateY = animation.interpolate({
          inputRange: [0, 2],
          outputRange: ['0deg', `${item.speedDelta.rotateY * 360 * 5}deg`]
        });
        const rotateZ = animation.interpolate({
          inputRange: [0, 2],
          outputRange: ['0deg', `${item.speedDelta.rotateZ * 360 * 2}deg`]
        });
        const translateX = animation.interpolate({
          inputRange: [0, 0.4, 1.2, 2],
          outputRange: [0, -(item.swingDelta * 30), (item.swingDelta * 30), 0]
        });
        const opacity = animation.interpolate({
          inputRange: [0, 1, 1.8, 2],
          outputRange: [1, 1, 1, fadeOut ? 0 : 1]
        });

        const containerTransform = [{ translateX: left }, { translateY: top }];
        const transform: Animated.AnimatedInterpolation[] = [{ rotateX }, { rotateY }, { rotateZ }, { translateX }];

        if (Platform.OS === 'android') {
          transform.push({ perspective: 100 } as any);
        }

        const innerConfettiStyle = {
          width: item.width,
          height: item.height,
          backgroundColor: item.color,
          transform: transform,
          opacity: opacity,
        };

        return (
          <Animated.View
            pointerEvents="none"
            renderToHardwareTextureAndroid={true}
            style={[styles.confetti, { transform: containerTransform }]}
            key={index}
            testID={`confetti-${index + 1}`}
          >
            <Animated.View style={[item.isRounded && styles.rounded, innerConfettiStyle]} />
          </Animated.View>
        );
      })}
    </>
  );
};

const styles = StyleSheet.create({
  confetti: {
    position: 'absolute',
    left: 0,
    bottom: 0
  },
  rounded: {
    borderRadius: 100
  }
});

export default Explosion;