import React, { useEffect, useState } from 'react'
import { StyleSheet, TextInput, View, Pressable, Platform, ScrollView } from 'react-native'
import {BottomSheet, useTheme} from 'apptile-core'
import {Text} from 'react-native'
import { Portal } from '@gorhom/portal'
import AuctionInfo from './AuctionInfo'

interface CustomBidBSProps {
  onClose: () => void;
  modelStyles: any;
  model: any;
  nextBid?: number;
  highestBid?: number;
  formatCurrency?: any;
  isWinning?: boolean;
  triggerEvent?: (event: string, payload?: any) => void;
  biddingCap: number;
  timeLeft?: number;
}

const WINNING_PROBABILITY_TEXTS = [
  'Very high chance of winning',
  'Good chance of winning',
  'Medium chance of winning',
  'Oops! You need to bid higher than the next bid',
]
const MIN_ABSOLUTE_HIGH = 50;
const MIN_ABSOLUTE_MEDIUM = 20;

const CustomBidBS = ({onClose, modelStyles, model, nextBid = 55, highestBid = 45, formatCurrency, timeLeft, isWinning, triggerEvent, biddingCap}: CustomBidBSProps) => {

  const {themeEvaluator} = useTheme();
  const headingTypo = themeEvaluator('typography.heading');
  const bodyTypo = themeEvaluator('typography.body');

  const [bidOptions, setBidOptions] = useState<number[]>([]);
  const [selectedBid, setSelectedBid] = useState<number>();
  const [customBid, setCustomBid] = useState<string>('');

  const canBid = !isWinning && (selectedBid || Number(customBid)) > nextBid! && (selectedBid || Number(customBid)) <= biddingCap;

  const calculateWinningProbability = (bid: number) => {
    const absoluteDifference = bid - nextBid!;
    const percentageDifference = nextBid! > 0 ? (bid / nextBid!) - 1 : 0;
    
    if(bid > biddingCap) return `Oops! You can't bid more than ${formatCurrency?.(biddingCap)}`;
    if (absoluteDifference >= MIN_ABSOLUTE_HIGH || percentageDifference >= 1.0) return WINNING_PROBABILITY_TEXTS[0];
    if (absoluteDifference >= MIN_ABSOLUTE_MEDIUM || percentageDifference >= 0.5) return WINNING_PROBABILITY_TEXTS[1];
    if (absoluteDifference > 0) return WINNING_PROBABILITY_TEXTS[2];
    return WINNING_PROBABILITY_TEXTS[3];
  }

  useEffect(() => {
    const mediumChanceOption = Math.ceil(nextBid!) + 5;
    const goodChanceOption = nextBid! + MIN_ABSOLUTE_MEDIUM;
    const highChanceOption1 = nextBid! + MIN_ABSOLUTE_HIGH + 10;
    const highChanceOption2 = nextBid! + MIN_ABSOLUTE_HIGH + 50;
    
    setBidOptions([mediumChanceOption, goodChanceOption, highChanceOption1, highChanceOption2])
  }, [nextBid])

  return (
      <Portal hostName={'root'}>
        <BottomSheet
          snapPoint="0%"
          borderRadius={16}
          hideBackdrop={false}
          backdropColor="rgba(0, 0, 0, 0.9)"
          style={styles.bottomSheet}
          onBackdropPress={onClose}
          onCloseComplete={onClose}
        >
          <View style={styles.bottomSheetContent}>
            <AuctionInfo
              model={model}
              modelStyles={modelStyles}
              formatCurrency={formatCurrency}
              highestBid={highestBid}
              containerStyles={styles.auctionTimerContainer}
              timerStyles={styles.auctionTimerText}
              highlightedTextStyles={styles.auctionTimerHighlightedText}
              textStyles={styles.auctionTimerTextStyles}
              timeLeft={timeLeft}
            />

            <View style={styles.customBidTextContainer}>
              <View style={styles.customBidHeadingContainer}>
                <Text style={[headingTypo, styles.customBidText]}>Place a Custom Bid</Text>
                <Pressable onPress={onClose}>
                  <Text style={[bodyTypo, {color: 'black'}]}>Skip</Text>
                </Pressable>
              </View>
              <TextInput 
                value={customBid}
                onChangeText={(value) => {
                  if(selectedBid) setSelectedBid(0);
                  setCustomBid(value);
                }}
                style={styles.customBidTextInput}
                keyboardType='numeric'
                autoFocus={Platform.OS !== 'web'}
              />

              <ScrollView
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.customBidOptionsWrapper}
                keyboardShouldPersistTaps="handled"
              >
                {bidOptions.map((option, index) => (
                  <Pressable
                    key={index}
                    style={[
                      styles.customBidOptionContainer, 
                      selectedBid == option && {
                        backgroundColor: '#000'
                      }
                    ]}
                    onPress={() => {
                      if(customBid) setCustomBid('');
                      setSelectedBid(option)
                    }}
                  >
                    <Text style={[bodyTypo, styles.customBidOptionText, selectedBid == option && {
                      color: 'white'
                    }]}>{formatCurrency?.(option)}</Text>
                  </Pressable>
                ))}
              </ScrollView>

              <Pressable
                style={[
                  styles.customBidButton,
                  {backgroundColor: modelStyles.bidSliderButtonBackground},
                  {opacity: canBid ? 1 : 0.5},
                  isWinning && {backgroundColor: modelStyles.bidSliderButtonWinningBackground, borderColor: modelStyles.bidSliderWinningText, borderWidth: 1}
                ]} 
                onPress={() => {
                  onClose();
                  triggerEvent?.('onBidPlaced', {bid: selectedBid || Number(customBid)})
                }}
                disabled={!canBid}
              >
                <Text style={[headingTypo, {color: isWinning ? modelStyles.bidSliderWinningText ?? 'black' : 'black'}]}>
                  {isWinning ? 'You are leading' : `Bid ${selectedBid ? formatCurrency?.(selectedBid) : customBid ? formatCurrency?.(customBid) : ''}`}
                </Text>
              </Pressable>
              
              {(selectedBid || customBid) && !isWinning && (
                <Text style={[
                  bodyTypo,
                  {textAlign: 'center', marginTop: 4, color: '#969696'},
                  !canBid && {color: '#FF5050'},
                ]}>
                  {calculateWinningProbability(selectedBid ? selectedBid : Number(customBid))}
                </Text>
              )}
            </View>
          </View>
        </BottomSheet>
      </Portal>
    )
}

export default CustomBidBS

const styles = StyleSheet.create({
  bottomSheet: {
    backgroundColor: 'white',
  },
  bottomSheetContent: {
    backgroundColor: 'white',
    padding: 20
  },
  auctionTimerContainer: {
    borderWidth: 1,
    borderColor: '#DDDDDD',
    paddingHorizontal: 14,
    paddingVertical: 16,
    borderRadius: 6
  },
  auctionTimerText: {
    color: '#000000'
  },
  auctionTimerHighlightedText: {
    color: '#00FF26'
  },
  auctionTimerTextStyles: {
    color: '#969696'
  },
  customBidText: {
    fontSize: 16,
    color: '#3C3C3C'
  },
  customBidTextContainer: {
    marginTop: 40
  },
  customBidHeadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  customBidTextInput: {
    marginTop: 14,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    paddingHorizontal: 14,
    height: 40,
    borderRadius: 100
  },
  customBidOptionsWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  customBidOptionContainer: {
    borderRadius: 100,
    backgroundColor: '#FAFAFA',
    paddingHorizontal: 16,
    marginRight: 8,
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center'
  },
  customBidOptionText: {
    fontSize: 14,
    color: '#000000'
  },
  customBidButton: {
    marginTop: 18,
    height: 44,
    paddingHorizontal: 20,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center'
  }
})