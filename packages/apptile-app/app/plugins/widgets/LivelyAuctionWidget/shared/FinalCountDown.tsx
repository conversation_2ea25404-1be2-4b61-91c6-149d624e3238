import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, StyleSheet, useWindowDimensions, View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

interface FinalCountDownProps {
  endTimeStamp: number;
  serverSentTime: number;
}

const STROKE_WIDTH = 6;
const CIRCLE_RADIUS = 60;
const COUNTDOWN_SECONDS = 3;
const CIRCLE_CIRCUMFERENCE = 2 * Math.PI * CIRCLE_RADIUS;

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const FinalCountDown = ({ endTimeStamp, serverSentTime }: FinalCountDownProps) => {
  const { height, width } = useWindowDimensions();
  const [count, setCount] = useState<number | null>(null);
  const [visible, setVisible] = useState(false);

  const timeoutRef1 = useRef<any>(null);
  const timeoutRef2 = useRef<any>(null);
  const scale = useRef(new Animated.Value(1)).current;
  const circleProgress = useRef(new Animated.Value(0)).current;
  const strokeDashOffset = useRef(new Animated.Value(CIRCLE_CIRCUMFERENCE)).current;

  const resetAnimation = () => {
    setCount(null);
    setVisible(false);
    if(timeoutRef1.current) clearTimeout(timeoutRef1.current);
    if(timeoutRef2.current) clearTimeout(timeoutRef2.current);
  }

  useEffect(() => {
    resetAnimation();
    if(endTimeStamp > 0) {   
      const timeOffset = serverSentTime - Date.now();
      const tick = () => {
        const timeRemaining = endTimeStamp - (Date.now() + timeOffset);
        const secondsRemaining = Math.ceil(timeRemaining / 1000);
        if(secondsRemaining > COUNTDOWN_SECONDS) timeoutRef1.current = setTimeout(tick, 400);
        else if(secondsRemaining > 0) {
          setVisible(true);
          setCount(COUNTDOWN_SECONDS);
        }
      }
      tick();
    }
  }, [endTimeStamp])

  useEffect(() => {
    if(count == null || count <= 0) {
      return setVisible(false)
    };

    circleProgress.setValue(0);
    strokeDashOffset.setValue(CIRCLE_CIRCUMFERENCE);
    scale.setValue(0.5);

    const circleAnimation = Animated.timing(circleProgress, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
      easing: Easing.linear
    })

    const scaleAnimation = Animated.sequence([
      Animated.spring(scale, {
        toValue: 1.1,
        friction: 3,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        friction: 5,
        tension: 100,
        useNativeDriver: true
      })
    ])

    Animated.parallel([scaleAnimation, circleAnimation]).start();

    circleProgress.addListener(({ value }) => {
      strokeDashOffset.setValue(CIRCLE_CIRCUMFERENCE * (1 - value))
    })

    timeoutRef2.current = setTimeout(() => setCount(prev => prev! - 1), 1000);

    return () => {
      circleProgress.removeAllListeners();
    }
  }, [count])

  if(!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          height,
          width,
        },
      ]}
    >
      <View style={styles.outerContainer}>
        <Svg
          width="100%"
          height="100%"
          viewBox="0 0 100 100"
          style={{
            transform: [{ rotate: '-90deg' }],
            position: 'absolute',
          }}
        >
          <AnimatedCircle
            cx="50"
            cy="50"
            r="45"
            stroke="white"
            strokeWidth={STROKE_WIDTH}
            fill="transparent"
            strokeDasharray={CIRCLE_CIRCUMFERENCE}
            strokeDashoffset={strokeDashOffset}
            strokeLinecap="square"
          />
        </Svg>

        <Animated.Text
          style={[
            styles.text,
            {
              transform: [{ scale }],
            },
          ]}
        >
          {count}
        </Animated.Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    bottom: 100,
  },
  outerContainer: {
    width: CIRCLE_RADIUS * 2,
    height: CIRCLE_RADIUS * 2,
    borderRadius: CIRCLE_RADIUS,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 60,
    fontWeight: 'bold',
    color: 'white',
  },
});

export default FinalCountDown;