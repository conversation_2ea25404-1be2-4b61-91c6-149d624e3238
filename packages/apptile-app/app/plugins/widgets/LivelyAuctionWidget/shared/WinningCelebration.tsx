import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {View, StyleSheet, Animated, Image, Text, Easing, useWindowDimensions} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Explosion from './Confetti';
import { performHapticFeedback } from 'apptile-core';

export type WinningCelebrationRef = {
  startAnimation: () => void;
  stopAnimation: () => void;
}

interface WinningCelebrationProps {
  winnerMeta: any;
  formatCurrency: any;
  winningHaptics: boolean;
}

const WinningCelebration = forwardRef<WinningCelebrationRef, WinningCelebrationProps>(({winnerMeta, formatCurrency, winningHaptics}, ref) => {
  const {width, height} = useWindowDimensions();
  const [showConfetti, setshowConfetti] = useState(false);
  const [hasPlayed, setHasPlayed] = useState(false);

  /* Refs */
  const circle_rotation = useRef(new Animated.Value(0)).current;
  const circle_scale = useRef(new Animated.Value(0)).current;
  const crown_scale = useRef(new Animated.Value(0)).current;
  const crown_translateY = useRef(new Animated.Value(-100)).current;
  const stars_scale = useRef(new Animated.Value(0)).current;
  const info_scale = useRef(new Animated.Value(0)).current;
  const info_translateY = useRef(new Animated.Value(100)).current;
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);

  /* Handlers */
  const stopAnimation = useCallback(() => {
    if(animationRef.current) {
      animationRef.current.stop();
      animationRef.current = null;
    }
    circle_rotation.setValue(0);
    circle_scale.setValue(0);
    crown_scale.setValue(0);
    crown_translateY.setValue(-100);
    stars_scale.setValue(0);
    info_scale.setValue(0);
    info_translateY.setValue(100);
    setshowConfetti(false);
  }, [])
  const startAnimation = useCallback(() => {
    stopAnimation();
    setHasPlayed(false);
    const circleAnimations = Animated.parallel([
      Animated.timing(circle_rotation, {
        toValue: 720,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.bezier(0.42, 0, 0.58, 1),
      }),
      Animated.timing(circle_scale, {
        toValue: 1,
        delay: 200,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.quad)
      }),
    ]);
    const crownAnimations = Animated.parallel([
      Animated.spring(crown_scale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 40,
      }),
      Animated.spring(crown_translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 40,
      }),
    ]);
    const starsAnimation = Animated.spring(stars_scale, {
      toValue: 1,
      useNativeDriver: true,
      tension: 40,
    });
    const infoAnimations = Animated.parallel([
      Animated.spring(info_translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 40,
      }),
      Animated.spring(info_scale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 40,
      }),
    ]);

    const exitAnimations = Animated.sequence([
      Animated.delay(4000),
      Animated.parallel([
        Animated.timing(circle_scale, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(crown_scale, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(stars_scale, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(info_scale, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
    ]);

    animationRef.current = Animated.sequence([
      circleAnimations,
      crownAnimations,
      Animated.parallel([starsAnimation, infoAnimations]),
      exitAnimations,
    ]);

    const confettiTimeOut = setTimeout(() => {
      setshowConfetti(true)
      setTimeout(async () => {
        // if (winningHaptics) {
        for (let i = 0; i < 30; i++) {
          performHapticFeedback('tap');
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      // }
      }, 1000);
    }, 3000);


    animationRef.current?.start(({finished}) => {
      if(finished) {
        setHasPlayed(true);
        setshowConfetti(false);
        stopAnimation();
        clearTimeout(confettiTimeOut);
      }
    })

    return () => clearTimeout(confettiTimeOut);
  }, [stopAnimation])

  useImperativeHandle(ref, () => ({
    startAnimation,
    stopAnimation,
  }))

  useEffect(() => {
    return () => {
      stopAnimation();
    }
  }, [stopAnimation]);

  if(hasPlayed || !animationRef.current) return null;

  return (
    <View style={[styles.container, {width: '100%', height: '100%'}]}>
      {showConfetti && (
        <>
          <Explosion 
            count={150}
            fadeOut={true}
            origin={{ x: width/2, y: 0 }}
          />
        </>
      )}

      <View style={{position: 'relative', alignItems: 'center', justifyContent: 'center', top: -120, zIndex: 1}}>

        <Animated.View
          style={[
            styles.starsWrapper,
            {
              width: width * 0.8,
              height: height * 0.4,
              transform: [
                {translateY: -height * 0.225},
                {scale: stars_scale}
              ],
            },
          ]}>
          <Image
            source={require('@/root/app/assets/auction-stars.png')}
            resizeMode="contain"
            style={{width: '100%', height: '100%'}}
          />
        </Animated.View>

        <View style={styles.info}>
          <Animated.View 
            style={[
              styles.shadowContainer, 
              {
                transform: [
                  { rotateY: circle_rotation.interpolate({
                    inputRange: [0, 360],
                    outputRange: ['0deg', '360deg']
                  }) },
                  { scale: circle_scale }
                ]
              }
            ]}
          >
            <LinearGradient
              colors={['#FFFFFF', '#DCDCDC']}
              start={{x: 0.2, y: 0.2}}
              end={{x: 0.5, y: 0.5}}
              style={styles.initialContainer}
            >
              <Text style={styles.initialText}>{winnerMeta?.user?.userName?.[0]}</Text>
            </LinearGradient>

          </Animated.View>
          <Animated.View style={[{marginTop: 10, alignItems: 'center'}, {transform: [{translateY: info_translateY},{scale: info_scale}]}]}>
            <Text style={[{fontSize: 20, fontWeight: '700', color: 'white'}]}>{winnerMeta?.user?.userName}</Text>
            <Text style={[{fontSize: 12, fontWeight: '500', color: 'white', marginTop: 2}]}>won the auction for {formatCurrency(winnerMeta?.winningBid)}</Text>
          </Animated.View>
        </View>

        <Animated.View 
          style={[
            styles.crownWrapper,
            { 
              transform: [
                { scale: crown_scale },
                { translateY: crown_translateY }
              ]
            }
          ]}
        >
          <Image
            source={require('@/root/app/assets/auction-crown.png')}
            resizeMode="contain"
            style={{width: 200, height: 115}}
          />
        </Animated.View>

      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 1
  },
  info: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  shadowContainer: {
    shadowColor: 'rgba(0, 0, 0, 0.8)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.8,
    shadowRadius: 20,
    elevation: 24,
  },
  initialContainer: {
    width: 130,
    height: 130,
    borderRadius: 130,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#EBAE06',
    overflow: 'hidden'
  },
  initialText: {
    fontSize: 70,
    fontWeight: 'bold',
    marginTop: 10,
    color: 'black'
  },
  crownWrapper: {
    position: 'absolute',
    top: -57,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 99999,
  },
  starsWrapper: {
    position: 'absolute',
    top: '50%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  face: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
  },
  faceFront: {
    backgroundColor: 'white',
  },
  faceBack: {
    transform: [{ rotateY: '180deg' }],
  },
});

export default WinningCelebration;
