import {LivelyLiveStreaming} from 'lively-live-stream-rn';
import _ from 'lodash';
import React from 'react';
import {IApptileCartDetails} from '.';
import KeepAwake from 'react-native-keep-awake';

export interface ILivelyCartLineItem {
  quantity: number;
  variant_id: number;
  product_title: string;
  line_price: number;
  image: string;
}

export interface ILivelyCartDetails {
  items: ILivelyCartLineItem[];
  items_subtotal_price: number;
}

export interface LivelyControlProps {
  brandId: string;
  cartDetails: IApptileCartDetails;
  onCallback: () => void;
  isLoading: boolean;
  streamConfig: any;
  live_stream_data: any;
  userName: string;
  userId: string;
  showProducts: boolean;
  connectionState: number;
}

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

const getTransformedCartItems = (cartDetails: IApptileCartDetails): ILivelyCartDetails => {
  const currentCartLines = cartDetails?.lines;

  if (_.isEmpty(currentCartLines)) {
    return {
      items: [],
      items_subtotal_price: 0,
    };
  }

  const cartLineItems = _.map(currentCartLines, currentCartLine => {
    const lineItemPrice = _.get(currentCartLine, 'variant.salePrice')
      ? _.get(currentCartLine, 'variant.salePrice') * _.get(currentCartLine, 'quantity') * 100
      : 0;
    return {
      quantity: _.get(currentCartLine, 'quantity'),
      variant_id: _.get(currentCartLine, 'variant.id')
        ? resolveNumericVariantId(_.get(currentCartLine, 'variant.id'))
        : 0,
      product_title: _.get(currentCartLine, 'variant.product.title'),
      line_price: lineItemPrice,
      image: _.get(currentCartLine, 'variant.featuredImage'),
    };
  });

  return {
    items: _.filter(cartLineItems, cartLine => cartLine?.quantity > 0),
    items_subtotal_price: _.get(cartDetails, 'subtotalAmount') * 100,
  };
};

const LivelyControl: React.FC<LivelyControlProps> = props => {
  const {brandId, streamConfig, cartDetails, connectionState, isLoading, onCallback, userName, userId, showProducts} = props;
  console.log(`LivelyControl streamConfig`, streamConfig, showProducts, connectionState);
  return (
    <>
      {!!brandId && (
        <>
          <LivelyLiveStreaming
            config={{
              live_stream_data: streamConfig,
              callBacks: onCallback,
              cartDetails: getTransformedCartItems(cartDetails),
              showLoader: false,
              isLoading: false,
              user_name: userName || 'Anonymous',
              user_id: `${
                userId ? userId : `${Math.round(Math.random() * 100000)}${Math.round(Math.random() * 100000)}`
              }`,
              uiConfig: {
                showProduct: showProducts,
                style: {
                  commentContainer: {
                    backgroundColor: 'rgba(0,0,0,0.4)',
                  },
                },
              },
            }}
            livelyBrandId={brandId}
            key={streamConfig?._id + connectionState ?? 'broadcast2'}
          />
          <KeepAwake />
        </>
      )}
    </>
  );
};

export default LivelyControl;
