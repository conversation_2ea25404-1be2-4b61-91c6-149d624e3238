import React, {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import _ from 'lodash';

export interface LivelyControlProps {
  brandId: string;
  streamConfig: any;
  onCallback: any;
}

const LivelyControl: React.FC<LivelyControlProps> = props => {
  const {brandId, streamConfig, onCallback} = props;
  useEffect(()=>{
    onCallback('PRODUCT_CHANGE', streamConfig?.product_info?.[0]);
  }, [streamConfig]);
  if (_.isEmpty(brandId)) {
    return (
      <View>
        <Text>BrandId missing</Text>
      </View>
    );
  }
  return (
    <>
      <View>
        <Text>Not available in webview</Text>
      </View>
    </>
  );
};

export default LivelyControl;
