import {
  PluginPropertySettings,
  connectWidget,
  hapticEditors,
  mergeWithDefaultStyles,
  performHapticFeedback,
  usePlaceHolder,
  makeBoolean,
  EventTriggerIdentifier,
  PluginListingSettings,
  getPlatformStyles,
  WidgetStyleEditorOptions,
  getShadowStyle,
} from 'apptile-core';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text} from 'react-native';
import _ from 'lodash';
const LivelyLiveSellingWidgetConfig = {
  value: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelySellingWidget',
  type: 'widget',
  name: 'Lively Live Selling Widget',
  description: 'Load Lively Live Selling Widget in the app',
  layout: {},
  section: 'Display',
  icon: 'web-view',
};
export const LivelyWidgetStyleConfig: WidgetStyleEditorOptions = [];
const Lively = React.forwardRef((props, ref) => {
  return (
    <View>
      <Text>Placeholder</Text>
    </View>
  );
});
const widgetEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
  ],
  advanced: [],
  layout: [],
};
const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return _.isNumber(renderedValue) ? renderedValue : renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;
export default connectWidget(
  'LivelyLiveSellingWidget',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: LivelyWidgetStyleConfig,
    pluginListing,
    docs: {},
    themeProfileSel: [],
  },
);
