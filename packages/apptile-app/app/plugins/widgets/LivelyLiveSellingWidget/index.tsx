import {createDeepEqualSelector, modelUpdateAction} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {datasourceByIdModelSel, datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {View, Image, Text} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../../datasource/ShopifyV_22_10/actions/checkoutAction';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import LivelyLiveBroadcast from './LivelyLiveBroadcast';
import ZegoExpressEngine, {ZegoRoomState} from 'zego-express-engine-reactnative';

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

export const resolveVariantGid = (variantId: string | number): string => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return `${variantId}`;
  }
  return `${SHOPIFY_PV_GID_PREFIX}` + variantId;
};

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyLiveSellingWidgetConfigType = {
  value: string;
  streamConfig: any;
  userName: string;
  userId: string;
  showProducts: boolean;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
  streamEnded: boolean;
};

const LivelyLiveSellingWidgetConfig: LivelyLiveSellingWidgetConfigType = {
  value: '',
  streamConfig: '',
  userName: '',
  userId: '',
  showProducts: true,
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
  streamEnded: false,
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelySellingWidget',
  type: 'widget',
  name: 'Lively Live Selling Widget',
  description: 'Load Lively Live Selling Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = state => datasourceTypeModelSel(state, 'lively');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);

const makeShopifyModelSelector = (pluginId: string) => {
  const shopifyDSSel = state => datasourceByIdModelSel(state, pluginId);
  return shopifyDSSel;
};

const makeShopifyConfigSelector = (pluginId: string) => {
  const shopifyDSConfigSel = state => selectPluginConfig(state, null, pluginId);
  return shopifyDSConfigSel;
};

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate, pageKey, id} = props;
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const cartDetails = model.get('cartDetails');
  const streamConfig = model.get('streamConfig');
  const userName = model.get('userName');
  const userId = model.get('userId');
  const showProducts = model.get('showProducts');
  const currentProductRef = useRef(null);
  const allProductsRef = useRef<any[]>([]);

  const shopifyDatasourceId = 'shopify';

  const shopifyDsModelSel = makeShopifyModelSelector(shopifyDatasourceId);
  const shopifyDsConfigSel = makeShopifyConfigSelector(shopifyDatasourceId);
  const shopifyDsModel = useSelector(shopifyDsModelSel);
  const shopifyDsConfig = useSelector(shopifyDsConfigSel);

  const shopifyLineItemIncreaseAction = shopifyDsModel?.get('increaseCartLineItemQuantity');
  const shopifyLineItemDecreaseAction = shopifyDsModel?.get('decreaseCartLineItemQuantity');
  const isLoading = shopifyDsModel?.get('syncingCartStatus');
  const [connectionState, setConnectionState] = useState(ZegoRoomState.Connected);
  const dispatch = useDispatch();

  const increaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemIncreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const decreaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemDecreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const onCartUpdateHandler = (type: string, data: {quantity: number; id: string}) => {
    if (!data?.id) {
      console.error(`No variant Id available data ${JSON.stringify(data, null, 2)}`);
    }

    if (type === 'add') {
      //increase qty
      increaseLineItem(data?.id);
    } else if (type === 'update') {
      if (data?.quantity < 0) {
        // decrease qty
        decreaseLineItem(data?.id);
      } else {
        //increase qty
        increaseLineItem(data?.id);
      }
    }
  };

  const onCallback = (event, data: {url: string; id: string}) => {
    console.log(`onCallback`, event, data);
    switch (event) {
      case 'ADD_CART': {
        onCartUpdateHandler('add', data);
        break;
      }
      case 'CART_UPDATE': {
        onCartUpdateHandler('update', data);
        break;
      }
      case 'ON_CHECKOUT_CLICK': {
        triggerEvent('onCheckoutTap');
        break;
      }
      // case 'PRODUCT_CHANGE' : {
      //   dispatch(modelUpdateAction([{
      //     selector: [model.get('pageKey'), 'plugins', model.get('id'), 'currentProduct'],
      //     newValue: data ?? null,
      //   }], undefined, true));
      //   break;
      // }
    }
  };
  console.log(`streamConfig`, streamConfig);

  useEffect(() => {
    const backoffArray = [500, 1000, 3000, 5000, 5000, 6000];
    function safeGetZegoInstance(triesLeft: number): Promise<ZegoExpressEngine | null> {
      let zegoInstance: ZegoExpressEngine | null = null;
      try {
        zegoInstance = ZegoExpressEngine.instance();
      } catch (err) {
        logger.info('Could not get zegoengine instance. Waiting for it to be created');
        return new Promise(resolve => {
          if (triesLeft > 0) {
            setTimeout(() => {
              resolve(safeGetZegoInstance(triesLeft - 1));
            }, backoffArray[5 - triesLeft]);
          } else {
            resolve(zegoInstance);
          }
        });
      }
      return Promise.resolve(zegoInstance);
    }
    const handleProductAdd = (roomId, streamList) => {
      const payload = JSON.parse(streamList[0].extraInfo);
      const event = payload.event;
      if (event !== 'PRODUCT_CHANGE') {
        return;
      }
      let data = payload.data;
      let isExistingProduct = !!(allProductsRef.current ?? [])?.find(e => e.product_id == data.product_id);
      const updates = [];
      if (currentProductRef.current != data?.product_id)
        updates.push({
          selector: [pageKey, 'plugins', id, 'currentProduct'],
          newValue: data ?? null,
        });
      currentProductRef.current = data?.product_id;

      if (!isExistingProduct) {
        (allProductsRef.current ?? [])?.push(data);
        updates.push({
          selector: [pageKey, 'plugins', id, 'allProducts'],
          newValue: allProductsRef.current,
        });
        allProductsRef.current = [...allProductsRef.current];
      }

      dispatch(modelUpdateAction(updates));
    };
    const handleInfoUpdate = (roomId, streamList) => {
      const payload = JSON.parse(streamList[0].extraInfo);
      const event = payload.event;
      if (event === 'PRODUCT_CHANGE') {
        handleProductAdd(roomId, streamList);
      } else if (event === 'DELETE_PRODUCT') {
        let data = payload.data;
        console.log(data);
        const newProducts = (allProductsRef.current ?? [])?.filter(e => e.product_id != data.product_id);
        console.log('newProducts', newProducts, allProductsRef.current);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'allProducts'],
              newValue: newProducts,
            },
          ]),
        );
        allProductsRef.current = [...newProducts];
      }
    };
    const handleConnectionUpdate = (roomID, state, errorCode, extendedData) => {
      setConnectionState(state);
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'streamEnded'],
            newValue: state == 0,
          },
        ]),
      );
    };
    let zegoInstanceClosure: null | ZegoExpressEngine = null;
    if (streamConfig) {
      allProductsRef.current = streamConfig?.product_info;
      const updates = [
        {
          selector: [pageKey, 'plugins', id, 'currentProduct'],
          newValue: streamConfig.product_info[0] ?? null,
        },
      ];
      dispatch(modelUpdateAction(updates));
      safeGetZegoInstance(5).then(zegoInstance => {
        if (!zegoInstance) {
          console.error('Could not get zego instance');
        } else {
          zegoInstanceClosure = zegoInstance;
          zegoInstance.on('roomStreamExtraInfoUpdate', handleInfoUpdate);
          zegoInstance.on('roomStateUpdate', handleConnectionUpdate);
        }
      });
    }

    return () => {
      if (zegoInstanceClosure) {
        zegoInstanceClosure.off('roomStreamExtraInfoUpdate', handleInfoUpdate);
        zegoInstanceClosure.off('roomStateUpdate', handleConnectionUpdate);
      }
    };
  }, [streamConfig, pageKey, id]);

  if (streamConfig) {
    return (
      <View style={[layoutStyles, modelStyles]}>
        {connectionState == 0 ? (
          <View style={{height: '100%', width: '100%'}}>
            <View style={{position: 'absolute', height: '100%', width: '100%'}}>
              <Image
                source={{uri: streamConfig?.streaming_thumbnail}}
                resizeMode="cover"
                style={{height: '100%', width: '100%'}}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                backgroundColor: '#0006',
                zIndex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{fontWeight: '900', fontSize: 28, width: '100%', textAlign: 'center', color: '#fff'}}>
                The live stream has ended
              </Text>
              <Text style={{fontSize: 14, width: '100%', textAlign: 'center', color: '#fff'}}>
                Explore and purchase featured products
              </Text>
            </View>
          </View>
        ) : (
          <LivelyLiveBroadcast
            brandId={brandId}
            onCallback={onCallback}
            cartDetails={cartDetails}
            isLoading={isLoading}
            streamConfig={streamConfig}
            key={streamConfig?._id + connectionState ?? 'broadcast1' + connectionState}
            userName={userName}
            userId={userId}
            showProducts={showProducts}
            connectionState={connectionState}
          />
        )}
      </View>
    );
  } else {
    return <View />;
  }
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'streamConfig',
      props: {
        label: 'Stream Config',
      },
    },
    {
      type: 'codeInput',
      name: 'userName',
      props: {
        label: 'User Name',
      },
    },
    {
      type: 'codeInput',
      name: 'userid',
      props: {
        label: 'User Id',
      },
    },
    {
      type: 'checkbox',
      name: 'showProducts',
      props: {
        label: 'Show Products',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'LivelyLiveSellingWidget',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
    themeProfileSel: [],
  },
);
