import {createDeepEqualSelector, modelUpdateAction} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {datasourceByIdModelSel, datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../../datasource/ShopifyV_22_10/actions/checkoutAction';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import LivelyLiveBroadcast from './LivelyLiveBroadcast';

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

export const resolveVariantGid = (variantId: string | number): string => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return `${variantId}`;
  }
  return `${SHOPIFY_PV_GID_PREFIX}` + variantId;
};

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyLiveSellingWidgetConfigType = {
  value: string;
  streamConfig: any;
  userName: string;
  userId: string;
  showProducts: boolean;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
};

const LivelyLiveSellingWidgetConfig: LivelyLiveSellingWidgetConfigType = {
  value: '',
  streamConfig: '',
  userName: '',
  userId: '',
  showProducts: true,
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelySellingWidget',
  type: 'widget',
  name: 'Lively Live Selling Widget',
  description: 'Load Lively Live Selling Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = state => datasourceTypeModelSel(state, 'lively');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);

const makeShopifyModelSelector = (pluginId: string) => {
  const shopifyDSSel = state => datasourceByIdModelSel(state, pluginId);
  return shopifyDSSel;
};

const makeShopifyConfigSelector = (pluginId: string) => {
  const shopifyDSConfigSel = state => selectPluginConfig(state, null, pluginId);
  return shopifyDSConfigSel;
};

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate, pageKey, id} = props;
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const cartDetails = model.get('cartDetails');
  const streamConfig = model.get('streamConfig');
  const userName = model.get('userName');
  const userId = model.get('userId');
  const showProducts = model.get('showProducts');

  const shopifyDatasourceId = 'shopify';

  const shopifyDsModelSel = makeShopifyModelSelector(shopifyDatasourceId);
  const shopifyDsConfigSel = makeShopifyConfigSelector(shopifyDatasourceId);
  const shopifyDsModel = useSelector(shopifyDsModelSel);
  const shopifyDsConfig = useSelector(shopifyDsConfigSel);

  const shopifyLineItemIncreaseAction = shopifyDsModel?.get('increaseCartLineItemQuantity');
  const shopifyLineItemDecreaseAction = shopifyDsModel?.get('decreaseCartLineItemQuantity');
  const isLoading = shopifyDsModel?.get('syncingCartStatus');
  const dispatch = useDispatch();

  const increaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemIncreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const decreaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemDecreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const onCartUpdateHandler = (type: string, data: {quantity: number; id: string}) => {
    if (!data?.id) {
      console.error(`No variant Id available data ${JSON.stringify(data, null, 2)}`);
    }

    if (type === 'add') {
      //increase qty
      increaseLineItem(data?.id);
    } else if (type === 'update') {
      if (data?.quantity < 0) {
        // decrease qty
        decreaseLineItem(data?.id);
      } else {
        //increase qty
        increaseLineItem(data?.id);
      }
    }
  };

  const onCallback = (event, data: {url: string; id: string}) => {
    console.log(`onCallback`, event, data);
    switch (event) {
      case 'ADD_CART': {
        onCartUpdateHandler('add', data);
        break;
      }
      case 'CART_UPDATE': {
        onCartUpdateHandler('update', data);
        break;
      }
      case 'ON_CHECKOUT_CLICK': {
        triggerEvent('onCheckoutTap');
        break;
      }
      case 'PRODUCT_CHANGE': {
        dispatch(
          modelUpdateAction(
            [
              {
                selector: [model.get('pageKey'), 'plugins', model.get('id'), 'currentProduct'],
                newValue: data ?? null,
              },
            ],
            undefined,
            true,
          ),
        );
        break;
      }
    }
  };
  console.log(`streamConfig`, streamConfig);

  useEffect(() => {
    if (streamConfig) {
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'allProducts'],
            newValue: streamConfig.product_info,
          },
        ]),
      );
    }
  }, [streamConfig]);

  if (streamConfig) {
    return (
      <View style={[layoutStyles, modelStyles]}>
        <LivelyLiveBroadcast
          brandId={brandId}
          onCallback={onCallback}
          cartDetails={cartDetails}
          isLoading={isLoading}
          streamConfig={streamConfig}
          key={streamConfig?._id ?? 'broadcast1'}
          userName={userName}
          userId={userId}
          showProducts={showProducts}
        />
      </View>
    );
  } else {
    return (
      <View>
        <Text>No streamconfig</Text>
      </View>
    );
  }
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'streamConfig',
      props: {
        label: 'Stream Config',
      },
    },
    {
      type: 'codeInput',
      name: 'userName',
      props: {
        label: 'User Name',
      },
    },
    {
      type: 'codeInput',
      name: 'userid',
      props: {
        label: 'User Id',
      },
    },
    {
      type: 'checkbox',
      name: 'showProducts',
      props: {
        label: 'Show Products',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'LivelyLiveSellingWidget',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
    themeProfileSel: [],
  },
);