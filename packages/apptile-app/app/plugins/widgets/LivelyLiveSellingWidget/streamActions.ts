import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {WidgetRefRegistry} from 'apptile-core';

export function sendComment(dispatch, config: PluginConfig, model, selector: Selector, params: string) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  logger.info(`[STREAMUPDATE] updating stream info`);
  currentRef?.current?.sendComment(params?.comment ?? {});
}