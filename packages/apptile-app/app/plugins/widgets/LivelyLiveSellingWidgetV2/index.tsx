import {
  containerLayoutEditors,
  createDeepEqualSelector,
  getUniqueDeviceId,
  getOptimalImageSize,
  layoutEditors,
  LayoutRecord,
  LivelyApi,
  mergeWithDefaultStyles,
  selectAppConfig,
  modelUpdateAction,
  TriggerActionIdentifier,
  WidgetRefContext,
  WidgetStyleEditorOptions,
} from 'apptile-core';
import {datasourceTypeModelSel} from 'apptile-core';
import {debounce} from 'lodash';
import React, {useCallback, useContext, useEffect, useRef, useState} from 'react';
import {
  View,
  Image,
  Text,
  findNodeHandle,
  ScrollView,
  AppState,
  Platform,
  NativeModules,
  NativeEventEmitter,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget, navigateToRoot} from 'apptile-core';
import ZegoExpressEngine, {
  ZegoPlayerConfig,
  ZegoPlayerMediaEvent,
  ZegoPublisherState,
  ZegoRemoteDeviceState,
  ZegoRoomConfig,
  ZegoRoomState,
  ZegoScenario,
  ZegoStreamResourceMode,
  ZegoTextureView,
  ZegoUpdateType,
  ZegoUser,
  ZegoView,
} from 'zego-express-engine-reactnative';
import KeepAwake from 'react-native-keep-awake';
import {sendComment, startPip, endPip, sendReactions, sendAddToCartReactions} from './streamActions';
import docs from './docs';
import CommentsView from './shared/CommentsView';
import ReactionEmojis from './shared/ReactionEmojis';
import {LocalStorage as localStorage} from 'apptile-core';
import _ from 'lodash';
import PollsView from './shared/PollsView';
import AddToCartReactionCards from './shared/AddToCartReactionCards';

// For pip don't remove
const enableLivelyPIP = false;

let pipEventEmitter = {};
const {PIPModule} = NativeModules;

console.log('[LIVELY-WIDGET] 🔧 Initializing Lively Widget');
console.log('[LIVELY-WIDGET] 🔧 Platform:', Platform.OS);
console.log('[LIVELY-WIDGET] 🔧 PIP Enabled:', enableLivelyPIP);

if (enableLivelyPIP && Platform.OS === 'android') {
  console.log('[PIP-ANDROID] 🔧 Initializing PIPModule event emitter');
  pipEventEmitter = new NativeEventEmitter(PIPModule);
  console.log('[PIP-ANDROID] ✅ PIPModule event emitter initialized');
}

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyLiveSellingWidgetConfigType = {
  value: string;
  streamConfig: any;
  userName: string;
  userId: string;
  showComments: boolean;
  sendComment: typeof TriggerActionIdentifier;
  startPip: typeof TriggerActionIdentifier;
  endPip: typeof TriggerActionIdentifier;
  sendReactions: typeof TriggerActionIdentifier;
  sendAddToCartReactions: typeof TriggerActionIdentifier;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
  onCommentSent: string;
  onCommentError: string;
  onLinkOpen: string;
  onProductLinkOpen: string;
  onCollectionLinkOpen: string;
  hideHeart: string;
  hideCard: string;
  viewersCount: string;
  viewersMultipler: number;
  muteSpeaker: boolean;
  sendingComment: boolean;
  reactionAnnoucement: string;
  cartAnnoucement: string;
  showHostTag: boolean;
  onReactionLimitReached: string;
  onRespondToComment: string;
  isInPip: boolean;
  zegoStreamResourceMode: ZegoStreamResourceMode;
  activePoll: any;
  showPolls: boolean;
  isBannedUser: boolean;
  reactionsAnimation: string;
};

const LivelyLiveSellingWidgetConfig: LivelyLiveSellingWidgetConfigType = {
  value: '',
  streamConfig: '',
  userName: '',
  userId: '',
  showComments: true,
  sendComment: TriggerActionIdentifier,
  startPip: TriggerActionIdentifier,
  endPip: TriggerActionIdentifier,
  sendReactions: TriggerActionIdentifier,
  sendAddToCartReactions: TriggerActionIdentifier,
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
  onCommentSent: '',
  onCommentError: '',
  onLinkOpen: '',
  onProductLinkOpen: '',
  onCollectionLinkOpen: '',
  hideHeart: '',
  hideCard: '',
  viewersCount: '',
  viewersMultipler: 0,
  muteSpeaker: false,
  sendingComment: false,
  reactionAnnoucement: '',
  cartAnnoucement: '',
  showHostTag: true,
  onReactionLimitReached: '',
  onRespondToComment: '',
  isInPip: false,
  zegoStreamResourceMode: ZegoStreamResourceMode.Default,
  activePoll: '',
  showPolls: false,
  isBannedUser: false,
  reactionsAnimation: 'single',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelySellingWidgetV2',
  type: 'widget',
  name: 'Lively Live Selling Widget V2',
  description: 'Load Lively Live Selling Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = state => datasourceTypeModelSel(state, 'lively');
const apptileDSSel = state => datasourceTypeModelSel(state, 'ApptileGlobal');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);
const apptileModelSel = createDeepEqualSelector(
  createSelector(apptileDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandLogoAssetId: dsModel?.get('brandLogoAssetId'),
    };
  }),
  s => s,
);

const allLayoutEditors = [...layoutEditors, ...containerLayoutEditors];

export const layoutConts = {
  comments: 'Comments',
  commentItem: 'Comment Item',
  hostCommentItem: 'Host Comment Item',
};

const styleConfigEditors = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  ...defaultStyleEditors,
];

const LivelyLiveSellingWidgetStyleConfig: WidgetStyleEditorOptions = [
  ...Object.keys(layoutConts)
    .map((e: string) => {
      return [
        {
          type: 'editorSectionHeader',
          name: '',
          props: {
            label: layoutConts[e],
          },
        },
        ...styleConfigEditors.map(f => {
          if (f.type == 'trblValuesEditor') {
            return {
              ...f,
              props: {
                ...f.props,
                options: f.props.options.map(s => `${e}-${s}`),
              },
              name: `${e}-${f.name}`,
            };
          }
          return {
            ...f,
            name: `${e}-${f.name}`,
          };
        }),
      ];
    })
    .flat(),
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Comments Text',
    },
  },
  {
    type: 'colorInput',
    name: 'commentsColor',
    props: {
      label: 'Comments Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'commentsTypography',
    props: {
      label: 'Comments Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'commentsHeadingColor',
    props: {
      label: 'Comments Heading Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'commentsHeadingTypography',
    props: {
      label: 'Comments Heading Typography',
    },
  },
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Host Comments Text',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsColor',
    props: {
      label: 'Host Comments Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsTypography',
    props: {
      label: 'Host Comments Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsHeadingColor',
    props: {
      label: 'Host Comments Heading Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsHeadingTypography',
    props: {
      label: 'Host Comments Heading Typography',
    },
  },
];

const Lively = React.forwardRef((props, ref) => {
  console.log('[LIVELY-WIDGET] 🔧 Component initializing');

  const {modelStyles, config, model, triggerEvent, modelUpdate, pageKey, id, instance} = props;
  const layout = config.get('layout');
  const layoutContStyle = {};
  Object.keys(layoutConts).map((e: string) => {
    if (!layoutContStyle[e]) layoutContStyle[e] = {};
    allLayoutEditors.map(f => {
      const data = model?.get(`${e}-${f.name}`);
      if (data) {
        layoutContStyle[e][f.name] = data;
      }
    });
  });
  const stylesContStyle = {};
  Object.keys(layoutConts).map((e: string) => {
    if (!stylesContStyle[e]) stylesContStyle[e] = {};
    styleConfigEditors.map(f => {
      const data = modelStyles[`${e}-${f.name}`];
      if (data) {
        stylesContStyle[e][f.name] = data;
      }
      if (f.type == 'trblValuesEditor') {
        f.props.options.map(s => {
          const data = modelStyles[`${e}-${s}`];
          if (data) {
            stylesContStyle[e][s] = data;
          }
        });
      }
    });
  });
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const commentsLayout = new LayoutRecord(layoutContStyle.comments ?? {});
  const commentsLayoutStyles = commentsLayout ? commentsLayout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const {brandLogoAssetId} = useSelector(apptileModelSel);
  const appConfig = useSelector(selectAppConfig);
  const restartKey = model.get('restartKey');
  const streamConfig = model.get('streamConfig');
  const showComments = model.get('showComments');
  const showPolls = model.get('showPolls');
  const showHostTag = model.get('showHostTag');
  const muteSpeaker = model.get('muteSpeaker');
  const userName = model.get('userName')
    ? model.get('userName').startsWith('undefined')
      ? 'Anonymous'
      : model.get('userName')
    : 'Anonymous';
  const zegoStreamResourceMode =
    parseInt(model?.get('zegoStreamResourceMode', ZegoStreamResourceMode.Default)) || ZegoStreamResourceMode.Default;
  const currentProductRef = useRef(null);
  const allProductsRef = useRef<any[]>([]);
  const zgViewRef = useRef(null);
  const streamConfigRef = useRef({});
  const networkQuality = useRef({up: 0, down: 0});
  const emojiRef = useRef<any>(null);
  const cardRef = useRef<any>(null);
  const mutedUsersRef = useRef<any>(null);
  let isBannedUser = model.get('isBannedUser');
  if(!isBannedUser && isBannedUser !== false) isBannedUser = false;
  let reactionsAnimation = model.get('reactionsAnimation');
  if(!reactionsAnimation && reactionsAnimation !== 'single') reactionsAnimation = 'single';

  console.log('[LIVELY-WIDGET] 🔧 Component config:', {
    brandId,
    userName,
    showComments,
    showPolls,
    muteSpeaker,
    zegoStreamResourceMode,
    streamConfigExists: !!streamConfig,
  });

  const triggerEmoji = (reactionUrl: string, userName: string) => {
    console.log('[LIVELY-WIDGET] 🎭 Triggering emoji reaction:', {reactionUrl, userName});
    try {
      emojiRef.current?.triggerAddEmoji(reactionUrl, userName);
      console.log('[LIVELY-WIDGET] ✅ Emoji reaction triggered successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error triggering emoji reaction:', error);
    }
  };

  const triggerAddCard = (productTitle: string, userName: string) => {
    console.log('[LIVELY-WIDGET] 🛒 Triggering add card:', {productTitle, userName});
    try {
      cardRef.current?.triggerAddCard(productTitle, userName);
      console.log('[LIVELY-WIDGET] ✅ Add card triggered successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error triggering add card:', error);
    }
  };

  const generateRandomString = length => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters[randomIndex];
    }

    console.log('[LIVELY-WIDGET] 🎲 Generated random string:', randomString);
    return randomString;
  };

  const getDeviceImage = useCallback(
    (assetId: string) => {
      console.log('[LIVELY-WIDGET] 📷 Getting device image for asset:', assetId);
      try {
        const imageRecord = appConfig.getImageId(assetId);

        const getOptimalImage = (layoutSize: string) => {
          console.log('[LIVELY-WIDGET] 📷 Getting optimal image size:', layoutSize);
          const result = getOptimalImageSize(imageRecord, layoutSize);
          console.log('[LIVELY-WIDGET] 📷 Optimal image result:', result);
          return result;
        };
        return {imageRecord, getOptimalImage};
      } catch (error) {
        console.error('[LIVELY-WIDGET] ❌ Error getting device image:', error);
        return {imageRecord: null, getOptimalImage: () => null};
      }
    },
    [appConfig],
  );

  const USER_TYPE = {
    USER: 'USER',
    MODERATOR: 'MODERATOR',
    HOST: 'HOST',
  };
  const appID = 1082395432;
  const userId = generateRandomString(5);
  const [connectionState, setConnectionState] = useState(ZegoRoomState.Connecting);
  const [publisherState, setPublisherState] = useState(ZegoPublisherState.NoPublish);
  const [hostMictState, setHostMicState] = useState(ZegoRemoteDeviceState.Open);
  const [hostCameraState, setHostCameraState] = useState(ZegoRemoteDeviceState.Open);
  const [viewersCount, setViewersCount] = useState(ZegoRemoteDeviceState.Open);
  const [viewersMultipler, setViewersMultipler] = useState(ZegoRemoteDeviceState.Open);
  const [comments, setComments] = useState<any[]>([]);
  const [activePoll, setActivePoll] = useState({});
  const [votedOptionId, setVotedOptionId] = useState<any>();
  const companyNameRef = useRef('HOST');

  console.log('[LIVELY-WIDGET] 🔧 State initialized:', {
    appID,
    userId,
    connectionState,
    publisherState,
    hostMictState,
    hostCameraState,
    viewersCount,
    commentsCount: comments.length,
  });

  const hideHeart = debounce(() => {
    console.log('[LIVELY-WIDGET] 💖 Hiding heart reaction');
    try {
      triggerEvent('hideHeart');
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'reactionAnnoucement'],
            newValue: '',
          },
        ]),
      );
      console.log('[LIVELY-WIDGET] ✅ Heart hidden successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error hiding heart:', error);
    }
  }, 4000);

  const hideCard = debounce(() => {
    console.log('[LIVELY-WIDGET] 🃏 Hiding card reaction');
    try {
      triggerEvent('hideCard');
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'cartAnnoucement'],
            newValue: '',
          },
        ]),
      );
      console.log('[LIVELY-WIDGET] ✅ Card hidden successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error hiding card:', error);
    }
  }, 4000);

  const dispatch = useDispatch();

  const handleProductAdd = (roomId: string, streamList: any[]) => {
    console.log('[LIVELY-WIDGET] 🛍️ handleProductAdd received:', {roomId, streamListLength: streamList.length});
    try {
      const payload = JSON.parse(streamList[0].extraInfo);
      console.log('[LIVELY-WIDGET] 🛍️ handleProductAdd payload:', payload);

      const event = payload.event;
      if (event !== 'PRODUCT_CHANGE') {
        console.log('[LIVELY-WIDGET] 🛍️ Event is not PRODUCT_CHANGE, ignoring');
        return;
      }

      let data = payload.data;
      let isExistingProduct = !!(allProductsRef.current ?? [])?.find(e => e.product_id == data.product_id);
      console.log('[LIVELY-WIDGET] 🛍️ Product analysis:', {
        productId: data.product_id,
        isExistingProduct,
        currentProductsCount: allProductsRef.current?.length || 0,
      });

      const updates = [];
      if (currentProductRef.current != data?.product_id) {
        updates.push({
          selector: [pageKey, 'plugins', id, 'currentProduct'],
          newValue: data ?? null,
        });
        console.log('[LIVELY-WIDGET] 🛍️ Current product updated:', data?.product_id);
      }
      currentProductRef.current = data?.product_id;

      if (!isExistingProduct) {
        (allProductsRef.current ?? [])?.push(data);
        updates.push({
          selector: [pageKey, 'plugins', id, 'allProducts'],
          newValue: allProductsRef.current,
        });
        allProductsRef.current = [...allProductsRef.current];
        console.log('[LIVELY-WIDGET] 🛍️ New product added to list');
      }

      if (updates.length) {
        dispatch(modelUpdateAction(updates));
        console.log('[LIVELY-WIDGET] ✅ Product updates dispatched successfully');
      }
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error in handleProductAdd:', error);
    }
  };

  const handleInfoUpdate = async (roomId: string, streamList: any[]) => {
    console.log('[LIVELY-WIDGET] 📢 handleInfoUpdate received:', {roomId, streamListLength: streamList.length});
    try {
      const payload = JSON.parse(streamList[0].extraInfo);
      console.log('[LIVELY-WIDGET] 📢 handleInfoUpdate payload:', payload);

      const event = payload.event;
      if (event === 'PRODUCT_CHANGE') {
        console.log('[LIVELY-WIDGET] 📢 Processing PRODUCT_CHANGE event');
        handleProductAdd(roomId, streamList);

        let engagementAvailable = Number(
          await localStorage.getValue(`${streamConfigRef?.current?.streaming_id}-EngagementAvailable`),
        );
        if (_.isNil(engagementAvailable) || isNaN(engagementAvailable)) engagementAvailable = 15;
        console.log('[LIVELY-WIDGET] 📢 Current engagement available:', engagementAvailable);

        if (Number(engagementAvailable) < 1) {
          await localStorage.setValue(`${streamConfigRef?.current?.streaming_id}-EngagementAvailable`, `${5}`);
          console.log('[LIVELY-WIDGET] 📢 Engagement reset to 5');
        }
      } else if (event === 'DELETE_PRODUCT') {
        console.log('[LIVELY-WIDGET] 📢 Processing DELETE_PRODUCT event');
        let data = payload.data;
        const newProducts = (allProductsRef.current ?? [])?.filter(e => e.product_id != data.product_id);
        console.log('[LIVELY-WIDGET] 📢 Products after deletion:', {
          oldCount: allProductsRef.current?.length || 0,
          newCount: newProducts.length,
        });

        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'allProducts'],
              newValue: newProducts,
            },
          ]),
        );
        allProductsRef.current = [...newProducts];
      } else if (event === 'MULTIPLIER_CHANGE') {
        console.log('[LIVELY-WIDGET] 📢 Processing MULTIPLIER_CHANGE event');
        const multiplier = isNaN(parseInt(payload.count)) ? 1 : parseInt(payload.count);
        console.log('[LIVELY-WIDGET] 📢 New multiplier:', multiplier);

        setViewersMultipler(multiplier);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'viewersMultipler'],
              newValue: multiplier,
            },
          ]),
        );
      }
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error in handleInfoUpdate:', error);
    }
  };

  const onRespondToComment = (userName: string) => {
    console.log('[LIVELY-WIDGET] 💬 Responding to comment from:', userName);
    try {
      triggerEvent('onRespondToComment', {name: userName});
      console.log('[LIVELY-WIDGET] ✅ Comment response triggered successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error responding to comment:', error);
    }
  };

  const onPollModalClose = () => {
    console.log('[LIVELY-WIDGET] 🗳️ Closing poll modal');
    try {
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'showPolls'],
            newValue: false,
          },
        ]),
      );
      console.log('[LIVELY-WIDGET] ✅ Poll modal closed successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error closing poll modal:', error);
    }
  };

  const handleVote = async (optionId: any) => {
    console.log('[LIVELY-WIDGET] 🗳️ Handling vote for option:', optionId);
    try {
      await LivelyApi.pollVote(activePoll.streaming_id, brandId, activePoll.poll_uuid, userId, optionId);
      setVotedOptionId(optionId);
      setActivePoll(prev => ({
        ...prev,
        pollResults: prev.pollResults
          ? {
              ...prev.pollResults,
              options: prev.pollResults?.options.map(option =>
                option.optionId === optionId
                  ? {
                      ...option,
                      votes: option.votes + 1,
                      percentage: `${Math.round(((option.votes + 1) / (prev.pollResults?.total_votes + 1)) * 100)}%`,
                    }
                  : option,
              ),
              total_votes: prev.pollResults?.total_votes + 1,
            }
          : {
              poll_uuid: activePoll.poll_uuid,
              total_votes: 1,
              options: [{optionId, votes: 1, percentage: '100%'}],
            },
      }));
      console.log('[LIVELY-WIDGET] ✅ Vote submitted successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error submitting vote:', error);
      // Assuming toast is available globally
      if (typeof toast !== 'undefined') {
        toast.show('Something went wrong. Please try again later!', {
          type: 'error',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
      }
    }
  };

  const addComment = (comment: string, userName: string, source: string) => {
    console.log('[LIVELY-WIDGET] 💬 Adding comment:', {comment, userName, source});
    try {
      const commentObject = {
        text: comment,
        userName: source === USER_TYPE.HOST || source === USER_TYPE.MODERATOR ? companyNameRef.current : userName,
        source: source,
      };
      console.log('[LIVELY-WIDGET] 💬 Comment object created:', commentObject);

      setComments(comments => [...comments.slice(-999), commentObject]);
      setTimeout(() => {
        try {
          if (commentsLayoutStyles.flexDirection == 'column-reverse') {
            scrollViewRef.current?.scrollTo(0, 0, true);
            scrollViewRef2.current?.scrollTo(0, 0, true);
          } else {
            scrollViewRef.current?.scrollToEnd({animated: true});
            scrollViewRef2.current?.scrollToEnd({animated: true});
          }
          console.log('[LIVELY-WIDGET] ✅ Comment scroll updated');
        } catch (scrollError) {
          console.error('[LIVELY-WIDGET] ❌ Error scrolling comments:', scrollError);
        }
      }, 200);
      console.log('[LIVELY-WIDGET] ✅ Comment added successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error adding comment:', error);
    }
  };

  const handleBroadcastPollResults = (info: any) => {
    console.log('[LIVELY-WIDGET] 🗳️ Handling broadcast poll results:', info);
    try {
      const {pollResults} = info;
      setActivePoll(prev => {
        const reformedPoll = {...prev, pollResults};
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'activePoll'],
              newValue: reformedPoll,
            },
          ]),
        );
        return reformedPoll;
      });
      console.log('[LIVELY-WIDGET] ✅ Poll results broadcast handled successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error handling poll results broadcast:', error);
    }
  };

  const handleStartPoll = (poll: any) => {
    console.log('[LIVELY-WIDGET] 🗳️ Starting poll:', poll);
    try {
      setActivePoll(poll);
      modelUpdate([
        {
          selector: 'activePoll',
          newValue: poll,
        },
      ]);
      console.log('[LIVELY-WIDGET] ✅ Poll started successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error starting poll:', error);
    }
  };

  const handleEndPoll = () => {
    console.log('[LIVELY-WIDGET] 🗳️ Ending poll');
    try {
      setActivePoll({});
      setVotedOptionId('');
      modelUpdate([
        {
          selector: 'showPolls',
          newValue: false,
        },
        {
          selector: 'activePoll',
          newValue: {},
        },
      ]);
      console.log('[LIVELY-WIDGET] ✅ Poll ended successfully');
    } catch (error) {
      console.error('[LIVELY-WIDGET] ❌ Error ending poll:', error);
    }
  };

  const startStreaming = async (zegoStreamEngineInstance: ZegoExpressEngine, restart?: boolean) => {
    LivelyApi.getCompanyInfo(brandId).then(response => {
      companyNameRef.current = response.data?.data?.company_details?.[0]?.company_name || 'HOST';
    });
    allProductsRef.current = streamConfigRef?.current?.product_info ?? [];
    if (!restart) {
      const updates = [
        {
          selector: [pageKey, 'plugins', id, 'currentProduct'],
          newValue: streamConfigRef?.current?.product_info?.[0] ?? null,
        },
      ];
      dispatch(modelUpdateAction(updates));
    }
    try {
      zegoStreamEngineInstance.on('roomStateUpdate', (roomID, state, errorCode, extendedData) => {
        console.log('Room State Changed', state);
        setConnectionState(state);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'streamEnded'],
              newValue: state == 0,
            },
            {
              selector: [pageKey, 'plugins', id, 'streamStatus'],
              newValue: state,
            },
          ]),
        );
      });

      zegoStreamEngineInstance.on('publisherStateUpdate', (streamID, state, errorCode, extendedData) => {
        console.log('Publisher State Changed', state);
        setPublisherState(state);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'publisherState'],
              newValue: state,
            },
          ]),
        );
      });

      zegoStreamEngineInstance.on('remoteMicStateUpdate', (streamID, state) => {
        console.log('Mic State Changed', state);
        setHostMicState(state);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'micState'],
              newValue: state,
            },
          ]),
        );
      });

      zegoStreamEngineInstance.on('remoteCameraStateUpdate', (streamID, state) => {
        console.log('Camera State Changed', state);
        setHostCameraState(state);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'cameraState'],
              newValue: state,
            },
          ]),
        );
      });

      zegoStreamEngineInstance.on('playerStateUpdate', (streamID, state) => {
        console.log('Player State Changed', state);
      });

      zegoStreamEngineInstance.on('playerMediaEvent', (streamID, state, errorCode, errorName) => {
        console.log('Media State Changed', streamID, state, errorCode, errorName);
        if (state == ZegoPlayerMediaEvent.VideoBreakOccur) {
          setHostCameraState(15);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'cameraState'],
                newValue: 15,
              },
            ]),
          );
        } else if (state == ZegoPlayerMediaEvent.VideoBreakResume) {
          setHostCameraState(0);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'cameraState'],
                newValue: 0,
              },
            ]),
          );
        } else if (state == ZegoPlayerMediaEvent.AudioBreakOccur) {
          setHostMicState(15);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'micState'],
                newValue: 10,
              },
            ]),
          );
        } else if (state == ZegoPlayerMediaEvent.AudioBreakResume) {
          setHostMicState(0);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'micState'],
                newValue: 0,
              },
            ]),
          );
        }
      });

      zegoStreamEngineInstance.on('roomOnlineUserCountUpdate', async (roomID, count) => {
        setViewersCount(count);
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'viewersCount'],
              newValue: count,
            },
          ]),
        );
      });

      zegoStreamEngineInstance.on('roomStreamUpdate', async (roomID, updateType, streamList, extendedData) => {
        try {
          console.log({roomID, updateType, streamList, extendedData});
          handleInfoUpdate(roomID, streamList);
        } catch (e) {}

        if (updateType === ZegoUpdateType.Delete) {
          setPublisherState(ZegoPublisherState.NoPublish);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'publisherState'],
                newValue: ZegoPublisherState.NoPublish,
              },
            ]),
          );
        } else if (updateType === ZegoUpdateType.Add) {
          setPublisherState(ZegoPublisherState.Publishing);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'publisherState'],
                newValue: ZegoPublisherState.Publishing,
              },
            ]),
          );
        }
      });

      zegoStreamEngineInstance.on('roomStreamExtraInfoUpdate', handleInfoUpdate);

      zegoStreamEngineInstance.on('IMRecvBarrageMessage', (roomID, messageList) => {
        console.log('Comment recieved');
        const message = JSON.parse(messageList[0]?.message);
        if (message?.muted) return;
        addComment(
          message?.comment,
          messageList[0]?.fromUser?.userName?.startsWith('Anonymous')
            ? message?.name
            : messageList[0]?.fromUser?.userName || 'Anonymous',
          message?.source,
        );
      });

      zegoStreamEngineInstance.on('onPipModeRestore', ev => {
        if (!global.activitySharedMem) {
          global.activitySharedMem = {};
        }
        global.activitySharedMem.isInPip = false;
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'isInPip'],
              newValue: false,
            },
          ]),
        );
        navigateToRoot();
      });
      zegoStreamEngineInstance.on('onPipModeExit', ev => {
        if (global.activitySharedMem.isInPip) {
          console.log('[LIVELY-WIDGET] 🔧 PiP Mode Changed; onPipModeExit');
          ZegoExpressEngine.instance().stopPlayingStream(streamConfigRef.current?.streaming_id);
          console.log('[LIVELY-WIDGET] ✅ Stop playing the stream; onPipModeExit');

          global.activitySharedMem.isInPip = false;
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'isInPip'],
                newValue: false,
              },
            ]),
          );
        } else {
          console.log('[LIVELY-WIDGET] ❌ Not in PIP Mode; onPipModeExit');
        }
      });
      zegoStreamEngineInstance.on('IMRecvCustomCommand', async (roomID, fromUser, command) => {
        console.log('CUSTOM COMMAND RECEIVED', command);
        let parsedCommand = JSON.parse(command);
        if (parsedCommand?.type == 'REACTION_ANNOUCEMENT') {
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'reactionAnnoucement'],
                newValue: {userName: parsedCommand?.userName, reactionUrl: parsedCommand?.reactionUrl},
              },
            ]),
          );
          triggerEmoji(parsedCommand?.reactionUrl);
          hideHeart();
        } else if (parsedCommand?.type == 'CART_ANNOUCEMENT') {
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'cartAnnoucement'],
                newValue: {userName: parsedCommand?.userName, productTitle: parsedCommand?.productTitle},
              },
            ]),
          );
          // triggerAddCard(parsedCommand?.productTitle, parsedCommand?.userName);
          hideCard();
        } else if (parsedCommand?.type === 'START_POLL') {
          const {poll} = parsedCommand;
          setActivePoll(poll);
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'activePoll'],
                newValue: poll,
              },
            ]),
          );
        } else if (parsedCommand?.type === 'MUTE_USER') mutedUsersRef.current = parsedCommand?.mutedUsers ?? [];
      });
      zegoStreamEngineInstance.on('IMRecvBroadcastMessage', (roomID, messageList) => {
        console.log('BROADCAST RECEIVED', messageList);
        const payload = JSON.parse(messageList[0]?.message);
        const payloadData = payload?.data;

        switch (payload.type) {
          case 'START_POLL':
            handleStartPoll(payloadData?.poll);
            break;
          case 'BROADCAST_POLL_RESULTS':
            handleBroadcastPollResults(payloadData);
            break;
          case 'END_POLL':
            handleEndPoll();
            break;
          case 'MUTE_USER':
            mutedUsersRef.current = payloadData?.mutedUsers ?? [];
          default:
            break;
        }
      });
    } catch (err) {
      console.error(err);
    }

    let token = await LivelyApi.generateZeegoToken(userId, streamConfigRef?.current?.streaming_id);
    token = token?.data?.data;
    if (!token) {
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'streamError'],
            newValue: 'Error in joining the stream',
          },
        ]),
      );
      console.log(`Error in generating token from lively`);
      return;
    }
    const roomConfig = new ZegoRoomConfig();
    roomConfig.isUserStatusNotify = true;
    roomConfig.token = token;

    const user = new ZegoUser(userId, userName);

    try {
      const result = await ZegoExpressEngine.instance().loginRoom(streamConfigRef?.current?.room_id, user, roomConfig);
      if (result?.errorCode !== 0) {
        dispatch(
          modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'streamError'],
              newValue: 'Error in joining the stream',
            },
          ]),
        );
        console.log(`Error in login to room\nCode ${result?.errorCode} message ${result?.extendedData}`);
      }
    } catch (e) {
      console.log(`Error in login to room\n ${e?.toString()}`);
    }

    const zgViewRefId = findNodeHandle(zgViewRef.current);
    console.log('Zego view ref id: ', zgViewRefId);
    const zegoViewInstance = new ZegoView(zgViewRefId, 1, 0);

    await ZegoExpressEngine.instance().startPlayingStream(
      streamConfigRef?.current?.streaming_id,
      zegoViewInstance,
      undefined,
    );
    ZegoExpressEngine.instance().muteSpeaker(false);
  };

  useEffect(() => {
    if (streamConfig) {
      streamConfigRef.current = streamConfig;
      if (!global.activitySharedMem) {
        global.activitySharedMem = {
          pipCb: null,
          mainCb: null,
          args: {stream_id: streamConfig?.streaming_id},
          isInPip: false,
        };
      }
      console.log('streamConfig changed');
      let zegoEngine = null;
      try {
        zegoEngine = ZegoExpressEngine.instance();
      } catch (err) {
        console.log('Creating Engine');
      }
      if (!zegoEngine) {
        try {
          ZegoExpressEngine.createEngineWithProfile({appID, scenario: ZegoScenario.Default}).then(engine => {
            console.log('Calling startStreaming');
            startStreaming(engine);
          });
        } catch (err) {
          console.log('Error creating Engine', err);
        }
      }
    }
    return () => {
      let zegoEngine = null;
      try {
        zegoEngine = ZegoExpressEngine.instance();
      } catch (err) {
        console.log('Destroying Engine');
      }
      // if (zegoEngine && !global.activitySharedMem?.isInPip) {
      if (zegoEngine) {
        console.log('Stop playing the stream');
        ZegoExpressEngine.instance().stopPlayingStream(streamConfig?.streaming_id);
        ZegoExpressEngine.destroyEngine();
      }
    };
  }, [streamConfig]);

  useEffect(() => {
    if (restartKey) {
      const streamConfig = streamConfigRef.current;
      if (!global.activitySharedMem) {
        global.activitySharedMem = {
          pipCb: null,
          mainCb: null,
          args: {stream_id: streamConfig?.streaming_id},
          isInPip: false,
        };
      }
      console.log('streamConfig changed');
      let zegoEngine = null;
      try {
        zegoEngine = ZegoExpressEngine.instance();
      } catch (err) {
        console.log('Creating Engine');
      }
      if (!zegoEngine) {
        try {
          ZegoExpressEngine.createEngineWithProfile({appID, scenario: ZegoScenario.Default}).then(engine => {
            console.log('Calling startStreaming');
            startStreaming(engine);
          });
        } catch (err) {
          console.log('Error creating Engine', err);
        }
      } else {
        startStreaming(zegoEngine, true);
      }
    }
  }, [restartKey]);

  const refContext = useContext(WidgetRefContext);
  useEffect(() => {
    refContext.registerWidgetRef(
      {
        // TODO(LIVE): Change this in the config
        current: {
          async sendComment(comment: any) {
            console.log('Sending comment', comment);
            dispatch(
              modelUpdateAction([
                {
                  selector: [pageKey, 'plugins', id, 'sendingComment'],
                  newValue: true,
                },
              ]),
            );
            const uniqueDeviceId = getUniqueDeviceId();
            const isMutedUser = mutedUsersRef.current?.some((u: any) => u.deviceId == uniqueDeviceId);
            if (comment.trim()) {
              let commentObj = {
                name: userName,
                comment: comment,
                source: USER_TYPE.USER,
                deviceId: uniqueDeviceId,
                muted: isMutedUser || isBannedUser,
                isBanned: isBannedUser
              };
              console.log('Sending comment', streamConfigRef?.current?.room_id, commentObj);
              ZegoExpressEngine.instance()
                .sendBarrageMessage(streamConfigRef?.current?.room_id, JSON.stringify(commentObj))
                .then(res => {
                  console.log(res);
                  addComment(comment, userName, USER_TYPE.USER);
                  dispatch(
                    modelUpdateAction([
                      {
                        selector: [pageKey, 'plugins', id, 'sendingComment'],
                        newValue: false,
                      },
                    ]),
                  );
                  triggerEvent('onCommentSent');
                })
                .catch(err => {
                  console.error(err);
                  dispatch(
                    modelUpdateAction([
                      {
                        selector: [pageKey, 'plugins', id, 'sendingComment'],
                        newValue: false,
                      },
                    ]),
                  );
                  triggerEvent('onCommentError');
                });
            } else {
              console.log('empty comment message');
              dispatch(
                modelUpdateAction([
                  {
                    selector: [pageKey, 'plugins', id, 'sendingComment'],
                    newValue: false,
                  },
                ]),
              );
            }
          },
          async sendReactions(reactionUrl: string) {
            let engagementAvailable = Number(
              await localStorage.getValue(`${streamConfigRef?.current?.streaming_id}-EngagementAvailable`),
            );
            if (_.isNil(engagementAvailable) || isNaN(engagementAvailable)) engagementAvailable = 10;
            console.log('Engagement Available', engagementAvailable);
            if (engagementAvailable > 0) {
              // Moving getOptimage Image logic here, since live dash will not have any apptile related context.
              if (reactionUrl.startsWith('asset:')) {
                const reactionAssetId = reactionUrl.replace('asset:', '');
                const {getOptimalImage} = getDeviceImage(reactionAssetId);
                reactionUrl = getOptimalImage(`700x700`)?.fileUrl;
              }
              const obj = {
                type: 'REACTION_ANNOUCEMENT',
                reactionUrl: reactionUrl, // Sending CDN link directly to web and mobile.
                userName: userName,
                roomId: streamConfigRef?.current?.room_id,
              };
              console.log('Sending reaction REACTION_ANNOUCEMENT', obj);
              ZegoExpressEngine.instance()
                .sendCustomCommand(streamConfigRef?.current?.room_id, JSON.stringify(obj), [])
                .then(errorCode => {
                  dispatch(
                    modelUpdateAction([
                      {
                        selector: [pageKey, 'plugins', id, 'reactionAnnoucement'],
                        newValue: {userName, reactionUrl},
                      },
                    ]),
                  );
                  triggerEmoji(reactionUrl, userName);
                  localStorage.setValue(
                    `${streamConfigRef?.current?.streaming_id}-EngagementAvailable`,
                    `${engagementAvailable - 1}`,
                  );
                  hideHeart();
                  console.log('REACTION_ANNOUCEMENT errorCode After Success', errorCode);
                })
                .catch(errorCode => {
                  console.log('REACTION_ANNOUCEMENT error * Code', errorCode);
                });
            } else {
              triggerEvent('onReactionLimitReached');
            }
          },
          async sendAddToCartReactions(productTitle: string) {
            const roomId = streamConfigRef?.current?.room_id;
            console.log('🛒 Sending CART_ANNOUCEMENT for:', productTitle);
            if (productTitle.trim()) {
              const AtcObj = {
                type: 'CART_ANNOUCEMENT',
                productTitle,
                userName: userName,
                roomId: roomId,
              };
              ZegoExpressEngine.instance()
                .sendCustomCommand(roomId, JSON.stringify(AtcObj), [])
                .then(response => {
                  dispatch(
                    modelUpdateAction([
                      {
                        selector: [pageKey, 'plugins', id, 'cartAnnoucement'],
                        newValue: {userName, productTitle},
                      },
                    ]),
                  );
                  // triggerAddCard(productTitle, userName);
                  hideCard();
                  console.log('CART_ANNOUCEMENT response After Success', response);
                })
                .catch(errorCode => {
                  console.log('CART_ANNOUCEMENT error * Code', errorCode);
                });
            } else {
              console.log('CART_ANNOUCEMENT error * Code', `Product ID is empty`);
            }
          },
          startPip() {
            if (enableLivelyPIP) {
              if (Platform.OS === 'ios') {
                console.log('Calling start pip');
                console.log('not in pip currently');
                if (!global.activitySharedMem) {
                  global.activitySharedMem = {};
                }
                global.activitySharedMem.isInPip = true;
                dispatch(
                  modelUpdateAction([
                    {
                      selector: [pageKey, 'plugins', id, 'isInPip'],
                      newValue: true,
                    },
                  ]),
                );
                console.log('Starting PIP activity');
                ZegoExpressEngine.startPip(0);
                console.log('PIP activity started');
              } else if (Platform.OS === 'android') {
                console.log('handling pip start for android');
                // if (!global.activitySharedMem?.isInPip) {
                console.log('not in pip currently');
                if (!global.activitySharedMem) {
                  global.activitySharedMem = {};
                }
                global.activitySharedMem.isInPip = true;
                dispatch(
                  modelUpdateAction([
                    {
                      selector: [pageKey, 'plugins', id, 'isInPip'],
                      newValue: true,
                    },
                  ]),
                );
                PIPModule.startPIPActivity().then(() => {
                  setTimeout(() => {
                    if (global.activitySharedMem) {
                      console.log('Calling pipactivity callback');
                      global.activitySharedMem.args.stream_id = streamConfigRef.current?.streaming_id;
                      if (global.activitySharedMem?.pipCb) {
                        global.activitySharedMem?.pipCb();
                      }
                    }
                  }, 300);
                });
                // } else {
                //   console.log('Ignoring pip start')
                // }

                // ZegoExpressEngine.instance().stopPlayingStream(streamConfigRef.current?.streaming_id)
                // .then(() => {
                //   PIPModule.startPIPActivity();
                // })
              } else {
                logger.info('Cannot do pip on this platform');
              }
            }
          },
          endPip() {
            if (enableLivelyPIP) {
              if (Platform.OS === 'ios') {
                ZegoExpressEngine.cleanupPip();
                if (!global.activitySharedMem) {
                  global.activitySharedMem = {};
                }
                global.activitySharedMem.isInPip = false;
                dispatch(
                  modelUpdateAction([
                    {
                      selector: [pageKey, 'plugins', id, 'isInPip'],
                      newValue: false,
                    },
                  ]),
                );
              } else if (Platform.OS === 'android') {
                PIPModule.endPIPActivity();
                if (!global.activitySharedMem) {
                  global.activitySharedMem = {};
                }
                global.activitySharedMem.isInPip = false;
                dispatch(
                  modelUpdateAction([
                    {
                      selector: [pageKey, 'plugins', id, 'isInPip'],
                      newValue: false,
                    },
                  ]),
                );
              } else {
                logger.info('Cannot do pip on this platform');
              }
            }
          },
        },
      },
      pageKey,
      id,
      instance,
    );
    return () => {
      refContext.unRegisterWidgetRef(pageKey, id, instance);
    };
  }, [pageKey, id, instance, refContext, userName]);

  useEffect(() => {
    if (streamConfig) {
      let zegoEngine = null;
      try {
        zegoEngine = ZegoExpressEngine.instance();
      } catch (err) {
        console.log('Creating Engine');
      }
      console.log('zegoEngine', zegoEngine);
      if (zegoEngine) {
        zegoEngine?.muteSpeaker(muteSpeaker);
      }
    }
  }, [muteSpeaker]);
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollViewRef2 = useRef<ScrollView>(null);
  const handlePipCb = () => {
    console.log('Receive call from pip activity', global.activitySharedMem);
  };

  const openLink = (urlFromComment: string) => {
    dispatch(
      modelUpdateAction([
        {
          selector: [pageKey, 'plugins', id, 'urlFromComment'],
          newValue: urlFromComment,
        },
      ]),
    );
    const params = {
      url: urlFromComment,
      type:
        urlFromComment.indexOf('/collections/') > 0
          ? 'collection'
          : urlFromComment.indexOf('/products/') > 0
          ? 'product'
          : 'url',
      handle: '',
    };
    params.handle =
      params.type == 'url'
        ? ''
        : urlFromComment.indexOf('?') > 0
        ? urlFromComment.split(`/${params.type}s/`).pop()?.slice(0, urlFromComment.indexOf('?'))
        : urlFromComment.split(`/${params.type}s/`).pop();
    triggerEvent(
      params.type == 'collection'
        ? 'onCollectionLinkOpen'
        : params.type == 'product'
        ? 'onProductLinkOpen'
        : 'onLinkOpen',
      params,
    );
  };

  useEffect(() => {
    console.log('Mount effect running for background subscriptions');
    // ios
    if (enableLivelyPIP && Platform.OS === 'ios') {
      const subscription = AppState.addEventListener('change', nextState => {
        if (nextState == 'background') {
          console.log('Background Starting PIP activity');
          ZegoExpressEngine.startPip(0);
          console.log('Background PIP activity started');
        }
      });
      return () => {
        subscription.remove();
      };
    } else if (enableLivelyPIP && Platform.OS === 'android') {
      // android
      if (!global.activitySharedMem) {
        global.activitySharedMem = {
          pipCb: null,
          mainCb: handlePipCb,
          args: {},
        };
      } else {
        global.activitySharedMem = {
          ...global.activitySharedMem,
          mainCb: handlePipCb,
        };
      }

      const pipDismissSubscription = pipEventEmitter.addListener('onPiPModeChanged', () => {
        // Handle PiP mode exit, e.g., continue playing the stream on the main activity
        if (global.activitySharedMem.isInPip) {
          console.log('[LIVELY-WIDGET] 🔧 PiP Mode Changed');
          global.activitySharedMem.isInPip = false;
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'isInPip'],
                newValue: false,
              },
            ]),
          );
          // Call a method to switch back to the main activity or continue the stream
          // continuePlayingStreamOnMainActivity();
          navigateToRoot();
          console.log('[LIVELY-WIDGET] ✅ Navigating Root onPiPModeChanged', global.activitySharedMem.isInPip);
          const zegoViewInstance = new ZegoView(findNodeHandle(zgViewRef.current), 1, 0);

          const playerConfig = new ZegoPlayerConfig();
          playerConfig.resourceMode = zegoStreamResourceMode;
          console.log('[LIVELY-WIDGET] ✅ Starging Stream onPiPModeChanged');
          ZegoExpressEngine.instance().startPlayingStream(
            streamConfigRef?.current?.streaming_id,
            zegoViewInstance,
            playerConfig,
          );
        } else {
          console.log('[LIVELY-WIDGET] ❌ Not in PIP Mode; onPiPModeChanged');
          PIPModule.endPIPActivity();
          console.log('[LIVELY-WIDGET] ❌ Not in PIP Mode, still ended PIP Activity; onPiPModeChanged');
        }
      });

      const pipModeDissmissed = pipEventEmitter?.addListener('onPipModeDismissed', async () => {
        if (global.activitySharedMem.isInPip) {
          console.log('[LIVELY-WIDGET] 🔧 PiP Mode Dismissed');
          await ZegoExpressEngine.instance().stopPlayingStream(streamConfigRef.current?.streaming_id);
          console.log('[LIVELY-WIDGET] ✅ Zego instance stopped playing');

          global.activitySharedMem.isInPip = false;
          dispatch(
            modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'isInPip'],
                newValue: false,
              },
            ]),
          );
          PIPModule.endPIPActivity();
        } else {
          console.log('[LIVELY-WIDGET] ❌ Not in PIP Mode; onPipModeDismissed');
          PIPModule.endPIPActivity();
          console.log('[LIVELY-WIDGET] ❌ Not in PIP Mode, still ended PIP Activity; onPipModeDismissed');
        }
      });

      return () => {
        pipDismissSubscription.remove();
        pipModeDissmissed.remove();
      };
    }
  }, []);

  if (streamConfig) {
    return (
      <View style={[layoutStyles, modelStyles]}>
        {connectionState == 0 ? (
          <View style={{height: '100%', width: '100%'}}>
            <View style={{position: 'absolute', height: '100%', width: '100%'}}>
              <Image
                source={{uri: streamConfig?.streaming_thumbnail}}
                resizeMode="cover"
                style={{height: '100%', width: '100%'}}
              />
            </View>
            <View
              style={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                backgroundColor: '#0006',
                zIndex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{fontWeight: '900', fontSize: 28, width: '100%', textAlign: 'center', color: '#fff'}}>
                The live stream has ended
              </Text>
              <Text style={{fontSize: 14, width: '100%', textAlign: 'center', color: '#fff'}}>
                Explore and purchase featured products
              </Text>
            </View>
          </View>
        ) : (
          <View style={{height: '100%', width: '100%'}}>
            {(hostCameraState != 0 || publisherState == 0) && (
              <View style={{position: 'absolute', height: '100%', width: '100%', zIndex: 1}}>
                <View style={{height: '100%', width: '100%'}}>
                  <Image
                    source={{uri: streamConfig?.streaming_thumbnail}}
                    resizeMode="cover"
                    style={{height: '100%', width: '100%'}}
                  />
                </View>
                <View
                  style={{
                    position: 'absolute',
                    height: '100%',
                    width: '100%',
                    backgroundColor: '#0006',
                    zIndex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text style={{fontWeight: '900', fontSize: 28, width: '100%', textAlign: 'center', color: '#fff'}}>
                    Waiting for host to present
                  </Text>
                  <ReactionEmojis ref={emojiRef} reactionsAnimation={reactionsAnimation} />
                  <AddToCartReactionCards ref={cardRef} />
                  {showComments && (
                    <CommentsView
                      ref={scrollViewRef2}
                      comments={comments}
                      USER_TYPE={USER_TYPE}
                      layoutContStyle={layoutContStyle}
                      stylesContStyle={stylesContStyle}
                      modelStyles={modelStyles}
                      openLink={openLink}
                      showHostTag={showHostTag}
                      onRespondToComment={onRespondToComment}
                    />
                  )}
                </View>
              </View>
            )}
            <View
              style={{
                height: '100%',
                width: '100%',
                flex: 1,
              }}>
              <ZegoTextureView
                ref={zgViewRef}
                style={{
                  height: '100%',
                  width: '100%',
                  flex: 1,
                }}
              />
              <ReactionEmojis ref={emojiRef} />
              <AddToCartReactionCards ref={cardRef} />

              {showComments && (
                <CommentsView
                  ref={scrollViewRef}
                  comments={comments}
                  USER_TYPE={USER_TYPE}
                  layoutContStyle={layoutContStyle}
                  stylesContStyle={stylesContStyle}
                  modelStyles={modelStyles}
                  openLink={openLink}
                  showHostTag={showHostTag}
                  onRespondToComment={onRespondToComment}
                />
              )}
            </View>
            <KeepAwake />
            {showPolls && (
              <PollsView
                pageKey={pageKey}
                brandLogoAssetId={brandLogoAssetId}
                activePoll={activePoll}
                votedOptionId={votedOptionId}
                onClose={onPollModalClose}
                handleVote={handleVote}
                viewerId={userId}
                roomId={streamConfigRef?.current?.room_id}
                viewersMultiplier={viewersMultipler}
              />
            )}
          </View>
        )}
      </View>
    );
  } else {
    return <View />;
  }
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'streamConfig',
      props: {
        label: 'Stream Config',
      },
    },
    {
      type: 'codeInput',
      name: 'userName',
      props: {
        label: 'User Name',
      },
    },
    {
      type: 'codeInput',
      name: 'userid',
      props: {
        label: 'User Id',
      },
    },
    {
      type: 'checkbox',
      name: 'showComments',
      props: {
        label: 'Show Comments',
        disableBinding: false,
      },
    },
    {
      type: 'checkbox',
      name: 'showHostTag',
      props: {
        label: 'Show Host Tag',
        disableBinding: false,
      },
    },
    ...defaultEditors.basic,
    {
      type: 'layoutEditor',
      name: 'commentsLayout',
      props: {
        label: 'Comments Layout',
        isContainer: true,
      },
    },
    {
      type: 'layoutEditor',
      name: 'commentItemLayout',
      props: {
        label: 'Comment Item Layout',
        isContainer: true,
      },
    },
  ],
  advanced: [
    {
      type: 'radioGroup',
      name: 'reactionsAnimation',
      props: {
        label: 'Reactions Animation',
        options: [
          {text: 'Single', value: 'single'},
          {text: 'Multiple', value: 'multiple'},
        ],
        disableBinding: true,
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
  onCommentSent: {
    type: EventTriggerIdentifier,
  },
  onCommentError: {
    type: EventTriggerIdentifier,
  },
  onLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onProductLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onCollectionLinkOpen: {
    type: EventTriggerIdentifier,
  },
  hideHeart: {
    type: EventTriggerIdentifier,
  },
  hideCard: {
    type: EventTriggerIdentifier,
  },
  sendComment: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendComment;
    },
    actionMetadata: {
      editableInputParams: {
        comment: '',
      },
    },
  },
  startPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return startPip;
    },
  },
  endPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return endPip;
    },
  },
  sendReactions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendReactions;
    },
    actionMetadata: {
      editableInputParams: {
        reactionUrl: '',
      },
    },
  },
  onReactionLimitReached: {
    type: EventTriggerIdentifier,
  },
  onRespondToComment: {
    type: EventTriggerIdentifier,
  },
  sendAddToCartReactions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendAddToCartReactions;
    },
    actionMetadata: {
      editableInputParams: {
        productTitle: '',
      },
    },
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'LivelyLiveSellingWidgetV2',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(LivelyLiveSellingWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: [],
  },
);
