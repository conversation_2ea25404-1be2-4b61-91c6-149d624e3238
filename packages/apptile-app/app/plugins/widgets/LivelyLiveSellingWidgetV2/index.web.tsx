import {
  containerLayoutEditors,
  createDeepEqualSelector,
  layoutEditors,
  LayoutRecord,
  mergeWithDefaultStyles,
  modelUpdateAction,
  TriggerActionIdentifier,
  WidgetRefContext,
  WidgetStyleEditorOptions,
} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {datasourceByIdModelSel, datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback, useContext, useEffect, useState} from 'react';
import {View, Text, ScrollView} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../../datasource/ShopifyV_22_10/actions/checkoutAction';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {sendComment, startPip, endPip, sendReactions, sendAddToCartReactions} from './streamActions';
import CommentsView from './shared/CommentsView';
import {LocalStorage as localStorage} from 'apptile-core';
import PollsView from './shared/PollsView';

// Copied from zego library
enum ZegoStreamResourceMode {
  /** Default mode. The SDK will automatically select the streaming resource according to the cdnConfig parameters set by the player config and the ready-made background configuration. */
  Default = 0,
  /** Playing stream only from CDN. */
  OnlyCDN = 1,
  /** Playing stream only from L3. */
  OnlyL3 = 2,
  /** Playing stream only from RTC. */
  OnlyRTC = 3,
  /** CDN Plus mode. The SDK will automatically select the streaming resource according to the network condition. */
  CDNPlus = 4,
}

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyLiveSellingWidgetConfigType = {
  value: string;
  streamConfig: any;
  userName: string;
  userId: string;
  showComments: boolean;
  sendComment: typeof TriggerActionIdentifier;
  startPip: typeof TriggerActionIdentifier;
  endPip: typeof TriggerActionIdentifier;
  sendReactions: typeof TriggerActionIdentifier;
  sendAddToCartReactions: typeof TriggerActionIdentifier;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
  onCommentSent: string;
  onLinkOpen: string;
  onProductLinkOpen: string;
  onCollectionLinkOpen: string;
  hideHeart: string;
  hideCard: string;
  viewersCount: string;
  viewersMultipler: number;
  muteSpeaker: boolean;
  sendingComment: boolean;
  showHostTag: boolean;
  onReactionLimitReached: string;
  onRespondToComment: string;
  isInPip: boolean;
  zegoStreamResourceMode: ZegoStreamResourceMode;
  activePoll: any;
  reactionsAnimation: string;
};

const LivelyLiveSellingWidgetConfig: LivelyLiveSellingWidgetConfigType = {
  value: '',
  streamConfig: '',
  userName: '',
  userId: '',
  showComments: true,
  sendComment: TriggerActionIdentifier,
  startPip: TriggerActionIdentifier,
  endPip: TriggerActionIdentifier,
  sendReactions: TriggerActionIdentifier,
  sendAddToCartReactions: TriggerActionIdentifier,
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
  onCommentSent: '',
  onLinkOpen: '',
  onProductLinkOpen: '',
  onCollectionLinkOpen: '',
  hideHeart: '',
  hideCard: '',
  viewersCount: '',
  viewersMultipler: 1,
  muteSpeaker: false,
  sendingComment: false,
  showHostTag: true,
  onReactionLimitReached: '',
  onRespondToComment: '',
  isInPip: false,
  zegoStreamResourceMode: ZegoStreamResourceMode.Default,
  activePoll: '',
  reactionsAnimation: 'single',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelySellingWidgetV2',
  type: 'widget',
  name: 'Lively Live Selling Widget V2',
  description: 'Load Lively Live Selling Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = state => datasourceTypeModelSel(state, 'lively');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);

const allLayoutEditors = [...layoutEditors, ...containerLayoutEditors];

const layoutConts = {
  comments: 'Comments',
  commentItem: 'Comment Item',
  hostCommentItem: 'Host Comment Item',
};

const styleConfigEditors = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  ...defaultStyleEditors,
];

const LivelyLiveSellingWidgetStyleConfig: WidgetStyleEditorOptions = [
  ...Object.keys(layoutConts)
    .map((e: string) => {
      return [
        {
          type: 'editorSectionHeader',
          name: '',
          props: {
            label: layoutConts[e],
          },
        },
        ...styleConfigEditors.map(f => {
          if (f.type == 'trblValuesEditor') {
            return {
              ...f,
              props: {
                ...f.props,
                options: f.props.options.map(s => `${e}-${s}`),
              },
              name: `${e}-${f.name}`,
            };
          }
          return {
            ...f,
            name: `${e}-${f.name}`,
          };
        }),
      ];
    })
    .flat(),
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Comments Text',
    },
  },
  {
    type: 'colorInput',
    name: 'commentsColor',
    props: {
      label: 'Comments Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'commentsTypography',
    props: {
      label: 'Comments Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'commentsHeadingColor',
    props: {
      label: 'Comments Heading Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'commentsHeadingTypography',
    props: {
      label: 'Comments Heading Typography',
    },
  },
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Host Comments Text',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsColor',
    props: {
      label: 'Host Comments Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsTypography',
    props: {
      label: 'Host Comments Typography',
    },
  },
  {
    type: 'colorInput',
    name: 'hostCommentsHeadingColor',
    props: {
      label: 'Host Comments Heading Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'hostCommentsHeadingTypography',
    props: {
      label: 'Host Comments Heading Typography',
    },
  },
];

const USER_TYPE = {
  USER: 'USER',
  MODERATOR: 'MODERATOR',
  HOST: 'HOST',
};

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate, pageKey, id, instance} = props;
  const layout = config.get('layout');
  const layoutContStyle = {};
  Object.keys(layoutConts).map((e: string) => {
    if (!layoutContStyle[e]) layoutContStyle[e] = {};
    allLayoutEditors.map(f => {
      const data = model?.get(`${e}-${f.name}`);
      if (data) {
        layoutContStyle[e][f.name] = data;
      }
    });
  });
  const stylesContStyle = {};
  Object.keys(layoutConts).map((e: string) => {
    if (!stylesContStyle[e]) stylesContStyle[e] = {};
    styleConfigEditors.map(f => {
      const data = modelStyles[`${e}-${f.name}`];
      if (data) {
        stylesContStyle[e][f.name] = data;
      }
      if (f.type == 'trblValuesEditor') {
        f.props.options.map(s => {
          const data = modelStyles[`${e}-${s}`];
          if (data) {
            stylesContStyle[e][s] = data;
          }
        });
      }
    });
  });
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const cartDetails = model.get('cartDetails');
  const streamConfig = model.get('streamConfig');
  const userName = model.get('userName');
  const userId = model.get('userId');
  const showComments = model.get('showComments');
  const showPolls = model.get('showPolls');
  const showHostTag = model.get('showHostTag');
  const [comments, setComments] = useState<any[]>([
    {
      userName: 'Lonnie Cain',
      text: 'test',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Hugo Hatfield',
      text: 'test2',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Joe Boone',
      text: 'test3',
      source: USER_TYPE.HOST,
    },
    {
      userName: 'Les Roy',
      text: 'test223',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Devon Woodward',
      text: 'test3234',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Florentino Santiago',
      text: 'test452',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Jannie Lucero',
      text: 'test31345',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Virgie Lutz',
      text: 'test342',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Terrell Leblanc',
      text: 'test353',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Myra Nichols',
      text: 'test2234',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Loraine Hardin',
      text: 'test3 test3 test3 test3 test3 test3 test3 test3 test3 test3 test3 test3',
      source: USER_TYPE.USER,
    },
    {
      userName: 'Stacy Ross',
      text: 'test2',
      source: USER_TYPE.HOST,
    },
    {
      userName: 'Gloria Carr',
      text: 'testing https://skandi-apparel.apptile.io/dresses test3 https://skandi-apparel.apptile.io/products/floral-bell-sleeve-off-shoulder-blouse',
      source: USER_TYPE.HOST,
    },
  ]);
  const [activePoll, setActivePoll] = useState({
    poll_uuid: "lvp-dd598c9df1",
    question: "Which color is your favorite?",
    options: [
      {
        optionId: 1,
        text: 'Red',
        votes: 0,
        _id: '67caca91988fe0fa4a56f700',
      },
      {
        optionId: 2,
        text: 'Blue',
        votes: 0,
        _id: '67caca91988fe0fa4a56f701',
      },
      {
        optionId: 3,
        text: 'Green',
        votes: 0,
        _id: '67caca91988fe0fa4a56f702',
      },
      {
        optionId: 4,
        text: 'Yellow',
        votes: 0,
        _id: '67caca91988fe0fa4a56f703',
      },
    ],
    total_votes: 0,
  });
  const dispatch = useDispatch();

  console.log(`streamConfig`, streamConfig);

  const refContext = useContext(WidgetRefContext);
  useEffect(() => {
    refContext.registerWidgetRef(
      {
        current: {
          setStreamExtraInfo(product: any) {
            console.log('Setting extra stream info to : ', product.value);
            const action = {
              selector: [pageKey, 'plugins', id, 'activeProductId'],
              newValue: product.value.product_id,
            };
            dispatch(modelUpdateAction([action], undefined, true));
          },
          sendReactions(reactionUrl: string) {
            dispatch(
              modelUpdateAction([
                {
                  selector: [pageKey, 'plugins', id, 'reactionAnnoucement'],
                  newValue: {userName, reactionUrl},
                },
              ]),
            );
          },
          sendAddToCartReactions(productTitle: string) {
            dispatch(
              modelUpdateAction([
                {
                  selector: [pageKey, 'plugins', id, 'cartAnnoucement'],
                  newValue: {userName, productTitle},
                },
              ]),
            );
          },
        },
      },
      pageKey,
      id,
      instance,
    );
    return () => {
      refContext.unRegisterWidgetRef(pageKey, id, instance);
    };
  }, [pageKey, id, instance, refContext]);

  const openLink = (urlFromComment: string) => {
    dispatch(
      modelUpdateAction([
        {
          selector: [pageKey, 'plugins', id, 'urlFromComment'],
          newValue: urlFromComment,
        },
      ]),
    );
    const params = {
      url: urlFromComment,
      type:
        urlFromComment.indexOf('/collections/') > 0
          ? 'collection'
          : urlFromComment.indexOf('/products/') > 0
          ? 'product'
          : 'url',
      handle: '',
    };
    params.handle =
      params.type == 'url'
        ? ''
        : urlFromComment.indexOf('?') > 0
        ? urlFromComment.split(`/${params.type}s/`).pop()?.slice(0, urlFromComment.indexOf('?'))
        : urlFromComment.split(`/${params.type}s/`).pop();
    triggerEvent(
      params.type == 'collection'
        ? 'onCollectionLinkOpen'
        : params.type == 'product'
        ? 'onProductLinkOpen'
        : 'onLinkOpen',
      params,
    );
  };

  useEffect(() => {
    if (streamConfig) {
      dispatch(
        modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'allProducts'],
            newValue: streamConfig.product_info,
          },
          {
            selector: [pageKey, 'plugins', id, 'currentProduct'],
            newValue: streamConfig?.product_info?.[0],
          },
          {
            selector: [pageKey, 'plugins', id, 'viewersMultipler'],
            newValue: 1,
          },
          {
            selector: [pageKey, 'plugins', id, 'viewersCount'],
            newValue: 1,
          },
        ]),
      );
    }
  }, [streamConfig]);

  const onRespondToComment = (userName: string) => {
    triggerEvent('onRespondToComment', {name: userName});
  };

  const onPollModalClose = () => {
    dispatch(modelUpdateAction([
      {
        selector: [pageKey, 'plugins', id, 'showPolls'],
        newValue: false
      }
    ]))
  }

  return (
    <View style={[layoutStyles, modelStyles]}>
      <View style={{height: '100%', width: '100%'}}>
        <View
          style={{
            height: '100%',
            width: '100%',
            flex: 1,
          }}>
          {showComments && (
            <CommentsView
              comments={comments}
              USER_TYPE={USER_TYPE}
              layoutContStyle={layoutContStyle}
              stylesContStyle={stylesContStyle}
              modelStyles={modelStyles}
              openLink={openLink}
              showHostTag={showHostTag}
              onRespondToComment={onRespondToComment}
            />
          )}
        </View>
      </View>
      {showPolls && (
        <PollsView
          activePoll={activePoll}
          onClose={onPollModalClose}
          handleVote={() => {}}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'streamConfig',
      props: {
        label: 'Stream Config',
      },
    },
    {
      type: 'codeInput',
      name: 'userName',
      props: {
        label: 'User Name',
      },
    },
    {
      type: 'codeInput',
      name: 'userid',
      props: {
        label: 'User Id',
      },
    },
    {
      type: 'checkbox',
      name: 'showComments',
      props: {
        label: 'Show Comments',
      },
    },
    {
      type: 'checkbox',
      name: 'showHostTag',
      props: {
        label: 'Show Host Tag',
      },
    },
    {
      type: 'radioGroup',
      name: 'zegoStreamResourceMode',
      props: {
        label: 'Zego Stream Resource Mode',
        options: [
          {text: 'Default', value: ZegoStreamResourceMode.Default},
          {text: 'OnlyCDN', value: ZegoStreamResourceMode.OnlyCDN},
          {text: 'OnlyRTC', value: ZegoStreamResourceMode.OnlyRTC},
          {text: 'OnlyL3', value: ZegoStreamResourceMode.OnlyL3},
          {text: 'CDNPlus', value: ZegoStreamResourceMode.CDNPlus},
        ],
        disableBinding: true,
      },
    },
    ...defaultEditors.basic,
  ],
  advanced: [
    {
      type: 'radioGroup',
      name: 'reactionsAnimation',
      props: {
        label: 'Reactions Animation',
        options: [
          {text: 'Single', value: 'single'},
          {text: 'Multiple', value: 'multiple'},
        ],
        disableBinding: true,
      },
    },
    ...Object.keys(layoutConts)
      .map((e: string) => {
        return [
          {
            type: 'editorSectionHeader',
            name: '',
            props: {
              label: layoutConts[e],
            },
          },
          ...allLayoutEditors.map(f => ({
            ...f,
            name: `${e}-${f.name}`,
          })),
        ];
      })
      .flat(),
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
  onCommentSent: {
    type: EventTriggerIdentifier,
  },
  onLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onProductLinkOpen: {
    type: EventTriggerIdentifier,
  },
  onCollectionLinkOpen: {
    type: EventTriggerIdentifier,
  },
  hideHeart: {
    type: EventTriggerIdentifier,
  },
  hideCard: {
    type: EventTriggerIdentifier,
  },
  sendComment: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendComment;
    },
    actionMetadata: {
      editableInputParams: {
        comment: '',
        bannedUser: '',
      },
    },
  },
  startPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return startPip;
    },
  },
  endPip: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return endPip;
    },
  },
  sendReactions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendReactions;
    },
    actionMetadata: {
      editableInputParams: {
        reactionUrl: '',
      },
    },
  },
  sendAddToCartReactions: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return sendAddToCartReactions;
    },
    actionMetadata: {
      editableInputParams: {
        productTitle: '',
      },
    },
  },
  onReactionLimitReached: {
    type: EventTriggerIdentifier,
  },
  onRespondToComment: {
    type: EventTriggerIdentifier,
  },
  onPollOpen: {
    type: EventTriggerIdentifier
  }
};
const emptyOnupdate = null;

export default connectWidget(
  'LivelyLiveSellingWidgetV2',
  Lively,
  LivelyLiveSellingWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(LivelyLiveSellingWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: [],
  },
);
