import {MaterialCommunityIcons} from 'apptile-core';
import React, {useState, forwardRef, useImperativeHandle, useCallback, useRef} from 'react';
import {StyleSheet, View, Text, Animated, Easing, Dimensions} from 'react-native';
import throttle from 'lodash/throttle';
import {Platform} from 'react-native';

const AddToCartReactionCards = forwardRef((props, ref) => {
  const {dimensions} = props;
  const [emojis, setEmojis] = useState([]);

  const addEmoji = useCallback((productTitle: string, userName: string) => {
    if (!productTitle) {
      console.warn('Product title and user name are required to add an emoji.');
      return;
    }
    const emoji = {
      id: Math.random().toString(),
      animationValue: new Animated.Value(0),
      reactionImage: productTitle,
      userName,
    };

    setEmojis(prevEmojis => [...prevEmojis, emoji]);

    Animated.timing(emoji.animationValue, {
      toValue: 1,
      duration: 2500,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start(() => {
      setEmojis(prevEmojis => prevEmojis.filter(e => e.id !== emoji.id));
    });
  }, []);

  // Use throttle instead of debounce
  const throttledAddEmoji = useRef(
    throttle(
      (productTitle: string, userName: string) => {
        addEmoji(productTitle, userName);
      },
      1000,
      {leading: true, trailing: false},
    ), // show instantly, then ignore next 1s
  ).current;

  useImperativeHandle(ref, () => ({
    triggerAddCard: (productTitle: string, userName: string) => {
      throttledAddEmoji(productTitle, userName);
    },
  }));

  return (
    <View style={[styles.container, {position: 'absolute'}]}>
      <View style={{...styles.container}}>
        {emojis.map(emoji => (
          <Animated.View
            key={emoji.id}
            style={[
              styles.emoji,
              {
                top: 150,
                opacity: emoji.animationValue.interpolate({
                  inputRange: [0, 0.8, 1],
                  outputRange: [1, 1, 0],
                }),
              },
            ]}>
            {Platform.OS === 'web' && (
              <View style={{...styles.card}}>
                <Text numberOfLines={1} ellipsizeMode="tail" style={styles.cardText}>
                  <MaterialCommunityIcons name="cart-plus" size={12} color="#ffffff" style={{paddingRight: 5}} />
                  {emoji.userName
                    .split(' ')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                    .join(' ')}{' '}
                  added <Text style={{color: '#F9DC00', marginLeft: 5}}>{emoji.reactionImage}</Text>
                </Text>
              </View>
            )}
          </Animated.View>
        ))}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  emoji: {
    position: 'absolute',
    zIndex: 1,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
  },
  card: {
    overflow: 'hidden',
    maxWidth: '80%',
    marginRight: 10,
  },
  cardText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'right',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.33)',
    display: 'flex',
    alignSelf: 'flex-end',
  },
});

export default AddToCartReactionCards;
