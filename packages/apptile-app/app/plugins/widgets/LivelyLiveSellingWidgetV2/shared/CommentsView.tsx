import {LayoutRecord, useTheme} from 'apptile-core';
import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, ScrollView, Pressable} from 'react-native';

const styles = StyleSheet.create({
  iconWrapper: {
    position: 'absolute',
    zIndex: 9,
    top: 0,
    right: 8,
    height: 28,
    width: 28,
    borderRadius: 28,
    backgroundColor: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    fontSize: 20,
    color: 'black',
  },
});

const CommentsView = React.forwardRef((props: any, scrollViewRef) => {
  const {comments, USER_TYPE, layoutContStyle, stylesContStyle, modelStyles, openLink, showHostTag, onRespondToComment} = props;
  const commentsLayout = new LayoutRecord(layoutContStyle?.comments ?? {});
  const commentItemLayout = new LayoutRecord(layoutContStyle?.commentItem ?? {});
  const hostCommentItemLayout = new LayoutRecord(layoutContStyle?.hostCommentItem ?? {});
  const commentsLayoutStyles = commentsLayout ? commentsLayout.getFlexProperties() : {flex: 1};
  const commentItemLayoutStyles = commentItemLayout ? commentItemLayout.getFlexProperties() : {flex: 1};
  const hostCommentItemLayoutStyles = hostCommentItemLayout ? hostCommentItemLayout.getFlexProperties() : {flex: 1};
  const commentsModelStyles = stylesContStyle?.comments ?? {};
  const commentItemModelStyles = stylesContStyle?.commentItem ?? {};
  const hostCommentItemModelStyles = stylesContStyle?.hostCommentItem ?? {};
  const {
    hostCommentsColor,
    commentsColor,
    hostCommentsHeadingColor,
    commentsHeadingColor,
    hostCommentsTypography,
    commentsTypography,
    hostCommentsHeadingTypography,
    commentsHeadingTypography,
  } = modelStyles;
  const [imagesLoading, setImagesLoading] = useState(comments.map(() => true));
  const {themeEvaluator} = useTheme();
  const primaryColor = themeEvaluator('colors.primary');
  const onPrimaryColor = themeEvaluator('colors.onPrimary');
  const bodyTypo = themeEvaluator('typography.body');

  return (
    <ScrollView
      contentContainerStyle={_.pick(commentsLayoutStyles, [
        'flexDirection',
        'justifyContent',
        'alignItems',
        'alignContent',
      ])}
      ref={scrollViewRef}
      onContentSizeChange={() => {
        if (commentsLayoutStyles.flexDirection == 'column-reverse') scrollViewRef?.current?.scrollTo(0, 0, true);
        else scrollViewRef?.current?.scrollToEnd({animated: true});
      }}
      style={[
        _.omit(commentsLayoutStyles, ['flexDirection', 'justifyContent', 'alignItems', 'alignContent']),
        commentsModelStyles,
      ]}>
      {comments.map((comment, i) => {
        const commentsUrl =
          comment.text.match(
            /(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?/gim,
          ) || [];
        const commentParts = comment.text
          .split(
            /(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?/gim,
          )
          .reduce((a, c, i) => {
            return [...a, {type: 'text', text: c}, {type: 'url', text: commentsUrl[i]}].filter(e => e?.text);
          }, []);
        return (
          <View
            contentContainerStyle={_.pick(
              comment.source == USER_TYPE.HOST ? hostCommentItemLayoutStyles : commentItemLayoutStyles,
              ['flexDirection', 'justifyContent', 'alignItems', 'alignContent'],
            )}
            style={[
              _.omit(comment.source == USER_TYPE.HOST ? hostCommentItemLayoutStyles : commentItemLayoutStyles, [
                'flexDirection',
                'justifyContent',
                'alignItems',
                'alignContent',
              ]),
              comment.source == USER_TYPE.HOST ? hostCommentItemModelStyles : commentItemModelStyles,
              {flexDirection: 'row'},
            ]}
            key={`comment-${i}`}>
            <View
              style={{
                alignItems:
                  (comment.source == USER_TYPE.HOST ? hostCommentItemLayoutStyles : commentItemLayoutStyles)
                    ?.alignSelf || 'flex-start',
              }}>
              <View style={{flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                <Text
                  onPress={() => {
                    onRespondToComment(comment?.userName);
                  }}
                  style={[
                    {
                      color: comment.source == USER_TYPE.HOST ? hostCommentsHeadingColor : commentsHeadingColor,
                    },
                    comment.source == USER_TYPE.HOST ? hostCommentsHeadingTypography : commentsHeadingTypography,
                  ]}>
                  {comment?.userName}{' '}
                </Text>
                {comment.source == USER_TYPE.HOST && showHostTag && (
                  <View
                    style={{
                      backgroundColor: primaryColor,
                      paddingHorizontal: 4,
                      borderRadius: 5,
                      paddingVertical: 0,
                    }}>
                    <Text
                      style={{
                        color: onPrimaryColor,
                        ...bodyTypo,
                      }}>
                      Host
                    </Text>
                  </View>
                )}
              </View>
              <View>
                <Text
                  style={[
                    {
                      color: comment.source == USER_TYPE.HOST ? hostCommentsColor : commentsColor,
                      wordBreak: 'break-all',
                    },
                    comment.source == USER_TYPE.HOST ? hostCommentsTypography : commentsTypography,
                  ]}>
                  {commentParts.map((part, i) =>
                    part.type == 'text' || comment.source != USER_TYPE.HOST ? (
                      part.text
                    ) : (
                      <Text
                        onPress={() => {
                          openLink(part.text);
                        }}
                        style={[
                          {color: '#1b88ff', borderBottomWidth: 0.5, borderBottomColor: '#1b88ff'},
                          comment.source == USER_TYPE.HOST ? hostCommentsTypography : commentsTypography,
                        ]}>
                        {part.text}
                      </Text>
                    ),
                  )}
                </Text>
              </View>
            </View>
          </View>
        );
      })}
    </ScrollView>
  );
});

export default CommentsView;
