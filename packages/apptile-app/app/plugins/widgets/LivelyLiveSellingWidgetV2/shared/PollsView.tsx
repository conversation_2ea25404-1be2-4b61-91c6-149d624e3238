import React, { useCallback } from 'react'
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native'
import {CurrentScreenContext, CustomModal, selectAppConfig, useTheme, getOptimalImageSize, LivelyApi} from 'apptile-core';
import { Portal } from '@gorhom/portal';
import { useSelector } from 'react-redux';

interface PollViewProps {
  pageKey?: string
  activePoll: any;
  onClose: () => void;
  brandLogoAssetId?: string;
  viewerId?: string;
  roomId?: string;
  handleVote: (optionId: string) => void;
  votedOptionId?: any;
  viewersMultiplier: number;
}

const PollsView = ({ pageKey, activePoll, onClose, brandLogoAssetId, viewerId, roomId, handleVote, votedOptionId, viewersMultiplier }: PollViewProps) => {
  const {themeEvaluator} = useTheme();
  const headingTypo = themeEvaluator('typography.heading');
  const subHeadingTypo = themeEvaluator('typography.subHeading');
  const bodyTypo = themeEvaluator('typography.body');
  const appConfig = useSelector(selectAppConfig);
  const getDeviceImage = useCallback(
    (assetId: string) => {
      const imageRecord = appConfig.getImageId(assetId);

      const getOptimalImage = (layoutSize: string) => {
        return getOptimalImageSize(imageRecord, layoutSize);
      };
      return {imageRecord, getOptimalImage};
    },
    [appConfig],
  );

  const {getOptimalImage} = getDeviceImage(brandLogoAssetId);
  const brandLogoUrl = getOptimalImage(`700x700`)?.fileUrl;

  return (
    <CurrentScreenContext.Consumer>
      {screen => (
        <Portal hostName={screen?.isModal ? pageKey : 'root'}>
          <CustomModal position='bottom' onClose={onClose} isDismissible={true}>
            <View style={[styles.modalWrapper]}>
              <View style={[styles.titleContainer]}>
                <Text style={{...headingTypo, color: '#000', fontSize: 18, textAlign: 'center'}}>Poll</Text>
              </View>

              <View style={[styles.bodyContainer]}>
                {Platform.OS == 'web' ? (
                  <View style={[styles.pollImg, {justifyContent: 'center', alignItems: 'center'}]}>
                    <Text style={{...bodyTypo, color: '#8B8B8B', fontSize: 10, textAlign: 'center'}}>Brand Logo</Text>
                  </View>
                ): (
                  <Image 
                    source={{uri: brandLogoUrl}}
                    style={styles.pollImg}
                    resizeMode='contain'
                  />
                )}

                <View style={[styles.pollInfo]}>
                  <View style={[styles.pollQuestion]}>
                    <Text style={{...subHeadingTypo, color: '#000'}}>{activePoll?.question}</Text>
                  </View>
                  <View style={[styles.pollOptions]}>
                    {activePoll?.options?.map((option: any, index: number) => (
                      <Pressable 
                        key={index} 
                        disabled={!!votedOptionId || Platform.OS == 'web'}
                        style={[styles.pollOptionWrapper]}
                        onPress={() => handleVote(option.optionId)}
                      >
                        <View 
                          style={[ 
                            styles.progressContainer,
                            {
                              backgroundColor: option.optionId == votedOptionId ? '#1060E033' : '#E8E6E6',
                              width: votedOptionId && activePoll?.pollResults?.options.find(o => o.optionId == option.optionId)?.votes > 0 ? activePoll?.pollResults?.options.find(o => o.optionId == option.optionId)?.percentage : 0
                            }
                          ]}
                        />
                        <View style={[styles.pollOptionText]}>
                          <Text 
                            style={{
                              ...bodyTypo,
                              color: option.optionId == votedOptionId ? '#1060E0' : '#000',
                              flex: 1,
                              paddingRight: 14
                            }}
                            numberOfLines={3}
                          >
                            {option.text}
                          </Text>
                          {votedOptionId && (
                            <Text style={{...bodyTypo, color: '#000'}}>{activePoll?.pollResults?.options.find(o => o.optionId == option.optionId)?.percentage}</Text>
                          )}
                        </View>
                      </Pressable>
                    ))}
                  </View>
                  <View style={[styles.pollTotalVotes]}>
                    <Text style={{...bodyTypo, color: '#8B8B8B'}}>
                    {activePoll?.pollResults?.total_votes === 0 || !votedOptionId ? "Vote to see results  •  " : ""} {viewersMultiplier * activePoll?.pollResults?.total_votes} votes
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </CustomModal>
        </Portal>
      )}
    </CurrentScreenContext.Consumer>
  )
}

export default PollsView

const styles = StyleSheet.create({
  modalWrapper: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    flex: 1
  },
  titleContainer: {
    marginTop: 24,
  },
  bodyContainer: {
    padding: 24,
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1
  },
  pollImg: {
    height: 50,
    width: 50,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: '#F6F6F6',
  },
  pollInfo: {
    marginLeft: 10,
    flexDirection: 'column',
    alignItems: 'flex-start',
    flex: 1,
  },
  pollQuestion: {
    flex: 1,
    padding: 10
  },
  pollOptions: {
    width: '100%'
  },
  pollOptionWrapper: {
    marginTop: 14,
    backgroundColor: '#F6F6F6',
    borderRadius: 8,
  },
  pollOptionText: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10
  },
  progressContainer: {
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 8
  },
  pollTotalVotes: {
    marginTop: 20,
    marginBottom: 10,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
})