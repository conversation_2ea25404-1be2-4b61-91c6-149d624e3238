import {getOptimalImageSize, selectAppConfig} from 'apptile-core';
import React, {useState, forwardRef, useImperativeHandle, useCallback} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, Animated, Easing, Dimensions, Image, Platform} from 'react-native';
import {useSelector} from 'react-redux';

const ReactionEmojis = forwardRef((props: any, ref) => {
  const {dimensions, reactionsAnimation} = props;
  let emojiCount = reactionsAnimation == 'single' ? 1 : 10;
  let emojiDuration = reactionsAnimation == 'single' ? 12000 : 4000;

  const [emojis, setEmojis] = useState([]);
  let screenWidth = dimensions?.width ?? Dimensions.get('window').width;
  let screenHeight = dimensions?.height ?? Dimensions.get('window').height;

  const generateRandomPosition = () => {
    return Math.random() * (screenWidth - 50); // Random horizontal position
  };
  const appConfig = useSelector(selectAppConfig);

  const getDeviceImage = useCallback(
    (assetId: string) => {
      const imageRecord = appConfig.getImageId(assetId);

      const getOptimalImage = (layoutSize: string) => {
        return getOptimalImageSize(imageRecord, layoutSize);
      };
      return {imageRecord, getOptimalImage};
    },
    [appConfig],
  );
  const addEmoji = async (reactionUrl: string, userName: string) => {
    // Just in case, the reactionUrl is an asset
    if (reactionUrl.startsWith('asset:') && Platform.OS != 'web') {
      const reactionAssetId = reactionUrl.replace('asset:', '');
      const {getOptimalImage} = getDeviceImage(reactionAssetId);
      reactionUrl = getOptimalImage(`700x700`)?.fileUrl;
    }
    try {
      await Image.prefetch(reactionUrl);
    } catch (error) {
      console.log(error);
      return;
    }
    const newEmojis = Array.from({length: emojiCount}, () => {
      const animationValue = new Animated.Value(0);
      const randomLeft = generateRandomPosition();
      const randomBottom = Math.random() * 30;
      const emojiId = Math.random().toString();
      return {
        id: emojiId,
        animationValue,
        left: randomLeft,
        bottom: randomBottom,
        reactionImage: reactionUrl,
        userName,
      };
    });

    setEmojis(prevEmojis => [...prevEmojis, ...newEmojis]);

    newEmojis.forEach(emoji => {
      Animated.timing(emoji.animationValue, {
        toValue: 1,
        duration: emojiDuration,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start(() => {
        setEmojis(prevEmojis => prevEmojis.filter(e => e.id !== emoji.id));
      });
    });
  };

  useImperativeHandle(ref, () => ({
    triggerAddEmoji: addEmoji,
  }));

  return (
    <View style={[styles.container, {position: 'absolute'}]}>
      <View style={[styles.container]}>

        {emojis.map(emoji => (
          <Animated.View
            key={emoji.id}
            style={[
              styles.emoji,
              {
                left: emoji.left,
                bottom: `${emoji.bottom}%`, // Apply random starting height
                transform: [
                  {
                    translateY: emoji.animationValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -screenHeight], // Move from bottom to top
                    }),
                  },
                ],
                opacity: emoji.animationValue.interpolate({
                  inputRange: [0, 0.8, 1],
                  outputRange: [1, 1, 0], // Fade out at the end
                }),
              },
            ]}>
            <Image source={{uri: emoji.reactionImage}} style={{width: 50, height: 50}} />
            {reactionsAnimation == 'single' && (
              <View style={{...styles.card}}>
                <Text numberOfLines={1} ellipsizeMode="tail" style={styles.cardText}>
                  {emoji.userName}
                </Text>
              </View>
            )}
          </Animated.View>
        ))}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  emoji: {
    position: 'absolute',
    bottom: 50,
    zIndex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emojiText: {
    fontSize: 32,
  },
  emojiWeb: {
    position: 'absolute',
    zIndex: 1,
    width: '100%',
    display: 'flex',
    alignItems: 'flex-end',
    alignContent: 'center',
  },
  card: {
    maxWidth: 100,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    borderRadius: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.33)',
  },
  cardText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#ffffff',
    textAlign: 'center',
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
});

export default ReactionEmojis;
