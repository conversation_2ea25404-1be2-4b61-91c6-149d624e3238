import {LivelyStories} from 'lively-feeds-rn';
import _ from 'lodash';
import React from 'react';
import {View, Text} from 'react-native';

export interface LivelyStoriesConfig {
  themeCode: number;
  widgetId: string;
}
export interface LivelyStoriesControlProps {
  brandId: string;
  onCallback: () => void;
  config: LivelyStoriesConfig;
}

const LivelyStoriesControl: React.FC<LivelyStoriesControlProps> = props => {
  const {brandId, onCallback, config} = props;
  if (_.isEmpty(brandId)) {
    return (
      <View>
        <Text>BrandId missing</Text>
      </View>
    );
  }
  return <LivelyStories livelyBrandId={brandId} callBacks={onCallback} config={config} />;
};

export default LivelyStoriesControl;
