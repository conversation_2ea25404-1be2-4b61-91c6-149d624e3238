import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {View, Text} from 'react-native';
import {LivelyStoriesConfig} from './LivelyStoriesControl';
export interface LivelyStoriesControlProps {
  brandId: string;
  config: LivelyStoriesConfig;
}
const LivelyStoriesControl: React.FC<LivelyStoriesControlProps> = props => {
  const {brandId, config} = props;
  const {widgetId} = config;
  useEffect(() => {
    window.reloadLively();
  }, []);

  if (_.isEmpty(brandId)) {
    return (
      <View>
        <Text>BrandId missing</Text>
      </View>
    );
  }
  return (
    <div className="lively-stories-widget" style={{width: '100%', minHeight: 130}}>
      <div className="lively-script-placeholder" />
      <div
        className="render_lively_story_plugin"
        brand_id={brandId}
        flow="shopify"
        wid_id={widgetId}
        style={{zIndex: 1000}}
      />
    </div>
  );
};
export default LivelyStoriesControl;
