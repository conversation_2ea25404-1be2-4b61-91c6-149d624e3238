import {createDeepEqualSelector} from 'apptile-core';
import {datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback, useEffect} from 'react';
import {View} from 'react-native';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import LivelyStoriesControl from './LivelyStoriesControl';
import docs from './docs';
import {useSelector} from 'react-redux';

type LivelyStoriesWidgetConfigType = {
  value: string;
  productHandle: string;
};

const LivelyStoriesWidgetConfig: LivelyStoriesWidgetConfigType = {
  value: '',
  productHandle: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'LivelyStoriesWidget',
  type: 'widget',
  name: 'Lively Stories Widget',
  description: 'Load Lively Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = (state: any) => datasourceTypeModelSel(state, 'livelyShoppable') || datasourceTypeModelSel(state, 'lively');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate} = props;

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const themeCode = model.get('themeCode');
  const widgetId = model.get('widgetId');
  const handleChange = useCallback(
    (value: any) => {
      modelUpdate([{selector: ['productHandle'], newValue: value}]);
      setTimeout(() => triggerEvent('onNavigateToProductTap'), 100);
    },
    [modelUpdate, triggerEvent],
  );
  const debouncedHandleChange = useCallback(debounce(handleChange, 340), [handleChange]);

  const onCallback = (data: {data: {product_url: string; id: string}}) => {
    const {product_url: url} = data?.data ?? {};
    const productHandle = _.isEmpty(url) ? null : _.last(_.split(url, '/'));
    if (!_.isEmpty(productHandle)) {
      debouncedHandleChange(productHandle);
    } else {
      console.log(`Could not trigger onNavigateToProductTap as productHandle is null ${productHandle}`);
    }
  };
  const defaultConfig = {
    themeCode: themeCode,
    widgetId: widgetId,
  };
  return (
    <View style={[layoutStyles, modelStyles]} ref={ref}>
      <LivelyStoriesControl brandId={brandId} key={brandId} onCallback={onCallback} config={defaultConfig} />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'widgetId',
      props: {
        label: 'Widget Id',
      },
    },
    {
      type: 'codeInput',
      name: 'themeCode',
      props: {
        label: 'themeCode',
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onNavigateToProductTap: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget('LivelyStoriesWidget', Lively, LivelyStoriesWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
