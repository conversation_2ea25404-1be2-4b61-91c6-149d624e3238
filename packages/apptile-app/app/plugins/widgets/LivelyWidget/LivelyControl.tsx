import {LivelyShoppableFeeds} from 'lively-feeds-rn';
import _ from 'lodash';
import React from 'react';
import {IApptileCartDetails} from '.';

export interface ILivelyCartLineItem {
  quantity: number;
  variant_id: number;
  product_title: string;
  line_price: number;
  image: string;
}

export interface ILivelyCartDetails {
  items: ILivelyCartLineItem[];
  items_subtotal_price: number;
}

export interface LivelyShoppableFeedsConfig {
  showCart: boolean;
  themeCode: number;
  widgetId: string;
}
export interface LivelyControlProps {
  brandId: string;
  cartDetails: IApptileCartDetails;
  onCartUpdate: (type: any, data: any) => any;
  onShopNow: () => void;
  onCallback: () => void;
  isLoading: boolean;
  config: LivelyShoppableFeedsConfig;
}

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

const getTransformedCartItems = (cartDetails: IApptileCartDetails): ILivelyCartDetails => {
  const currentCartLines = cartDetails?.lines;

  if (_.isEmpty(currentCartLines)) {
    return {
      items: [],
      items_subtotal_price: 0,
    };
  }

  const cartLineItems = _.map(currentCartLines, currentCartLine => {
    const lineItemPrice = _.get(currentCartLine, 'variant.salePrice')
      ? _.get(currentCartLine, 'variant.salePrice') * _.get(currentCartLine, 'quantity') * 100
      : 0;
    return {
      quantity: _.get(currentCartLine, 'quantity'),
      variant_id: _.get(currentCartLine, 'variant.id')
        ? resolveNumericVariantId(_.get(currentCartLine, 'variant.id'))
        : 0,
      product_title: _.get(currentCartLine, 'variant.product.title'),
      line_price: lineItemPrice,
      image: _.get(currentCartLine, 'variant.featuredImage'),
    };
  });

  return {
    items: _.filter(cartLineItems, cartLine => cartLine?.quantity > 0),
    items_subtotal_price: _.get(cartDetails, 'subtotalAmount') * 100,
  };
};

const LivelyControl: React.FC<LivelyControlProps> = props => {
  const {brandId, onCartUpdate, cartDetails, onShopNow, isLoading, onCallback, config} = props;
  return (
    <>
      {!!brandId && (
        <LivelyShoppableFeeds
          livelyBrandId={brandId}
          onCartUpdate={onCartUpdate}
          cartDetails={getTransformedCartItems(cartDetails)}
          onShopNow={onShopNow}
          callBacks={onCallback}
          isLoading={isLoading}
          config={config}
        />
      )}
    </>
  );
};

export default LivelyControl;
