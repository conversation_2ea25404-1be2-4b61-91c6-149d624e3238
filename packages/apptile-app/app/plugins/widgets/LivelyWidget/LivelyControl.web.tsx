import React, {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import _ from 'lodash';
import {LivelyShoppableFeedsConfig} from './LivelyControl';

export interface LivelyControlProps {
  brandId: string;
  config: LivelyShoppableFeedsConfig;
}

const LivelyControl: React.FC<LivelyControlProps> = props => {
  const {brandId, config} = props;
  const {widgetId} = config;
  useEffect(() => {
    window.reloadShoppableFeeds();
  }, []);

  if (_.isEmpty(brandId)) {
    return (
      <View>
        <Text>BrandId missing</Text>
      </View>
    );
  }
  return (
    <>
      <div
        className="render_lively_html_content"
        brand_id={brandId}
        wid_id={widgetId}
        flow="w3rt5"
        style={{zIndex: 1000}}
      />
    </>
  );
};

export default LivelyControl;
