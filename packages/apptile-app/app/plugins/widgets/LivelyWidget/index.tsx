import {createDeepEqualSelector} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {datasourceByIdModelSel, datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../../datasource/ShopifyV_22_10/actions/checkoutAction';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import LivelyControl from './LivelyControl';
import docs from './docs';

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

export const resolveVariantGid = (variantId: string | number): string => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return `${variantId}`;
  }
  return `${SHOPIFY_PV_GID_PREFIX}` + variantId;
};

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyWidgetConfigType = {
  value: string;
  productHandle: string;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
  onNavigateToProductTap: string;
};

const LivelyWidgetConfig: LivelyWidgetConfigType = {
  value: '',
  productHandle: '',
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
  onNavigateToProductTap: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'livelyWidget',
  type: 'widget',
  name: 'Lively Widget',
  description: 'Load Lively Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const livelyDSSel = (state: any) => datasourceTypeModelSel(state, 'livelyShoppable') || datasourceTypeModelSel(state, 'lively');
const livelyModelSel = createDeepEqualSelector(
  createSelector(livelyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      brandId: dsModel?.get('brandId'),
    };
  }),
  s => s,
);

const shopifyDSSel = (state: any) => datasourceTypeModelSel(state, 'shopifyV_22_10');

const makeShopifyConfigSelector = (pluginId: string) => {
  const shopifyDSConfigSel = state => selectPluginConfig(state, null, pluginId);
  return shopifyDSConfigSel;
};

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate} = props;

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {brandId} = useSelector(livelyModelSel);
  const cartDetails = model.get('cartDetails');
  const showCart = model.get('showCart');
  const themeCode = model.get('themeCode');
  const widgetId = model.get('widgetId');

  const shopifyDatasourceId = 'shopify';

  const shopifyDsModel = useSelector(shopifyDSSel);
  const shopifyDsConfigSel = makeShopifyConfigSelector(shopifyDsModel?.get('id'));
  const shopifyDsConfig = useSelector(shopifyDsConfigSel);

  const shopifyLineItemIncreaseAction = shopifyDsModel?.get('increaseCartLineItemQuantity');
  const shopifyLineItemDecreaseAction = shopifyDsModel?.get('decreaseCartLineItemQuantity');
  const isLoading = shopifyDsModel?.get('syncingCartStatus');
  const dispatch = useDispatch();

  const increaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemIncreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const decreaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemDecreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const onCartUpdateHandler = (type: string, data: {quantity: number; id: string}) => {
    if (!data?.id) {
      console.error(`No variant Id available data ${JSON.stringify(data, null, 2)}`);
    }

    if (type === 'add') {
      //increase qty
      increaseLineItem(data?.id);
    } else if (type === 'update') {
      if (data?.quantity < 0) {
        // decrease qty
        decreaseLineItem(data?.id);
      } else {
        //increase qty
        increaseLineItem(data?.id);
      }
    }
  };

  const handleChange = useCallback(
    (value: any) => {
      modelUpdate([{selector: ['productHandle'], newValue: value}]);
      setTimeout(() => triggerEvent('onNavigateToProductTap'), 100);
    },
    [modelUpdate, triggerEvent],
  );
  const debouncedHandleChange = useCallback(debounce(handleChange, 340), [handleChange]);

  const onCallback = (event, data: {url: string; id: string}) => {
    const {url} = data ?? {};
    const productHandle = _.isEmpty(url) ? null : _.last(_.split(url, '/'));
    if (!_.isEmpty(productHandle)) {
      debouncedHandleChange(productHandle);
    } else {
      console.log(`Could not trigger onNavigateToProductTap as productHandle is null ${productHandle}`);
    }
  };
  const defaultConfig = {
    showCart: showCart,
    themeCode: themeCode,
    widgetId: widgetId,
  };
  return (
    <View style={[layoutStyles, modelStyles]} ref={ref}>
      <LivelyControl
        brandId={brandId}
        onCartUpdate={onCartUpdateHandler}
        cartDetails={cartDetails}
        onCallback={onCallback}
        onShopNow={() => triggerEvent('onCheckoutTap')}
        isLoading={isLoading}
        config={defaultConfig}
      />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'widgetId',
      props: {
        label: 'Widget Id',
      },
    },
    {
      type: 'codeInput',
      name: 'themeCode',
      props: {
        label: 'themeCode',
      },
    },
    {
      type: 'codeInput',
      name: 'showCart',
      props: {
        label: 'Show Cart',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
  onNavigateToProductTap: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget('LivelyWidget', Lively, LivelyWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
