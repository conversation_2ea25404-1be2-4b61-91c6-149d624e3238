import React from 'react';

import {WidgetStyleEditorOptions} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {CurrentScreenContext} from 'apptile-core';
import {Portal} from '@gorhom/portal';
import {CustomModal, Position} from 'apptile-core';
import {ApptileFlexbox} from 'apptile-core';
import {renderWidgetTreeNode} from 'apptile-core';

const ModalWidgetConfig = {
  value: false,
  isDismissible: true,
  onClose: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'Modal',
  type: 'widget',
  name: 'Modal',
  description: 'Display Modal on screen with varying styles.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
  layout: {
    flex: 1,
    flexDirection: 'column',
  },
  icon: 'modal',
};

export const modelWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backdropColor',
    props: {
      label: 'Backdrop Color',
      placeholder: '#HexCode',
    },
  },
];

interface IProps {
  model: any;
  modelUpdate: any;
  triggerEvent: any;
}

const ModalWidget: React.FC<IProps> = React.forwardRef((props, ref) => {
  const position: Position = (props.model?.get('position') as Position) || 'bottom';

  return (
    <CurrentScreenContext.Consumer>
      {(screen: any) => (
        <Portal
          key={props.model.get('pageKey') || 'root'}
          hostName={screen.isModal ? (props.model.get('pageKey') as string) : 'root'}>
          {props.model?.get('value') ? (
            <CustomModal position={position} isDismissible = {props.model?.get('isDismissible')} onClose={() => props.triggerEvent('onClose') } model={props.model}>
              <ApptileFlexbox
                {...props}
                ref={ref}
                renderWidgetTreeNode={renderWidgetTreeNode({...props})}
                message="Start by dropping a container widget."
              />
            </CustomModal>
          ) : (
            <></>
          )}
        </Portal>
      )}
    </CurrentScreenContext.Consumer>
  );
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (_model, _renderedValue, _selector) => _renderedValue,
  },
  onClose: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<typeof ModalWidgetConfig> = {
  basic: [
    {
      type: 'checkbox',
      name: 'value',
      props: {
        label: 'Show Modal',
      },
    },
    {
      type: 'checkbox',
      name: 'isDismissible',
      props: {
        label: 'Is Dismissible',
      },
    },
  ],
  layout: [
    {
      type: 'radioGroup',
      name: 'position',
      props: {
        label: 'Position',
        options: ['top', 'center', 'bottom'],
      },
    },
  ],
};

export default connectWidget('ModalWidget', ModalWidget, ModalWidgetConfig, () => {}, editors, {
  propertySettings,
  widgetStyleConfig: modelWidgetStyleConfig,
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'modal'],
});
