import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import PazoSiteGroupReportControl from './PazoSiteGroupReportControl';
import {Calendar} from 'react-native-calendars';
import moment from 'moment';
import { PazoCalendar } from './PazoCalendar';

const styles = StyleSheet.create({
  headerBox: {
    backgroundColor: '#3C3050',
    padding: 10,
  },
  headerDateRow: {
    flexDirection: 'row',
  },
  headerDateBox: {
    padding: 8,
  },
  headerDateLabel: {
    fontSize: 8,
    color: '#FFF',
  },
  headerDateValue: {
    fontSize: 10,
    color: '#FFF',
  },
  headerStatsWell: {
    backgroundColor: '#544667',
    padding: 5,
    flexDirection: 'row',
    borderRadius: 5,
  },
  headerStatBox: {
    backgroundColor: '#3C3050',
    padding: 8,
    margin: 5,
    borderRadius: 5,
  },
  headerStatTitle: {
    color: '#FFF',
    fontSize: 10,
    paddingVertical: 5,
  },
  headerStatValue: {
    color: '#FFF',
    fontSize: 20,
  },
});

type DateUIState = '' | 'setStart' | 'setEnd';

const PazoReportControl: React.FC<any> = props => {
  const {siteGroups, totalSalesActiveStores, totalNoOfStores, fromDate, toDate} = props?.data;
  const [dateUIState, setDateUIState] = useState<DateUIState>('');
  const maxDate = moment().format('YYYY-MM-DD');
  const minDate = moment().subtract(90, 'days').format('YYYY-MM-DD');
  const [startDate, setStartDate] = useState(fromDate);
  const [endDate, setEndDate] = useState(toDate);
  const [markedDates, setMarkedDates] = useState({});

  const setDate = useCallback(
    (dateString: string) => {
      const dateToSet = moment(dateString, 'YYYY-MM-DD');
      if (dateUIState == 'setStart') {
        const currentEndDate = moment(endDate, 'DD-MM-YYYY');
        if (currentEndDate.isBefore(dateToSet)) {
          // Error
        } else {
          setStartDate(dateToSet.format('DD-MM-YYYY'));
        }
      }
      if (dateUIState == 'setEnd') {
        const currentStartDate = moment(startDate, 'DD-MM-YYYY');
        if (currentStartDate.isAfter(dateToSet)) {
          // Error
        } else {
          setEndDate(dateToSet.format('DD-MM-YYYY'));
        }
      }
      setDateUIState('');
    },
    [dateUIState, endDate, startDate],
  );

  useEffect(() => {
    const markedDatesObj = {};
    const startDateObj = moment(startDate, 'DD-MM-YYYY');
    const endDateObj = moment(endDate, 'DD-MM-YYYY');
    markedDatesObj[startDateObj.format('YYYY-MM-DD')] = {
      startingDay: true,
      color: '#50cebb',
      textColor: 'white',
    };
    markedDatesObj[endDateObj.format('YYYY-MM-DD')] = {
      selected: true,
      endingDay: true,
      color: '#50cebb',
      textColor: 'white',
    };
    let markedDateObj = startDateObj.add(1, 'days');
    while (markedDateObj.isBefore(endDateObj)) {
      markedDatesObj[markedDateObj.format('YYYY-MM-DD')] = {color: '#70d7c7', textColor: 'white'};
      markedDateObj = markedDateObj.add(1, 'days');
    }
    console.log(markedDatesObj);
    setMarkedDates(markedDatesObj);
  }, [endDate, startDate]);

  return (
    <View>
      <View style={styles.headerBox}>
        {dateUIState == '' ? (
          <View style={styles.headerDateRow}>
            <Pressable style={styles.headerDateBox} onPress={() => setDateUIState('setStart')}>
              <Text style={styles.headerDateLabel}>From date</Text>
              <Text style={styles.headerDateValue}>{startDate}</Text>
            </Pressable>
            <Pressable style={styles.headerDateBox} onPress={() => setDateUIState('setEnd')}>
              <Text style={styles.headerDateLabel}>To date</Text>
              <Text style={styles.headerDateValue}>{endDate}</Text>
            </Pressable>
          </View>
        ) : (
          <PazoCalendar
            maxDate={maxDate}
            minDate={minDate}
            enableSwipeMonths
            onDayPress={date => {
              console.log(date);
              setDate(date?.dateString);
            }}
            markingType={'period'}
            markedDates={markedDates}
          />
        )}
        <View style={styles.headerStatsWell}>
          <View style={styles.headerStatBox}>
            <Text style={styles.headerStatTitle}>Sales active stores</Text>
            <Text style={styles.headerStatValue}>{totalSalesActiveStores}</Text>
          </View>
          <View style={styles.headerStatBox}>
            <Text style={styles.headerStatTitle}>Total stores</Text>
            <Text style={styles.headerStatValue}>{totalNoOfStores}</Text>
          </View>
        </View>
      </View>
      {siteGroups?.map((siteGroup, idx) => {
        return <PazoSiteGroupReportControl siteGroup={siteGroup} key={idx} />;
      })}
    </View>
  );
};

export default PazoReportControl;
