import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import PazoSiteReportControl from './PazoSiteReportControl';

const styles = StyleSheet.create({
  groupContainer: {
    paddingHorizontal: 8,
    marginVertical: 8,
  },
  labelBox: {
    backgroundColor: '#EBEEF3',
    marginBottom: 8,
  },
});

const PazoSiteGroupReportControl: React.FC<any> = props => {
  const {sitesData, name} = props?.siteGroup;

  return (
    <View style={styles.groupContainer}>
      <View style={styles.labelBox}>
        <Text>{name}</Text>
      </View>
      {sitesData.map((siteData, idx) => {
        return <PazoSiteReportControl siteData={siteData} key={idx} />;
      })}
    </View>
  );
};

export default PazoSiteGroupReportControl;
