import moment from 'moment';
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
// import {getShadowStyle} from 'apptile-core';

const styles = StyleSheet.create({
  siteContainer: {
    flex: 1,
    backgroundColor: '#FFF',
    padding: 10,
    fontSize: 10,
    marginVertical: 4,
    // ...getShadowStyle(2),
  },
  labelBox: {
    padding: 10,
  },
  billDatesRow: {
    flexDirection: 'row',
    paddingVertical: 4,
    marginBottom: 8,
  },
  billDatesBox: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  billDatesText: {
    marginVertical: 4,
  },
  siteStatsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  siteStatBox: {
    backgroundColor: '#F5F5F5',
    flex: 1,
    flexShrink: 0,
    minWidth: '26%',
    maxWidth: '40%',
    height: 50,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    // ...getShadowStyle(2),
  },
  siteStatValue: {fontSize: 20, marginBottom: 4},
  siteStatLabel: {fontSize: 10},
  deptStatsBox: {
    flex: 1,
  },
  deptLabel: {fontSize: 12, marginVertical: 4},
});

const PazoSiteStatBox: React.FC<{title: string; value: any}> = props => {
  const {title, value} = props;
  return (
    <View style={styles.siteStatBox}>
      <Text style={styles.siteStatValue}>{value}</Text>
      <Text style={styles.siteStatLabel}>{title}</Text>
    </View>
  );
};

const PazoSiteReportControl: React.FC<any> = props => {
  const {
    _id,
    name,
    totalSalesQty,
    cashSales,
    otherSales,
    return: returns,
    voidBills,
    bankDeposit,
    firstBill,
    lastBill,
    depts,
  } = props?.siteData;

  return (
    <View style={styles.siteContainer}>
      <View style={styles.labelBox}>
        <Text>{name}</Text>
      </View>
      <View style={styles.billDatesRow}>
        <View style={styles.billDatesBox}>
          <Text>First Bill</Text>
          <View>
            <View>
              <Text>{moment(firstBill).format('DD-MM-YYYY')}</Text>
              <Text>{moment(firstBill).format('hh-mm')}</Text>
            </View>
          </View>
        </View>
        <View style={styles.billDatesBox}>
          <Text>Last Bill</Text>
          <View>
            <View>
              <Text>{moment(lastBill).format('DD-MM-YYYY')}</Text>
              <Text>{moment(lastBill).format('hh-mm')}</Text>
            </View>
          </View>
        </View>
      </View>
      <View style={styles.siteStatsRow}>
        <PazoSiteStatBox title="Total sales quantity" value={totalSalesQty} />
        <PazoSiteStatBox title="Cash sales" value={cashSales} />
        <PazoSiteStatBox title="Other sales" value={otherSales} />
        <PazoSiteStatBox title="Return" value={returns} />
        <PazoSiteStatBox title="Void bills" value={voidBills} />
        <PazoSiteStatBox title="Bank deposit" value={bankDeposit} />
      </View>
      {depts.map((dept, idx) => {
        return (
          <View style={styles.deptStatsBox} key={idx}>
            <Text style={styles.deptLabel}>{dept?.name}</Text>
            <View style={styles.siteStatsRow}>
              <PazoSiteStatBox title="Sales" value={dept?.sales} />
              <PazoSiteStatBox title="Quantity" value={dept?.quantity} />
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default PazoSiteReportControl;
