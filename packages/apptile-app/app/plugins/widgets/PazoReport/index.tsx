import React from 'react';

import docs from './docs';
// import {WidgetStyleEditorOptions} from 'apptile-core';
// import {PluginEditorsConfig} from 'apptile-core';
// import {connectWidget} from 'apptile-core';
// import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
// import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {View, Text, ActivityIndicator} from 'react-native';
import PazoReportControl from './PazoReportControl';
import {PluginListingSettings, PluginPropertySettings} from '../../plugin';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import {connectWidget} from 'apptile-core';

const PazoReportConfig = {
  value: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'PazoReport',
  type: 'widget',
  name: 'Pazo Report',
  description: 'Display Pazo Sales Report',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
  layout: {
    flex: 1,
    flexDirection: 'column',
  },
  icon: 'icon',
};

const widgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Color',
      placeholder: '#000000',
    },
  },
  {
    type: 'codeInput',
    name: 'fontSize',
    props: {
      label: 'Size',
      placeholder: '18',
    },
  },
];

interface IProps {
  model: any;
  modelStyles: any;
  config: any;
}

const PazoReport: React.FC<IProps> = React.forwardRef((props, ref: any) => {
  const {model, modelStyles, config} = props;
  const data = model?.get('value');

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(
    layout ? layout.getFlexProperties() : {flex: 1},
  );
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const {typography, ...styles} = modelPlatformStyles;
  return (
    <View ref={ref} style={[layoutStyles, styles]}>
      {data ? <PazoReportControl data={data} /> : <ActivityIndicator />}
    </View>
  );
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (_model, _renderedValue, _selector) => _renderedValue,
  },
};

const editors: PluginEditorsConfig<typeof PazoReportConfig> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'value',
        placeholder: '{{PazoQuery1.data}}',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
    },
  ],
};

export default connectWidget(
  'PazoReport',
  PazoReport,
  PazoReportConfig,
  null,
  editors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(widgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'text'],
  },
);
