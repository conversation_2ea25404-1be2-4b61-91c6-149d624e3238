/* eslint-disable react-native/no-inline-styles */
import React, {useCallback, useRef} from 'react';
import {StyleSheet, Pressable, Animated} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, hapticEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {MaterialCommunityIcons} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import {performHapticFeedback} from 'apptile-core';
import {makeBoolean} from 'apptile-core';
import {useTheme} from 'apptile-core';
import {usePlaceHolder} from 'apptile-core';
import {useIsEditable} from 'apptile-core';

const pillWidgetConfig = {
  value: '',
  type: 'without-cross',
  onRemove: '',
  onTap: '',
  enableHaptics: '',
  hapticMethod: '',
  isLoading: false,
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'pill',
  type: 'widget',
  name: 'Pill',
  description: 'Display pill with text',
  layout: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  section: 'Inputs',
  icon: 'pills',
};

export const pillWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },

  {
    type: 'colorInput',
    name: 'crossColor',
    props: {
      label: 'Cross color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'codeInput',
    name: 'crossSize',
    props: {
      label: 'Cross Size',
      placeholder: '0',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'borderRadius',
    props: {
      label: 'Border Radius',
      placeholder: '0',
      options: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
      layout: 'square',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'margin',
    props: {
      label: 'Margin',
      placeholder: '0',
      options: ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'padding',
    props: {
      label: 'Padding',
      placeholder: '0',
      options: ['paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft'],
    },
  },
];

const PillWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, triggerEvent, config} = props;
  const Placeholder = usePlaceHolder();

  const enableHaptics = model.get('enableHaptics');
  const hapticMethod = model.get('hapticMethod');
  const value = model.get('value')?.toString();
  const type = model.get('type')?.toString();
  const layout = config.get('layout');
  const isLoading = !!model.get('isLoading');

  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const {typography, ...restModelPlatformStyles} = modelPlatformStyles;

  const handleClick = useCallback(() => {
    if (enableHaptics) performHapticFeedback(hapticMethod);
    triggerEvent('onTap');
  }, [enableHaptics, hapticMethod, triggerEvent]);

  const handleCancel = useCallback(() => {
    if (enableHaptics) performHapticFeedback(hapticMethod);
    triggerEvent('onRemove');
  }, [enableHaptics, hapticMethod, triggerEvent]);

  const {themeEvaluator} = useTheme();
  // const pressedScale = themeEvaluator('tile.pressable.pressed.scale');

  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim,
      {
        toValue: 1.5,
        useNativeDriver: true
      }
    ).start();
  }

  const handlePressOut = () => {
    Animated.spring(scaleAnim,
      {
        toValue: 1,
        useNativeDriver: true
      }
    ).start();
    handleClick();
  }

  return !isLoading ? (
    <Animated.View
      style={[
        styles.container, 
        layoutStyles, 
        restModelPlatformStyles,
        { transform: [{scale: scaleAnim}] }
      ]}
      ref={ref}
    >
        <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut}>
          <Animated.Text
            style={[
              styles.text,
              typography,
              {
                color: modelPlatformStyles.color,
              },
            ]}>
            {value ? value : ''}
          </Animated.Text>
        </Pressable>
      {type === 'with-cross' && (
          <Pressable onPress={handleCancel}>
            <Animated.View 
              style={[styles.crossContainer]} 
            >
              <MaterialCommunityIcons
                name="close"
                size={modelPlatformStyles.crossSize}
                color={modelPlatformStyles.crossColor}
              />
            </Animated.View>
          </Pressable>
      )}
    </Animated.View>
  ) : (
    <Placeholder layoutStyles={{...layoutStyles, ...restModelPlatformStyles, minHeight: 35}} />
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'radioGroup',
      name: 'type',
      props: {
        label: 'Type',
        options: ['with-cross', 'without-cross'],
      },
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      props: {
        label: 'Loading State',
      },
    },
    ...defaultEditors.basic,
  ],
  advanced: hapticEditors.advanced,
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  onTap: {
    type: EventTriggerIdentifier,
  },
  onRemove: {
    type: EventTriggerIdentifier,
  },
  isLoading: {
    getValue: (model, val, _) => {
      return makeBoolean(val);
    },
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  text: {
    marginHorizontal: 6,
  },
  crossContainer: {
    marginRight: 2,
  },
  container: {
    minWidth: 50,
  },
});

export default connectWidget('PillWidget', PillWidget, pillWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(pillWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'pill'],
});
