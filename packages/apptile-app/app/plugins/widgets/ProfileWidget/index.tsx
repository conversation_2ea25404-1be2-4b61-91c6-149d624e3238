/* eslint-disable react-native/no-inline-styles */
import { LocalStorage as localStorage } from 'apptile-core';
import {makeBoolean} from 'apptile-core';
import {usePlaceHolder} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, View, Text} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {WidgetProps, connectWidget} from 'apptile-core';
import docs from './docs';
import {Icon} from 'apptile-core';

const ProfileWidgetConfig = {
  value: 'Text',
  onLoggedInTap: '',
  onGuestInTap: '',
  iconName: '',
  iconType: 'Material Icon',
  isLoading: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'profileWidget',
  type: 'widget',
  name: 'ProfileWidget',
  description: 'Display Text on screen with varying styles.',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'text',
};

export const ProfileWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'codeInput',
    name: 'iconSize',
    props: {
      label: 'Icon Size',
      placeholder: '20',
    },
  },
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
    },
  },
  {
    type: 'colorInput',
    name: 'iconColor',
    props: {
      label: 'Icon Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
    },
  },
  {
    type: 'typographyInput',
    name: 'subHeadingTypography',
    props: {
      label: 'SubHeading Typography',
    },
  },
];

const ProfileWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const Placeholder = usePlaceHolder();
  const modelValue = model.get('value')?.toString();
  const [userProfile, setUserProfile] = useState<any>({});

  useEffect(() => {
    async function getUserProfile() {
      const profileWidget = await localStorage.getValue('loggedInUser');
      setUserProfile(profileWidget);
    }
    getUserProfile();
  }, []);


  const layout = config.get('layout');
  const isLoading = !!model.get('isLoading');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {});

  const {shadowColor, shadowOffset, shadowRadius, iconSize, iconColor, typography: _typography, ...modelPlatformStyles} = modelStyles
    ? getPlatformStyles(modelStyles)
    : {};

  const {typography, subHeadingTypography, color, ...restModelPlatformStyles} = modelPlatformStyles;
  const horizontalAlign = _.isEmpty(model.get('horizontalAlign', 'auto'))
    ? 'auto'
    : model.get('horizontalAlign', 'auto');
  const verticalAlign = _.isEmpty(model.get('verticalAlign', 'auto')) ? 'auto' : model.get('verticalAlign', 'auto');

  const overflowType = model.get('overflowType') ? model.get('overflowType') : 'hidden';
  const isLoggedIn = !_.isEmpty(userProfile) ? true : false;

  const iconType = model.get('iconType')?.toString() || 'Material Icons';
  const iconName = model.get('iconName') || 'help';

  const onButtonTap = useCallback(() => {
    if (isLoggedIn) {
      triggerEvent('onLoggedInTap');
    } else {
      triggerEvent('onGuestInTap');
    }
  }, [triggerEvent, isLoggedIn]);

  return isLoading ? (
    <Placeholder
      layoutStyles={{...layoutStyles, ...restModelPlatformStyles, height: typography?.fontSize, minWidth: '75%'}}
    />
  ) : (
    <View
      ref={ref}
      style={[
        layoutStyles,
        modelPlatformStyles,
        restModelPlatformStyles,
        {padding: 8},
        {
          textShadowColor: shadowColor,
          textShadowOffset: shadowOffset,
          textShadowRadius: shadowRadius,
        },
      ]}>
      <Pressable onPress={onButtonTap}>
        <Text
          style={[
            typography,
            {padding: 2, color},
            {
              textAlign: ['auto', 'left', 'center', 'right'].includes(horizontalAlign) ? horizontalAlign : 'auto',
              textAlignVertical: ['auto', 'top', 'center', 'bottom'].includes(verticalAlign) ? verticalAlign : 'auto',
              overflow: overflowType,
            },
          ]}>
          {isLoggedIn ? userProfile.firstName + ' ' + userProfile?.lastName : 'Hello Guest'}
        </Text>
        <Text
          style={[
            subHeadingTypography,
            {padding: 2, color},
            {
              textAlign: ['auto', 'left', 'center', 'right'].includes(horizontalAlign) ? horizontalAlign : 'auto',
              textAlignVertical: ['auto', 'top', 'center', 'bottom'].includes(verticalAlign) ? verticalAlign : 'auto',
              overflow: overflowType,
            },
            {
              textShadowColor: shadowColor,
              textShadowOffset: shadowOffset,
              textShadowRadius: shadowRadius,
            },
          ]}>
          {isLoggedIn ? userProfile.email : 'Login'}
        </Text>
      </Pressable>

      {isLoggedIn ? (
        <Pressable style={{position: 'absolute', top: 0, right: 4, padding: 4}} onPress={onButtonTap}>
          <Icon
            iconType={iconType}
            name={iconName}
            style={[layoutStyles, modelPlatformStyles, {fontSize: iconSize, color: iconColor}]}
          />
        </Pressable>
      ) : (
        <></>
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'iconChooserInput',
      name: 'iconName',
      props: {
        label: 'Icon Name',
        placeholder: 'help',
      },
    },
  ],
  // advanced: defaultEditors.advanced,
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  isLoading: {
    getValue: (model, val) => {
      return makeBoolean(val);
    },
  },
  onLoggedInTap: {
    type: EventTriggerIdentifier,
  },
  onGuestInTap: {
    type: EventTriggerIdentifier,
  },
  iconName: {
    getValue: (model, val) => {
      return val;
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('ProfileWidget', ProfileWidget, ProfileWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(ProfileWidgetStyleConfig),
  pluginListing,
  docs,
  animations: {
    references: {
      textValue: {name: 'textValue'},
    },
  },
  themeProfileSel: ['tile', 'text'],
});
