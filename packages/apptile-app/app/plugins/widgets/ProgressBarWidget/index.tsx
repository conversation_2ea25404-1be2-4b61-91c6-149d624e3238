/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget, WidgetProps} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import {View, StyleSheet} from 'react-native';
import {getShadowStyle} from 'apptile-core';

const ProgressBarWidgetConfig = {
  value: '',
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'progressBar',
  type: 'widget',
  name: 'Progress Bar',
  description: 'Show progress bar on screen',
  layout: {
    flex: 1,
    width: '100%',
    height: '10',
  },
  section: 'Display',
  icon: 'container',
};

export const progressBarStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'colorInput',
    name: 'foregroundColor',
    props: {
      label: 'Foreground',
    },
  },
];

const ProgressBarWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config} = props;
  const value = model.get('value', 0)?.toString();
  const clampedValue = _.clamp(0, Number(value), 100);

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});

  const modelPlatformStyles = modelStyles ? getPlatformStyles(modelStyles) : {};
  const elevation = _.toInteger(config.getIn(['style', 'elevation'], 0));
  const shadowStyles = {...getShadowStyle(elevation), elevation};

  const {foregroundColor, ...restModelPlatformStyles} = modelPlatformStyles;

  return (
    <View ref={ref} style={[layoutStyles, restModelPlatformStyles, shadowStyles, {overflow: 'hidden'}]}>
      <View style={[style.foregroundContainer, {width: `${clampedValue}%`}, {backgroundColor: foregroundColor}]} />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: defaultEditors.basic,
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {};
const emptyOnupdate = null;

const style = StyleSheet.create({
  foregroundContainer: {
    height: '100%',
  },
});

export default connectWidget(
  'ProgressBarWidget',
  ProgressBarWidget,
  ProgressBarWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(progressBarStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'progressBar'],
  },
);
