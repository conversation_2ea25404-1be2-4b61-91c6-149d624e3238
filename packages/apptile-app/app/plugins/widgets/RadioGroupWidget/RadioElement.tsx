import React from 'react';
import {Image, Pressable, Text, View} from 'react-native';
import {useIsEditable} from 'apptile-core';
import _ from 'lodash';

const RadioElement = (props: any) => {
  const {
    activeTypography,
    handleSelection,
    horizontalAlign,
    item,
    itemSelected,
    itemLayoutStyles,
    modelPlatformStyles,
    shadowStyles,
    verticalAlign,
    radioGroupType,
    modelStyles,
  } = props;
  // const pressed_sv = useSharedValue(false);

  const {
    backgroundColor,
    borderColor,
    color,
    selectedBackgroundColor,
    disabledBackgroundColor,
    strikedBackgroundColor,
    selectedBorderColor,
    disabledBorderColor,
    strikedBorderColor,
    selectedTextColor,
    disabledTextColor,
    strikedTextColor,
    selectedTypography,
    disabledTypography,
    typography,
    border,
    padding,
    paddingLeft,
    paddingRight,
    paddingBottom,
    paddingTop,
    ...restModelPlatformStyles
  } = modelPlatformStyles ?? {};

  // const itemBGcolor = itemSelected ? selectedBackgroundColor : backgroundColor;
  // const itemTextcolor = itemSelected ? selectedTextColor : color;
  // const itemBorderColor = itemSelected ? selectedBorderColor : borderColor;
  const isEditable = useIsEditable();

  const {width, height, margin} = modelStyles;

  // Determine the appropriate colors
  const itemBgColor = itemSelected
    ? selectedBackgroundColor
    : item?.striked
    ? strikedBackgroundColor
    : item?.disabled
    ? disabledBackgroundColor
    : backgroundColor;

  // Determine the appropriate border color
  const itemBorderColor = itemSelected
    ? selectedBorderColor
    : item?.striked
    ? strikedBorderColor
    : item?.disabled
    ? disabledBorderColor
    : borderColor;

  // Determine the appropriate text color
  const itemTextColor = itemSelected
    ? selectedTextColor
    : item?.striked
    ? strikedTextColor
    : item?.disabled
    ? disabledTextColor
    : color;

  const paddingStyles = {
    paddingTop: padding,
    paddingBottom: padding,
    paddingLeft: padding,
    paddingRight: padding,
  };

  if (!_.isNil(paddingTop)) {
    paddingStyles.paddingTop = paddingTop;
  }

  if (!_.isNil(paddingLeft)) {
    paddingStyles.paddingLeft = paddingLeft;
  }

  if (!_.isNil(paddingRight)) {
    paddingStyles.paddingRight = paddingRight;
  }

  if (!_.isNil(paddingBottom)) {
    paddingStyles.paddingBottom = paddingBottom;
  }

  return (
    <Pressable
      disabled={item?.disabled}
      onPress={isEditable ? () => {} : () => handleSelection(item)}
      style={[
        shadowStyles,
        restModelPlatformStyles,
        paddingStyles,
        {margin: margin ?? 5},
        {backgroundColor: itemBgColor},
        {borderColor: itemBorderColor},
      ]}>
      {radioGroupType === 'Text' ? (
        <Text
          style={{
            textAlign: horizontalAlign,
            textAlignVertical: verticalAlign,
            ...modelStyles,
            ...typography,
            ...activeTypography,
            ...(item?.disabled ? disabledTypography : {}),
            ...itemLayoutStyles,
            color: itemTextColor,
            textDecorationLine: item?.striked ? 'line-through' : 'none',
          }}>
          {item.name}
        </Text>
      ) : (
        <></>
      )}
      {radioGroupType === 'Color' ? (
        <View
          style={{
            width: width ?? 40,
            height: height ?? 40,
            backgroundColor: item.name,
            borderRadius: restModelPlatformStyles.borderRadius ?? 4,
            ...itemLayoutStyles,
          }}
        />
      ) : (
        <></>
      )}

      {radioGroupType === 'Image' ? (
        <Image
          style={{
            width: width ?? 40,
            height: height ?? 40,
            borderRadius: restModelPlatformStyles.borderRadius ?? 4,
            ...itemLayoutStyles,
          }}
          source={{uri: item.name}}
        />
      ) : (
        <></>
      )}
    </Pressable>
  );
};

export default RadioElement;
