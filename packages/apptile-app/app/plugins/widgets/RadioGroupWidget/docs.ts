const docs = {
  value: {
    '!type': 'string',
    '!doc': 'The selected value.',
  },
  displayValue: {
    '!type': 'string',
    '!doc': 'The selected display value.',
  },
  horizontalAlign: {
    '!type': 'string',
    '!doc': 'Horizontal alignment of Text. One of:  "auto", "left", "center", "right", "justify"',
  },
  verticalAlign: {
    '!type': 'string',
    '!doc': 'Horizontal alignment of Text. One of:  "auto", "top", "center", "bottom"',
  },
  overflowType: {
    '!type': 'string',
    '!doc': 'Overflow behaivour. One of: "hidden", "visible"',
  },
};

export default docs;
