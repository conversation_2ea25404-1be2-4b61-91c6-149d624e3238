/* eslint-disable react-native/no-inline-styles */
import React, {useCallback, useEffect} from 'react';
import {ScrollView, View} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import {getShadowStyle} from 'apptile-core';
import {toInteger} from 'lodash';
import _ from 'lodash';
import {makeBoolean} from 'apptile-core';
import {performHapticFeedback} from 'apptile-core';
import {hapticEditors} from 'apptile-core';
import RadioElement from './RadioElement';
import {usePlaceHolder} from 'apptile-core';

const RadioGroupConfig = {
  value: '',
  isMultiSelect: false,
  input: '{{[{name: "Text", value: "new", isDisabled?: false}]}}',
  onTap: '',
  enableHaptics: '',
  hapticMethod: '',
  isLoading: false,
  radioGroupType: 'Text',
  itemAlignSelf: 'center',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'RadioGroup',
  type: 'widget',
  name: 'RadioGroup',
  description: 'Displays radio group button.',
  section: 'Display',
  layout: {
    flex: 1,
    flexDirection: 'row',
    width: 70,
  },
  icon: 'radio-button',
};
export const RadioGroupStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedBackgroundColor',
    props: {
      label: 'SelectedBackground',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedTextColor',
    props: {
      label: 'Selected Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledBackgroundColor',
    props: {
      label: 'Disabled Item Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'strikedBackgroundColor',
    props: {
      label: 'Striked Item Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'strikedTextColor',
    props: {
      label: 'Striked Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledTextColor',
    props: {
      label: 'Disabled Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'selectedTypography',
    props: {
      label: 'Selected Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'disabledTypography',
    props: {
      label: 'Disabled Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'codeInput',
    name: 'itemWidth',
    props: {
      label: 'Item width',
      placeholder: '',
    },
  },
  {
    type: 'codeInput',
    name: 'itemHeight',
    props: {
      label: 'Item height',
      placeholder: '',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'borderRadius',
    props: {
      label: 'Border Radius',
      placeholder: '0',
      options: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
      layout: 'square',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'padding',
    props: {
      label: 'Padding',
      placeholder: '0',
      options: ['paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'margin',
    props: {
      label: 'Margin',
      placeholder: '0',
      options: ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'itemMargin',
    props: {
      label: 'Item Margin',
      placeholder: '0',
      options: ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'borderWidth',
    props: {
      label: 'Border',
      placeholder: '0',
      options: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],
    },
  },
  {
    type: 'colorInput',
    name: 'borderColor',
    props: {
      label: 'Border Color',
      placeholder: 'transparent',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedBorderColor',
    props: {
      label: 'Selected Border Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledBorderColor',
    props: {
      label: 'Disabled Border Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'strikedBorderColor',
    props: {
      label: 'Striked Border Color',
      placeholder: '#HexCode',
    },
  },
];

const RadioGroup = React.forwardRef((props, ref) => {
  const {model, modelStyles, modelUpdate, config, triggerEvent} = props;

  const value = model.get('value');

  const Placeholder = usePlaceHolder();

  const [radioGroupSelection, handleRadioGroupSelection]: any = React.useState(value);

  useEffect(() => {
    if (!_.isEqual(value, radioGroupSelection)) {
      modelUpdate([
        {
          selector: ['value'],
          newValue: value,
        },
      ]);
    }
    handleRadioGroupSelection(value);
  }, [modelUpdate, triggerEvent, value]);

  const enableHaptics = model.get('enableHaptics');
  const hapticMethod = model.get('hapticMethod');
  const radioGroupType = model.get('radioGroupType');

  let input = model.get('input');
  const isLoading = !!model.get('isLoading');

  const isArray = Array.isArray(input);
  input = isArray ? input : [];

  const handleSingleSelect = useCallback(
    (item: any) => {
      // TODO: Figure out how we want to support unselect for single selection.
      // const newValue = !_.isEmpty(value) && value === item?.value ? '' : item.value;
      handleRadioGroupSelection(item?.value);
      if (enableHaptics) performHapticFeedback(hapticMethod);
      modelUpdate([
        {
          selector: ['value'],
          newValue: item?.value,
        },
      ]);
    },
    [enableHaptics, hapticMethod, modelUpdate],
  );

  const handleMultiSelect = useCallback(
    (item: any) => {
      if (enableHaptics) performHapticFeedback(hapticMethod);

      let newValue = radioGroupSelection || [];

      if (!Array.isArray(newValue)) newValue = [newValue];

      if (newValue.includes(item?.value)) {
        newValue = newValue.filter((val: string) => val !== item.value);
      } else {
        newValue.push(item?.value);
      }

      handleRadioGroupSelection([...newValue]);
      modelUpdate([
        {
          selector: ['value'],
          newValue: [...newValue],
        },
      ]);
    },
    [enableHaptics, hapticMethod, modelUpdate, radioGroupSelection],
  );

  const multiSelect = model.get('isMultiSelect', false);

  const handleSelection = useCallback(
    (item: any) => {
      if (!multiSelect) {
        handleSingleSelect(item);
      } else {
        handleMultiSelect(item);
      }
      //TODO: Its a big hack to stop the event trigger before the model update.
      //Need to find a better way to do this.
      setTimeout(() => triggerEvent('onTap'), 50);
    },
    [handleMultiSelect, handleSingleSelect, multiSelect, triggerEvent],
  );

  const layout = config.get('layout');

  const layoutStyles = getPlatformStyles(layout ? layout.getNonContainerFlexProperties() : {flex: 1});
  const layoutContainerStyles = getPlatformStyles(layout ? layout?.getContainerFlexProperties() : {flex: 1});

  const modelPlatformStyles = getPlatformStyles(modelStyles);

  //Picking item layout styles
  const itemLayoutStyles: any = {};
  if (model.get('itemOverflow')) itemLayoutStyles['overflow'] = model.get('itemOverflow');
  if (model.get('itemWrap')) itemLayoutStyles['flexWrap'] = model.get('itemWrap');
  if (model.get('itemAlignSelf')) itemLayoutStyles['alignSelf'] = model.get('itemAlignSelf');

  //Picking Item Model styles
  const itemModelStyles: any = {};
  const {itemWidth, itemHeight, itemMargin} = modelStyles;
  if (itemMargin) itemModelStyles['margin'] = itemMargin;
  if (itemHeight) itemModelStyles['height'] = itemHeight;
  if (itemWidth) itemModelStyles['width'] = itemWidth;

  const elevation = toInteger(config.getIn(['style', 'elevation'], 0));
  const shadowStyles = {...getShadowStyle(elevation), elevation};
  const isScrollView = layoutContainerStyles?.overflow === 'scroll';
  const ViewComponent = isScrollView ? ScrollView : View;
  const scrollViewProps = isScrollView
    ? {
        contentContainerStyle: layoutContainerStyles,
        horizontal: layoutContainerStyles?.flexDirection === 'row',
        showsHorizontalScrollIndicator: false,
      }
    : {};

  const {typography, selectedTypography, ...restModelPlatformStyles} = modelPlatformStyles || {};

  return !isLoading ? (
    <ViewComponent
      ref={ref}
      {...scrollViewProps}
      style={[{margin: modelPlatformStyles.margin ?? 0}, layoutStyles, !isScrollView ? layoutContainerStyles : {}]}>
      {input.map((item, index) => {
        const itemSelected =
          (Array.isArray(radioGroupSelection) && radioGroupSelection.includes(item.value)) ||
          radioGroupSelection === item.value;

        const activeTypography = itemSelected ? selectedTypography : typography;
        return (
          <RadioElement
            activeTypography={activeTypography}
            handleSelection={handleSelection}
            horizontalAlign={modelPlatformStyles.alignItems}
            item={item}
            itemSelected={itemSelected}
            radioGroupType={radioGroupType}
            itemLayoutStyles={itemLayoutStyles}
            key={`${value}-${index}`}
            modelPlatformStyles={modelPlatformStyles}
            shadowStyles={shadowStyles}
            modelStyles={itemModelStyles}
            verticalAlign={modelPlatformStyles.justifyContent}
          />
        );
      })}
    </ViewComponent>
  ) : (
    <Placeholder layoutStyles={{...layoutStyles, ...restModelPlatformStyles, minHeight: 35}} />
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'input',
      props: {
        label: 'Input Options [{name,value,disabled}]',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'value',
        placeholder: '',
      },
    },

    {
      type: 'radioGroup',
      name: 'isMultiSelect',
      props: {
        label: 'MultiSelect',
        options: ['true', 'false'],
      },
    },

    {
      type: 'radioGroup',
      name: 'radioGroupType',
      defaultValue: 'Text',
      props: {
        label: 'RadioGroup Type',
        options: ['Image', 'Text', 'Color'],
      },
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      props: {
        label: 'Loading State',
      },
    },
  ],
  advanced: hapticEditors.advanced,
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
    {
      type: 'radioGroup',
      name: 'itemOverflow',
      props: {
        label: 'Item Overflow',
        options: ['Visible', 'Hidden', 'Scroll'],
      },
    },
    {
      type: 'radioGroup',
      name: 'itemWrap',
      props: {
        label: 'Item Wrap',
        options: [
          {icon: 'wrap', text: 'Wrap', value: 'wrap'},
          {icon: 'wrap-disabled', text: 'No-Wrap', value: 'nowrap'},
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'itemAlignSelf',
      props: {
        label: 'Item Align Self',
        options: ['auto', 'flex-start', 'flex-end', 'center', 'stretch', 'baseline'],
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      const isMultiSelect = !!model?.isMultiSelect;
      if (!isMultiSelect) {
        return renderedValue ? renderedValue?.toString() : '';
      } else {
        renderedValue = _.isArray(renderedValue) ? renderedValue : [];
        return renderedValue;
      }
    },
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },

  isMultiSelect: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
  onTap: {
    type: EventTriggerIdentifier,
  },

  isLoading: {
    getValue: (model, val, _) => {
      return makeBoolean(val);
    },
  },
};

const emptyOnupdate = null;

export default connectWidget('RadioGroupWidget', RadioGroup, RadioGroupConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(RadioGroupStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'radioGroup'],
});
