const docs = {
  minBoundary: {
    '!type': 'number',
    '!doc': 'The Minimum boundary for slider.',
  },
  maxBoundary: {
    '!type': 'number',
    '!doc': 'The Maximum boundary for slider.',
  },
  minCurrentVal: {
    '!type': 'number',
    '!doc': 'The Minimum Initial Value for slider.',
  },
  maxCurrentVal: {
    '!type': 'number',
    '!doc': 'The Maximum Initial Value for slider.',
  },
  sliderType: {
    '!type': 'string',
    '!doc': 'Slider Type of Slider Component. One of:  "2 Way", "1 Way"',
  },
};

export default docs;
