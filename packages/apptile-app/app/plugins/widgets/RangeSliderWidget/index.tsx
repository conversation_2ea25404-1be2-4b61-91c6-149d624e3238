/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useCallback} from 'react';
import {StyleSheet, Animated, PanResponder, View, TextInput, Platform} from 'react-native';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {set} from 'lodash';

const RangeSliderWidgetConfig = {
  minBoundary: 0, // the minimum value slider can go to
  maxBoundary: 100, // the max value slider can go to
  minCurrentVal: 0, // read only value thats updated by this widget and read by everyone else on sliding
  maxCurrentVal: 100, // read only value thats updated by this widget and read by everyone else on sliding
  sliderType: '2 Way',
  showLabel: 'Show',
  showBreakpoints: 'Hide',
  breakpointsType: 'Static',
  breakpoints: '',
  onChange: '',
  prefix: '',
  hideMinSlider: 'Show',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'rangeSlider',
  type: 'widget',
  name: 'Range-Slider',
  description: 'Display A 2/1 Way Range Slider',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'slider-range',
  defaultHeight: 0,
  defaultWidth: 0,
};

export const RangeSliderWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'codeInput',
    name: 'lineThickness',
    props: {
      label: 'Line Thickness',
      placeholder: '4',
    },
  },
  {
    type: 'codeInput',
    name: 'minIconSize',
    props: {
      label: 'Min Icon Size',
      placeholder: '20',
    },
  },
  {
    type: 'codeInput',
    name: 'maxIconSize',
    props: {
      label: 'Max Icon Size',
      placeholder: '25',
    },
  },
  {
    type: 'codeInput',
    name: 'breakpointHeight',
    props: {
      label: 'Breakpoint Height',
      placeholder: '6',
    },
  },
  {
    type: 'codeInput',
    name: 'breakpointWidth',
    props: {
      label: 'Breakpoint Width',
      placeholder: '2',
    },
  },
  {
    type: 'colorInput',
    name: 'fontColor',
    props: {
      label: 'Font Color',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedLineColor',
    props: {
      label: 'Selected Line Color',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'colorInput',
    name: 'lineColor',
    props: {
      label: 'Line Color',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'colorInput',
    name: 'breakpointColor',
    props: {
      label: 'Breakpoint Color',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'colorInput',
    name: 'iconColor',
    props: {
      label: 'Icon Color',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'colorInput',
    name: 'endPointsBackground',
    props: {
      label: 'End Points Background',
      placeholder: 'transparentBackground',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const styles = StyleSheet.create({
  line: {
    height: '100%',
    width: '100%',
    position: 'absolute',
  },
  circle: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.24,
    shadowRadius: 2.8,
    elevation: 3,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    overflow: 'visible',
    padding: 2,
  },
  labelContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    bottom: 0,
    wordBreak: 'break-all',
    textAlign: 'center',
  },
});

type BreakpointProps = {
  breakpoints: number[];
  minBoundary: number;
  maxBoundary: number;
  breakpointColor: string;
  breakpointHeight: number | string;
  breakpointWidth: number | string;
};
class BreakpointViews extends React.Component<BreakpointProps, {rootBoxWidth: number}> {
  constructor(props: BreakpointProps) {
    super(props);
    this.state = {rootBoxWidth: 0};
  }

  setRootboxWidth(value: number) {
    this.setState({rootBoxWidth: value});
  }

  render() {
    const {breakpoints, minBoundary, maxBoundary, breakpointColor, breakpointHeight, breakpointWidth} = this.props;

    const sliderWidth = this.state.rootBoxWidth * 0.8;
    const sideWhitespace = this.state.rootBoxWidth * 0.1;

    return breakpoints.map((breakpointValue: number, index: number) => {
      const percentageOffset = (breakpointValue - minBoundary) / (maxBoundary - minBoundary);
      const offsetInPx = sideWhitespace + percentageOffset * sliderWidth;
      return (
        <View
          key={'breakpoint-' + index}
          style={{
            backgroundColor: breakpointColor,
            height: breakpointHeight,
            width: breakpointWidth,
            borderRadius: 5,
            left: offsetInPx,
            position: 'absolute',
          }}
        />
      );
    });
  }
}

const RangeSliderWidget = React.forwardRef(props => {
  const OS = Platform.OS;
  const {model, modelUpdate, modelStyles, triggerEvent} = props;
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const typography = modelPlatformStyles.typography;
  const icon = model.get('icon') ? model.get('icon') : 'circle';
  let minBoundary = model.get('minBoundary')
    ? parseInt(model.get('minBoundary'), 10)
    : RangeSliderWidgetConfig.minBoundary;

  minBoundary = isNaN(minBoundary) ? 0 : minBoundary;

  let maxBoundary = model.get('maxBoundary')
    ? parseInt(model.get('maxBoundary'), 10) * 1
    : RangeSliderWidgetConfig.maxBoundary;

  maxBoundary = isNaN(maxBoundary) ? 100 : maxBoundary;

  const hideMinSlider = model.get('hideMinSlider', 'Show');

  const labelPrefix = model.get('prefix');
  const modelMinCurrentVal = model.get('minCurrentVal');
  let parsedMinCurrentVal = parseInt(modelMinCurrentVal, 10);
  parsedMinCurrentVal = isNaN(parsedMinCurrentVal) ? minBoundary : parsedMinCurrentVal;
  const breakpointsRef = useRef<typeof BreakpointViews>(null);
  const minCurrentValLocalRef = useRef(parsedMinCurrentVal < minBoundary ? minBoundary : parsedMinCurrentVal);
  const minCurrentValPxRef = useRef(0);
  const minPrevValLocalRef = useRef(minCurrentValLocalRef.current);
  const modelMaxCurrentVal = model.get('maxCurrentVal');
  // This hack is due to previous hacks done in tile designs
  let parsedMaxCurrentVal = parseInt(modelMaxCurrentVal, 10);
  parsedMaxCurrentVal = isNaN(parsedMaxCurrentVal) ? maxBoundary : parsedMaxCurrentVal;
  const maxCurrentValLocalRef = useRef(parsedMaxCurrentVal > maxBoundary ? maxBoundary : parsedMaxCurrentVal);
  const maxCurrentValPxRef = useRef(0);
  const maxPrevValLocalRef = useRef(maxCurrentValLocalRef.current);
  const maxResponderListenerThrottle = useRef(0);

  const sliderType = model.get('sliderType') ? model.get('sliderType') : '2 Way';
  let breakpoints: number[] = [];
  const breakpointConf = model.get('breakpoints');
  const showBreakpoints = model.get('showBreakpoints') === 'Show';
  const breakpointsType = model.get('breakpointsType') ? model.get('breakpointsType') : 'Static';
  if (breakpointsType === 'Fixed Difference') {
    let difference = parseFloat(breakpointConf) || (maxBoundary - minBoundary) / 10;
    for (let i = minBoundary; i < maxBoundary; i += difference) {
      breakpoints.push(i);
    }
  } else if (breakpointsType === 'Fixed Total Breakpoints') {
    let total = parseInt(breakpointConf ?? 5, 10);
    total = isNaN(total) ? 5 : total;
    const difference = Math.floor((maxBoundary - minBoundary) / total);
    for (let i = 0; i < total; i++) {
      breakpoints.push(minBoundary + i * difference);
    }
  } else if (breakpointConf.trim()) {
    breakpoints = breakpointConf.split(',').map((it: string) => {
      let result = parseInt(it, 10);
      result = isNaN(result) ? 0 : result;
      return result;
    });
  }

  const selectedLineColor = modelPlatformStyles?.selectedLineColor ? modelPlatformStyles?.selectedLineColor : '#008ee6';
  const lineColor = modelPlatformStyles?.lineColor ? modelPlatformStyles?.lineColor : '#888888';
  const breakpointColor = modelPlatformStyles?.breakpointColor ? modelPlatformStyles?.breakpointColor : '#555555';
  const lineThickness = modelPlatformStyles?.lineThickness ? modelPlatformStyles?.lineThickness : 4;
  const iconColor = modelPlatformStyles?.iconColor ? modelPlatformStyles?.iconColor : '#008ee6';
  const minIconSize = modelPlatformStyles?.minIconSize ? parseInt(modelPlatformStyles?.minIconSize, 10) : 20;
  const maxIconSize = modelPlatformStyles?.maxIconSize ? parseInt(modelPlatformStyles?.maxIconSize, 10) : 25;
  const breakpointHeight = modelPlatformStyles?.breakpointHeight
    ? parseInt(modelPlatformStyles?.breakpointHeight, 10)
    : 6;
  const breakpointWidth = modelPlatformStyles?.breakpointWidth ? parseInt(modelPlatformStyles?.breakpointWidth, 10) : 2;

  const endPointsBackground = modelPlatformStyles?.endPointsBackground
    ? modelPlatformStyles?.endPointsBackground
    : '#ffffff';
  const fontColor = modelPlatformStyles?.fontColor ? modelPlatformStyles?.fontColor : '#ffffff';
  let showLabel = model.get('showLabel');

  if (showBreakpoints && breakpoints.length > 0) {
    if (breakpoints[0] !== minBoundary) {
      breakpoints.unshift(minBoundary);
    }

    if (breakpoints[breakpoints.length - 1] !== maxBoundary) {
      breakpoints.push(maxBoundary);
    }
  }

  const leftSliderOffset = useRef(new Animated.Value(0)).current;
  const rightSliderOffset = useRef(new Animated.Value(300)).current;

  // We keep two references to the dimensions of the rootbox because we
  // need both an animated and a non-animated reference
  // We know that rootBoxWidth will
  const boxDimension = useRef(new Animated.ValueXY({x: 300, y: 100})).current; // width / height = 3
  const rootBoxWidth = useRef(300);

  const widgetUnitsToPx = useCallback(
    (currentVal: number) => {
      const sliderWidth = rootBoxWidth.current * 0.8;
      const sideWhitespace = rootBoxWidth.current * 0.1;
      const percentageOffset = (currentVal - minBoundary) / (maxBoundary - minBoundary);
      const offsetInPx = sideWhitespace + percentageOffset * sliderWidth;
      return offsetInPx;
    },
    [rootBoxWidth.current],
  );

  const pxToWidgetUnits = useCallback(
    (pxOffset: number) => {
      const sliderWidth = rootBoxWidth.current * 0.8;
      const sideWhitespace = rootBoxWidth.current * 0.1;
      const percentageOffset = (pxOffset - sideWhitespace) / sliderWidth;
      const offsetInWidgetUnits = minBoundary + percentageOffset * (maxBoundary - minBoundary);
      return offsetInWidgetUnits;
    },
    [rootBoxWidth.current],
  );

  const minSliderTooltip = useRef<TextInput>(null);
  const maxSliderTooltip = useRef<TextInput>(null);
  const fixedSliderTooltip = useRef<TextInput>(null);

  //   useEffect(() => {
  //
  //   }, [breakpointsRef.current]);

  const setDimensions = useCallback(
    (layout: any) => {
      boxDimension.setValue({x: layout.width, y: layout.height});
      rootBoxWidth.current = layout.width;

      if (breakpointsRef.current) {
        breakpointsRef.current.setRootboxWidth(layout.width);
      }

      const leftOffsetInPx = widgetUnitsToPx(minCurrentValLocalRef.current);
      leftSliderOffset.setValue(leftOffsetInPx);

      const rightOffsetInPx = widgetUnitsToPx(maxCurrentValLocalRef.current);
      rightSliderOffset.setValue(rightOffsetInPx);

      if (minSliderTooltip.current) {
        if (OS !== 'web') {
          if (OS !== 'web') {
            minSliderTooltip.current.setNativeProps({
              text: labelPrefix + Math.floor(minCurrentValLocalRef.current).toString(),
            });
          } else {
            minSliderTooltip.current.value = labelPrefix + Math.floor(minCurrentValLocalRef.current).toString();
          }
        } else {
          minSliderTooltip.current.value = labelPrefix + Math.floor(minCurrentValLocalRef.current).toString();
        }
      }

      if (maxSliderTooltip.current) {
        if (OS !== 'web') {
          maxSliderTooltip.current.setNativeProps({
            text: labelPrefix + Math.floor(maxCurrentValLocalRef.current).toString(),
          });
        } else {
          maxSliderTooltip.current.value = labelPrefix + Math.floor(maxCurrentValLocalRef.current).toString();
        }
      }

      if (fixedSliderTooltip.current) {
        if (OS !== 'web') {
          fixedSliderTooltip.current.setNativeProps({
            text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
              maxCurrentValLocalRef.current,
            ).toString()}`,
          });
        } else {
          fixedSliderTooltip.current.value = 
            `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(maxCurrentValLocalRef.current,).toString()}`
        }
      }
    },
    [
      boxDimension,
      leftSliderOffset,
      rightSliderOffset,
      minSliderTooltip.current,
      maxSliderTooltip.current,
      fixedSliderTooltip.current,
    ],
  );

  const handleChange = (selector: string, value: any) => {
    logger.info('[LIFECYCLE] handleChange: ', selector, value);
    modelUpdate([
      {
        selector: [selector],
        newValue: value,
      },
    ]);
    // triggerEvent('onChange');
  };

  const throttleValue = Platform.select({
    android: 4,
    ios: 1,
    web: 1,
  });

  const minPanResponder = React.useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: () => {
        leftSliderOffset.setOffset(leftSliderOffset._value);
        minCurrentValPxRef.current = leftSliderOffset._value;
        maxResponderListenerThrottle.current = 0;
        minPrevValLocalRef.current = minCurrentValLocalRef.current;
        return true;
      },
      onPanResponderMove: Animated.event([null, {dx: leftSliderOffset}], {
        listener: (_, gestureState) => {
          const here = gestureState.dx;
          if (Math.abs(here - maxResponderListenerThrottle.current) > throttleValue) {
            maxResponderListenerThrottle.current = here;
            // console.log("listener");
            const dx = gestureState.dx;
            const pxOffset = minCurrentValPxRef.current + dx;
            const newCurrentValue = pxToWidgetUnits(pxOffset);
            if (minSliderTooltip.current) {
              if (OS !== 'web') {
                minSliderTooltip.current.setNativeProps({
                  text: labelPrefix + Math.floor(newCurrentValue).toString(),
                });
              } else {
                minSliderTooltip.current.value = labelPrefix + Math.floor(newCurrentValue).toString();
              }
            }

            if (fixedSliderTooltip.current) {
              if (OS !== 'web') {
                fixedSliderTooltip.current.setNativeProps({
                  text: `${labelPrefix}${Math.floor(newCurrentValue).toString()} - ${labelPrefix}${Math.floor(
                    maxCurrentValLocalRef.current,
                  ).toString()}`,
                });
              } else {
                fixedSliderTooltip.current.value = 
                  `${labelPrefix}${Math.floor(newCurrentValue).toString()} - ${labelPrefix}${Math.floor(
                    maxCurrentValLocalRef.current,
                  ).toString()}`
              }
            }
            minCurrentValLocalRef.current = newCurrentValue;
          }
        },
      }),
      onPanResponderRelease: () => {
        leftSliderOffset.flattenOffset();
        let newCurrentVal = minCurrentValLocalRef.current;
        if (newCurrentVal < minBoundary) {
          newCurrentVal = minBoundary;
          const pxValue = widgetUnitsToPx(minBoundary);
          Animated.spring(leftSliderOffset, {
            toValue: pxValue,
            useNativeDriver: false,
          }).start();
          if (minSliderTooltip.current) {
            if (OS !== 'web') {
              minSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(minBoundary).toString(),
              });
            } else {
              minSliderTooltip.current.value = labelPrefix + Math.floor(minBoundary).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(minBoundary).toString()} - ${labelPrefix}${Math.floor(
                  maxCurrentValLocalRef.current,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(minBoundary).toString()} - ${labelPrefix}${Math.floor(
                  maxCurrentValLocalRef.current,
                ).toString()}`;
            }
          }
        } else if (newCurrentVal > maxCurrentValLocalRef.current) {
          newCurrentVal = minPrevValLocalRef.current;
          const pxValue = widgetUnitsToPx(newCurrentVal);
          Animated.spring(leftSliderOffset, {
            toValue: pxValue,
            useNativeDriver: false,
          }).start();
          if (minSliderTooltip.current) {
            if (OS !== 'web') {
              minSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(minCurrentValLocalRef.current).toString(),
              });
            } else {
              minSliderTooltip.current.value = labelPrefix + Math.floor(minCurrentValLocalRef.current).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`;
            }
          }
        } else if (showBreakpoints) {
          let leftBreakpoint = null,
            rightBreakpoint = null;
          for (let index = 0; index < breakpoints.length - 1; ++index) {
            if (breakpoints[index] < newCurrentVal && newCurrentVal < breakpoints[index + 1]) {
              leftBreakpoint = breakpoints[index];
              rightBreakpoint = breakpoints[index + 1];
              break;
            }
          }

          if (leftBreakpoint && rightBreakpoint) {
            if (newCurrentVal - leftBreakpoint < rightBreakpoint - newCurrentVal) {
              newCurrentVal = leftBreakpoint;
            } else {
              newCurrentVal = rightBreakpoint;
            }
          }

          const pxValue = widgetUnitsToPx(newCurrentVal);
          if (Platform.OS === 'ios') {
            Animated.spring(leftSliderOffset, {
              toValue: pxValue,
              useNativeDriver: false,
            }).start();
          } else {
            leftSliderOffset.setValue(pxValue);
          }

          if (minSliderTooltip.current) {
            if (OS !== 'web') {
              minSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(newCurrentVal).toString(),
              });
            } else {
              minSliderTooltip.current.value = labelPrefix + Math.floor(newCurrentVal).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(newCurrentVal).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(newCurrentVal).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`;
            }
          }
        }
        minCurrentValLocalRef.current = newCurrentVal;
        handleChange('minCurrentVal', newCurrentVal);
      },
    }),
  ).current;

  const maxPanResponder = React.useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: () => {
        rightSliderOffset.setOffset(rightSliderOffset._value);
        maxCurrentValPxRef.current = rightSliderOffset._value;
        maxResponderListenerThrottle.current = 0;
        maxPrevValLocalRef.current = maxCurrentValLocalRef.current;
        return true;
      },
      onPanResponderMove: Animated.event([null, {dx: rightSliderOffset}], {
        listener: (_, gestureState) => {
          const here = gestureState.dx;
          if (Math.abs(here - maxResponderListenerThrottle.current) > throttleValue) {
            maxResponderListenerThrottle.current = here;
            // console.log("listener");
            const dx = gestureState.dx;
            const pxOffset = maxCurrentValPxRef.current + dx;
            const newCurrentValue = pxToWidgetUnits(pxOffset);
            if (maxSliderTooltip.current) {
              if (OS !== 'web') {
                maxSliderTooltip.current.setNativeProps({
                  text: labelPrefix + Math.floor(newCurrentValue).toString(),
                });
              } else {
                maxSliderTooltip.current.value = labelPrefix + Math.floor(newCurrentValue).toString();
              }
            }

            if (fixedSliderTooltip.current) {
              if (OS !== 'web') {
                fixedSliderTooltip.current.setNativeProps({
                  text: `${labelPrefix}${Math.floor(
                    minCurrentValLocalRef.current,
                  ).toString()} - ${labelPrefix}${Math.floor(newCurrentValue).toString()}`,
                });
              } else {
                fixedSliderTooltip.current.value = 
                  `${labelPrefix}${Math.floor(
                    minCurrentValLocalRef.current,
                  ).toString()} - ${labelPrefix}${Math.floor(newCurrentValue).toString()}`;
              }
            }
            maxCurrentValLocalRef.current = newCurrentValue;
          }
        },
      }),
      onPanResponderRelease: () => {
        rightSliderOffset.flattenOffset();
        let newCurrentVal = maxCurrentValLocalRef.current;
        if (newCurrentVal > maxBoundary) {
          newCurrentVal = maxBoundary;
          const pxValue = widgetUnitsToPx(maxBoundary);
          Animated.spring(rightSliderOffset, {
            toValue: pxValue,
            useNativeDriver: false,
          }).start();
          if (maxSliderTooltip.current) {
            if (OS !== 'web') {
              maxSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(maxBoundary).toString(),
              });
            } else {
              maxSliderTooltip.current.value = labelPrefix + Math.floor(maxBoundary).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  maxBoundary,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  maxBoundary,
                ).toString()}`;
            }
          }
        } else if (newCurrentVal < minCurrentValLocalRef.current) {
          newCurrentVal = maxPrevValLocalRef.current;

          const pxValue = widgetUnitsToPx(newCurrentVal);
          Animated.spring(rightSliderOffset, {
            toValue: pxValue,
            useNativeDriver: false,
          }).start();
          if (maxSliderTooltip.current) {
            if (OS !== 'web') {
              maxSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(newCurrentVal).toString(),
              });
            } else {
              maxSliderTooltip.current.value = labelPrefix + Math.floor(newCurrentVal).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`;
            }
          }
        } else if (showBreakpoints) {
          let leftBreakpoint = null,
            rightBreakpoint = null;
          for (let index = 0; index < breakpoints.length - 1; ++index) {
            if (breakpoints[index] < newCurrentVal && newCurrentVal < breakpoints[index + 1]) {
              leftBreakpoint = breakpoints[index];
              rightBreakpoint = breakpoints[index + 1];
              break;
            }
          }

          if (leftBreakpoint && rightBreakpoint) {
            if (newCurrentVal - leftBreakpoint < rightBreakpoint - newCurrentVal) {
              newCurrentVal = leftBreakpoint;
            } else {
              newCurrentVal = rightBreakpoint;
            }
          }

          const pxValue = widgetUnitsToPx(newCurrentVal);
          if (Platform.OS === 'ios') {
            Animated.spring(rightSliderOffset, {
              toValue: pxValue,
              useNativeDriver: false,
            }).start();
          } else {
            rightSliderOffset.setValue(pxValue);
          }

          if (maxSliderTooltip.current) {
            if (OS !== 'web') {
              maxSliderTooltip.current.setNativeProps({
                text: labelPrefix + Math.floor(newCurrentVal).toString(),
              });
            } else {
              maxSliderTooltip.current.value = labelPrefix + Math.floor(newCurrentVal).toString();
            }
          }

          if (fixedSliderTooltip.current) {
            if (OS !== 'web') {
              fixedSliderTooltip.current.setNativeProps({
                text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`,
              });
            } else {
              fixedSliderTooltip.current.value = 
                `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
                  newCurrentVal,
                ).toString()}`;
            }
          }
        }

        maxCurrentValLocalRef.current = newCurrentVal;
        handleChange('maxCurrentVal', newCurrentVal);
      },
    }),
  ).current;

  // We do not add minCurrentVal and maxCurrentVal in the dependencies here because we only
  // want to pull in the values once on mount. This breaks the editor but it is the most efficient thing
  // we can do for now
  useEffect(() => {
    if (!model.get('minCurrentVal') || parsedMinCurrentVal < minBoundary) {
      handleChange('minCurrentVal', minCurrentValLocalRef.current);
    }

    if (!model.get('maxCurrentVal') || parsedMaxCurrentVal > maxBoundary) {
      handleChange('maxCurrentVal', maxCurrentValLocalRef.current);
    }

    if (minSliderTooltip.current) {
      if (OS !== 'web') {
        minSliderTooltip.current.setNativeProps({
          text: labelPrefix + Math.floor(minCurrentValLocalRef.current).toString(),
        });
      } else {
        minSliderTooltip.current.value = labelPrefix + Math.floor(minCurrentValLocalRef.current).toString();
      }
    }

    if (maxSliderTooltip.current) {
      if (OS !== 'web') {
        maxSliderTooltip.current.setNativeProps({
          text: labelPrefix + Math.floor(maxCurrentValLocalRef.current).toString(),
        });
      } else {
        maxSliderTooltip.current.value = labelPrefix + Math.floor(maxCurrentValLocalRef.current).toString();
      }
    }

    if (fixedSliderTooltip.current) {
      if (OS !== 'web') {
        fixedSliderTooltip.current.setNativeProps({
          text: `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
            maxCurrentValLocalRef.current,
          ).toString()}`,
        });
      } else {
        fixedSliderTooltip.current.value = 
          `${labelPrefix}${Math.floor(minCurrentValLocalRef.current).toString()} - ${labelPrefix}${Math.floor(
            maxCurrentValLocalRef.current,
          ).toString()}`;
      }
    }
  }, []);

  useEffect(() => {
    const minCurrentVal = model.get('minCurrentVal');
    const maxCurrentVal = model.get('maxCurrentVal');
    // Im checking for equality here instead of inequality because first it triggers with
    // old value of minCurrentVal in model and then again once the model catches up to the
    // minCurrentValLocalRef (similarly for max)
    // console.log('onchange detection hook', minCurrentValLocalRef.current, minCurrentVal);
    if (minCurrentValLocalRef.current === minCurrentVal || maxCurrentValLocalRef.current === maxCurrentVal) {
      logger.info('[LIFECYCLE] trigger on change: ', minCurrentVal, maxCurrentVal);
      triggerEvent('onChange');
    }
  }, [model, minCurrentValLocalRef, maxCurrentValLocalRef]);

  // We calculate these here so that these are calculate once per render and
  // animated's utilities don't have to calculate them
  const halfMinIconSize = 0.5 * (minIconSize + 5);
  const halfMaxIconSize = 0.5 * (maxIconSize + 5);

  const breakpointViews = showBreakpoints ? (
    <Animated.View
      style={{
        top: Animated.subtract(Animated.divide(boxDimension.y, 2), 0.5 * lineThickness + 1),
        width: boxDimension.x,
        position: 'absolute',
      }}>
      <BreakpointViews
        ref={breakpointsRef}
        breakpoints={breakpoints}
        minBoundary={minBoundary}
        maxBoundary={maxBoundary}
        breakpointColor={breakpointColor}
        breakpointHeight={breakpointHeight}
        breakpointWidth={breakpointWidth}
        rootBoxWidth={rootBoxWidth.current}
      />
    </Animated.View>
  ) : null;

  const noSelect = Platform.select({
    web: {userSelect: 'none'},
    ios: {},
    android: {},
  });

  return (
    <View
      key="root-container"
      style={[
        {
          overflow: 'hidden',
          width: '100%',
          aspectRatio: 3,
          position: 'relative',
        },
      ]}
      onLayout={event => setDimensions(event.nativeEvent.layout)}>
      {showLabel === 'ShowFixed' && (
        <TextInput
          ref={fixedSliderTooltip}
          style={{
            position: 'absolute',
            textAlign: 'center',
            top: 10,
            left: '10%',
            color: fontColor,
          }}
          editable={false}
        />
      )}
      <Animated.View
        key="full-line"
        style={[
          {
            overflow: 'hidden',
            flexDirection: 'row',
            position: 'relative',
            backgroundColor: lineColor,
            height: lineThickness,
            width: Animated.multiply(0.8, boxDimension.x),
            left: Animated.multiply(0.1, boxDimension.x),
            // top = (boxHeight - lineThickness) / 2
            top: Animated.subtract(Animated.multiply(0.5, boxDimension.y), lineThickness * 0.5),
          },
        ]}
      />
      <Animated.View
        key="selected-line"
        style={[
          {
            backgroundColor: selectedLineColor,
            position: 'absolute',
            height: lineThickness,
            width: Animated.subtract(rightSliderOffset, leftSliderOffset),
            transform: [{translateX: leftSliderOffset}],
            top: Animated.subtract(Animated.multiply(0.5, boxDimension.y), lineThickness * 0.5),
          },
        ]}
      />
      {breakpointViews}
      {hideMinSlider === 'Show' && (
        <Animated.View
          key="min-slider"
          style={[
            {
              position: 'absolute',
              width: minIconSize + 10,
              height: boxDimension.y,
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              transform: [{translateX: Animated.subtract(leftSliderOffset, halfMaxIconSize)}],
            },
            noSelect,
          ]}
          {...(sliderType === '2 Way' ? minPanResponder.panHandlers : {})}>
          {showLabel === 'Show' && (
            <TextInput
              ref={minSliderTooltip}
              style={{
                position: 'absolute',
                textAlign: 'center',
                top: 10,
                color: fontColor,
                width: 100,
              }}
              editable={false}
            />
          )}
          <View
            style={[
              styles.circle,
              {
                backgroundColor: endPointsBackground,
                borderRadius: minIconSize + 5,
                width: minIconSize + 5,
                height: minIconSize + 5,
              },
            ]}>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
              }}>
              <MaterialCommunityIcons name={icon} size={minIconSize} color={iconColor} />
            </View>
          </View>
        </Animated.View>
      )}

      <Animated.View
        key="max-slider"
        style={[
          {
            position: 'absolute',
            width: maxIconSize + 10,
            height: boxDimension.y,
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            transform: [{translateX: Animated.subtract(rightSliderOffset, halfMinIconSize)}],
          },
          noSelect,
        ]}
        {...maxPanResponder.panHandlers}>
        {showLabel === 'Show' && (
          <TextInput
            ref={maxSliderTooltip}
            style={[
              typography,
              {
                position: 'absolute',
                textAlign: 'center',
                top: 10,
                color: fontColor,
                width: 100,
              },
            ]}
            editable={false}
          />
        )}
        <View
          style={[
            styles.circle,
            {
              backgroundColor: endPointsBackground,
              borderRadius: maxIconSize + 5,
              width: maxIconSize + 5,
              height: maxIconSize + 5,
            },
          ]}>
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
            }}>
            <MaterialCommunityIcons name={icon} size={maxIconSize} color={iconColor} />
          </View>
        </View>
      </Animated.View>
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'icon',
      props: {
        label: 'Slider Icon',
        placeholder: 'circle',
      },
    },
    {
      type: 'codeInput',
      name: 'minBoundary',
      props: {
        label: 'Min Boundary',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'maxBoundary',
      props: {
        label: 'Max Boundary',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'codeInput',
      name: 'minCurrentVal',
      props: {
        label: 'Min. Current Value (no binding)',
        placeholder: '0',
      },
    },
    {
      type: 'codeInput',
      name: 'maxCurrentVal',
      props: {
        label: 'Max. Current Value (no binding)',
        placeholder: '100',
      },
    },
    {
      type: 'radioGroup',
      name: 'hideMinSlider',
      props: {
        label: 'Hide min slider',
        options: ['Show', 'Hide'],
      },
    },
    {
      type: 'radioGroup',
      name: 'sliderType',
      props: {
        label: 'Slider Type',
        options: ['2 Way', '1 Way'],
      },
    },
    {
      type: 'radioGroup',
      name: 'showLabel',
      props: {
        label: 'Show Label',
        options: ['Show', 'ShowFixed', 'Hide'],
      },
    },
    {
      type: 'radioGroup',
      name: 'showBreakpoints',
      props: {
        label: 'Show Breakpoints',
        options: ['Show', 'Hide'],
      },
    },
    {
      type: 'radioGroup',
      name: 'breakpointsType',
      props: {
        label: 'Breakpoints Type',
        options: ['Static', 'Fixed Difference', 'Fixed Total Breakpoints'],
      },
    },
    {
      type: 'codeInput',
      name: 'breakpoints',
      props: {
        label: 'Breakpoints',
        placeholder: '25,50,75(Static) or 10(Fixed)',
      },
    },
    {
      type: 'codeInput',
      name: 'prefix',
      props: {
        label: 'Label prefix',
        placeholder: '₹',
      },
    },
  ],
  layout: [...defaultEditors.layout],
};

const propertySettings: PluginPropertySettings = {
  onChange: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'RangeSliderWidget',
  RangeSliderWidget,
  RangeSliderWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(RangeSliderWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'rangeSlider'],
  },
);
