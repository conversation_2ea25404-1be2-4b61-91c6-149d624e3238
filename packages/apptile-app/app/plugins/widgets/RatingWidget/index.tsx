/* eslint-disable react-native/no-inline-styles */
import React, {useCallback, useEffect, useState} from 'react';
import _ from 'lodash';
import {Text, TouchableOpacity, View} from 'react-native';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from '../../../styles/types';

import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';

const RatingWidgetConfig = {
  value: 0,
  readonly: false,
  onChange: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'Rating',
  type: 'widget',
  name: 'Rating',
  description: 'Display A 5 Star Rating',
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  section: 'Display',
  icon: 'star-rating',
  defaultHeight: 0,
  defaultWidth: 0,
};

export const ratingWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'codeInput',
    name: 'fontSize',
    props: {
      label: 'Size',
      placeholder: '0',
    },
  },
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
];

const RatingWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;

  const ratingValue = model.get('value') ? model.get('value') : 0;
  const readonly = model.get('readonly') ? model.get('readonly') : false;
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 5, flexDirection: 'row'});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const horizontalAlign = model.get('horizontalAlign', 'left');
  const verticalAlign = model.get('verticalAlign', 'auto');
  const flexDirection = model.get('flexDirection', 'row');

  const [value, setValue] = useState<number>(ratingValue);

  useEffect(() => {
    if (value !== ratingValue) {
      setValue(ratingValue);
    }
  }, [value, ratingValue, modelUpdate]);

  const handleChange = useCallback(
    (val: any) => {
      setValue(val);
      if (value !== val) {
        modelUpdate([
          {
            selector: ['value'],
            newValue: val,
          },
        ]);
      }
      triggerEvent('onChange');
    },
    [modelUpdate, triggerEvent, value],
  );

  const totalRatings = 5;
  if (readonly)
    return (
      <View
        ref={ref}
        style={[
          layoutStyles,
          modelPlatformStyles,
          {
            textAlign: horizontalAlign,
            textAlignVertical: verticalAlign,
            flexDirection: flexDirection,
          },
        ]}>
        {[...new Array(totalRatings)].map((arr, index) => {
          return index < value ? (
            <MaterialCommunityIcons
              key={index}
              style={[
                layoutStyles,
                modelPlatformStyles,
                {
                  textAlign: horizontalAlign,
                  textAlignVertical: verticalAlign,
                },
              ]}
              name={
                index == Math.floor(value) && Math.round(value) - index == 1
                  ? 'star-half-full'
                  : value - index > 0 && value - index < 1
                  ? 'star-outline'
                  : 'star'
              }
            />
          ) : (
            <MaterialCommunityIcons
              key={index}
              style={[
                layoutStyles,
                modelPlatformStyles,
                {
                  textAlign: horizontalAlign,
                  textAlignVertical: verticalAlign,
                },
              ]}
              name="star-outline"
            />
          );
        })}
      </View>
    );
  else
    return (
      <View
        ref={ref}
        style={[
          layoutStyles,
          modelPlatformStyles,
          {
            textAlign: horizontalAlign,
            textAlignVertical: verticalAlign,
            flexDirection: flexDirection,
          },
        ]}>
        {[...new Array(totalRatings)].map((arr, index) => {
          return index < value ? (
            <TouchableOpacity key={index} activeOpacity={0.7} onPress={() => handleChange(index + 1)}>
              <MaterialCommunityIcons
                style={[
                  layoutStyles,
                  modelPlatformStyles,
                  {
                    textAlign: horizontalAlign,
                    textAlignVertical: verticalAlign,
                  },
                ]}
                name={
                  index == Math.floor(value) && Math.round(value) - index == 1
                    ? 'star-half-full'
                    : value - index > 0 && value - index < 1
                    ? 'star-outline'
                    : 'star'
                }
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity key={index} activeOpacity={0.7} onPress={() => handleChange(index + 1)}>
              <MaterialCommunityIcons
                style={[
                  layoutStyles,
                  modelPlatformStyles,
                  {
                    textAlign: horizontalAlign,
                    textAlignVertical: verticalAlign,
                  },
                ]}
                name="star-outline"
              />
            </TouchableOpacity>
          );
        })}
      </View>
    );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '{{textInput1.value}}',
      },
    },
    {
      type: 'checkbox',
      name: 'readonly',
      props: {
        label: 'Read Only',
      },
    },
  ],
  layout: [...defaultEditors.layout],
};
const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, val, sel) => {
      return isNaN(Number(val)) ? Number(val) : Number(val);
    },
  },
  onChange: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget('RatingWidget', RatingWidget, RatingWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(ratingWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'star'],
});
