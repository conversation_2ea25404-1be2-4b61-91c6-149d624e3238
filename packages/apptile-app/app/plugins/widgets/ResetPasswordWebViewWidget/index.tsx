import React, {useEffect, useRef, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {WebView} from 'react-native-webview';

import _ from 'lodash';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles, modelUpdateAction, navigateToScreen} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {useDispatch} from 'react-redux';

type ResetPasswordWebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
  onTokenExpiryRedirectTo: string;
  incognito: boolean;
  domStorageEnabled: boolean;
  injectedJavaScriptWhileContentIsLoading: string;
  resetPasswordUrl: string;
};

interface NavState {
  url: string;
  title: string;
  loading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
}

const ResetPasswordWebViewWidgetConfig: ResetPasswordWebViewWidgetConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
  onTokenExpiryRedirectTo: '',
  incognito: true,
  domStorageEnabled: false,
  injectedJavaScriptWhileContentIsLoading: '',
  resetPasswordUrl: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'resetPasswordWebView',
  type: 'widget',
  name: 'Reset Password Web View',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const ResetPasswordWebViewWidget = React.forwardRef((props, ref) => {
  const r = useRef(null);
  const dispatch = useDispatch();
  const [redirectCount, setRedirectCount] = useState(0);
  const {model, modelUpdate, modelStyles, config, triggerEvent, pageKey, id} = props;
  const value = model.get('value')?.toString();

  const headers = model.get('headers')?.toString();
  const injectedJavaScript = model.get('injectedJavaScript').toString();
  const injectedJavaScriptWhileContentIsLoading = model.get('injectedJavaScriptWhileContentIsLoading')?.toString();

  const onTokenExpiryRedirectTo = model.get('onTokenExpiryRedirectTo');
  const incognito = model.get('incognito');
  const domStorageEnabled = model.get('domStorageEnabled');
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onSuccess = () => {
    triggerEvent('onSuccess');
  };
  const onTokenExpiry = () => {
    triggerEvent('onTokenExpiry');
  };

  const onTokenAcquired = () => {
    triggerEvent('onTokenAcquired');
  };

  const onError = () => {
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onMessage = event => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.type === 'log') {
      console.log('WebView log:', data.message);
    }
  };

  // convert key-value pair input into an object
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};
  // let source = {uri: value};
  // requestHeaders && (source.headers = requestHeaders);
  const [webViewKey, setKey] = useState(1);
  const [source, setSource] = useState({uri: value, headers: requestHeaders});
  useEffect(() => {
    let newSource = {
      uri: value,
      headers: requestHeaders,
    };
    if (!_.isEqual(source, newSource)) {
      setSource(newSource);
      setKey(webViewKey + 1);
    }
  }, [requestHeaders, source, value, webViewKey]);

  const handleNavigationStateChange = (navState: NavState) => {
    const url = navState.url;
    setRedirectCount(prevCount => prevCount + 1);
    if (url.includes('/account/invalid_token')) {
      onTokenExpiry();
      dispatch(navigateToScreen(onTokenExpiryRedirectTo, {}));
    }
    if (
      url.includes('/account/reset/') &&
      ((Platform.OS === 'ios' && redirectCount > 1) || (Platform.OS === 'android' && redirectCount >= 1))
    ) {
      modelUpdate([{selector: ['resetPasswordUrl'], newValue: url}]);
      onTokenAcquired();
    }
  };

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      {source.uri && (
        <WebView
          // androidLayerType="software"

          incognito={incognito}
          key={webViewKey}
          onLoadEnd={onLoadEnd}
          cacheEnabled={false}
          cacheMode="LOAD_NO_CACHE"
          onSuccess={onSuccess}
          originWhitelist={['*']}
          source={source}
          onNavigationStateChange={handleNavigationStateChange}
          onError={onError}
          onMessage={onMessage}
          domStorageEnabled={domStorageEnabled}
          {...(injectedJavaScriptWhileContentIsLoading
            ? {injectedJavaScript: injectedJavaScriptWhileContentIsLoading}
            : {})}
          {...(injectedJavaScript ? {injectedJavaScriptBeforeContentLoaded: injectedJavaScript} : {})}
          javaScriptEnabled={true}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'onTokenExpiryRedirectTo',
      props: {
        label: 'On Token Expiry Redirect To',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'incognito',
      props: {
        label: 'Incognito Mode\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'checkbox',
      name: 'domStorageEnabled',
      props: {
        label: 'DomStorage Enabled\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScriptWhileContentIsLoading',
      props: {
        label: 'Injected JS Loading',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  loader: {
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
});

export default connectWidget(
  'ResetPasswordWebViewWidget',
  ResetPasswordWebViewWidget,
  ResetPasswordWebViewWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
