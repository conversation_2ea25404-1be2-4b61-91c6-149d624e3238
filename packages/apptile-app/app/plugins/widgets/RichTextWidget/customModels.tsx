import _ from 'lodash';
import {iframeModel} from '@native-html/iframe-plugin';
import {defaultHTMLElementModels} from 'react-native-render-html';

const excludedStyles = ['fontWeight', 'fontStyle'];

_.forEach(defaultHTMLElementModels, (v, k) => {
  _.set(
    defaultHTMLElementModels,
    `${k}.mixedUAStyles`,
    _.omit(_.get(defaultHTMLElementModels, `${k}.mixedUAStyles`), excludedStyles),
  );
});

const customHTMLElementModels = {
  iframe: iframeModel,
};

export default customHTMLElementModels;
