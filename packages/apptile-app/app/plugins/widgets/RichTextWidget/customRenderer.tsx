import React from 'react';
import {CustomRendererProps, TBlock, useRendererProps} from 'react-native-render-html';
import {RichTextIFrame} from './iframe';
import {RichTextImage} from './image';
import {UnorderedListRenderer, OrderedListRenderer, ListItemRenderer} from './list';

const IframeRenderer = (props: any) => {
  return <RichTextIFrame iframeNode={props.tnode.domNode} />;
};

const ImgRenderer = (props: any) => {
  return <RichTextImage imageNode={props.tnode.domNode} styles={props.style} />;
};

const ScriptRenderer = (_props: any) => {
  return <></>;
};

// Create wrapper renderers that match the expected interface
const UlRenderer = (props: CustomRendererProps<TBlock>) => {
  const propsFromCustomRenderer = useRendererProps('ul');
  return UnorderedListRenderer(props, propsFromCustomRenderer);
};

const OlRenderer = (props: CustomRendererProps<TBlock>) => {
  const propsFromCustomRenderer = useRendererProps('ol');
  return OrderedListRenderer(props, propsFromCustomRenderer);
};

const LiRenderer = (props: CustomRendererProps<TBlock>) => {
  const propsFromCustomRenderer = useRendererProps('li');
  return ListItemRenderer(props, propsFromCustomRenderer);
};

const customRenderer = {
  iframe: IframeRenderer,
  img: ImgRenderer,
  script: ScriptRenderer,
  ul: UlRenderer,
  ol: OlRenderer,
  li: LiRenderer,
};

export default customRenderer;
