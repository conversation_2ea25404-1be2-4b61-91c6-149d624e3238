import React from 'react';
import {Dimensions, Platform} from 'react-native';
import {WebView} from 'react-native-webview';
const window = Dimensions.get('window');
import validator from 'validator';

type IRichTextIFrame = {
  iframeNode: any;
};
export const RichTextIFrame: React.FC<IRichTextIFrame> = props => {
  const [frameHeight, setFrameHeight] = React.useState<number | string>(0);
  const screenWidth = Platform.OS === 'web' ? 445 : window.width;
  const screenHeight = Platform.OS === 'web' ? 445 : window.height;

  const {width, height} = props.iframeNode.attribs;

  React.useEffect(() => {
    if (width && height) {
      const ratio = height / screenHeight;
      if (isNaN(ratio)) {
        setFrameHeight('auto');
      } else {
        setFrameHeight(height * ratio);
      }
    }
    else{
      const YOUTUBE_VIDEO_ASPECT_RATIO = 9/16
      setFrameHeight(YOUTUBE_VIDEO_ASPECT_RATIO * screenWidth)
    }
  }, [height, screenWidth, width]);

  const attribs = {...props.iframeNode.attribs, width: screenWidth, height: frameHeight};

  if (!attribs.src) {
    return <></>;
  }

  // if (attribs.src.includes('youtube')) {
  //   return <></>;
  // }

  if (!validator.isURL(attribs.src)) {
    return <></>;
  }

  return (
     <WebView
      originWhitelist={['*']}
      source={{uri: attribs.src}}
      // style={{resizeMode: 'contain', opacity: 0.99}}
      scalesPageToFit={false}
      style={{height: frameHeight, width: '100%', resizeMode: 'cover', opacity: 0.99}}
      // androidLayerType="software"
      androidHardwareAccelerationDisabled={true}
    />
  );
};
