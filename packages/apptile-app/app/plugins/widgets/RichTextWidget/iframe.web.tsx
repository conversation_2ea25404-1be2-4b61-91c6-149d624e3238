import React from 'react';
import {Dimensions, Platform} from 'react-native';

const window = Dimensions.get('window');

type IRichTextIFrame = {
  iframeNode: any;
};
export const RichTextIFrame: React.FC<IRichTextIFrame> = props => {
  const [frameHeight, setFrameHeight] = React.useState<string | number>(0);
  const screenWidth = Platform.OS === 'web' ? 445 : window.width;

  const {width, height, ...restAttributes} = props.iframeNode.attribs;

  React.useEffect(() => {
    if (width && height) {
      const ratio = screenWidth / width;
      setFrameHeight(height * ratio);
    }
    else{
      setFrameHeight("auto")
    }
  }, [height, screenWidth, width]);

  const {frameborder: frameBorder, allowfullscreen: allowFullScreen, ...restAttribs} = restAttributes;
  const attribs = {...restAttribs, frameBorder, allowFullScreen, width: '100%', height: frameHeight};

  if (!attribs.src) {
    return <></>;
  }

  return <iframe {...attribs} />;
};
