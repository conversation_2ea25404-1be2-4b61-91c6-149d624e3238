import {Image, StyleSheet} from 'react-native';
import React from 'react';
import FastImage from 'react-native-fast-image';
import {isEmpty} from 'lodash';
import {parseSrcset} from 'srcset';

type IRichTextImage = {
  imageNode: any;
  styles: Record<string, any>;
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 'auto',
  },
});

export const RichTextImage: React.FC<IRichTextImage> = props => {
  const [aspectRatio, setAspectRatio] = React.useState(1);
  const {imageNode, styles: DomStyle} = props;

  const {srcset, src} = imageNode.attribs;
  const stylesFromTheme = DomStyle ?? {};
  let imageUri;

  if (srcset) {
    let scrapedSrc = parseSrcset(srcset);
    imageUri = (scrapedSrc[Math.floor(scrapedSrc.length / 2)] || scrapedSrc[0]).url;
  } else {
    imageUri = src;
  }

  imageUri = imageUri?.split('//')[1];

  if (isEmpty(imageUri)) {
    return <></>;
  }

  imageUri = `https://${imageUri}`;
  const {resizeMode, ...restStyles} = stylesFromTheme;

  Image.getSize(imageUri, (width, height) => {
    setAspectRatio(width / height);
  });

  return (
    <FastImage
      source={{
        uri: imageUri,
      }}
      style={[{...restStyles, aspectRatio}, styles.container]}
      resizeMode={resizeMode}
    />
  );
};
