import React from 'react';
import _ from 'lodash';
import {View, useWindowDimensions, Platform, I18nManager} from 'react-native';
import RenderHtml from 'react-native-render-html';

import {getPlatformStyles} from 'apptile-core';
import {isTypographyStyleSheet} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';

import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {connectWidget, defaultEditors} from 'apptile-core';
import {editableTags} from 'apptile-core';
import {useLoadedFonts} from 'apptile-core';
import {isJSBinding} from 'apptile-core';

import customRenderer from './customRenderer';
import customModel from './customModels';
import docs from './docs';

const RichTextWidgetConfig = {
  value: 'Html Mark up',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'richText',
  type: 'widget',
  name: 'Rich Text',
  description: 'Parses the html to react native components',
  defaultHeight: 20,
  defaultWidth: 'auto',
  section: 'Inputs',
  icon: 'text',
  layout: {},
};

//TODO: GUIDELINES AROUND COMPONENT FOR BETTER VISIBILITY OF TAGS
const RichTextWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config} = props;
  const {loadedFonts} = useLoadedFonts();

  const modelValue = model.get('value');

  let value;
  // Make sure that function is always called monomorphically to avoid deoptimization
  if (typeof modelValue === 'string') {
    value = isJSBinding(modelValue) ? '' : modelValue;
  } else {
    value = modelValue;
  }

  // Get the alignment property from the model
  const alignment = _.isEmpty(model.get('horizontalAlignment', 'left'))
    ? 'left'
    : model.get('horizontalAlignment', 'left');

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const modelPlatformStyles = getPlatformStyles(modelStyles) ?? {};

  const tagsStyles = {};
  const containerStyles = {};
  const fonts = new Set<string>([]);
  let containerTypographyStyles = {};

  // Scraping out container styles
  _.forEach(modelPlatformStyles, (styleVal, styleKey) => {
    if (!styleKey.includes('_')) {
      if (!isTypographyStyleSheet(styleVal)) {
        _.set(containerStyles, styleKey, styleVal);
      } else {
        if (styleVal.fontFamily) {
          fonts.add(styleVal.fontFamily);
        }
        containerTypographyStyles = styleVal;
      }
    }
  });

  // Scraping out tags styles
  _.forEach(editableTags, entry => {
    const tagName = entry.toLowerCase();

    _.forEach(modelPlatformStyles, (styleVal, styleKey) => {
      if (_.startsWith(styleKey, `${tagName}_`)) {
        if (isTypographyStyleSheet(styleVal)) {
          if (styleVal.fontFamily) {
            fonts.add(styleVal.fontFamily);
          }
          _.set(tagsStyles, tagName, {..._.get(tagsStyles, tagName, {}), ...styleVal});
        } else {
          _.set(tagsStyles, _.replace(styleKey, '_', '.'), styleVal);
        }
      }
    });
  });

  const systemFonts = Platform.OS === 'web' ? Array.from(fonts) : (Object.values(loadedFonts) as string[]);

  return (
    <View ref={ref} style={[layoutStyles, containerStyles]}>
      <RichTextDisplay
        html={value}
        tagsStyles={tagsStyles}
        baseStyle={containerTypographyStyles}
        systemFonts={systemFonts}
        alignment={alignment}
        modelPlatformStyles={modelPlatformStyles}
      />
    </View>
  );
});

type RichTextDisplayProps = {
  html: string;
  tagsStyles?: Record<string, any>;
  baseStyle?: Record<string, any>;
  systemFonts?: string[];
  alignment?: 'left' | 'center' | 'right' | 'auto' | 'justify';
  modelPlatformStyles: any;
};

export const RichTextDisplay: React.FC<RichTextDisplayProps> = React.memo(
  props => {
    const {html, tagsStyles, baseStyle, systemFonts, alignment = 'left', modelPlatformStyles} = props;
    const {width} = useWindowDimensions();

    if (_.isEmpty(html)) {
      return <></>;
    }

    const listStyles: any = {};
    Object.keys(modelPlatformStyles).forEach(key => {
      const [element, property] = key.split('_');
      if (['ul', 'ol', 'li'].includes(element)) {
        if (!listStyles[element]) {
          listStyles[element] = {};
        }
        listStyles[element][property] = modelPlatformStyles[key];
      }
    });

    // Apply alignment to the base style
    const updatedBaseStyle = {
      ...baseStyle,
      textAlign: alignment,
    };

    return (
      <RenderHtml
        source={{
          html: html,
        }}
        contentWidth={width}
        enableCSSInlineProcessing={false}
        tagsStyles={tagsStyles}
        baseStyle={updatedBaseStyle}
        renderers={customRenderer}
        renderersProps={{
          ul: {
            markerTextStyle: {
              textAlign: alignment,
            },
          },
          li: {
            markerTextStyle: {
              textAlign: alignment,
              ulIconSize: listStyles.li?.ulIconSize,
              olIconSize: listStyles.li?.olIconSize,
              color: listStyles.li?.color,
            },
          },
          ol: {
            markerTextStyle: {
              textAlign: alignment,
            },
          },
        }}
        customHTMLElementModels={customModel}
        ignoredDomTags={['source', 'meta']}
        systemFonts={systemFonts}
      />
    );
  },
  (prevProps, currentProps) => _.isEqual(prevProps, currentProps),
);

const richTextWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'richTextStyleControl',
    name: 'richText', // Just a placeholder not using name refer richTextControl to know working
    props: {
      label: 'richText',
    },
  },
];

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (_model, renderedValue, _selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};

const emptyOnupdate = null;

// Define custom editors for the widget
const customEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'HTML Content',
      },
    },
  ],
  layout: [
    {
      type: 'radioGroup',
      name: 'horizontalAlignment',
      props: {
        label: 'Horizontal Alignment',
        options: [
          {icon: 'alpha-a', value: 'auto'},
          {icon: 'format-align-left', value: 'left'},
          {icon: 'format-align-center', value: 'center'},
          {icon: 'format-align-right', value: 'right'},
          {icon: 'format-align-justify', value: 'justify'},
        ],
      },
    },
    ...defaultEditors.layout,
  ],
};

export default connectWidget('RichTextWidget', RichTextWidget, RichTextWidgetConfig, emptyOnupdate, customEditors, {
  propertySettings,
  widgetStyleConfig: richTextWidgetStyleConfig,
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'richTextV2'],
});
