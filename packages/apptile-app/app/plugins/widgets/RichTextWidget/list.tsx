import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {View, StyleSheet} from 'react-native';
import {TNodeChildrenRenderer, CustomRendererProps, TBlock, ListElementConfig} from 'react-native-render-html';

const styles = StyleSheet.create({
  list: {
    paddingLeft: 16,
    marginVertical: 8,
  },
  listItem: {
    marginVertical: 4,
  },
});

export function UnorderedListRenderer(props: CustomRendererProps<TBlock>, propsFromCustomRenderer: ListElementConfig) {
  const {tnode} = props;

  // Get the alignment from the parent component
  const alignment = propsFromCustomRenderer.markerTextStyle?.textAlign || 'auto';

  return (
    <View style={{...styles.list, alignItems: getAlignmentValue(alignment)}}>
      <TNodeChildrenRenderer tnode={tnode} />
    </View>
  );
}

export function OrderedListRenderer(props: CustomRendererProps<TBlock>, propsFromCustomRenderer: ListElementConfig) {
  const {tnode} = props;

  const alignment = propsFromCustomRenderer.markerTextStyle?.textAlign || 'auto';

  return (
    <View style={{...styles.list, alignItems: getAlignmentValue(alignment)}}>
      <TNodeChildrenRenderer tnode={tnode} />
    </View>
  );
}

const getNestedIndex = (tnode, alignment) => {
  let indexStack = [];
  let currentNode = tnode;

  while (currentNode?.parent) {
    if (currentNode.tagName === 'li' && currentNode.parent.tagName === 'ol') {
      let listItems = currentNode.parent.children.filter(child => child.tagName === 'li'); // Only <li> elements
      let parentIndex = listItems.findIndex(child => child === currentNode);

      if (parentIndex !== -1) {
        indexStack.unshift(parentIndex + 1); // Ensure numbering starts at 1
      }
    }

    // Move up to the parent <ol>, then find the <li> that contains it
    if (currentNode.parent.tagName === 'ol') {
      currentNode = currentNode.parent.parent; // Move to the <li> that contains this <ol>
    } else {
      currentNode = currentNode.parent;
    }
  }

  if (indexStack.length === 0) return '1';

  // let result = indexStack;
  let result = indexStack.join('.');

  // Fix for right alignment: No extra dots for top-level items
  if (alignment === 'right') {
    return indexStack.length === 1 ? `.${result}` : result;
  }

  return result;
};

export function ListItemRenderer(props: CustomRendererProps<TBlock>, propsFromCustomRenderer: ListElementConfig) {
  const {tnode} = props;

  // Use type assertion to avoid TypeScript errors
  const alignment = propsFromCustomRenderer?.markerTextStyle?.textAlign || 'auto';
  const ulIconSize = propsFromCustomRenderer?.markerTextStyle?.ulIconSize || 18;
  const olIconSize = propsFromCustomRenderer?.markerTextStyle?.olIconSize || 12;
  const IconColor = propsFromCustomRenderer?.markerTextStyle?.color || 'black';

  // Determine if this is part of an ordered or unordered list
  const isOrderedList = tnode.parent?.tagName === 'ol';

  const marker = isOrderedList ? (
    <TextElement
      style={{
        textAlign: alignment,
        alignSelf: 'center',
        marginLeft: alignment === 'right' ? 8 : 0,
        marginRight: alignment === 'right' ? 0 : 8,
        color: IconColor,
        fontSize: olIconSize,
      }}>
      {getNestedIndex(tnode, alignment)}
    </TextElement>
  ) : (
    <TextElement
      style={{
        textAlign: alignment,
        alignSelf: 'center',
        marginLeft: alignment === 'right' ? 8 : 0,
        marginRight: alignment === 'right' ? 0 : 8,
        color: IconColor,
        fontSize: ulIconSize,
        fontWeight: 'bold',
      }}>
      &#x2022;
    </TextElement>
  );

  // Create the content element with children renderer
  // const content = React.createElement(
  //   View,
  //   {
  //     style: [styles.content, {alignItems: getAlignmentValue(alignment)}],
  //   },
  //   React.createElement(TNodeChildrenRenderer, {tnode}),
  // );

  return (
    <View
      style={[
        styles.listItem,
        {
          flexDirection: getFlexDirection(alignment),
          alignContent: 'center',
          justifyContent: getAlignmentValue(alignment),
        },
      ]}>
      <View>{marker}</View>
      <View>
        <TNodeChildrenRenderer tnode={tnode} />
      </View>
    </View>
  );
}

// Helper function to convert textAlign to alignItems value
function getAlignmentValue(textAlign: string): 'flex-start' | 'center' | 'flex-end' | 'stretch' {
  switch (textAlign) {
    case 'center':
      return 'center';
    case 'right':
      return 'flex-end';
    case 'justify':
    case 'left':
    case 'auto':
    default:
      return 'flex-start';
  }
}

function getFlexDirection(textAlign: string): 'row' | 'row-reverse' | 'column' | 'column-reverse' {
  switch (textAlign) {
    case 'auto':
    case 'left':
      return 'row';
    case 'right':
      return 'row-reverse';
    case 'center':
      return 'row';
    case 'justify':
      return 'row';
    default:
      return 'row';
  }
}
