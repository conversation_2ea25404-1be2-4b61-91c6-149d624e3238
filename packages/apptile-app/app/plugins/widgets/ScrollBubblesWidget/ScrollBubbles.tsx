/* eslint-disable react-native/no-inline-styles */
import {ApptileAnimationsContext} from 'apptile-core';
import {makeBoolean} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import _, { last } from 'lodash';
import React, {useEffect, useContext, useRef, useCallback} from 'react';
import {PluginEditorsConfig} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {EditorStyleItem, defaultEditors} from 'apptile-core';
import {WidgetProps, connectWidget} from 'apptile-core';
import docs from './docs';
import {View, Animated} from 'react-native';

const ScrollBubblesWidgetConfig = {
  value: '',
  isLoading: false,
  scaleBubbles: true
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'scrollBubbles',
  type: 'widget',
  name: 'Scroll Indicator(Bubbles)',
  description: 'Display Bubble Indicator on screen tied to a listview carousel.',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'text',
};

export const ScrollBubblesWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'numericInput',
    name: 'Itemwidth',
    props: {
      label: 'Item Width',
      noUnit: true,
    },
  },
  {
    type: 'numericInput',
    name: 'Itemheight',
    props: {
      label: 'Item Height',
      noUnit: true,
    },
  },
  {
    type: 'colorInput',
    name: 'ItembackgroundColor',
    props: {
      label: 'Item Background',
    },
  },
  {
    type: 'colorInput',
    name: 'ActiveItembackgroundColor',
    props: {
      label: 'Active Item Background',
    },
  },
  {
    type: 'numericInput',
    name: `ItemborderRadius`,
    props: {
      label: `Item Border Radius`,
      noUnit: true,
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
    },
  },
];

const ScrollBubblesWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const animProvider = useContext(ApptileAnimationsContext);
  const {model, modelStyles, config, isAnimated, animations, pageKey} = props;
  const modelValue = model?.get('value');
  const modelPadItems = model?.get('padItems');
  const padItems = _.toInteger(modelPadItems) || 2;
  const value = _.isNumber(modelValue) ? _.toNumber(modelValue) : 0;
  let scaleBubbles = model?.get('scaleBubbles');
  if (scaleBubbles !== false) {
    scaleBubbles = true;
  }

  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const animValues = animations?.get('references').get('currentItem');
  const namespace = config.get('namespace');
  let eventName: string = "";
  if (namespace && animValues) {
    const namespaceSelector = namespace.getNamespace().join('.');
    eventName = `${pageKey}.${namespaceSelector}::${animValues.join('.')}`;
  } else if (animValues) {
    eventName = `${pageKey}.${animValues.join('.')}`;
  }
  // console.log("eventName: ", eventName);

  const modelPlatformStyles = modelStyles ? getPlatformStyles(modelStyles) : {};
  const {Itemwidth, Itemheight, ItembackgroundColor, ItemborderRadius, ActiveItembackgroundColor, ...restModelPlatformStyles} =
    modelPlatformStyles;
  const commonBubbleStyles = {
    width: Itemwidth ?? 16,
    height: Itemheight ?? 16,
    backgroundColor: ItembackgroundColor ?? '#888',
    borderRadius: ItemborderRadius ?? 8,
    activeItemBackgroundColor: (ActiveItembackgroundColor ?? ItembackgroundColor) ?? '#888'
  };

  const numBubbles = Math.min(value, 5);
  const mainWidth = (commonBubbleStyles.width + padItems * 2) * numBubbles;

  // Offset of the current item being shown on the scroll, the active bubble
  const animatedOffsetCurrent = useRef(new Animated.Value(0)).current;
  // Offset of the group of inactive bubbles
  const animatedWindowOffset = useRef(new Animated.Value(0)).current;
  // Previous value of currentItem
  const lastItem = useRef(0);

  const scaleFactors = [
    useRef(new Animated.Value(1)).current,
    useRef(new Animated.Value(1)).current,
    useRef(new Animated.Value(1)).current,
    useRef(new Animated.Value(1)).current,
    useRef(new Animated.Value(1)).current,
  ];

  const scaleFactorsMiddle = [0.6, 0.8, 0.85, 0.8, 0.6];
  const scaleFactorsRight = [0.5, 0.5, 0.6, 0.8, 0.85];
  const scaleFactorsLeft = [0.85, 0.8, 0.6, 0.5, 0.5];

  if (scaleBubbles) {
    if (numBubbles < value) {
      for (let i = 0; i < scaleFactors.length; ++i) {
        Animated.timing(scaleFactors[i],
          {
            toValue: scaleFactorsLeft[i],
            duration: 150,
            useNativeDriver: true
          }).start();
      }
    } else {
      // On component mount currentItem is 0
      const currentItem = 0;
      const scalingFactors = [0.85, 0.7, 0.6, 0.5, 0.3];
      for (let i = 0; i < value; ++i) {
        const distanceFromCenter = Math.abs(i - currentItem);
        Animated.timing(scaleFactors[i],
          {
            toValue: scalingFactors[distanceFromCenter],
            duration: 100,
            useNativeDriver: true
          }
        ).start();
       }
    }
  }

  const eventCallback = useCallback((payload) => {
    function applyMiddleScalingToBubbles () {
      for (let i = 0; i < scaleFactors.length; ++i) {
        Animated.timing(scaleFactors[i],
          {
            toValue: scaleFactorsMiddle[i],
            duration: 150,
            useNativeDriver: true
          }).start();
      }
    }
    
    function applyLeftScalingToBubbles() {
      Animated.timing(animatedWindowOffset, 
          {
            toValue: 1,
            duration: 100,
            useNativeDriver: true
          }
        ).start(() => {
          animatedWindowOffset.setValue(0);
          if (scaleBubbles) {
            for (let i = 0; i < scaleFactors.length; ++i) {
              Animated.timing(scaleFactors[i],
                {
                  toValue: scaleFactorsLeft[i],
                  duration: 150,
                  useNativeDriver: true
                }).start();
            }
          }
        });
    }
    
    function applyRightScalingToBubbles() {
      Animated.timing(animatedWindowOffset, 
          {
            toValue: -1,
            duration: 100,
            useNativeDriver: true
          }
        ).start(() => {
          animatedWindowOffset.setValue(0);
          if (scaleBubbles) {
            for (let i = 0; i < scaleFactors.length; ++i) {
              Animated.timing(scaleFactors[i],
                {
                  toValue: scaleFactorsRight[i],
                  duration: 150,
                  useNativeDriver: true
                }).start();
            }
          }
        });
    }

    function slideWindowLeft() {
      Animated.timing(animatedWindowOffset, 
        {
          toValue: -1,
          duration: 100,
          useNativeDriver: true
        }
      ).start(() => {
        animatedWindowOffset.setValue(0);
      });
    }

    function slideWindowRight() {
      Animated.timing(animatedWindowOffset, 
        {
          toValue: 1,
          duration: 100,
          useNativeDriver: true
        }
      ).start(() => {
        animatedWindowOffset.setValue(0);
      });
    }

    function scaleByCurrentItem(currentItem) {
      const scalingFactors = [0.85, 0.6, 0.5, 0.4, 0.3]; 
         for (let i = 0; i < value; ++i) {
          const distanceFromCenter = Math.abs(currentItem - i);
          Animated.timing(scaleFactors[i],
            {
              toValue: scalingFactors[distanceFromCenter],
              duration: 100,
              useNativeDriver: true
            }
          ).start();
        }
    }

    function animateBubbleWithOrigin(originOffset: number, currentItem: number) {
      Animated.timing(animatedOffsetCurrent,
        {
           toValue: (originOffset + currentItem) * ((2 * padItems) + commonBubbleStyles.width),
           duration: 100,
           useNativeDriver: true
        }).start();
    }

    let currentItem;
    const itemThatBecameVisible = payload.changed.find(it => it.isViewable)?.index ?? -1; 
    if (itemThatBecameVisible >= 0) {
      currentItem = itemThatBecameVisible;
    } else {
      currentItem = lastItem.current;
    }
    console.log("lastItem: ", lastItem.current, " currentItem: ", currentItem);
    let direction: string = "";
    if (currentItem > lastItem.current) {
      direction = "right";
    } else if (currentItem < lastItem.current) {
      direction = "left";
    } else {
      direction = "";
    }

    if (value >= 8) {
      const inMiddle = (currentItem > 2) && (currentItem < ((value - 2) - 1));
      const wasInMiddle = (lastItem.current > 2) && (lastItem.current < ((value - 2) - 1));

      console.log("configs: ", direction, wasInMiddle, inMiddle, currentItem, value - 3)
      if (wasInMiddle != inMiddle) {
        if (inMiddle && scaleBubbles) {
          applyMiddleScalingToBubbles();
        } else if (currentItem <= 2) {
          applyLeftScalingToBubbles();
        } else {
          applyRightScalingToBubbles();
        }
      }

      if (direction !== "") {
        // console.log(`inMiddle: ${inMiddle}, direction: ${direction}, currentItem: ${currentItem}, value-2: ${value - 2}`);
        if (inMiddle && (direction === "right")) {
          // shift window left
          slideWindowLeft();
        } else if (inMiddle && (direction === "left")) {
          // shift window right
          slideWindowRight();
        } else {
          if (currentItem < 5) {
            animateBubbleWithOrigin(0, currentItem);
          } else {
            animateBubbleWithOrigin(5 - value, currentItem);
          }
        }
      }
    // 6 and 7 are special cases
    } else if (value == 7) {
      let direction = "";
      if (currentItem > lastItem.current) {
        direction = "right";
      } else if (currentItem < lastItem.current) {
        direction = "left";
      }
      
      if (direction == "right" && currentItem == 2) {
        applyMiddleScalingToBubbles();
        slideWindowLeft();
        animateBubbleWithOrigin(0, currentItem);
      } else if (direction == "right" && (currentItem == 3 || currentItem == 4)) {
        slideWindowLeft();
      } else if (direction == "left" && currentItem == 4) {
        applyMiddleScalingToBubbles();
        slideWindowRight();
        animateBubbleWithOrigin(-2, currentItem);
      } else if (direction == "left" && (currentItem == 2 || currentItem == 3)) {
        slideWindowRight();
      } else if (direction == "right" && currentItem < 2) {
        animateBubbleWithOrigin(0, currentItem);
      } else if (direction == "left" && currentItem > 4) {
        animateBubbleWithOrigin(-2, currentItem);
      } else if (direction == "right" && currentItem > 4) {
        applyRightScalingToBubbles();
        animateBubbleWithOrigin(-2, currentItem);
      } else if (direction == "left" && lastItem.current < 3) {
        if (lastItem.current == 2) {
          applyLeftScalingToBubbles();
        }
        animateBubbleWithOrigin(0, currentItem);
      } 
    } else if (value == 6) {
      // To understand this part just draw out what happens when there are 6
      // bubbles and follow the animations 
      // There are two cases for the window position 
      // | 0, 1, 2, 3, 4 | 5
      // 0 | 1, 2, 3, 4, 5|
      // in each the bubble can be moving left or right. The following encodes
      // which animations to run for each possible movement
      let direction = "";
      if (currentItem > lastItem.current) {
        direction = "right";
      } else if (currentItem < lastItem.current) {
        direction = "left";
      }
      if (direction != "") {
        if (direction == "right" && (currentItem < 3)) {
          animateBubbleWithOrigin(0, currentItem);
        } else if (direction == "right" && currentItem == 3) {
          slideWindowLeft();
          applyMiddleScalingToBubbles();
        } else if (direction == "right" && currentItem > 3) {
          animateBubbleWithOrigin(-1, currentItem);
          if (currentItem == 4) {
            applyRightScalingToBubbles();
          }
        } else if (direction == "left" && (currentItem > 3)) {
          animateBubbleWithOrigin(-1, currentItem);
        } else if (direction == "left" && (currentItem == 3)) {
          slideWindowRight();
          applyMiddleScalingToBubbles();
          animateBubbleWithOrigin(-1, currentItem);
        } else if (direction == "left" && (currentItem == 2)) {
          slideWindowRight();
          applyLeftScalingToBubbles();
        } else if (direction == "left" && (currentItem < 2)) {
          animateBubbleWithOrigin(0, currentItem);
        } else {
          console.error("Bubbles should never have reached here")
        }
      }
    
    } else if (value <= 5) {
      if (direction !== "") {
        animateBubbleWithOrigin(0, currentItem);
        if (scaleBubbles) {
          scaleByCurrentItem(currentItem);
        }
      }
    }
    lastItem.current = currentItem;
  }, [value]);

  useEffect(() => {
    if (eventName) {
      animProvider.addListener(eventName, eventCallback);
      return () => {
        animProvider.removeListener(eventName, eventCallback);
      }
    }
  }, [eventCallback, eventName]);

  const bubbles = new Array(numBubbles).fill(0).map((_, idx) => {
    const translateX = Animated.add(
      Animated.multiply(animatedWindowOffset, (commonBubbleStyles.width + 2 * padItems)),
      ((commonBubbleStyles.width + 2 * padItems) * idx)
    );
    return (
      <Animated.View key={idx} style={{
        backgroundColor: commonBubbleStyles.backgroundColor, 
        width: commonBubbleStyles.width, 
        height: commonBubbleStyles.height, 
        position: 'absolute',
        borderRadius: commonBubbleStyles.borderRadius,
        opacity: 0.7,
        transform: [
          { translateX }, 
          { scale: scaleFactors[idx] }
        ],
      }}/>
    );
  });

  const minHeight = _.isNumber(commonBubbleStyles.height) ? (2 + commonBubbleStyles.height) : 18;

  return (
    <View
      ref={ref}
      style={[
        layoutStyles,
        restModelPlatformStyles,
        {
          width: mainWidth, 
          overflow: 'hidden', 
          minHeight, 
          marginTop: 2,
        },
      ]}>
      <View style={{flexDirection: 'row', height: minHeight}}>
        {bubbles}
        <Animated.View style={{
          backgroundColor: commonBubbleStyles.activeItemBackgroundColor, 
          width: commonBubbleStyles.width, 
          height: commonBubbleStyles.height, 
          borderRadius: commonBubbleStyles.borderRadius, 
          position: 'absolute',
          left: 0,
          transform: [{ translateX: animatedOffsetCurrent }]
        }}/>
      </View>
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'numericInput',
      name: 'padItems',
      defaultValue: false,
      props: {
        label: 'Padded Items',
        noUnit: true,
      },
    },
  ],
  advanced: [
    {
      type: 'codeInput',
      name: 'isLoading',
      defaultValue: false,
      props: {
        label: 'Loading State',
      },
    },
    {
      type: 'codeInput',
      name: 'scaleBubbles',
      defaultValue: true,
      props: {
        label: 'Varying bubble sizes'
      }
    }
  ],
  layout: defaultEditors.layout,
  animations: defaultEditors.animations,
};

const propertySettings: PluginPropertySettings = {
  isLoading: {
    getValue: (model, val, _) => {
      return makeBoolean(val);
    },
  },
  value: {
    getValue(model, val, selector) {
      return _.isNaN(val * 1) ? 0 : _.toNumber(val);
    },
  },
  scaleBubbles: {
    getValue(model, val, _) {
      return !!val;
    }
  }
};
const emptyOnupdate = null;

export default connectWidget(
  'ScrollBubblesWidget',
  ScrollBubblesWidget,
  ScrollBubblesWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: mergeWithDefaultStyles(ScrollBubblesWidgetStyleConfig),
    pluginListing,
    docs,
    themeProfileSel: ['tile', 'text'],
    animations: {
      references: {
        currentItem: {name: 'currentItem'},
      },
    },
  },
);
