import React, {useCallback} from 'react';
import WebView from 'react-native-webview';
import {WebViewNavigationEvent} from 'react-native-webview/src/WebViewTypes';
import queryString from 'query-string';
import _ from 'lodash';
import {SHOPIFY_SUCCESS_PAGE_URL_REGEX} from './ShopFloWebViewTypes';

const jsCode = `
window.addEventListener(
    'message',
    event => {
      window.ReactNativeWebView.postMessage(event);
    },
    false,
  );
`;

const ShopFloWebView = props => {
  const {value, onCancel, onSuccess} = props;
  const onLoad = useCallback(
    (syntheticEvent: WebViewNavigationEvent) => {
      const {nativeEvent} = syntheticEvent;
      console.log(nativeEvent);
      if (nativeEvent?.loading === false) {
        // Do stuff once loading is done
        const url = nativeEvent?.url;
        const parsedURL = queryString.parseUrl(url);
        if (parsedURL?.url && _.endsWith(parsedURL.url, '/null')) {
          if (onCancel) onCancel();
        }

        if (SHOPIFY_SUCCESS_PAGE_URL_REGEX.test(url)) {
          if (onSuccess) onSuccess();
        }
      }
    },
    [onCancel, onSuccess],
  );

  const onBackButtonClick = (event: any) => {
    // logger.warn('[UNIQUE] event', event?.nativeEvent?.data, typeof event?.nativeEvent?.data);
    let eventName = '';
    try {
      if (event?.nativeEvent?.data !== 'undefined') {
        const stringData = event?.nativeEvent?.data.toString();
        logger.warn('stringData: ', stringData);
        eventName = JSON.parse(stringData)?.type;
      }
      if (eventName === 'FLO_EXIT_CHECKOUT') {
        onCancel();
      }
    } catch (err) {
      logger.error('Failure when handling iframe message');
    }
  };

  return (
    <WebView
      onMessage={event => {
        onBackButtonClick(event);
      }}
      androidLayerType="software"
      incognito={false}
      key={value}
      onLoad={onLoad}
      originWhitelist={['*']}
      source={{uri: value}}
      injectedJavaScript={jsCode}
    />
  );
};

export default ShopFloWebView;
