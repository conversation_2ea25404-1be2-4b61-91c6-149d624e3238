import React, {useCallback} from 'react';
import queryString from 'query-string';
import _ from 'lodash';
import {SyntheticEvent} from 'react';
import {SHOPIFY_SUCCESS_PAGE_URL_REGEX} from './ShopFloWebViewTypes';

const ShopFloWebView = props => {
  const {value, onCancel, onSuccess} = props;
  const onLoad = useCallback(
    (event: SyntheticEvent<HTMLIFrameElement, UIEvent>) => {
      console.log(event);
      const {target} = event;
      // Do stuff once loading is done
      const url = (target as HTMLIFrameElement)?.src;
      const parsedURL = queryString.parseUrl(url);
      if (parsedURL?.url && _.endsWith(parsedURL.url, '/null')) {
        if (onCancel) onCancel();
      }
      if (SHOPIFY_SUCCESS_PAGE_URL_REGEX.test(url)) {
        if (onSuccess) onSuccess();
      }
    },
    [onCancel, onSuccess],
  );
  return <iframe src={value} height={'100%'} onLoad={onLoad} />;
};

export default ShopFloWebView;
