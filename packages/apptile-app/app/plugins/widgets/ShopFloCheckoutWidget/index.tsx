import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import _ from 'lodash';
import ShopFloWebView from './ShopFloWebView';

const ShopFloCheckoutWidgetConfig = {
  value: '',
  onCancel: '',
  onSuccess: '',
};
type ShopFloCheckoutWidgetConfigType = typeof ShopFloCheckoutWidgetConfig;

const pluginListing: PluginListingSettings = {
  labelPrefix: 'shopfloCheckout',
  type: 'widget',
  name: 'ShopFlo Checkout View',
  description: 'Load ShopFlo Checkout in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const ShopFloCheckoutWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onCancel = useCallback(() => {
    triggerEvent('onCancel');
  }, [triggerEvent]);
  const onSuccess = useCallback(() => {
    triggerEvent('onSuccess');
  }, [triggerEvent]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]} ref={ref}>
      <ShopFloWebView value={value} onCancel={onCancel} onSuccess={onSuccess} />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [...defaultEditors.basic],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCancel: {
    type: EventTriggerIdentifier,
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'ShopFloCheckoutWidget',
  ShopFloCheckoutWidget,
  ShopFloCheckoutWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
