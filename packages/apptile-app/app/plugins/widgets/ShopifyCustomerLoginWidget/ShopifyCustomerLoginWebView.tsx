import React, {useCallback, useEffect, useState} from 'react';
import WebView from 'react-native-webview';
import {WebViewNavigationEvent, WebViewErrorEvent} from 'react-native-webview/src/WebViewTypes';
import _ from 'lodash';
import {Text, Linking, StyleSheet, View, ActivityIndicator} from 'react-native';

const jsCode = `
`;
const ShopifyCustomerLoginWebView = props => {
  const {value, onCancel, onSuccess, onLogout} = props;
  const [isLoading, setLoading] = useState(true);
  const onLoad = useCallback(
    (syntheticEvent: WebViewNavigationEvent) => {
      const {nativeEvent} = syntheticEvent;
      console.log(nativeEvent);
      if (nativeEvent?.loading === false) {
        const url = nativeEvent?.url;
        // if (SHOPIFY_SUCCESS_PAGE_URL_REGEX.test(url)) {
        //   if (onSuccess) onSuccess();
        // }
      }
    },
    [onSuccess],
  );
  const onMessage = useCallback(
    event => {
      // console.log(event.nativeEvent.data);
      // if (event.nativeEvent.data === 'loaded') setTimeout(() => setLoading(false), 100);
      // if (event.nativeEvent.data === 'cancel') onCancel ? onCancel() : null;
      // if (event.nativeEvent.data === 'success') onSuccess ? onSuccess() : null;
      // if (event.nativeEvent.data && _.startsWith(event.nativeEvent.data, 'link$$')) {
      //   const urlToOpen = _.trimStart(event.nativeEvent.data, 'link$$');
      //   console.log('Opening URL: ' + urlToOpen);
      //   Linking.openURL(urlToOpen);
      // }
    },
    [onCancel, onSuccess],
  );

  const jsToLoad = jsCode;
  console.log(jsToLoad);
  console.log(`URL: ${value}`);
  const [currentURI, setURI] = useState(value);
  useEffect(() => {
    if (value !== currentURI) setURI(value);
  }, [currentURI, value]);
  const newSource = {
    uri: currentURI,
  };

  const handleNavigationStateChange = navState => {
    const {url} = navState;
    console.log('ShopifyCustomerLoginWebView:handleNavigationStateChange', url);
  };

  const handleLoadStart = () => {
    setLoading(true);
  };

  const handleLoadEnd = useCallback(
    (syntheticEvent: WebViewNavigationEvent | WebViewErrorEvent) => {
      const {nativeEvent} = syntheticEvent;
      const {url, title} = nativeEvent;

      setLoading(false);

      if (url.includes('/logout')) {
        // Shopify logout detected
        if (onLogout) {
          setTimeout(() => {
            onLogout();
          }, 500);
          // onLogout()
        }
      }
    },
    [onLogout],
  );

  console.log(`WebviewSource: ${JSON.stringify(newSource)}`);
  return (
    <View style={{width: '100%', height: '100%'}}>
      {isLoading && (
        <View
          style={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            width: '100%',
            height: '100%',
            zIndex: 99,
          }}>
          <ActivityIndicator size="large" color="#696969" />
        </View>
      )}

      <WebView
        // FIXME: Disable this for performace reasons. If this causes crashes please revert
        // androidLayerType="software"
        key={value}
        onLoad={onLoad}
        originWhitelist={['*']}
        source={newSource}
        // Comment from https://github.com/react-native-webview/react-native-webview/pull/1119
        /* Must be populated in order for `messagingEnabled` to be `true` to activate the
         * JS injection user scripts, consistent with current behaviour. This is undesirable,
         * so I'll address it in a follow-up PR. */
        onMessage={onMessage}
        injectedJavaScriptForMainFrameOnly={true}
        injectedJavaScript={jsToLoad}
        onNavigationStateChange={handleNavigationStateChange}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onShouldStartLoadWithRequest={request => {
          // // If we're loading the current URI, allow it to load
          // if (request.url === currentURI) return true;
          // // We're loading a new URL -- change state first
          // setURI(request.url);
          // return false;
          console.log(`Loading URL: ${request.url}`);
          if (request.url.startsWith('http')) {
            // setURI(request.url);
            return true;
          } else {
            // Linking.canOpenURL(request.url)
            //   .then(canOpen => {
            //     console.log('Opening link:', request.url);
            // if (canOpen)
            Linking.openURL(request.url);
            //   else {
            //     console.log('CANNOT Open link:', request.url);
            //   }
            //   // else throw Error();
            // })
            // .catch(e => { });
            return false;
          }
        }}
      />
    </View>
  );
};

export default ShopifyCustomerLoginWebView;
