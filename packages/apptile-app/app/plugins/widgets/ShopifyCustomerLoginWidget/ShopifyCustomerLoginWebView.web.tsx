import React, {useCallback} from 'react';
import queryString from 'query-string';
import _ from 'lodash';
import {SyntheticEvent} from 'react';

const ShopifyCustomerLoginWebView = props => {
  const {value, onCancel, onSuccess} = props;
  const onLoad = useCallback(
    (event: SyntheticEvent<HTMLIFrameElement, UIEvent>) => {
      console.log(event);
      const {target} = event;
      // Do stuff once loading is done
      const url = (target as HTMLIFrameElement)?.src;
      const parsedURL = queryString.parseUrl(url);
    },
    [onCancel, onSuccess],
  );
  return <iframe src={value} height={'100%'} onLoad={onLoad} />;
};

export default ShopifyCustomerLoginWebView;
//Content-Security-Policy: frame-ancestors 'none';