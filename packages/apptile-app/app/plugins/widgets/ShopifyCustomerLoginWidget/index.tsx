import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import _ from 'lodash';
import ShopifyCustomerLoginWebView from './ShopifyCustomerLoginWebView';

const ShopifyCustomerLoginWidgetConfig = {
  value: '',
  code: '',
  onCancel: '',
  onSuccess: '',
  onLogout: '',
};
type ShopifyCustomerLoginWidgetConfigType = typeof ShopifyCustomerLoginWidgetConfig;

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyCustomerLoginWidget',
  type: 'widget',
  name: 'Shopify Customer Login View',
  description: 'Load Shopify Customer Login View in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const ShopifyCustomerLoginWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const code = model.get('code');
  logger.info('CODE !!!: ', code);
  const [currentCode, setCode] = useState(code);
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  useEffect(() => {
    if (code && !currentCode) {
      setCode(code);
      logger.info('SENDING CODE !!!: ', code);
      triggerEvent('onSuccess');
    }
  }, [currentCode, code, triggerEvent]);

  const onCancel = useCallback(() => {
    triggerEvent('onCancel');
  }, [triggerEvent]);
  const onSuccess = useCallback(() => {
    triggerEvent('onSuccess');
  }, [triggerEvent]);
  const onLogout = useCallback(() => {
    triggerEvent('onLogout');
  }, [triggerEvent]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]} ref={ref}>
      <ShopifyCustomerLoginWebView value={value} onLogout={onLogout} />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'code',
      props: {
        label: 'Auth Code',
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCancel: {
    type: EventTriggerIdentifier,
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
  onLogout: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'ShopifyCustomerLoginWidget',
  ShopifyCustomerLoginWidget,
  ShopifyCustomerLoginWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
