import React, {useCallback, useEffect, useMemo, useState} from 'react';
import WebView from 'react-native-webview';
import {WebViewNavigationEvent} from 'react-native-webview/src/WebViewTypes';
import _ from 'lodash';
import {SHOPIFY_SUCCESS_PAGE_URL_REGEX} from './ShopifyWebCheckoutViewTypes';
import {Text, Linking, StyleSheet, View, ActivityIndicator} from 'react-native';
import {ImageComponent} from 'apptile-core';

const jsCode = `
window.setInterval(() => {
  const SHOPIFY_LOGIN_URL_REGEX = /(\\s*)\\/account\\/(\\s*)/g;
  const SHOPIFY_OAUTH_URL_REGEX = /(\\s*)\\/oauth\\/(\\s*)/g;
  const at_links = document.getElementsByTagName("A");
  for (const el of at_links) {
    if (SHOPIFY_LOGIN_URL_REGEX.test(el.href) || SHOPIFY_OAUTH_URL_REGEX.test(el.href)) {
      el.style.display = "none";
    }
  }

  const at_footer = document.getElementsByTagName("FOOTER");
  for (const el of at_footer) {
    el.style.display = "none";
  }

  if (window.location.href?.endsWith('/thank-you')) {
    window.location.reload();
  }
  if (window.location.href?.endsWith('/thank_you')) {
    window.location.reload();
  }
}, 300);
`;

const disableFacebookPixelJsCode = `
window.fbq = function() {};
`;

const ShopifyWebCheckoutView = props => {
  const {value, onCancel, onSuccess, customerAccessToken, disableFacebookPixel} = props;
  const [isLoading, setLoading] = useState(true);
  const onLoad = useCallback((syntheticEvent: WebViewNavigationEvent) => {
    const {nativeEvent} = syntheticEvent;
    if (nativeEvent?.loading === false) {
      setLoading(false);
    }
    const url = nativeEvent?.url;
  }, []);
  const onLoadStart = useCallback(
    (syntheticEvent: WebViewNavigationEvent) => {
      const {nativeEvent} = syntheticEvent;
      const url = nativeEvent?.url;
      console.log('OnLoadStart: ', url);
      if (SHOPIFY_SUCCESS_PAGE_URL_REGEX.test(url) || url?.endsWith('/thank_you') || url?.endsWith('/thank-you')) {
        if (onSuccess) {
          onSuccess();
          setURI('');
        }
      }
    },
    [onSuccess],
  );
  const onMessage = useCallback(event => {}, []);
  const [currentURI, setURI] = useState(value);
  const newSource =
    value === currentURI
      ? {
          headers: {'X-Shopify-Customer-Access-Token': customerAccessToken},
          uri: currentURI,
        }
      : {
          uri: currentURI,
        };

  const injectedJavaScript = useMemo(() => {
    return Boolean(disableFacebookPixel) ? jsCode + disableFacebookPixelJsCode : jsCode;
  }, [disableFacebookPixel]);

  console.log(`WebviewSource: ${JSON.stringify(newSource)}`);
  if (_.isEmpty(currentURI)) {
    return <View />;
  }
  return (
    <>
      <WebView
        // FIXME: Disable this for performace reasons. If this causes crashes please revert
        // androidLayerType="software"
        key={currentURI}
        onLoad={onLoad}
        onLoadStart={onLoadStart}
        originWhitelist={['*']}
        source={newSource}
        incognito={false}
        // Comment from https://github.com/react-native-webview/react-native-webview/pull/1119
        /* Must be populated in order for `messagingEnabled` to be `true` to activate the
         * JS injection user scripts, consistent with current behaviour. This is undesirable,
         * so I'll address it in a follow-up PR. */
        startInLoadingState={true}
        renderLoading={() => (
          <View style={[StyleSheet.absoluteFill, {alignItems: 'center', height: 20}]}>
            <ActivityIndicator size="small" />
          </View>
        )}
        onMessage={onMessage}
        injectedJavaScriptForMainFrameOnly={true}
        injectedJavaScript={injectedJavaScript}
        onShouldStartLoadWithRequest={request => {
          // // If we're loading the current URI, allow it to load
          // console.log(`Requesting: `, request);
          // if (request.isTopFrame) {
          //   if (request.url === currentURI) {
          //     return true;
          //   } else {
          //     console.log(`Resetting URI: `, request.url);
          //     setURI(request.url);
          //     return false;
          //   }
          // }

          if (request.url.startsWith('http')) {
            return true;
          } else {
            Linking.canOpenURL(request.url)
              .then(canOpen => {
                console.log('Opening link:', request.url);
                if (canOpen) Linking.openURL(request.url);
                else {
                  console.log('CANNOT Open link:', request.url);
                }
                // else throw Error();
              })
              .catch(e => {});
            return false;
          }
        }}
      />
    </>
  );
};

export default ShopifyWebCheckoutView;
