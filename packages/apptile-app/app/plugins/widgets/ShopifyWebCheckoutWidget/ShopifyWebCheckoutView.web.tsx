import {
  DevicePlatformType,
  setOpenDevicePreviewQRModal,
  setQRModalPlatformType,
} from '@/root/web/actions/editorActions';
import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import {useDispatch} from 'react-redux';

const ShopifyWebCheckoutView = () => {
  const dispatch = useDispatch();
  const setOpenQRModal = (platform: DevicePlatformType) => {
    dispatch(setOpenDevicePreviewQRModal(true));
    dispatch(setQRModalPlatformType(platform));
  };
  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontWeight="500" fontSize="lg" style={styles.headerText}>
        Checkout is not available on web.
      </TextElement>
      <TextElement color="EDITOR_LIGHT_BLACK" fontSize="md" style={[styles.paddingTop10, styles.textAlignCenter]}>
        Please click below to preview on your respective mobile devices.
      </TextElement>
      <View style={styles.platformIconsContainer}>
        <Pressable onPress={() => setOpenQRModal('IOS')}>
          <View style={styles.platformIconWrapper}>
            <Image style={styles.platformIcon} source={require('@/root/web/assets/images/apple-icon.png')} />
          </View>
        </Pressable>
        <Pressable onPress={() => setOpenQRModal('ANDROID')}>
          <View style={styles.platformIconWrapper}>
            <Image style={styles.platformIcon} source={require('@/root/web/assets/images/android-icon.png')} />
          </View>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    height: '100vh',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  headerText: {
    textAlign: 'center',
  },
  paddingTop10: {
    paddingTop: 10,
  },
  textAlignCenter: {
    textAlign: 'center',
  },
  platformIconsContainer: {
    flexDirection: 'row',
    width: 185,
    justifyContent: 'center',
    marginTop: 20,
    gap: 20,
  },
  platformIcon: {
    width: 32,
    height: 32,
  },
  platformIconWrapper: {
    borderWidth: 1,
    borderColor: '#000000',
    padding: 14,
    borderRadius: 8,
  },
});

export default ShopifyWebCheckoutView;
