import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import _ from 'lodash';
import ShopifyWebCheckoutView from './ShopifyWebCheckoutView';
import {SHOPIFY_CHECKOUT_URL_REGEX} from './ShopifyWebCheckoutViewTypes';
var parse = require('url-parse');

const ShopifyWebCheckoutWidgetConfig = {
  value: '',
  customerAccessToken: '',
  shopDomain: '',
  onCancel: '',
  onSuccess: '',
};
type ShopifyWebCheckoutWidgetConfigType = typeof ShopifyWebCheckoutWidgetConfig;

const pluginListing: PluginListingSettings = {
  labelPrefix: 'ShopifyWebCheckout',
  type: 'widget',
  name: 'Shopify Web Checkout View',
  description: 'Load Shopify Web Checkout in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const ShopifyWebCheckoutWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const customerAccessToken = model.get('customerAccessToken', '');
  const shopDomain = model.get('shopDomain', '');
  const disableFacebookPixel = Boolean(model.get('disableFacebookPixel', false));
  const [urlToLoad, setURL] = useState(value);
  const [isValid, setValid] = useState(false);

  useEffect(() => {
    setURL(value);
    if (SHOPIFY_CHECKOUT_URL_REGEX.test(value)) {
      if (shopDomain) {
        let parsed = parse(value);
        parsed.set('host', shopDomain);
        setURL(parsed.toString());
      }
      setValid(true);
    }
  }, [shopDomain, value]);

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onCancel = useCallback(() => {
    triggerEvent('onCancel');
  }, [triggerEvent]);
  const onSuccess = useCallback(() => {
    triggerEvent('onSuccess');
  }, [triggerEvent]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]} ref={ref}>
      {isValid ? (
        <ShopifyWebCheckoutView
          value={urlToLoad}
          customerAccessToken={customerAccessToken}
          onCancel={onCancel}
          onSuccess={onSuccess}
          disableFacebookPixel={disableFacebookPixel}
        />
      ) : (
        <></>
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'customerAccessToken',
      props: {
        label: 'Shopify customer token',
      },
    },
    {
      type: 'codeInput',
      name: 'shopDomain',
      props: {
        label: 'Shopify shop primary domain',
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'disableFacebookPixel',
      props: {
        label: 'Disable Facebook Pixel',
        checkedValue: false,
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCancel: {
    type: EventTriggerIdentifier,
  },
  onSuccess: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget(
  'ShopifyWebCheckoutWidget',
  ShopifyWebCheckoutWidget,
  ShopifyWebCheckoutWidgetConfig,
  emptyOnupdate,
  widgetEditors,
  {
    propertySettings,
    widgetStyleConfig: defaultStyleEditors,
    pluginListing,
    docs,
  },
);
