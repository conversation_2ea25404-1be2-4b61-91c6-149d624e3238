import React, {useEffect, useState} from 'react';
import {StorifyMeEnv, StorifyMeWidget} from 'react-native-storifyme';

export interface StorifyMeControlProps {
  accountId: string;
  apiKey: string;
  env: string;
  widgetId: string;
  width: number;
  height: number;
}

const getStorifyMeEnv = (envString: string) => {
  return envString == 'EU' ? StorifyMeEnv.EU : StorifyMeEnv.US;
};

const StorifyMeControl: React.FC<StorifyMeControlProps> = props => {
  const {accountId, apiKey, env, widgetId, height, width} = props;
  const [storifyMeEnv, setStorifyMeEnv] = useState(getStorifyMeEnv(env));

  useEffect(() => {
    setStorifyMeEnv(getStorifyMeEnv(env));
  }, [env]);

  return (
    <StorifyMeWidget
      accountId={accountId}
      apiKey={apiKey}
      env={storifyMeEnv}
      widgetId={widgetId}
      autoLayout={true}
      style={{flex: 1, width: width, height: height}}
    />
  );
};

export default StorifyMeControl;
