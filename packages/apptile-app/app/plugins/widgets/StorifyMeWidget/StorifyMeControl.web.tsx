import React, {useEffect, useState} from 'react';

export interface StorifyMeControlProps {
  accountId: string;
  apiKey: string;
  widgetId: string;
  width: number;
  height: number;
}

const StorifyMeControl: React.FC<StorifyMeControlProps> = props => {
  const {accountId, apiKey, widgetId, height, width} = props;
  const [scriptLoaded, setScriptLoaded] = useState(false);
  useEffect(() => {
    let id = 'storifyMeScript';
    if (document.getElementById(id) === null) {
      let scriptEle = document.createElement('script');

      scriptEle.setAttribute('src', 'https://cdn.storifyme.com/static/web-components/storifyme-elements.min.js');
      scriptEle.setAttribute('id', id);
      scriptEle.setAttribute('type', 'text/javascript');
      scriptEle.setAttribute('async', true);

      // success event
      scriptEle.addEventListener('load', () => {
        setScriptLoaded(true);
      });
      // error event
      scriptEle.addEventListener('error', ev => {
        console.log('Error on loading file', ev);
      });

      document.body.appendChild(scriptEle);
    } else {
      setScriptLoaded(true);
    }
  });
  return scriptLoaded ? (
    <storifyme-collection
      url={`https://storifyme.com/widgets/${accountId}/widget/custom/${widgetId}`}></storifyme-collection>
  ) : (
    <></>
  );
};

export default StorifyMeControl;
