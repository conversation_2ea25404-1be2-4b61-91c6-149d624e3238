import React, {useState} from 'react';
import {Dimensions, LayoutChangeEvent, View} from 'react-native';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from 'apptile-core';
import _ from 'lodash';
import StorifyMeControl from './StorifyMeControl';
import {datasourceTypeModelSel} from 'apptile-core';
import {createSelector} from 'reselect';
import {useSelector} from 'react-redux';
import {createDeepEqualSelector} from 'apptile-core';

type StorifyMeWidgetConfigType = {
  value: string;
  onLoadEnd: string;
};
const StorifyMeWidgetConfig: StorifyMeWidgetConfigType = {
  value: '',
  onLoadEnd: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'storifyMeWidget',
  type: 'widget',
  name: 'StorifyMe Widget',
  description: 'Load StorifyMe Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const storifyDSSel = state => datasourceTypeModelSel(state, 'storifyMe');
const storifyModelSel = createDeepEqualSelector(
  createSelector(storifyDSSel, (dsModel: Immutable.Map<string, any>) => {
    return {
      accountId: dsModel?.get('accountId'),
      apiKey: dsModel?.get('apiKey'),
      env: dsModel?.get('env'),
    };
  }),
  s => s,
);

const StorifyMe = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const deviceWidth = Dimensions?.get('window')?.width;
  const [widgetWidth, setWidgetWidth] = useState(deviceWidth);
  const [widgetHeight, setWidgetHeight] = useState(100);
  const {accountId, apiKey, env} = useSelector(storifyModelSel);

  const onLayout = (event: LayoutChangeEvent) => {
    const {height, width} = event.nativeEvent.layout;
    setWidgetHeight(height);
    setWidgetWidth(width);
  };

  return (
    <View style={[layoutStyles, modelStyles]} onLayout={onLayout} ref={ref}>
      <StorifyMeControl
        accountId={accountId}
        apiKey={apiKey}
        env={env}
        widgetId={value}
        width={widgetWidth}
        height={widgetHeight}
      />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [...defaultEditors.basic],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

export default connectWidget('StorifyMeWidget', StorifyMe, StorifyMeWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
