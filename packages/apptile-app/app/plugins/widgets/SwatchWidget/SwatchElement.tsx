import React from 'react';
import {Image, Pressable, View, Text} from 'react-native';

const DiagonalStrike = ({x, y}) => {
  const angleRadians = Math.atan2(y, x);
  const angleDegrees = angleRadians * (180 / Math.PI);

  const width = Math.sqrt(x * x + y * y) * 1.1;

  return (
    <View
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'visible',
        },
      ]}>
      <View
        style={[
          {
            width: width,
            height: 3,
            backgroundColor: '#12121236',
            transform: [{rotate: `-${angleDegrees}deg`}],
            zIndex: 99,
          },
        ]}
      />
    </View>
  );
};

const SwatchElement = props => {
  const {item, handleSelection, isSelected, shadowStyles, modelPlatformStyles} = props;

  const getInnerStyles = (styles: any) => {
    const innerStyles: any = {};
    Object.keys(styles).forEach(s => {
      if (s.includes('item_') && (s.includes('width') || s.includes('height') || s.includes('Radius')))
        innerStyles[s.replace('item_', '')] = styles[s];
    });

    return innerStyles;
  };

  const getOuterStyles = (styles: any) => {
    const outerStyles: any = {};
    Object.keys(styles).forEach(s => {
      if (s.includes('shadow') || (s.includes('item_') && !s.includes('width') && !s.includes('height')))
        outerStyles[s.replace('item_', '')] = styles[s];
    });

    return outerStyles;
  };

  const getSelectedStyles = (styles: any) => {
    const selectedStyles: any = {};
    Object.keys(styles).forEach(s => {
      if (s.includes('selectedItem_')) selectedStyles[s.replace('selectedItem_', '')] = styles[s];
    });

    return selectedStyles;
  };

  const getDisabledStyles = (styles: any) => {
    const selectedStyles: any = {};
    Object.keys(styles).forEach(s => {
      if (s.includes('disabledItem_')) selectedStyles[s.replace('disabledItem_', '')] = styles[s];
    });

    return selectedStyles;
  };

  return (
    <Pressable disabled={item?.disabled ?? false} onPress={() => handleSelection(item)}>
      <View
        style={[
          getOuterStyles(modelPlatformStyles),
          shadowStyles,
          isSelected ? getSelectedStyles(modelPlatformStyles) : {},
          item?.disabled ? getDisabledStyles(modelPlatformStyles) : {},
        ]}>
        {item.type === 'colour' && (
          <>
            <View
              style={[
                {width: 50, height: 50},
                getInnerStyles(modelPlatformStyles),
                {backgroundColor: item.source},
                item?.disabled ? {opacity: 0.5} : {},
              ]}></View>
            {item?.striked && (
              <DiagonalStrike x={modelPlatformStyles.item_width ?? 50} y={modelPlatformStyles.item_height ?? 50} />
            )}
          </>
        )}
        {item.type === 'image' && (
          <>
            <Image
              style={[
                {width: 50, height: 50},
                getInnerStyles(modelPlatformStyles),
                item?.disabled ? {opacity: 0.5} : {},
              ]}
              source={{uri: item.source}}
            />
            {item?.striked && (
              <DiagonalStrike x={modelPlatformStyles.item_width ?? 50} y={modelPlatformStyles.item_height ?? 50} />
            )}
          </>
        )}
        {item.type === 'text' && (
          <Text
            style={[
              {
                color: isSelected
                  ? modelPlatformStyles?.selectedText_color ?? 'black'
                  : modelPlatformStyles?.text_color ?? 'black',
              },
              {...(isSelected ? modelPlatformStyles.selectedTypography : modelPlatformStyles.typography)},
              {...(item?.disabled ? modelPlatformStyles.disabledTypography : {})},
              {textDecorationLine: item?.striked ? 'line-through' : 'none'},
              item?.disabled ? {opacity: 0.5} : {},
            ]}>
            {item.source ?? item.value}
          </Text>
        )}
      </View>
    </Pressable>
  );
};

export default SwatchElement;
