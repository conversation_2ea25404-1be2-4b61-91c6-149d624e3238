import {
  PluginPropertySettings,
  connectWidget,
  hapticEditors,
  mergeWithDefaultStyles,
  performHapticFeedback,
  usePlaceHolder,
  makeBoolean,
  EventTriggerIdentifier,
  PluginListingSettings,
  getPlatformStyles,
  WidgetStyleEditorOptions,
  getShadowStyle,
} from 'apptile-core';
import React, {useCallback, useEffect, useState} from 'react';
import {ScrollView, View} from 'react-native';
import SwatchElement from './SwatchElement';
import _ from 'lodash';

const SwatchWidgetConfig = {
  value: '',
  input: `{{
    [
      {type: 'colour', source: '#EB8334', value: 1},
      {type: 'image', source: 'https://images.unsplash.com/photo-1559703248-dcaaec9fab78?w=100&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8aWNlY3JlYW18ZW58MHx8MHx8fDA%3D', value: 2},
    ]
  }}`,
  onTap: '',
  enableHaptics: '',
  hapticMethod: '',
  isLoading: false,
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'SwatchWidget',
  type: 'widget',
  name: 'SwatchWidget',
  description: 'Displays Swatch Widget.',
  section: 'Display',
  layout: {
    flex: 1,
    flexDirection: 'row',
    width: 100,
  },
  icon: 'radio-button',
};

export const SwatchWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'codeInput',
    name: 'item_width',
    props: {
      label: 'Item width',
      placeholder: '',
    },
  },
  {
    type: 'codeInput',
    name: 'item_height',
    props: {
      label: 'Item height',
      placeholder: '',
    },
  },
  {
    type: 'colorInput',
    name: 'text_color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedText_color',
    props: {
      label: 'Selected Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'selectedTypography',
    props: {
      label: 'Selected Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'typographyInput',
    name: 'disabledTypography',
    props: {
      label: 'Disabled Typography',
      placeholder: 'typography.body',
    },
  },
  {
    type: 'colorInput',
    name: 'item_backgroundColor',
    props: {
      label: 'Item Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedItem_backgroundColor',
    props: {
      label: 'Selected Item Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledItem_backgroundColor',
    props: {
      label: 'Disabled Item Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'item_borderRadius',
    props: {
      label: 'Item Border Radius',
      placeholder: '0',
      options: [
        'item_borderTopLeftRadius',
        'item_borderTopRightRadius',
        'item_borderBottomRightRadius',
        'item_borderBottomLeftRadius',
      ],
      layout: 'square',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'item_margin',
    props: {
      label: 'Item Margin',
      placeholder: '0',
      options: ['item_marginTop', 'item_marginRight', 'item_marginBottom', 'item_marginLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'item_padding',
    props: {
      label: 'Item Padding',
      placeholder: '0',
      options: ['item_paddingTop', 'item_paddingRight', 'item_paddingBottom', 'item_paddingLeft'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'item_borderWidth',
    props: {
      label: 'Item Border',
      placeholder: '0',
      options: ['item_borderTopWidth', 'item_borderRightWidth', 'item_borderBottomWidth', 'item_borderLeftWidth'],
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'selectedItem_borderWidth',
    props: {
      label: 'Selected Item Border',
      placeholder: '0',
      options: [
        'selectedItem_borderTopWidth',
        'selectedItem_borderRightWidth',
        'selectedItem_borderBottomWidth',
        'selectedItem_borderLeftWidth',
      ],
    },
  },
  {
    type: 'colorInput',
    name: 'item_borderColor',
    props: {
      label: 'Item Border Color',
      placeholder: 'transparent',
    },
  },
  {
    type: 'colorInput',
    name: 'selectedItem_borderColor',
    props: {
      label: 'Selected Item Border Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'disabledItem_borderColor',
    props: {
      label: 'Disabled Item Border Color',
      placeholder: '#HexCode',
    },
  },
];

const SwatchWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, modelUpdate, config, triggerEvent} = props;

  const value = model.get('value');
  let input = model.get('input');
  input = Array.isArray(input) ? input : [];
  const isLoading = !!model.get('isLoading');
  const enableHaptics = model.get('enableHaptics');
  const hapticMethod = model.get('hapticMethod');

  const Placeholder = usePlaceHolder();

  const [selectedSwatch, setSelectedSwatch] = useState(value);

  useEffect(() => {
    setSelectedSwatch(value);
  }, [value]);

  const handleSelection = useCallback(
    item => {
      setSelectedSwatch(item?.value);
      if (enableHaptics) performHapticFeedback(hapticMethod);
      modelUpdate([
        {
          selector: 'value',
          newValue: item?.value,
        },
      ]);

      setTimeout(() => {
        triggerEvent('onTap'), 50;
      });
    },
    [modelUpdate, triggerEvent, enableHaptics, hapticMethod],
  );

  const layout = config.get('layout');

  const layoutStyles = getPlatformStyles(layout ? layout.getNonContainerFlexProperties() : {flex: 1});
  const layoutContainerStyles = getPlatformStyles(layout ? layout?.getContainerFlexProperties() : {flex: 1});
  const modelPlatformStyles = getPlatformStyles(modelStyles);

  const isScrollView = layoutContainerStyles?.overflow === 'scroll';
  const ViewComponent = isScrollView ? ScrollView : View;
  const scrollViewProps = isScrollView
    ? {
        contentContainerStyle: layoutContainerStyles,
        horizontal: layoutContainerStyles?.flexDirection === 'row',
        showsHorizontalScrollIndicator: false,
      }
    : {};

  const elevation = _.toInteger(config.getIn(['style', 'elevation'], 0));
  const shadowStyles = {...getShadowStyle(elevation), elevation};

  const getParentStyles = (styles: any) => {
    const parentStyles: any = {};
    Object.keys(styles).forEach(s => {
      if (!s.includes('item_') && !s.includes('selectedItem_') && !s.includes('shadow') && !s.includes('elevation'))
        parentStyles[s] = styles[s];
    });

    return parentStyles;
  };

  return isLoading ? (
    <Placeholder layoutStyles={{...layoutStyles, minHeight: 35}} />
  ) : (
    <ViewComponent
      ref={ref}
      {...scrollViewProps}
      style={[getParentStyles(modelPlatformStyles), layoutStyles, !isScrollView ? layoutContainerStyles : {}]}>
      {input.map((item, index) => {
        return (
          <SwatchElement
            key={`${value}-${index}`}
            item={item}
            handleSelection={handleSelection}
            isSelected={selectedSwatch === item?.value}
            modelPlatformStyles={modelPlatformStyles}
            shadowStyles={shadowStyles}
          />
        );
      })}
    </ViewComponent>
  );
});

const widgetEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'input',
      props: {
        label: 'Input [{type,source,value,disabled,striked}]',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'value',
      props: {
        label: 'Value',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      props: {
        label: 'Loading',
      },
    },
  ],
  advanced: hapticEditors.advanced,
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, selector) => {
      return _.isNumber(renderedValue) ? renderedValue : renderedValue ? renderedValue.toString() : '';
    },
  },
  enableHaptics: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },

  onTap: {
    type: EventTriggerIdentifier,
  },

  isLoading: {
    getValue: (model, value, _) => {
      return makeBoolean(value);
    },
  },
};

const emptyOnupdate = null;

export default connectWidget('SwatchWidget', SwatchWidget, SwatchWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(SwatchWidgetStyleConfig),
  pluginListing,
  docs: {},
  themeProfileSel: ['tile', 'swatchWidget'],
});
