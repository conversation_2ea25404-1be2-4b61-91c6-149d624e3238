import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {isNil} from 'lodash';
import {WidgetRefRegistry} from 'apptile-core';
import {Platform} from 'react-native';
import {set} from 'lodash';

export interface ITextInputSetText {
  value: string;
}

export const setText = (dispatch, config: PluginConfig, model, selector: Selector, params: ITextInputSetText) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model?.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  const {value, focus} = params;
  if (!currentRef) return;
  if (isNil(value)) value = '';

  if (Platform.OS === 'web') {
    set(currentRef, 'current.value', value);
  } else {
    currentRef?.current?.textInputRef?.current?.setNativeProps({text: value});
    currentRef?.current?.setText(value ?? '');
    if (focus)
      setTimeout(() => {
        currentRef?.current?.textInputRef?.current?.focus();
      }, 100);
  }
};

export const setFocus = (dispatch, config: PluginConfig, model, selector: Selector) => {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model?.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  if (!currentRef) return;
  currentRef?.current?.textInputRef?.current?.focus();
};
