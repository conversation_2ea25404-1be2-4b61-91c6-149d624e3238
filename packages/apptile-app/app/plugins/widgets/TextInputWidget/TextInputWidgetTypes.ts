import {Platform} from 'react-native';

const AutoCompleteTypes = [
  'birthdate-day',
  'birthdate-full',
  'birthdate-month',
  'birthdate-year',
  'cc-csc',
  'cc-exp',
  'cc-exp-day',
  'cc-exp-month',
  'cc-exp-year',
  'cc-number',
  'email',
  'gender',
  'name',
  'name-family',
  'name-given',
  'name-middle',
  'name-middle-initial',
  'name-prefix',
  'name-suffix',
  'password',
  'password-new',
  'postal-address',
  'postal-address-country',
  'postal-address-extended',
  'postal-address-extended-postal-code',
  'postal-address-locality',
  'postal-address-region',
  'postal-code',
  'street-address',
  'sms-otp',
  'tel',
  'tel-country-code',
  'tel-national',
  'tel-device',
  'username',
  'username-new',
  'off',
];

const TextContentTypes = [
  'none',
  'URL',
  'addressCity',
  'addressCityAndState',
  'addressState',
  'countryName',
  'creditCardNumber',
  'emailAddress',
  'familyName',
  'fullStreetAddress',
  'givenName',
  'jobTitle',
  'location',
  'middleName',
  'name',
  'namePrefix',
  'nameSuffix',
  'nickname',
  'organizationName',
  'postalCode',
  'streetAddressLine1',
  'streetAddressLine2',
  'sublocality',
  'telephoneNumber',
  'username',
  'password',
  'newPassword',
  'oneTimeCode',
];

const TextInputAutocompleteSettings: Record<
  string,
  Record<'ios' | 'android', (typeof AutoCompleteTypes)[number] | (typeof TextContentTypes)[number]>
> = {
  off: {ios: 'none', android: 'off'},
  // 'name': {ios: 'addressState', android: 'postal-address-region'},
  country: {ios: 'countryName', android: 'postal-address-country'},
  'cc-number': {ios: 'creditCardNumber', android: 'cc-number'},
  email: {ios: 'emailAddress', android: 'email'},
  'family-name': {ios: 'familyName', android: 'name-family'},
  'street-address': {ios: 'fullStreetAddress', android: 'street-address'},
  'given-name': {ios: 'givenName', android: 'name-given'},
  // 'name': {ios: 'location', android: 'postal-address-locality'},
  name: {ios: 'name', android: 'name-given'},
  'honorific-prefix': {ios: 'namePrefix', android: 'name-prefix'},
  'honorific-suffix': {ios: 'nameSuffix', android: 'name-suffix'},
  'postal-code': {ios: 'postalCode', android: 'postal-code'},
  // 'street-address': {ios: 'streetAddressLine1', android: 'street-address'},
  // 'name': {ios: 'streetAddressLine2', android: 'postal-address-extended'},
  // 'name': {ios: 'sublocality', android: 'postal-address-locality'},
  tel: {ios: 'telephoneNumber', android: 'tel'},
  username: {ios: 'username', android: 'username'},
  'current-password': {ios: 'password', android: 'password'},
  'new-password': {ios: 'newPassword', android: 'password-new'},
  'one-time-code': {ios: 'oneTimeCode', android: 'sms-otp'},
};
export const TextInputWidgetAutocompleteTypes = Object.keys(TextInputAutocompleteSettings);
export type TextInputWidgetAutocompleteType = keyof typeof TextInputAutocompleteSettings;

export function getPlatformAutoCompleteType(acType: string = 'off') {
  if (!acType) acType = 'off';
  const propName: string = Platform.select({ios: 'textContentType', default: 'autoComplete'});
  const propVal = Platform.select(TextInputAutocompleteSettings[acType]);
  return {[propName]: propVal};
}
