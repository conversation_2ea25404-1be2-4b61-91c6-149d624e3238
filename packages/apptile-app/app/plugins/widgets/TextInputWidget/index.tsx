import React, {useContext, useEffect, useRef, useState} from 'react';
import {TextInput, View, Platform} from 'react-native';
import {set} from 'lodash';

import docs from './docs';
import {PluginEditorsConfig} from 'apptile-core';
import {LabelControlComponent, labelEditorsConfig, LabelTemplateConfig} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import {EventTriggerIdentifier, TriggerActionIdentifier} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {Selector} from 'apptile-core';
import {mergeWithDefaultStyles} from 'apptile-core';
import {populateValidationConfig, getValidationEditorProps, fieldValidator} from './validation';
import {getPlatformAutoCompleteType, TextInputWidgetAutocompleteTypes} from './TextInputWidgetTypes';
import {WidgetRefContext} from 'apptile-core';
import {setFocus, setText} from './TextInputActions';
import {isJSBinding} from 'apptile-core';

const pluginListing: PluginListingSettings = {
  labelPrefix: 'textInput',
  type: 'widget',
  name: 'Text Input',
  description: 'An Input field to accept various values from the user.',
  layout: {
    flex: 1,
  },
  section: 'Inputs',
  icon: 'text-input',
};

const fieldsToValidateInConfig = ['value'];

const TextInputWidgetConfig = populateValidationConfig(
  {
    value: '',
    placeholder: 'Enter value',
    onSubmit: '',
    onBlur: '',
    onFocus: '',
    onTypingPaused: '',
    keyboardType: '',
    setText: 'action',
    setFocus: 'action',
    ...LabelTemplateConfig,
  },
  fieldsToValidateInConfig,
);

export const textInputWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'colorInput',
    name: 'innerBackgroundColor',
    props: {
      label: 'InnerBackgroundColor',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
    },
  },
  {
    type: 'colorInput',
    name: 'placeholderColor',
    props: {
      label: 'Placeholder Color',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'borderWidth',
    props: {
      label: 'Border',
      options: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],
    },
  },
  {
    type: 'colorInput',
    name: 'borderColor',
    props: {
      label: 'Border Color',
    },
  },
  {
    type: 'borderRadiusEditor',
    name: 'borderRadius',
    props: {
      label: 'Border Radius',
      options: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
      layout: 'square',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'padding',
    props: {
      label: 'Padding',
      options: ['paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft'],
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
    },
  },
  {
    type: 'trblValuesEditor',
    name: 'margin',
    props: {
      label: 'Margin',
      options: ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'],
    },
  },
];

const TextInputWidget = React.forwardRef((props, ref) => {
  const {model, modelUpdate, triggerEvent, config, modelStyles, instance, pageKey, id} = props as any;
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});

  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const {typography, innerBackgroundColor, backgroundColor, placeholderColor, ...restModelPlatformStyles} =
    modelPlatformStyles;

  const inFocus = useRef<Boolean>(false);
  const textInputRef = useRef<TextInput>();
  const refContext = useContext(WidgetRefContext);
  useEffect(() => {
    if (textInputRef?.current) {
      refContext.registerWidgetRef(
        {
          current: {
            setText(text: any) {
              latestText.current = text;
            },
            textInputRef,
          },
        },
        pageKey,
        id,
        instance,
      );
    }
    return () => {
      refContext.unRegisterWidgetRef(pageKey, id, instance);
    };
  }, [id, instance, pageKey, textInputRef, refContext]);

  const modelValue = model.get('value');
  const placeholder = model.get('placeholder');
  const value = isJSBinding(modelValue) ? '' : modelValue;

  const latestText = useRef('');
  const shouldTriggerTypingPaused = useRef(false);
  const shouldTriggerOnSubmit = useRef(false);
  // The placeholder update is a hack. After the user has finished typing
  // and the appModel has the updated text value if they hit submit, then
  // the modelUpdate will be ignored if there is no change at all in the model.
  // So we just update the placeholder to trick the system into doing a render
  // cycle.
  const setInputValueWithModalUpdate = shouldUpdatePlaceholder => {
    // console.log("Setting latest text: ", latestText.current);
    // setInputValue(v);
    // debouncedHandleChange(v);
    const updates = [{selector: ['value'], newValue: latestText.current}];
    if (shouldUpdatePlaceholder) {
      updates.push({
        selector: ['placeholder'],
        newValue: model.get('placeholder') + ' ',
      });
    }
    modelUpdate(updates);
    // triggerEvent('onTypingPaused');
    shouldTriggerTypingPaused.current = true;
    fieldValidator(model, fieldsToValidateInConfig, modelUpdate);
  };

  // const debounceId = useRef(0);
  function onChangeText(text: string | null, debounced?: boolean) {
    if (text !== null) {
      latestText.current = text;
    }
    // console.log("running textchange: ", text, latestText.current);
    // if (!debounced) {
    //   if (debounceId.current) {
    //     clearTimeout(debounceId.current);
    //   }
    //   // This setTimeout does not fire when metro is running and
    //   // chrome debugger is attached. But it will work when only metro is
    //   // running.
    //   debounceId.current = setTimeout(() => {
    //     // console.log("running debounced onChangeText", text);
    //     onChangeText(null, true);
    //   }, 10);
    // } else {
    // debounceId.current = 0;
    setInputValueWithModalUpdate(false);
    // }
  }

  function handleSubmit() {
    setInputValueWithModalUpdate(latestText.current === value);
    shouldTriggerOnSubmit.current = true;
  }

  useEffect(() => {
    // We don't put debounceId in the dependencies array because
    // we don't actually want to run this effect when that changes.
    // It changes all the time while typing but we only care about
    // this check when the effect is running due to a re-render
    if (textInputRef.current && !inFocus?.current) {
      if (Platform.OS === 'web') {
        set(textInputRef, 'current.value', value);
      } else {
        textInputRef.current?.setNativeProps({
          text: value,
        });
      }
    }

    if (shouldTriggerTypingPaused.current) {
      triggerEvent('onTypingPaused');
      shouldTriggerTypingPaused.current = false;
    }

    if (shouldTriggerOnSubmit.current) {
      triggerEvent('onSubmit');
      shouldTriggerOnSubmit.current = false;
    }
  }, [value, placeholder, triggerEvent]);

  const disabled = model.get('disabled');
  const hasLabel = model.get('hasLabel');
  const clearButtonMode = model.get('clearButtonMode', 'always');
  const numberOfLines = model.get('numberOfLines');
  const keyboardModeValue = model.get('keyboardMode');
  const keyboardType = model.get('keyboardType') || 'default';
  const autoCompleteProp = getPlatformAutoCompleteType(model.get('autoCompleteType'));
  const autoFocus = model.get('autoFocus', false);
  const {width, ...restLayout} = layoutStyles;
  const hasPassword = model.get('hasPassword', false);
  const passwordProps = hasPassword
    ? {
        textContentType: 'oneTimeCode',
        secureTextEntry: true,
        autoCorrect: false,
      }
    : {};

  const multilineMode = parseInt(numberOfLines, 10)
    ? {multiline: true, numberOfLines: parseInt(numberOfLines, 10)}
    : {};

  const keyboardMode = keyboardModeValue ? {keyboardModeValue} : {};

  return (
    <View style={{backgroundColor, width}} ref={ref}>
      {hasLabel && <LabelControlComponent {...props} />}
      <TextInput
        style={[{backgroundColor: innerBackgroundColor}, restLayout, restModelPlatformStyles, typography]}
        ref={textInputRef}
        autoCapitalize="none"
        clearButtonMode={clearButtonMode}
        autoFocus={autoFocus}
        editable={!disabled}
        keyboardType={keyboardType}
        onSubmitEditing={() => handleSubmit()}
        {...multilineMode}
        onBlur={() => {
          inFocus.current = false;
          triggerEvent('onBlur');
        }}
        onFocus={() => {
          inFocus.current = true;
          triggerEvent('onFocus');
        }}
        onChangeText={t => onChangeText(t)}
        placeholder={placeholder}
        placeholderTextColor={placeholderColor}
        {...keyboardMode}
        {...passwordProps}
        {...autoCompleteProp}
      />
    </View>
  );
});

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model: unknown, renderedValue: unknown, _selector: Selector) => {
      return renderedValue ? renderedValue : '';
    },
  },
  onSubmit: {
    type: EventTriggerIdentifier,
  },
  onBlur: {
    type: EventTriggerIdentifier,
  },
  onFocus: {
    type: EventTriggerIdentifier,
  },
  onTypingPaused: {
    type: EventTriggerIdentifier,
  },
  setText: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setText;
    },
    actionMetadata: {
      editableInputParams: {
        value: '',
        focus: false,
      },
    },
  },
  setFocus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return setFocus;
    },
  },
};

const editorsConfig: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
    {
      type: 'codeInput',
      name: 'placeholder',
      props: {
        label: 'Placeholder',
      },
    },
  ],
  layout: defaultEditors.layout,
  advanced: [
    ...labelEditorsConfig.advanced,
    {
      type: 'checkbox',
      name: 'hasPassword',
      props: {
        label: 'Has Password',
      },
    },
    {
      type: 'codeInput',
      name: 'numberOfLines',
      props: {
        label: 'Number of lines',
      },
    },
    {
      type: 'radioGroup',
      name: 'clearButtonMode',
      props: {
        label: 'Show Clear Button',
        options: ['always', 'never', 'unless-editing', 'while-editing'],
      },
    },
    {
      type: 'codeInput',
      name: 'disabled',
      props: {
        label: 'Disabled',
      },
    },
    {
      type: 'dropDown',
      name: 'keyboardType',
      defaultValue: 'default',
      props: {
        label: 'keyboardType',
        options: ['default', 'number-pad', 'numeric', 'email-address', 'phone-pad'],
      },
    },
    {
      type: 'checkbox',
      name: 'autoFocus',
      props: {
        label: 'Auto Focus',
      },
    },
    {
      type: 'dropDown',
      name: 'autoCompleteType',
      defaultValue: 'off',
      props: {
        label: 'Auto Complete Type',
        options: TextInputWidgetAutocompleteTypes,
      },
    },
    ...getValidationEditorProps(fieldsToValidateInConfig),
  ],
};

const emptyOnupdate = null;
export default connectWidget('TextInputWidget', TextInputWidget, TextInputWidgetConfig, emptyOnupdate, editorsConfig, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(textInputWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'textInput'],
});
