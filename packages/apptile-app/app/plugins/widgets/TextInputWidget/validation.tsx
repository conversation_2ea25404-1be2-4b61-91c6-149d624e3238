import _ from 'lodash';
import React from 'react';

export const populateValidationConfig = (config: Record<string, unknown>, fieldsToValidate: string[]) => {
  _.forEach(fieldsToValidate, entry => {
    const {isValid, regexArr, validateToggle} = generateValidationState(entry);
    config[isValid] = false;
    config[regexArr] = [];
    config[validateToggle] = false;
  });

  return config;
};

export const generateValidationState = (fieldName: string) => {
  return {
    isValid: _.camelCase(`is ${fieldName} valid`),
    regexArr: _.camelCase(`${fieldName} Regex`),
    validateToggle: _.camelCase(`validate ${fieldName}`),
  };
};

export const ValidationWrapper = (
  Widget: React.ForwardRefExoticComponent<any & React.RefAttributes<any>>,
  fieldsToValidate: string[],
) => {
  const Wrapper = React.forwardRef<any, any>((props, ref) => {
    const {model, modelUpdate} = props;
    React.useEffect(() => fieldValidator(model, fieldsToValidate, modelUpdate), [model, modelUpdate]);

    return <Widget {...props} ref={ref} />;
  });
  return Wrapper;
};

export const fieldValidator = (model, fieldsToValidate, modelUpdate) => {
  for (let i = 0; i < fieldsToValidate.length; i++) {
    const fieldName = fieldsToValidate[i];
    const {isValid, regexArr, validateToggle} = generateValidationState(fieldName);

    if (!model.get(validateToggle)) continue;

    let validateExp = true;
    let fieldToValidate = model.get(fieldName);

    _.forEach(model.get(regexArr), regexEntry => {
      const expression = regexEntry.expression;

      const pattern = new RegExp(expression as RegExp);
      if (!pattern.test(fieldToValidate)) {
        validateExp = false;
      }
    });

    if (model.get(isValid) === validateExp) continue;

    modelUpdate([
      {
        selector: [isValid],
        newValue: validateExp,
      },
    ]);
  }
};

export const getValidationEditorProps = (fieldsToValidate: string[]) => {
  const editorConfig = [];

  fieldsToValidate.forEach(entry => {
    const {regexArr, validateToggle} = generateValidationState(entry);

    editorConfig.push({
      type: 'checkbox',
      name: validateToggle,
      props: {
        label: `Validate ${entry}`,
      },
    });

    editorConfig.push({
      type: 'regexInput',
      name: regexArr,
      defaultValue: [],
      props: {
        label: `${entry} Regex`,
      },
      hidden: model => !model.get(validateToggle),
    });
  });

  return editorConfig;
};
