import React from 'react';
import {TextInput, Platform} from 'react-native';
import Animated, {useAnimatedProps, useAnimatedReaction, useAnimatedRef} from 'react-native-reanimated';
import {set} from 'lodash';

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);

const AnimatedText = React.forwardRef<any, any>((props, forwardedRef) => {
  const {animatedTextRef} = props;
  const viewRef = useAnimatedRef();
  const reaction = useAnimatedReaction(
    () => {
      return animatedTextRef?.current?.value?.toString();
    },
    value => {
      if (viewRef.current) {
        set(viewRef, 'current.value', value);
      }
    },
    [animatedTextRef?.current],
  );
  const setViewRefs = ref => {
    viewRef.current = ref;
    if (forwardedRef !== null) {
      if (typeof forwardedRef === 'function') {
        forwardedRef(ref);
      } else {
        if (!forwardedRef.hasOwnProperty('current')) {
          logger.error(
            'Unexpected ref object provided for %s. ' + 'Use either a ref-setter function or React.createRef().',
            // getComponentName(finishedWork.type),
          );
        }
        forwardedRef.current = ref;
      }
    }
  };
  return <AnimatedTextInput ref={setViewRefs} {...props} />;
});

export default AnimatedText;
