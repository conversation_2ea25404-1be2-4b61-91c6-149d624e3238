/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {Animated} from 'react-native';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from '../../../styles/types';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget, WidgetProps} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import _ from 'lodash';
import {makeBoolean} from 'apptile-core';
import {usePlaceHolder} from 'apptile-core';
import {isJSBinding} from 'apptile-core';

const TextWidgetConfig = {
  value: 'Text',
  adjustsFontSizeToFit: false,
  minFontScale: 1,
  numLines: 1,
  useAnimamtedValue: false,
  isLoading: false,
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'text',
  type: 'widget',
  name: 'Text',
  description: 'Display Text on screen with varying styles.',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'text',
};

export const textWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
    },
  },
];

const TextWidget = React.forwardRef<any, WidgetProps>((props, ref) => {
  const {model, modelStyles, config, animations} = props;
  const Placeholder = usePlaceHolder();
  const modelValue = model.get('value')?.toString();
  const value = isJSBinding(modelValue) ? '' : modelValue;

  const layout = config.get('layout');
  const isLoading = !!model.get('isLoading');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});

  const {shadowColor, shadowOffset, shadowRadius, ...modelPlatformStyles} = modelStyles
    ? getPlatformStyles(modelStyles)
    : {};

  const {typography, ...restModelPlatformStyles} = modelPlatformStyles;
  const horizontalAlign = _.isEmpty(model.get('horizontalAlign', 'auto'))
    ? 'auto'
    : model.get('horizontalAlign', 'auto');
  const verticalAlign = _.isEmpty(model.get('verticalAlign', 'auto')) ? 'auto' : model.get('verticalAlign', 'auto');

  const overflowType = model.get('overflowType') ? model.get('overflowType') : 'hidden';
  const adjustsFontSizeToFit = model.get('adjustsFontSizeToFit', false);
  const sizeAdjustProps = adjustsFontSizeToFit
    ? {
        adjustsFontSizeToFit,
        minimumFontScale: _.clamp(_.toNumber(model.get('minFontScale', 1)), 0, 1),
        numberOfLines: _.clamp(_.toNumber(model.get('numLines', 1)), 1, Number.MAX_SAFE_INTEGER),
      }
    : {};
  // const animationProps = isAnimated
  //   ? {
  //       entering: animations?.transitions?.entering,
  //       exiting: animations?.transitions?.exiting,
  //       layout: animations?.transitions?.layout,
  //     }
  //   : {};
  const animationProps = {};

  return isLoading ? (
    <Placeholder
      layoutStyles={{...layoutStyles, ...restModelPlatformStyles, height: typography?.fontSize, minWidth: '75%'}}
    />
  ) : (
    <Animated.Text
      ref={ref}
      {...sizeAdjustProps}
      style={[
        layoutStyles,
        restModelPlatformStyles,
        typography,
        {
          textAlign: ['auto', 'left', 'center', 'right'].includes(horizontalAlign) ? horizontalAlign : 'auto',
          textAlignVertical: ['auto', 'top', 'center', 'bottom'].includes(verticalAlign) ? verticalAlign : 'auto',
          overflow: overflowType,
        },
        {
          textShadowColor: shadowColor,
          textShadowOffset: shadowOffset,
          textShadowRadius: shadowRadius,
        },
      ]}>
      {value ? value : ''}
    </Animated.Text>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    ...defaultEditors.basic,
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'adjustsFontSizeToFit',
      props: {
        label: 'Adjust font size to fit',
      },
    },
    {
      type: 'codeInput',
      name: 'minFontScale',
      props: {
        label: 'Minimum font scale',
      },
      hidden: model => !model.get('adjustsFontSizeToFit'),
    },
    {
      type: 'codeInput',
      name: 'numLines',
      props: {
        label: 'Number of lines',
      },
      hidden: model => !model.get('adjustsFontSizeToFit'),
    },
    {
      type: 'codeInput',
      name: 'isLoading',
      defaultValue: false,
      props: {
        label: 'Loading State',
      },
    },
  ],
  layout: [
    {
      type: 'radioGroup',
      name: 'horizontalAlign',
      props: {
        label: 'Horizontal Alignment',
        options: [
          {icon: 'alpha-a', value: 'auto'},
          {icon: 'format-align-left', value: 'left'},
          {icon: 'format-align-center', value: 'center'},
          {icon: 'format-align-right', value: 'right'},
          {icon: 'format-align-justify', value: 'justify'},
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'verticalAlign',
      props: {
        label: 'Vertical Alignment',
        options: [
          {icon: 'alpha-a', value: 'auto'},
          {icon: 'format-vertical-align-top', value: 'top'},
          {icon: 'format-vertical-align-center', value: 'center'},
          {icon: 'format-vertical-align-bottom', value: 'bottom'},
        ],
      },
    },
    {
      type: 'radioGroup',
      name: 'overflowType',
      props: {
        label: 'Overflow',
        options: ['scroll', 'hidden'],
      },
    },
    ...defaultEditors.layout,
  ],
  animations: defaultEditors.animations,
};

const propertySettings: PluginPropertySettings = {
  isLoading: {
    getValue: (model, val) => {
      return makeBoolean(val);
    },
  },
  minFontScale: {
    getValue: (model, val) => {
      return val;
    },
  },
  numLines: {
    getValue: (model, val) => {
      return val;
    },
  },
  useAnimamtedValue: {
    getValue: (model, val) => {
      return makeBoolean(val);
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('TextWidget', TextWidget, TextWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(textWidgetStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'text'],
});
