import React, {useContext, useEffect, useRef, useCallback} from 'react';
import {
  connectWidget,
  PluginEditorsConfig,
  defaultEditors,
  defaultStyleEditors,
  PluginListingSettings,
  PluginPropertySettings,
  WidgetRefContext,
  TriggerActionIdentifier,
  EventTriggerIdentifier,
  modelUpdateAction,
  ApptileAnimationsContext,
} from 'apptile-core';
import {Platform, Pressable} from 'react-native';
import docs from './docs';
import {Video} from './video';
import {
  replayVideo,
  pause,
  play,
  mute,
  unmute,
  skipAhead,
  rewind,
  setTapped,
  seekByPercentage,
  seekByTime,
} from './videoActions';
import {useDispatch} from 'react-redux';

const widgetConfig = {
  // Do not bind these props to bindings in the studio.
  // They are not given and propertyeditor because they are supposed
  // to be used by this widget to expose states when actions are triggered.
  // If you need to set values for the props, use the actions instead
  source: '',
  poster: '',
  paused: '',
  ended: '',
  muted: '',
  tapped: '',
  loop: '',
  pictureInPicture: '',
  controls: '',
  play: 'action',
  pause: 'action',
  replayVideo: 'action',
  mute: 'action',
  unmute: 'action',
  skipAhead: 'action',
  rewind: 'action',
  setTapped: 'action',
  seekByPercentage: 'action',
  seekByTime: 'action',
};

const propertySettings: PluginPropertySettings = {
  source: {
    getValue(model, renderedValue, selector) {
      return renderedValue.toString();
    },
  },
  poster: {
    getValue(model, renderedValue, selector) {
      return renderedValue.toString();
    },
  },
  paused: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  muted: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  ended: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  tapped: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  play: {
    type: TriggerActionIdentifier,
    getValue() {
      return play;
    },
  },
  pause: {
    type: TriggerActionIdentifier,
    getValue() {
      return pause;
    },
  },
  replayVideo: {
    type: TriggerActionIdentifier,
    getValue() {
      return replayVideo;
    },
  },
  mute: {
    type: TriggerActionIdentifier,
    getValue() {
      return mute;
    },
  },
  unmute: {
    type: TriggerActionIdentifier,
    getValue() {
      return unmute;
    },
  },
  skipAhead: {
    type: TriggerActionIdentifier,
    getValue() {
      return skipAhead;
    },
  },
  rewind: {
    type: TriggerActionIdentifier,
    getValue() {
      return rewind;
    },
  },
  setTapped: {
    type: TriggerActionIdentifier,
    getValue() {
      return setTapped;
    },
    actionMetadata: {
      editableInputParams: {
        sticky: '{{true}}',
      },
    },
  },
  seekByPercentage: {
    type: TriggerActionIdentifier,
    getValue() {
      return seekByPercentage;
    },
    actionMetadata: {
      editableInputParams: {
        percentage: '{{videoSeeker.seekPercentage}}',
      },
    },
  },
  seekByTime: {
    type: TriggerActionIdentifier,
    getValue() {
      return seekByTime;
    },
    actionMetadata: {
      editableInputParams: {
        time: '{{videoSeeker.seekByTime}}',
      },
    },
  },
  onPause: {
    type: EventTriggerIdentifier,
  },
  loop: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  pictureInPicture: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  controls: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  }
};

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'source',
      props: {
        label: 'Source',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'poster',
      props: {
        label: 'poster',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'loop',
      props: {
        label: 'Loop',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'pictureInPicture',
      props: {
        label: 'Picture in picture',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'paused',
      props: {
        label: 'Paused',
        placeholder: '',
      }
    },
    {
      type: 'codeInput',
      name: 'controls',
      props: {
        label: 'Controls',
        placeholder: '',
      }
    }
  ],
  layout: defaultEditors.layout,
  animations: defaultEditors.animations,
};

const VideoPlayerWidgetV2 = React.forwardRef((props, ref) => {
  logger.info('[VIDEOPLAYER] rendering');

  const {model, config, triggerEvent, pageKey, id, instance, namespace} = props;
  const widgetRefContext = useContext(WidgetRefContext);
  const animProvider = useContext(ApptileAnimationsContext);
  const source = model.get('source');
  const poster = model.get('poster');
  let controls = model.get('controls')
  if(!controls && controls !== false) {
    controls = false
  }
  let tapped = model.get('tapped');
  if (!tapped && tapped !== 0) {
    tapped = 0;
  }

  let muted = model.get('muted');
  if (!muted && muted !== false) {
    muted = false;
  }
  let paused = model.get('paused');
  if (!paused && paused !== false) {
    paused = false;
  }
  let loop = model.get('loop');
  if (!loop && loop !== false) {
    loop = false;
  }
  let pictureInPicture = model.get('pictureInPicture');
  if (!pictureInPicture && pictureInPicture !== false) {
    pictureInPicture = false;
  }

  const layout = config.get('layout');
  const dispatch = useDispatch();
  const layoutStyles = layout ? layout.getFlexProperties() : {};

  const videoRef = useRef<typeof Video>();
  const currentProgress = useRef({
    currentTime: 0,
    playableDuration: 0,
    seekableDuration: 0,
  }).current;
  const tapOut = useRef<Timeout | null>(null);

  // This is just to facilitate a focus blur event on the video plugin.
  // If the video plugin is tapped it will set a `tapped` state that it will
  // exit after 5 seconds. This can then be used to show overlay controls that
  // disappear if video is not tapped for some duration
  useEffect(() => {
    if (tapOut.current) {
      clearTimeout(tapOut.current);
    }

    // only schedule a revert if currently its tapped
    if (tapped > 0) {
      tapOut.current = setTimeout(() => {
        // only do a reset if currently it is tapped
        if (tapped > 0) {
          const updateAction = modelUpdateAction([
            {
              selector: [pageKey, 'plugins', id, 'tapped'],
              newValue: 0,
            },
          ]);

          dispatch(updateAction);
        }
      }, 5000);
    } else if (tapped < 0) {
      logger.info('Entered sustained focus till a positive tapped value is set');
    }
  }, [tapped, tapOut.current]);

  useEffect(() => {
    widgetRefContext.registerWidgetRef(
      {
        current: {
          play() {
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'paused'],
                newValue: false,
              },
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
            if (Platform.OS === 'web' && videoRef.current) {
              videoRef.current.play();
            }
          },
          pause() {
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'paused'],
                newValue: true,
              },
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
            if (Platform.OS === 'web' && videoRef.current) {
              videoRef.current.pause();
            }
          },
          replayVideo() {
            if (videoRef.current) {
              videoRef.current.seek(0);
            }

            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'paused'],
                newValue: true,
              },
              {
                selector: [pageKey, 'plugins', id, 'ended'],
                newValue: false,
              },
            ]);
            dispatch(updateAction);

            if (Platform.OS === 'web' && videoRef.current) {
              videoRef.current.pause();
            }
          },
          mute() {
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'muted'],
                newValue: true,
              },
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          unmute() {
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'muted'],
                newValue: false,
              },
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          skipAhead() {
            const currentTime = currentProgress.currentTime;
            let targetTime = currentTime + 10;
            if (targetTime >= currentProgress.seekableDuration) {
              targetTime = currentProgress.seekableDuration;
            }
            videoRef.current?.seek(targetTime);
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          rewind() {
            const currentTime = currentProgress.currentTime;
            let targetTime = currentTime - 10;
            if (targetTime < 0) {
              targetTime = 0;
            }
            videoRef.current?.seek(targetTime);
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          setTapped(params: {sticky: boolean}) {
            // setting a negative value to tapped makes it not exit the tapped
            // state in 10 second. Once that happens it has to be set to a positive value
            // again in order to exit
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: params && params?.sticky ? -1 : Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          seekByPercentage(params: {percentage: number}) {
            let targetTime = params.percentage * currentProgress.seekableDuration;
            if (targetTime >= currentProgress.seekableDuration) {
              targetTime = currentProgress.seekableDuration;
            } else if (targetTime < 0) {
              targetTime = 0;
            }
            videoRef.current?.seek(targetTime, 50);
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
          seekByTime(params: {time: number}) {
            let targetTime = params.time;
            if (targetTime >= currentProgress.seekableDuration) {
              targetTime = currentProgress.seekableDuration;
            } else if (targetTime < 0) {
              targetTime = 0;
            }
            videoRef.current?.seek(targetTime, 50);
            const updateAction = modelUpdateAction([
              {
                selector: [pageKey, 'plugins', id, 'tapped'],
                newValue: Date.now(),
              },
            ]);
            dispatch(updateAction);
          },
        },
      },
      pageKey,
      id,
      instance,
    );

    return () => {
      widgetRefContext.unRegisterWidgetRef(pageKey, id, instance);
    };
  }, [widgetRefContext, pageKey, id, instance, dispatch, videoRef.current, currentProgress]);

  const handlePlaybackEnd = useCallback(() => {
    const updateAction = modelUpdateAction([
      {
        selector: [pageKey, 'plugins', id, 'ended'],
        newValue: true,
      },
    ]);
    dispatch(updateAction);
  }, [dispatch]);

  let eventName: string;
  if (namespace) {
    const namespaceSelector = namespace.getNamespace().join('.');
    eventName = `${pageKey}.${namespaceSelector}::${id}.playbackProgress`;
  } else {
    eventName = `${pageKey}.${id}.playbackProgress`;
  }

  const handleProgress = useCallback(
    payload => {
      currentProgress.currentTime = payload.currentTime;
      currentProgress.playableDuration = payload.playableDuration;
      currentProgress.seekableDuration = payload.seekableDuration;

      animProvider.triggerEvent(eventName, payload);
    },
    [currentProgress, animProvider, eventName],
  );

  const handleTap = () => {
    if (!tapped) {
      const updateAction = modelUpdateAction([
        {
          selector: [pageKey, 'plugins', id, 'tapped'],
          newValue: Date.now(),
        },
      ]);

      dispatch(updateAction);
    }
  };

  // The Pressable wrapper here is to force this component to only be used
  // with position absolute on video.
  // Setting height and width directly on the video widget doesn't seem
  // to work sometimes
  return (
    <Pressable
      onPress={handleTap}
      style={[
        {
          position: 'relative',
          width: '100%',
          height: '100%',
        },
        layoutStyles,
      ]}>
      <Video
        ref={videoRef}
        source={{uri: source}}
        controls={controls}
        poster={poster}
        paused={paused}
        muted={muted}
        pictureInPicture={pictureInPicture}
        resizeMode={'cover'}
        posterResizeMode={'cover'}
        style={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        }}
        repeat={loop}
        ignoreSilentSwitch={'ignore'}
        preventsDisplaySleepDuringVideoPlayback={true}
        onEnd={handlePlaybackEnd}
        onProgress={handleProgress}
      />
    </Pressable>
  );
});

const pluginListing: PluginListingSettings = {
  labelPrefix: 'videoPlayerv2',
  type: 'widget',
  name: 'VideoPlayerV2',
  description: 'Play external videos',
  layout: {
    flex: 1,
  },
  defaultHeight: 'auto',
  defaultWidth: 'auto',
  section: 'Display',
  icon: 'video-player',
};

export default connectWidget('VideoPlayerWidgetV2', VideoPlayerWidgetV2, widgetConfig, null, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
  animations: {
    animatedValues: ['playbackProgress'],
  },
});
