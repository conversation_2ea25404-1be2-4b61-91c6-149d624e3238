import React, {useEffect, useRef, forwardRef, useImperativeHandle} from 'react';
import videojs, {VideoJsPlayer, VideoJsPlayerOptions} from 'video.js';

export const Video = forwardRef((props, ref) => {
  const {source, muted, paused, onProgress, handlePlaybackEnd, repeat} = props;
  const videoPlayerRoot = useRef();
  const player = useRef();
  useImperativeHandle(
    ref,
    () => {
      return {
        seek(seconds: number) {
          if (player.current) {
            player.current.currentTime(seconds);
          }
        },
        play() {
          if (player.current) {
            player.current.play();
          }
        },
        pause() {
          if (player.current) {
            player.current.pause();
          }
        },
      };
    },
    [player.current, videoPlayerRoot],
  );

  useEffect(() => {
    const interval = setInterval(() => {
      if (player.current) {
        const time = player.current.currentTime();
        const seekableDuration = player.current.duration();
        onProgress({
          currentTime: time,
          playableDuration: seekableDuration,
          seekableDuration: seekableDuration,
        });
      }
    }, 1000);
    return () => clearInterval(interval);
  }, [player.current, onProgress]);

  useEffect(() => {
    if (videoPlayerRoot.current) {
      const box = videoPlayerRoot.current.getBoundingClientRect();
      const videoElement = document.createElement('video-js');
      const webplayer = videojs(
        videoElement,
        {
          muted,
          sources: [{src: source.uri, type: 'video/mp4'}],
          controls: false,
          loop: repeat,
          width: box.width,
          height: box.height,
          autoplay: !paused,
        },
        () => {
          console.log('web video player is ready');
        },
      );

      player.current = webplayer;
      // webplayer.on("ended", handlePlaybackEnd)
      videoPlayerRoot.current.appendChild(videoElement);
      videoElement.setAttribute('style', 'width: 100%; height: 100%;');
    }
    return () => {
      if (videoPlayerRoot.current) {
        videoPlayerRoot.current.innerHTML = '';
      }
      player.current?.dispose();
    };
  }, [videoPlayerRoot.current, source.uri]);
  return (
    <div data-vjs-player style={{width: '100%', height: '100%'}}>
      <div ref={videoPlayerRoot} style={{width: '100%', height: '100%'}} />
    </div>
  );
});
