import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {WidgetRefRegistry} from 'apptile-core';

export function play(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.play();
}

export function pause(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.pause();
}

export function replayVideo(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.replayVideo(); 
}

export function mute(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.mute(); 
}

export function unmute(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.unmute(); 
}

export function skipAhead(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.skipAhead(); 
}

export function rewind(dispatch, config: PluginConfig, model, selector: Selector) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.rewind(); 
}

export function setTapped(dispatch, config: PluginConfig, model, selector: Selector, params: {sticky: boolean}) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.setTapped(params); 
}

export function seekByPercentage(dispatch, config: PluginConfig, model, selector: Selector, params: {percentage: number}) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.seekByPercentage(params);
}

export function seekByTime(dispatch, config: PluginConfig, model, selector: Selector, params: {percentage: number}) {
  const pageKey = selector[0];
  const pluginId = selector[2];
  const instance = model.get('instance');
  let currentRef = WidgetRefRegistry.getWidgetRef(pageKey, pluginId, instance);
  currentRef?.current?.seekByTime(params);
}
