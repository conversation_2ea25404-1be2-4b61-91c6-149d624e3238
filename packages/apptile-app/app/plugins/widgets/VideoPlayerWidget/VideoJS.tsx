import React, {useImperativeHandle} from 'react';
import videojs, {VideoJsPlayer, VideoJsPlayerOptions} from 'video.js';
import {cloneDeep} from 'lodash';

import './VideoDefault.web.css';
import './VideoPlayer.web.css';

type Props = {
  options: VideoJsPlayerOptions;
  onReady?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onEnd?: () => void;
};

export const VideoJS = React.forwardRef<VideoJsPlayer, Props>((props, ref) => {
  const options = cloneDeep(props.options);

  const videoRef = React.useRef<HTMLDivElement | null>(null);
  const playerRef = React.useRef<VideoJsPlayer | null>(null);
  useImperativeHandle(ref, () => playerRef.current as VideoJsPlayer);

  React.useEffect(() => {
    if (!playerRef.current) {
      if (!options.sources?.some(o => !!o.src)) return;
      const videoElement = document.createElement('video-js');
      videoRef.current?.appendChild(videoElement);
      playerRef.current = videojs(videoElement, options, props.onReady);
    } else {
      const player = playerRef.current;
      if (options.sources) player.src(options.sources);
      player.controls(!!options.controls);
      if (options.poster) player.poster(options.poster);
      player.muted(!!options.muted);
      if (options.width) player.width(options.width);
      if (options.height) player.height(options.height);
    }

    playerRef.current?.on('play', props.onPlay);
    playerRef.current?.on('pause', props.onPause);
    playerRef.current?.on('ended', props.onEnd);
    return () => {
      const eventsToRemove = ['play', 'pause', 'ended'];
      playerRef.current?.off(eventsToRemove);
    };
  }, [options, props.onReady, props.onPlay, props.onPause, props.onEnd, videoRef]);

  React.useEffect(() => {
    const player = playerRef.current;
    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  return (
    <div data-vjs-player>
      <div ref={videoRef} />
    </div>
  );
});

export default VideoJS;
