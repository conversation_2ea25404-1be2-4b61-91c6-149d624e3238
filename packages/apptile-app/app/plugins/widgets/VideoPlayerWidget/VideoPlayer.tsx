import React, {useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, Pressable, AppState, Image, Modal, StatusBar, View} from 'react-native';
import {useIsFocused} from '@react-navigation/native';
import Orientation from 'react-native-orientation-locker';
import MobileVideoPlayer from 'react-native-video-controls';
import once from 'lodash/once';
import _ from 'lodash';
import {VideoPlayerProps} from './types';
import {MaterialCommunityIcons} from 'apptile-core';

const VideoPlayer: React.FC<VideoPlayerProps> = props => {
  const {URL, controls, loop, mute, autoplay, resizeMode = 'contain', poster} = props;
  const {onPlay, onPause, onEnd} = props;
  const [paused, setPaused] = useState(!autoplay);
  const [showPoster, setShowPoster] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const secureURL = URL.replace(/^http:\/\//, 'https://');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const _onPlay = useCallback(
    once(() => {
      setShowPoster(false);
      setPaused(false);
      onPlay?.();
    }),
    [],
  );

  const _controls = {
    disableFullscreen: !(!paused && controls),
    disablePlayPause: !(!paused && controls),
    disableSeekbar: !(!paused && controls),
    disableVolume: true || !(!paused && controls),
    disableTimer: !(!paused && controls),
    disableBack: true || !(!paused && controls),
  };

  const isFocused = useIsFocused();
  useEffect(() => {
    if (!isFocused) setPaused(true);
    if (autoplay && isFocused) setPaused(false);
  }, [autoplay, isFocused]);

  const appState = useRef(AppState.currentState);
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        if (autoplay && isFocused) setPaused(false);
      } else setPaused(true);
      appState.current = nextAppState;
    });
    return () => subscription.remove();
  }, [autoplay, isFocused]);

  const handleEnterFullscreen = () => {
    Orientation.lockToLandscape();
    setIsFullscreen(true);
  };

  const handleExitFullscreen = () => {
    Orientation.lockToPortrait();
    setIsFullscreen(false);
  };

  const renderVideoPlayer = () => (
    <MobileVideoPlayer
      source={{uri: secureURL}}
      paused={paused}
      resizeMode={resizeMode}
      {..._controls}
      repeat={loop}
      muted={mute}
      onEnterFullscreen={handleEnterFullscreen}
      onExitFullscreen={handleExitFullscreen}
      onProgress={_onPlay}
      onPause={() => {
        setPaused(true);
        onPause?.();
      }}
      onEnd={() => {
        setPaused(true);
        onEnd?.();
      }}
    />
  );

  return (
    <>
      {!_.isEmpty(secureURL) && (
        <>
          {!isFullscreen && (
            <>
              {renderVideoPlayer()}
              {paused && controls && (
                <Pressable style={styles.playButton} onPress={() => setPaused(false)}>
                  <MaterialCommunityIcons size={30} name="play" color="#ffffff" />
                </Pressable>
              )}
            </>
          )}

          {isFullscreen && (
            <Modal visible animationType="fade" supportedOrientations={['landscape', 'portrait']}>
              <View style={styles.fullscreenContainer}>
                {renderVideoPlayer()}
                {paused && controls && (
                  <Pressable style={styles.playButton} onPress={() => setPaused(false)}>
                    <MaterialCommunityIcons size={30} name="play" color="#ffffff" />
                  </Pressable>
                )}
              </View>
            </Modal>
          )}
        </>
      )}

      {paused && !!poster && showPoster && <Image source={{uri: poster}} style={{...styles.posterImage, resizeMode}} />}
    </>
  );
};

const styles = StyleSheet.create({
  playButton: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    top: '50%',
    left: '50%',
    transform: [{translateX: -32}, {translateY: -32}],
    width: 64,
    height: 64,
    borderRadius: 64,
    zIndex: 2,
    backgroundColor: '#2b333fb3',
  },
  posterImage: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
});

export default VideoPlayer;
