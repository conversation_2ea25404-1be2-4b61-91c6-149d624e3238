import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useIsFocused} from '@react-navigation/native';
import {VideoJsPlayer} from 'video.js';
import {isEqual} from 'lodash';

import {VideoPlayerProps} from './types';
import WebVideoPlayer from './VideoJS';

const VideoPlayer: React.FC<VideoPlayerProps> = ({layout, ...props}) => {
  const {URL, controls, loop, mute, autoplay, resizeMode = 'contain', poster} = props;
  const {onPlay, onPause, onEnd: _onEnd} = props;
  const playerRef = useRef<VideoJsPlayer>(null);
  const isFocused = useIsFocused();
  useEffect(() => {
    if (!isFocused) playerRef.current?.pause();
    if (autoplay && isFocused) playerRef.current?.play();
  }, [autoplay, isFocused]);

  const [isReady, setIsReady] = useState(false);
  const onReady = useCallback(() => setIsReady(true), []);
  const onEnd = useCallback(() => {
    if (loop) playerRef.current?.play();
    _onEnd?.();
  }, [loop, _onEnd]);

  return (
    <>
      {isReady && (
        <style
          dangerouslySetInnerHTML={{
            __html: `
            #${playerRef.current?.id_}.video-js .vjs-tech {
              ${
                resizeMode === 'cover'
                  ? `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translateX(-50%) translateY(-50%);
                min-width: 100%;
                min-height: 100%;
                width: auto;
                height: auto;
              `
                  : ''
              }
            }
            #${playerRef.current?.id_}.video-js .vjs-poster img {
              object-fit: ${resizeMode};
            }`,
          }}
        />
      )}

      <WebVideoPlayer
        ref={playerRef}
        options={{
          sources: [{src: URL}],
          controls,
          poster,
          muted: mute,
          width: layout.width,
          height: layout.height,
        }}
        onReady={onReady}
        onPlay={onPlay}
        onPause={onPause}
        onEnd={onEnd}
      />
    </>
  );
};

const MemoVideoPlayer = React.memo(VideoPlayer, (prevProps, nextProps) => isEqual(prevProps, nextProps));

export default MemoVideoPlayer;
