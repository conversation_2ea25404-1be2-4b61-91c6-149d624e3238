const docs = {
  value: {
    '!type': 'number',
    '!doc': 'URL of the video.',
  },
  controls: {
    '!type': 'boolean',
    '!doc': 'Show controls on the player.',
  },
  loop: {
    '!type': 'boolean',
    '!doc': 'Repeat video on end.',
  },
  mute: {
    '!type': 'boolean',
    '!doc': 'Mute video.',
  },
  autoplay: {
    '!type': 'boolean',
    '!doc': 'Start video as soon as loaded.',
  },
  poster: {
    '!type': 'string',
    '!doc': 'Poster URL, to be shown if video is yet to load.',
  },
};

export default docs;
