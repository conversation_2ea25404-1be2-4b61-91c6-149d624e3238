import React, {useCallback, useState, useEffect} from 'react';
import {LayoutChangeEvent, LayoutRectangle, PixelRatio, View} from 'react-native';

import docs from './docs';
import {connectWidget} from 'apptile-core';
import {
  PluginListingSettings,
  PluginPropertySettings,
  EventTriggerIdentifier,
  PluginModelType,
  PluginModelChange,
} from 'apptile-core';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {PluginConfigType} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {getPlatformStyles} from 'apptile-core';
import VideoPlayer from './VideoPlayer';

const ValidVideoFormats = ['.mp4', '.m3u8'];

type videoPlayerWidgetConfigType = {
  value: string;
  controls: boolean;
  loop: boolean;
  mute: boolean;
  autoplay: boolean;
  resizeMode: 'contain' | 'cover';
  poster: string;
  onPlay: any;
  onPause: any;
  onEnd: any;
};

const videoPlayerWidgetConfig: videoPlayerWidgetConfigType = {
  value: '',
  controls: true,
  loop: false,
  mute: false,
  autoplay: false,
  resizeMode: 'contain',
  poster: '',
  onPlay: 'Event_onPlay',
  onPause: 'Event_onPause',
  onEnd: 'Event_onEnd',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'videoPlayer',
  type: 'widget',
  name: 'Video Player',
  description: 'Play external videos',
  layout: {
    flex: 1,
  },
  defaultHeight: 'auto',
  defaultWidth: 'auto',
  section: 'Display',
  icon: 'video-player',
};

interface IProps {
  model: PluginModelType;
  modelStyles: Record<string, any>;
  config: PluginConfigType<videoPlayerWidgetConfigType>;
  modelUpdate: (changes: PluginModelChange[]) => void;
  triggerEvent: (pluginId: string, event?: string) => void;
}

const VideoPlayerWidget = React.forwardRef<View, IProps>((props, ref) => {
  const {model, modelStyles, getDeviceImage, config, modelUpdate, triggerEvent} = props;

  const innerRef = React.useRef<View>(null);
  React.useEffect(() => {
    if (!ref) return;
    if (typeof ref === 'function') ref(innerRef.current);
    else ref.current = innerRef.current;
  }, [ref]);

  const value = model.get('value');
  const controls = !!model.get('controls');
  const loop = !!model.get('loop');
  const mute = !!model.get('mute');
  const autoplay = !!model.get('autoplay');
  const resizeMode = model.get('resizeMode');
  const poster = model.get('poster');
  const posterSourceType = model.get('posterSourceType');
  const posterAssetId = model.get('posterAssetId');
  const {getOptimalImage} = getDeviceImage(posterAssetId);

  const [layoutRectangle, setLayoutRectangle] = useState<LayoutRectangle>();
  const onLayout = (event: LayoutChangeEvent) => {
    setLayoutRectangle(event.nativeEvent.layout);
  };

  let imageSource: string =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=';

  if (posterSourceType && posterSourceType?.toLowerCase() !== 'url' && posterAssetId && getOptimalImage && layoutRectangle) {
    const newWidth = PixelRatio.getPixelSizeForLayoutSize(layoutRectangle?.width!);
    const newHeight = PixelRatio.getPixelSizeForLayoutSize(layoutRectangle?.height!);
    const assetSource = getOptimalImage && getOptimalImage(`${newWidth}x${newHeight}`);
    const assetSourceLink = assetSource?.fileUrl;
    imageSource = assetSourceLink;
  } else if (posterSourceType && posterSourceType?.toLowerCase() === 'url') {
    imageSource = poster;
  }

  const isUriValid = ValidVideoFormats.some(format => value?.includes(format));

  const onPlay = useCallback(() => triggerEvent('onPlay'), [triggerEvent]);
  const onPause = useCallback(() => triggerEvent('onPause'), [triggerEvent]);
  const onEnd = useCallback(() => triggerEvent('onEnd'), [triggerEvent]);

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const {borderRadius, margin, padding, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  return (
    <View ref={innerRef} onLayout={onLayout} style={[layoutStyles, modelPlatformStyles]}>
      {layoutRectangle && (
        <VideoPlayer
          URL={value}
          {...{controls, loop, mute, autoplay, resizeMode}}
          {...{onPlay, onPause, onEnd}}
          poster={imageSource}
          layout={layoutRectangle}
        />
      )}
      {!isUriValid && <></>}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'cloudinaryEditor',
      name: 'value',
      props: {
        label: 'Video',
        urlProperty: 'value',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'controls',
      props: {
        label: 'Controls',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'loop',
      props: {
        label: 'Loop',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'mute',
      props: {
        label: 'Mute',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'autoplay',
      props: {
        label: 'Auto Play',
        placeholder: '',
      },
    },
    {
      type: 'radioGroup',
      name: 'resizeMode',
      defaultValue: 'contain',
      props: {
        label: 'Resize Mode',
        options: ['contain', 'cover'],
      },
    },
    {
      type: 'assetEditor',
      name: 'poster',
      props: {
        label: 'Thumbnail',
        urlProperty: 'poster',
        assetProperty: 'posterAssetId',
        sourceTypeProperty: 'posterSourceType',
      },
    },
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue, _selector) => renderedValue?.toString(),
  },
  controls: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  loop: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  autoplay: {
    getValue: (model, renderedValue, _selector) => !!renderedValue,
  },
  onPlay: {
    type: EventTriggerIdentifier,
  },
  onPause: {
    type: EventTriggerIdentifier,
  },
  onEnd: {
    type: EventTriggerIdentifier,
  },
};

export default connectWidget('VideoPlayerWidget', VideoPlayerWidget, videoPlayerWidgetConfig, null, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
