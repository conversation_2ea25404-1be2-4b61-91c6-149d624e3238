import React, {useRef, useEffect, useCallback, useContext} from 'react';
import {
  connectWidget,
  PluginEditorsConfig,
  defaultEditors,
  defaultStyleEditors,
  PluginListingSettings,
  PluginPropertySettings,
  ApptileAnimationsContext,
  MaterialCommunityIcons,
  EventTriggerIdentifier,
  modelUpdateAction,
  mergeWithDefaultStyles,
  WidgetStyleEditorOptions,
} from 'apptile-core';
import { Animated, TextInput, Easing, PanResponder, Platform } from 'react-native';
import { useDispatch } from 'react-redux';
import docs from './docs';

const AnimatedText = Animated.createAnimatedComponent(TextInput);

const widgetConfig = {
  onPanStart: '',
  onPanEnd: '',
  // readonly property to be used by nocode to ask videoplayer to seek to some location
  seekPercentage: '',
  activeBarColor: '#ffffffff',
  inactiveBarColor: '#ccccccff',
  sliderIcon: 'circle',
  barHeight: '10'
};

const propertySettings: PluginPropertySettings = {
  onPanStart: {
    type: EventTriggerIdentifier
  }, 
  onPanEnd: {
    type: EventTriggerIdentifier
  }
};

const VideoSeekerStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'activeBarColor',
    props: {
      label: 'Played bar color',
      placeholder: 'transparentBackground'
    }
  },
  {
    type: 'colorInput',
    name: 'inactiveBarColor',
    props: {
      label: 'Unplayed bar color',
      placeholder: 'transparentBackground'
    }
  },
];

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'sliderIcon',
      props: {
        label: 'Slider Icon',
        placeholder: 'circle'
      }
    },
    {
      type: 'codeInput',
      name: 'barHeight',
      props: {
        label: 'Bar thickness',
        placeholder: '10'
      }
    }
  ],
  layout: defaultEditors.layout,
  animations: defaultEditors.animations
}

function secondsToTimestamp(seconds: number) {
  seconds = Math.trunc(seconds); // ignore milliseconds

  const hours = Math.floor(seconds/(60*60));
  let secondsLeft = seconds % (60*60);

  const minutes = Math.floor(secondsLeft/60);
  secondsLeft = secondsLeft % 60;

  let result = '';
  if (hours > 0) {
    result += hours.toString().padStart(2, '0') + ':';
  }

  result += minutes.toString().padStart(2, '0') + ':';
  result += secondsLeft.toString().padStart(2, '0');

  return result;
}

const VideoSeeker = React.forwardRef((props, ref) => {
  const noSelect = Platform.select({
    web: {userSelect: 'none'},
    ios: {},
    android: {},
  });
  const { model, config, triggerEvent, pageKey, id, instance, animations, namespace } = props;
  const sliderIcon = model.get('sliderIcon') || 'circle';
  let barHeight = parseInt(model.get('barHeight'));
  if (isNaN(barHeight)) {
    barHeight = 10
  } 
  
  const shouldTriggerPanEnd = useRef(false);
  
  const activeBarColor = model.get('activeBarColor') || '#ffffffff';
  const inactiveBarColor = model.get('inactiveBarColor') || '#ccccccff';

  const dispatch = useDispatch();

  const animProvider = useContext(ApptileAnimationsContext);
  const playbackDuration = parseInt(model.get('playbackDuration')) || 320;

  const animValues = animations?.get('references').get('playbackProgress');
  const onMountfadeIn = useRef(new Animated.Value(0)).current;
  const ignoreBigDelta = useRef(false);

  let eventName: string = "";
  if (namespace && animValues) {
    const namespaceSelector = namespace.getNamespace().join('.');
    eventName = `${pageKey}.${namespaceSelector}::${animValues.join('.')}`;
  } else if (animValues) {
    eventName = `${pageKey}.${animValues.join('.')}`;
  }

  const currentPlayedPercentage = useRef(new Animated.Value(0)).current;  
  const boxDimension = useRef(new Animated.ValueXY({x: 300, y: 100})).current;
  const upperLimitTextRef = useRef();

  const lowerLimitTextRef = useRef();

  const setDimensions = (event: any) => {
    const width = event.nativeEvent.layout.width;
    const height = event.nativeEvent.layout.height;
    if (width && height) {
      boxDimension.setValue({x: width, y: height});
    }

    let playedSeconds;
    if (lowerLimitTextRef.current) {
      playedSeconds = secondsToTimestamp(currentPlayedPercentage._value * playbackDuration);
      
      Platform.select({
        web: () => {lowerLimitTextRef.current.value = `-${playedSeconds}`},
        default: () => {lowerLimitTextRef.current.setNativeProps({
          text: `-${playedSeconds}`
        })},
      })();
    }

    if (upperLimitTextRef.current) {
      playedSeconds = secondsToTimestamp((1 - currentPlayedPercentage._value) * playbackDuration);

      Platform.select({
        web: () => {upperLimitTextRef.current.value = `${playedSeconds}`},
        default: () => {upperLimitTextRef.current.setNativeProps({
          text: `${playedSeconds}`
        })},
      })();
    }
  };

  const eventCallback = useCallback(currentProgress => {
    let percentage = currentProgress.currentTime / currentProgress.seekableDuration;
    if ((currentProgress.seekableDuration == 0) || isNaN(percentage)) {
      percentage = 0;
    }

    if (onMountfadeIn._value == 0) {
      Animated.timing(onMountfadeIn,
      {
        toValue: 1,
        duration: 300,
        easing: Easing.in,
        useNativeDriver: true
      }).start();
    }
    if (!isNaN(percentage)) {
      const currentPercentage = currentPlayedPercentage._value;
      const deltaTime = Math.abs(currentPercentage - percentage) * currentProgress.seekableDuration;

      const handleUpdate = () => {
        currentPlayedPercentage.setValue(percentage);
        
        let playedSeconds = "";
        if (lowerLimitTextRef.current) {
          playedSeconds = secondsToTimestamp(currentProgress.currentTime);

          Platform.select({
            web: () => {lowerLimitTextRef.current.value = `-${playedSeconds}`},
            default: () => {lowerLimitTextRef.current.setNativeProps({
              text: `-${playedSeconds}`
            })},
          })();
        }

        if (upperLimitTextRef.current) {
          playedSeconds = secondsToTimestamp(currentProgress.seekableDuration - currentProgress.currentTime);
          
          Platform.select({
            web: () => {upperLimitTextRef.current.value = `${playedSeconds}`},
            default: () => {upperLimitTextRef.current.setNativeProps({
              text: `${playedSeconds}`
            })},
          })();
        }       
      }
      // console.log("Delta for playback percentage: ", delta);
      if (ignoreBigDelta.current) {
        if (deltaTime < 3) {
          ignoreBigDelta.current = false;
          handleUpdate()         
        }
      } else {
        handleUpdate();
      }
    } else {
      logger.info("Trying to set invalid time value", currentProgress);
    }
  }, [currentPlayedPercentage]);

  useEffect(() => {
    if (eventName) {
      animProvider.addListener(eventName, eventCallback);
      return () => {
        animProvider.removeListener(eventName, eventCallback);
      };
    }
  }, [eventCallback, eventName]);

  // y = 1 turns off the panner's offset from the animRegistry event 
  // and lets y control the offset
  const offsetChoice = useRef(new Animated.Value(0)).current;
  const pannerOffset = useRef(new Animated.Value(0)).current;
  const panner = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: (evt, gestureState) => {
        return true
      },
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        triggerEvent('onPanStart');
        const width = boxDimension.x._value;
        pannerOffset.setOffset(width * currentPlayedPercentage._value);
        offsetChoice.setValue(1);
        return true;
      },
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
        return true
      },
      onMoveShouldSetPanResponder: (evt) => {
        return true
      },
      onPanResponderMove: Animated.event([null, {dx: pannerOffset}]),
      onPanResponderRelease: () => {
        offsetChoice.setValue(0);
        pannerOffset.flattenOffset();
        const  finalOffset = pannerOffset._value;

        const width = boxDimension.x._value;
                let percentage = finalOffset / width;
        if (percentage < 0) {
          percentage = 0;
        } else if (percentage > 1) {
          percentage = 1;
        }
        logger.info("final playback percentage: ", percentage);
        currentPlayedPercentage.setValue(percentage);
        const percentageUpdateAction = modelUpdateAction([
          {
            selector: [pageKey, 'plugins', id, 'seekPercentage'],
            newValue: percentage
          }
        ]);
        ignoreBigDelta.current = true;
        shouldTriggerPanEnd.current = true;
        dispatch(percentageUpdateAction);
      }
    })
  ).current;

  const seekPercentage = model.get('seekPercentage');
  useEffect(() => {
    let percentage = parseFloat(seekPercentage);
    if (!isNaN(percentage) && shouldTriggerPanEnd.current) {
      shouldTriggerPanEnd.current = false;
      setTimeout(() => {
        triggerEvent('onPanEnd');
      }, 100);
    }
  }, [seekPercentage, shouldTriggerPanEnd])
  
  const textOffset = Platform.select({
    web: 25,
    android: 25,
    ios: 40
  })


  return (
    <Animated.View
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        opacity: onMountfadeIn,
        ...noSelect
      }}
      onLayout={setDimensions}
      {...panner.panHandlers}
    >
      <AnimatedText 
        ref={lowerLimitTextRef}
        style={{
          fontSize: 10,
          position: 'absolute',
          left: 0,
          top: textOffset,
          color: activeBarColor,
          transform: [
            { translateY: Animated.subtract(Animated.multiply(0.5, boxDimension.y), 35) }
          ],
          ...noSelect
        }}
      >
      </AnimatedText>
      <AnimatedText
        ref={upperLimitTextRef}
        style={{
          fontSize: 10,
          position: 'absolute',
          right: 0,
          top: textOffset,
          color: activeBarColor,
          textAlign: 'right',
          transform: [
            { translateY: Animated.subtract(Animated.multiply(0.5, boxDimension.y), 35) }
          ],
          ...noSelect
        }}
      >
      </AnimatedText>
      <Animated.View
        key="barcontainer"
        style={{
          position: 'absolute',
          height: barHeight,
          width: '100%',
          backgroundColor: inactiveBarColor,
          transform: [{ 
            translateY: Animated.subtract(
              Animated.multiply(0.5, boxDimension.y),
              5
            )
          }],
          ...noSelect
        }}
      />
      <Animated.View
        key="filledbar"
        style={{
          position: 'absolute',
          height: barHeight,
          width: '100%',
          backgroundColor: activeBarColor,
          transform: [
            { 
              translateX: Animated.multiply(
                Animated.subtract(currentPlayedPercentage, 1.0), 
                boxDimension.x
              ) 
            },
            { 
              translateY: Animated.subtract(
                Animated.multiply(0.5, boxDimension.y),
                5
              )
            }
          ],
          ...noSelect
        }}
      />
      <Animated.View
        key="dragger"
        style={{
          position: 'absolute',
          width: 40,
          height: 40,
          transform: [
            { 
              translateX: Animated.add(
                Animated.multiply(offsetChoice, pannerOffset),
                Animated.multiply(
                  Animated.subtract(1, offsetChoice),
                  Animated.subtract(
                    Animated.multiply(currentPlayedPercentage, boxDimension.x),
                    12
                  )
                )
              )
            },
            { 
              translateY: Animated.subtract(
                Animated.multiply(0.5, boxDimension.y),
                13
              )
            }
          ],
          ...noSelect
        }}
      >
        <MaterialCommunityIcons name={sliderIcon} size={20} color={activeBarColor} />
      </Animated.View>
    </Animated.View>
  );
});

const pluginListing: PluginListingSettings = {
  labelPrefix: 'VideoSeeker',
  type: 'widget',
  name: 'VideoSeeker',
  description: 'seek in a videoPlayerV2 plugin',
  layout: {
    flex: 1,
  },
  defaultHeight: 'auto',
  defaultWidth: 'auto',
  section: 'Display',
  icon: 'slider-range'
}

export default connectWidget('VideoSeeker', VideoSeeker, widgetConfig, null, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(VideoSeekerStyleConfig),
  pluginListing,
  docs,
  themeProfileSel: ['tile', 'text'],
  animations: {
    references: {
      playbackProgress: {
        name: 'playbackProgress'
      }
    }
  }
});
