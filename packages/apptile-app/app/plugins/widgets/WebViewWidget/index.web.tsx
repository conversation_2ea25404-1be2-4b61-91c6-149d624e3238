import React from 'react';
import {View} from 'react-native';
import identity from 'lodash/identity';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {useIsEditable} from 'apptile-core';

type WebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
};
const WebViewWidgetConfig: WebViewWidgetConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'webView',
  type: 'widget',
  name: 'Web View',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const WebViewWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const headers = model.get('headers')?.toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
  };
  const isEditable = useIsEditable();

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      <iframe src={value} height={'100%'} onLoad={onLoadEnd} />
      {isEditable && <View style={{backgroundColor: '#0000', height: '100%', width: '100%', position: 'absolute'}} />}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('WebViewWidget', WebViewWidget, WebViewWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
