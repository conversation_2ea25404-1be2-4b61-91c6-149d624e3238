import React, {useEffect, useRef, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {WebView} from 'react-native-webview';

import _ from 'lodash';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';

type WebViewWidgetV2ConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
  incognito: boolean;
  domStorageEnabled: boolean;
  injectedJavaScriptWhileContentIsLoading: string;
};
const WebViewWidgetV2Config: WebViewWidgetV2ConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
  incognito: true,
  domStorageEnabled: false,
  injectedJavaScriptWhileContentIsLoading: '',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'webViewV2',
  type: 'widget',
  name: 'Web View V2',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const WebViewWidgetV2 = React.forwardRef((props, ref) => {
  const r = useRef(null);
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();

  const headers = model.get('headers')?.toString();
  const injectedJavaScript = model.get('injectedJavaScript').toString();
  const injectedJavaScriptWhileContentIsLoading = model.get('injectedJavaScriptWhileContentIsLoading')?.toString();

  const incognito = model.get('incognito');
  const domStorageEnabled = model.get('domStorageEnabled');
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  let userAgent = model.get('userAgent');
  if (!userAgent) userAgent = Platform.OS == 'ios' ? 'iOS' : 'Android';
  
  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onError = () => {
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onMessage = event => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.type === 'log') {
      console.log('WebView log:', data.message);
    }
  };

  // convert key-value pair input into an object
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};
  // let source = {uri: value};
  // requestHeaders && (source.headers = requestHeaders);
  const [webViewKey, setKey] = useState(1);
  const [source, setSource] = useState({uri: value, headers: requestHeaders});
  useEffect(() => {
    let newSource = {
      uri: value,
      headers: requestHeaders,
    };
    if (!_.isEqual(source, newSource)) {
      setSource(newSource);
      setKey(webViewKey + 1);
    }
  }, [requestHeaders, source, value, webViewKey]);

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      {source.uri && (
        <WebView
          // androidLayerType="software"
          incognito={incognito}
          key={webViewKey}
          userAgent={userAgent}
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          source={source}
          onError={onError}
          onMessage={onMessage}
          domStorageEnabled={domStorageEnabled}
          {...(injectedJavaScriptWhileContentIsLoading
            ? {injectedJavaScript: injectedJavaScriptWhileContentIsLoading}
            : {})}
          {...(injectedJavaScript ? {injectedJavaScriptBeforeContentLoaded: injectedJavaScript} : {})}
          javaScriptEnabled={true}
        />
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'incognito',
      props: {
        label: 'Incognito Mode\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'checkbox',
      name: 'domStorageEnabled',
      props: {
        label: 'DomStorage Enabled\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScriptWhileContentIsLoading',
      props: {
        label: 'Injected JS Loading',
        placeholder: '',
      },
    },
    {
      type: 'radioGroup',
      name: 'userAgent',
      props: {
        label: 'User Agent',
        disableBinding: false,
        options: [
          {text: 'iOS', value: 'iOS'},
          {text: 'Android', value: 'Android'},
          {text: 'macOS', value: 'macOS'}
        ]
      }
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  loader: {
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
});

export default connectWidget('WebViewWidgetV2', WebViewWidgetV2, WebViewWidgetV2Config, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});