import React from 'react';
import {View} from 'react-native';

import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {useIsEditable} from 'apptile-core';

type WebViewWidgetV2ConfigType = {
  value: string;
  loading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
  incognito: boolean;
  domStorageEnabled: boolean;
  injectedJavaScriptWhileContentIsLoading: string;
};
const WebViewWidgetV2Config: WebViewWidgetV2ConfigType = {
  value: '',
  loading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
  incognito: true,
  domStorageEnabled: false,
  injectedJavaScriptWhileContentIsLoading: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'webViewV2',
  type: 'widget',
  name: 'Web View V2',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

const WebViewWidgetV2 = React.forwardRef((props, ref) => {
  const {model, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  const headers = model.get('headers')?.toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
  };
  const isEditable = useIsEditable();

  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      <iframe src={value} height={'100%'} onLoad={onLoadEnd} />
      {isEditable && <View style={{backgroundColor: '#0000', height: '100%', width: '100%', position: 'absolute'}} />}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'incognito',
      props: {
        label: 'Incognito Mode\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'checkbox',
      name: 'domStorageEnabled',
      props: {
        label: 'DomStorage Enabled\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScriptWhileContentIsLoading',
      props: {
        label: 'Injected JS Loading',
        placeholder: '',
      },
    },
    {
      type: 'radioGroup',
      name: 'userAgent',
      props: {
        label: 'User Agent',
        disableBinding: false,
        options: [
          {text: 'iOS', value: 'iOS'},
          {text: 'Android', value: 'Android'},
          {text: 'macOS', value: 'macOS'}
        ]
      }
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('WebViewWidgetV2', WebViewWidgetV2, WebViewWidgetV2Config, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});