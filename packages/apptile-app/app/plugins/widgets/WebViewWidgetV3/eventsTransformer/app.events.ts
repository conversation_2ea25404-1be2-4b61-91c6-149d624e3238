import _ from 'lodash';
import {eventMapping, EventHandlers} from './events';
import {ApptileAnalytics} from '@/root/app/common/ApptileAnalytics';

const appEvents: EventHandlers = {
  [eventMapping.toast]: (data, triggerEvent, sendMessage) => {
    triggerEvent('toast', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Toast triggered successfully',
      data: data.eventData,
    });
  },
  [eventMapping.triggerHaptic]: (data, triggerEvent, sendMessage) => {
    triggerEvent('triggerHaptic', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Haptic triggered successfully',
      data: data.eventData,
    });
  },
  [eventMapping.trackAnalytics]: (data, triggerEvent, sendMessage) => {
    ApptileAnalytics.sendEvent(data.eventData.type, data.eventData.event, data.eventData.properties);
    triggerEvent('trackAnalytics', data.eventData);

    sendMessage(data.eventKey, 'success', {
      message: 'Analytics tracked successfully',
      data: data.eventData,
    });
  },
  [eventMapping.shareText]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Text shared successfully',
      data: data.eventData,
    });
    triggerEvent('shareText', data.eventData);
  },
  [eventMapping.getDeviceAndApptileInfo]: (data, triggerEvent, sendMessage, model) => {
    if (model?.get('apptileDetails')) {
      sendMessage(data.eventKey, 'success', {
        message: 'Device and Apptile info fetched successfully',
        data: _.omit(model?.get('apptileDetails'), '_raw'),
      });
    } else {
      sendMessage(data.eventKey, 'error', {
        message: 'Error fetching device and Apptile info',
      });
    }
    triggerEvent('getDeviceAndApptileInfo', {});
  },
  [eventMapping.getApptileThemeDetails]: (data, triggerEvent, sendMessage, model, themeEvaluator) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Apptile theme details fetched successfully',
      data: {
        primaryColor: themeEvaluator('colors.primary'),
        onPrimaryColor: themeEvaluator('colors.onPrimary'),
        secondaryColor: themeEvaluator('colors.secondary'),
        onSecondaryColor: themeEvaluator('colors.onSecondary'),
        backgroundColor: themeEvaluator('colors.background'),
        onBackgroundColor: themeEvaluator('colors.onBackground'),
        typography: themeEvaluator('typography'),
      },
    });
    triggerEvent('getApptileThemeDetails', {});
  },
};

export default appEvents;
