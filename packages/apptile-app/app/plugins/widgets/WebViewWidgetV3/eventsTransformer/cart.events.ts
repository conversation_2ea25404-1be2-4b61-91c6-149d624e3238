import _ from 'lodash';
import {eventMapping, EventHandlers} from './events';

const cartEvents: EventHandlers = {
  [eventMapping.addToCart]: (data, triggerEvent, sendMessage) => {
    triggerEvent('addToCart', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Add to Cart successfully',
      data: data.eventData,
    });
  },
  [eventMapping.decreaseItemQuantityFromCart]: (data, triggerEvent, sendMessage) => {
    triggerEvent('decreaseItemQuantityFromCart', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Item quantity decreased successfully',
      data: data.eventData,
    });
  },
  [eventMapping.updateNote]: (data, triggerEvent, sendMessage) => {
    if (typeof data.eventData?.note === 'string' && data.eventData.note.trim() !== '') {
      sendMessage(data.eventKey, 'success', {
        message: 'Cart Note updated successfully',
        data: data.eventData,
      });
      triggerEvent('updateNote', data.eventData);
    } else {
      sendMessage(data.eventKey, 'success', {
        message: 'Cart Note Invalid, please send a valid cart note.',
        data: data.eventData,
      });
    }
  },
  [eventMapping.updateAttributes]: (data, triggerEvent, sendMessage) => {
    // Simulate updating attributes
    sendMessage(data.eventKey, 'success', {
      message: 'Attributes updated successfully',
      data: data.eventData,
    });
    triggerEvent('updateAttributes', data.eventData);
  },
  [eventMapping.removeFromCart]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Removed from cart successfully',
      data: data.eventData,
    });
    triggerEvent('removeFromCart', data.eventData);
  },
  [eventMapping.applyDiscount]: (data, triggerEvent, sendMessage) => {
    // Simulate applying discount
    sendMessage(data.eventKey, 'success', {
      message: 'Processing Discount',
      data: data.eventData,
    });
    triggerEvent('applyDiscount', data.eventData);
  },
  [eventMapping.removeAllDiscounts]: (data, triggerEvent, sendMessage) => {
    // Simulate removing all discounts
    sendMessage(data.eventKey, 'success', {
      message: 'All discounts removed successfully',
    });
    triggerEvent('removeAllDiscounts', {});
  },
  [eventMapping.getCartDetails]: (data, triggerEvent, sendMessage, model) => {
    const cart = model?.get('cart');
    if (cart) {
      if (cart?.id) {
        sendMessage(data.eventKey, 'success', {
          message: 'Cart fetched successfully',
          data: _.omit(cart, '_raw'),
        });
        triggerEvent('getCartDetails', {});
      } else {
        sendMessage(data.eventKey, 'error', {
          message: 'Cart is not initalised.',
        });
      }
    } else {
      sendMessage(data.eventKey, 'error', {
        message: 'Some error occurred while fetching cart',
      });
    }
  },
};

export default cartEvents;
