import _ from 'lodash';
import {eventMapping, EventHandlers} from './events';

const customerEvents: EventHandlers = {
  [eventMapping.getCurrentCustomer]: (data, triggerEvent, sendMessage, model) => {
    if (model?.get('user')) {
      if (model?.get('user')?.loggedInUser) {
        sendMessage(data.eventKey, 'success', {
          message: 'Current customer fetched successfully',
          data: _.omit(model?.get('user')?.loggedInUser, '_raw'),
        });
      } else {
        sendMessage(data.eventKey, 'error', {
          message: 'Customer is not logged in',
        });
      }
    } else {
      sendMessage(data.eventKey, 'error', {
        message: 'Some error occurred while fetching customer',
      });
    }
    triggerEvent('getCurrentCustomer', {});
  },
  [eventMapping.getSession]: (data, triggerEvent, sendMessage, model) => {
    if (model?.get('user')) {
      if (model?.get('user')?.accessToken) {
        sendMessage(data.eventKey, 'success', {
          message: 'Session details fetched successfully',
          data: _.omit(model?.get('user')?.accessToken, '_raw'),
        });
      } else {
        sendMessage(data.eventKey, 'error', {
          message: 'Customer is not logged in',
        });
      }
    } else {
      sendMessage(data.eventKey, 'error', {
        message: 'Some error occurred while fetching session',
      });
    }
    triggerEvent('getSession', {});
  },
  [eventMapping.setSession]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Set Session successfully',
      data: data.eventData,
    });
    triggerEvent('setSession', data.eventData);
  },
  [eventMapping.logout]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Logout successfully',
    });
    triggerEvent('logout', {});
  },
};

export default customerEvents;
