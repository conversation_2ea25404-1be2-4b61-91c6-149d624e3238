import {eventMapping, EventHandlers} from './events';
import {LocalStorage} from 'apptile-core';

const dataEvents: EventHandlers = {
  [eventMapping.getLocalStorageItem]: async (data, triggerEvent, sendMessage) => {
    const itemKey = data.eventData?.key;
    const itemValue = await LocalStorage.getValue(itemKey);
    sendMessage(data.eventKey, 'success', {
      message: 'Local storage item fetched successfully',
      data: {
        [itemKey]: itemValue,
      },
    });
    triggerEvent('getLocalStorageItem', data.eventData);
  },
  [eventMapping.setLocalStorageItem]: async (data, triggerEvent, sendMessage) => {
    const itemKey = data.eventData?.key;
    const itemValue = data.eventData?.value;
    if (itemValue) await LocalStorage.setValue(itemKey, itemValue);
    else await LocalStorage.removeItem(itemKey);
    sendMessage(data.eventKey, 'success', {
      message: itemValue ? 'Local storage item updated successfully' : 'Local storage item removed successfully',
      data: {},
    });
    triggerEvent('setLocalStorageItem', data.eventData);
  },
  [eventMapping.runQuery]: async (
    data,
    triggerEvent,
    sendMessage,
    _model,
    _themeEvaluator,
    _modelUpdate,
    runDatasourceQuery,
  ) => {
    const queryName = data.eventData?.queryName;
    const queryParams = data.eventData?.params;
    try {
      runDatasourceQuery(queryName, queryParams, ({data: queryResponse, errors, hasError}) => {
        triggerEvent('runQuery', {...data.eventData, queryResponse, errors, hasError});
        sendMessage(data.eventKey, 'success', queryResponse);
      });
    } catch (error) {
      sendMessage(data.eventKey, 'error', {
        message: 'Query Run Failed',
        error,
      });
    }
  },
};

export default dataEvents;
