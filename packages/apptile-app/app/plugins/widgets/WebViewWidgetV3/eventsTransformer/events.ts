export enum eventMapping {
  initialize = 'initialize',
  addToCart = 'ADD_TO_CART',
  decreaseItemQuantityFromCart = 'DECREASE_ITEM_QUANTITY_FROM_CART',
  updateNote = 'UPDATE_NOTE',
  updateAttributes = 'UPDATE_ATTRIBUTES',
  removeFromCart = 'REMOVE_FROM_CART',
  applyDiscount = 'APPLY_DISCOUNT',
  removeAllDiscounts = 'REMOVE_ALL_DISCOUNTS',
  getCartDetails = 'GET_CART_DETAILS',
  openProduct = 'OPEN_PRODUCT',
  openCollection = 'OPEN_COLLECTION',
  openScreen = 'OPEN_SCREEN',
  getCurrentCustomer = 'GET_CURRENT_CUSTOMER',
  getSession = 'GET_SESSION',
  setSession = 'SET_SESSION',
  logout = 'LOGOUT',
  toast = 'TOAST',
  triggerHaptic = 'TRIGGER_HAPTIC',
  trackAnalytics = 'TRACK_ANALYTICS',
  shareText = 'SHARE_TEXT',
  getDeviceAndApptileInfo = 'GET_DEVICE_AND_APPTILE_INFO',
  getApptileThemeDetails = 'GET_APPTILE_THEME_DETAILS',
  addToWishlist = 'ADD_TO_WISHLIST',
  removeFromWishlist = 'REMOVE_FROM_WISHLIST',
  getLocalStorageItem = 'GET_LOCAL_STORAGE_ITEM',
  setLocalStorageItem = 'SET_LOCAL_STORAGE_ITEM',
  runQuery = 'RUN_QUERY',
}

export type EventStatus = 'success' | 'error';

export enum APPTILE_EVENTS_KEY {
  SEND_KEY = 'APPTILE_EVENTS',
  RECEIVE_KEY = 'APPTILE_EVENTS_RECEIVE',
  UPDATE_KEY = 'APPTILE_EVENTS_UPDATE',
}

export interface EventHandlers {
  [key: string]: (
    data: any,
    triggerEvent: (eventKey: string, data: any) => void,
    sendMessage: (eventKey: string, status: EventStatus, data: any) => void,
    model: any,
    themeEvaluator: any,
    modelUpdate: any,
    runDatasourceQuery: (
      queryId: string,
      params: any,
      callback: (props: {
        data: any;
        rawData: any;
        errors: any;
        hasError: any;
        hasNextPage: any;
        paginationMeta: any;
      }) => void,
    ) => void,
  ) => void;
}
