import {eventMapping, EventStatus, EventHandlers} from './events';
import cartEvents from './cart.events';
import navigationEvents from './navigation.events';
import customerEvents from './customer.events';
import appEvents from './app.events';
import {v4 as uuidv4} from 'uuid';
import {RefObject} from 'react';
import wishlistEvents from './wishlist.events';
import dataEvents from './data.events';

const eventHandlers: EventHandlers = {
  [eventMapping.initialize]: (data, triggerEvent, sendMessage, model, themeEvaluator, modelUpdate) => {
    modelUpdate([{selector: ['apptileTunnelLoading'], newValue: false}]);
    const newWebKey = uuidv4();
    data.webKey.current = [...data?.webKey?.current, newWebKey];
    sendMessage(data.eventKey, 'success', {
      secretKey: newWebKey,
      event: eventMapping.initialize,
      eventData: {
        message: 'Initialized successfully',
      },
    });
  },
  ...cartEvents,
  ...wishlistEvents,
  ...navigationEvents,
  ...customerEvents,
  ...appEvents,
  ...dataEvents,
};

export const handleEvent = (
  eventKey: string,
  messageData: any,
  sendMessage: (eventKey: string, status: EventStatus, data: any) => void,
  triggerEvent: (eventName: string, eventData: any) => void,
  webKey: RefObject<string[]>,
  model: any,
  themeEvaluator: any,
  modelUpdate: any,
  runDatasourceQuery: (
    queryId: string,
    params: any,
    callback: (props: {
      data: any;
      rawData: any;
      errors: any;
      hasError: any;
      hasNextPage: any;
      paginationMeta: any;
    }) => void,
  ) => void,
) => {
  const eventHandler = eventHandlers[messageData.event];
  if (messageData.event === eventMapping.initialize) {
    return eventHandler(
      {...messageData, eventKey, webKey},
      triggerEvent,
      sendMessage,
      model,
      themeEvaluator,
      modelUpdate,
      runDatasourceQuery,
    );
  }
  if (eventHandler && webKey?.current && webKey?.current?.includes(messageData.secretKey)) {
    eventHandler(
      {...messageData, eventKey, webKey: webKey?.current},
      triggerEvent,
      sendMessage,
      model,
      themeEvaluator,
      modelUpdate,
      runDatasourceQuery,
    );
  } else if (!eventHandler) {
    console.log(`No handler found for event: ${messageData.event}`);
    sendMessage(eventKey, 'error', {
      message: 'Invalid handler sent to AppTile',
    });
  } else if (!webKey?.current?.includes(messageData.secretKey)) {
    if (messageData.event != eventMapping.runQuery) {
      console.log(
        `Invalid secret key, reinitialize required: ${messageData.event}`,
        webKey?.current,
        messageData.secretKey,
      );
      sendMessage(eventKey, 'error', {
        message: 'Invalid secret key, please reinitialize',
      });
    } else {
      eventHandler(
        {...messageData, eventKey, webKey: webKey?.current},
        triggerEvent,
        sendMessage,
        model,
        themeEvaluator,
        modelUpdate,
        runDatasourceQuery,
      );
    }
  }
};
