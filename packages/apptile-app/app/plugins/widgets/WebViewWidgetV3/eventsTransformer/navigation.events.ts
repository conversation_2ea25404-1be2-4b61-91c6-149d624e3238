import {eventMapping, EventHandlers} from './events';

const navigationEvents: EventHandlers = {
  [eventMapping.openProduct]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Navgiated to product successfully',
      data: data.eventData,
    });
    triggerEvent('openProduct', data.eventData);
  },
  [eventMapping.openCollection]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Navgiated to collection successfully',
      data: data.eventData,
    });
    triggerEvent('openCollection', data.eventData);
  },
  [eventMapping.openScreen]: (data, triggerEvent, sendMessage) => {
    sendMessage(data.eventKey, 'success', {
      message: 'Navgiated to screen successfully.',
      data: data.eventData,
    });
    triggerEvent('openScreen', data.eventData);
  },
};

export default navigationEvents;
