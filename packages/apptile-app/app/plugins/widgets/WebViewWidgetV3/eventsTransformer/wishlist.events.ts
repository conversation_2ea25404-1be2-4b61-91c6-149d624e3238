import {eventMapping, EventHandlers} from './events';

const wishlistEvents: EventHandlers = {
  [eventMapping.addToWishlist]: (data, triggerEvent, sendMessage) => {
    triggerEvent('addToWishlist', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Item added to wishlist successfully',
      data: data.eventData,
    });
  },
  [eventMapping.removeFromWishlist]: (data, triggerEvent, sendMessage) => {
    triggerEvent('removeFromWishlist', data.eventData);
    sendMessage(data.eventKey, 'success', {
      message: 'Item removed from wishlist successfully',
      data: data.eventData,
    });
  },
};

export default wishlistEvents;
