import React, {useRef, useEffect, useState} from 'react';
import {View} from 'react-native';
import {WebView} from 'react-native-web-webview';

import {
  datasourceTypeModelSel,
  getPlatformStyles,
  GetRegisteredPlugin,
  selectPluginConfig,
  TriggerActionIdentifier,
  useTheme,
} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings, EventTriggerIdentifier} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {useIsEditable} from 'apptile-core';
import {eventMapping} from './eventsTransformer/events';
import {handleEvent} from './eventsTransformer';
import {APPTILE_EVENTS_KEY} from './eventsTransformer/events';
import snippets from './snippets.json';
import {useSelector} from 'react-redux';
import _ from 'lodash';
import {runSaga} from 'redux-saga';
import axios from 'axios';

let base64ApptileWebTunnelSDK = '';
const webTunnelUrl = 'https://unpkg.com/@apptile/apptile-web-tunnel@latest/dist/index.min.js';
axios.get(webTunnelUrl).then(response => {
  if (response.data) base64ApptileWebTunnelSDK = 'data:application/javascript;base64,' + window.btoa(response.data);
});

type WebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  apptileTunnelLoading: boolean;
  onLoadEnd: string;
  navigationChanged: string;
  headers: string;
  injectedJavaScript: string;
  injectedCss: string;
  injectJavaScriptBeforeContentLoad: boolean;
  useHtml: boolean;
  html: string;
  cart: any;
  user: any;
  apptileDetails: any;
  reloadWebView: any;
  errorReport: string;
  snippetMode: string;
};

const WebViewWidgetConfig: WebViewWidgetConfigType = {
  value: '',
  loading: true,
  apptileTunnelLoading: true,
  onLoadEnd: '',
  navigationChanged: '',
  headers: '',
  injectedJavaScript: '',
  injectedCss: '',
  injectJavaScriptBeforeContentLoad: true,
  useHtml: false,
  html: '',
  cart: '',
  user: '',
  apptileDetails: '',
  reloadWebView: 'action',
  errorReport: '',
  snippetMode: 'none',
  ...Object.fromEntries(Object.keys(eventMapping).map((e: string) => [e, ''])),
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'webTunnelView',
  type: 'widget',
  name: 'Web Tunnel View',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};

async function runGenerator(generatorFn, ...args) {
  let result;
  await runSaga({}, function* () {
    result = yield* generatorFn(...args);
  }).toPromise();
  return result;
}

const WebViewWidgetV3 = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  let html = model.get('html')?.toString();
  const useHtml = model.get('useHtml');
  const useAppTunnel = model.get('useAppTunnel');
  const navigationalRegex = model.get('navigationalRegex');
  const defaultNavigationScreenName = model.get('defaultNavigationScreenName');
  const hideSelectors = model.get('hideSelectors')?.toString().trim();
  const reloadKey = model.get('reloadKey');

  let injectedJavaScript = model.get('injectedJavaScript')?.toString() ?? '';
  let injectedCss = model.get('injectedCss')?.toString() ?? '';

  const snippetMode = model.get('snippetMode');
  let componentProps = {};
  if (snippetMode) {
    componentProps = Object.fromEntries(
      Object.keys(model?.toJSON())
        .filter(e => e.startsWith('componentProps-'))
        .map(e => [e.slice(15), model.get(e)]),
    );
  }
  // Prepare injected JavaScript for hiding selectors
  if (hideSelectors) {
    injectedJavaScript = `
    const addThisSet2 = setInterval(()=>{
      if(document.getElementById('hideStyle1211-apptile')) clearInterval(addThisSet2);
      else{
        var hideStyle1211 = document.createElement('style');
        hideStyle1211.setAttribute('id', 'hideStyle1211-apptile');
        hideStyle1211.innerHTML = '${hideSelectors} { display: none !important;}';
        document.head.appendChild(hideStyle1211);
      }
    }, 10);
    ${injectedJavaScript}`;
  }

  // Prepare app tunnel script
  if (useAppTunnel) {
    injectedJavaScript = `
    const addThisSet3 = setInterval(()=>{
        if(document.getElementById('sc1211-apptile')) clearInterval(addThisSet3);
        else{
          if(!window.Apptile) {
            var sc1211 = document.createElement("script");
            sc1211.setAttribute("src", ${webTunnelUrl});
            sc1211.setAttribute('id', 'sc1211-apptile');
            sc1211.setAttribute("type", "text/javascript");
            sc1211.setAttribute("fetchprority", "high");
            document.head.appendChild(sc1211);
          }
        }
      }, 100);
    try{
      ${injectedJavaScript}
    } catch(error) {
      window.Apptile && window.Apptile.sendRawMessageAsync({type: 'POST', event: 'errorReport', eventData: error});
      console.error(error);
    }`;
  }

  const headers = model.get('headers')?.toString();
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const isEditable = useIsEditable();
  const webViewRef = useRef(null);
  const [webViewKey, setKey] = useState(Math.floor(Math.random() * 10000000));

  // Convert key-value pair input into an object for headers
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};

  // Prepare HTML content for snippet mode
  if (snippetMode && snippets[snippetMode] && useHtml) {
    html =
      `<script>
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
};
console.log = (...args) => {
  originalConsole.log(...args);
  window.parent.postMessage({ type: 'console', webViewKey: '${webViewKey}' , method: 'log', args }, '*');
};
console.warn = (...args) => {
  originalConsole.warn(...args);
  window.parent.postMessage({ type: 'console', webViewKey: '${webViewKey}' , method: 'warn', args }, '*');
};
console.error = (...args) => {
  originalConsole.error(...args);
  window.parent.postMessage({ type: 'console', webViewKey: '${webViewKey}' , method: 'error', args }, '*');
};
;window.componentProps=${JSON.stringify(componentProps)}</script>` +
      snippets[snippetMode].prefix +
      html +
      snippets[snippetMode].suffix;
  }

  // Update webview key when reloadKey changes
  useEffect(() => {
    if (reloadKey && webViewKey !== reloadKey) {
      setKey(reloadKey);
    }
    modelUpdate([{selector: ['webViewKey'], newValue: webViewKey}]);
  }, [reloadKey, webViewKey]);

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate && modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onError = () => {
    modelUpdate && modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const handleNavigationStateChange = navState => {
    const {url} = navState;
    let found = false;
    try {
      // Routing logic based on specific path patterns
      if (value === url) {
        return true;
      } else {
        if (url) {
          const urlParts = url?.split('?');
          const urlParams =
            urlParts.length > 1 ? Object.fromEntries(urlParts[1]?.split('&').map(e => e.split('='))) : {};
          const urlNodes = urlParts[0].split('/');
          for (let i in navigationalRegex) {
            const regex = new RegExp(
              navigationalRegex[i]?.expressionRaw?.regex,
              navigationalRegex[i]?.expressionRaw?.flags,
            );
            if (regex?.test(url)) {
              triggerEvent('navigationChanged', {
                screenName: navigationalRegex[i].screenName,
                urlNodes,
                urlParams,
                ...navState,
              });
              found = true;
            }
          }
          if (!found && defaultNavigationScreenName) {
            triggerEvent('navigationChanged', {
              screenName: defaultNavigationScreenName,
              urlNodes,
              urlParams,
              ...navState,
            });
          }
        }
        return true;
      }
    } catch (error) {
      console.warn('Invalid URL format:', error);
      return true;
    }
  };
  const {themeEvaluator} = useTheme();
  const webKey = useRef<string[]>([]);

  const datasource = model.get('queryDataSource');
  const dsConfig = useSelector(state => selectPluginConfig(state, null, datasource));
  const dsType = dsConfig?.subtype;
  const dsModelValues = useSelector(state => datasourceTypeModelSel(state, dsType));

  const runDatasourceQuery = async function (
    queryName: string,
    userInputVariables: any = {},
    callback: (props: {
      data: any;
      rawData: any;
      errors: any;
      hasError: any;
      hasNextPage: any;
      paginationMeta: any;
    }) => void,
  ) {
    if (!datasource) return {data: null, errors: ['Integration Not Active In App'], hasError: true};
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = dsModel?.getQueries();
    const querySchema = queries?.[queryName];

    if (!querySchema) return {data: null, errors: ['Invalid Request'], hasError: true};
    const {editableInputParams: inputVariables, transformer, cachePolicy} = querySchema;
    const mergedInputVariables = _.mergeWith({}, userInputVariables, inputVariables, (objValue, srcValue) => {
      if (typeof objValue === 'boolean' || typeof objValue === 'number') {
        return objValue;
      } else if (objValue === undefined) {
        return _.isEmpty(srcValue) ? undefined : srcValue;
      } else {
        return _.isEmpty(objValue) ? srcValue : objValue;
      }
    });
    const response = await runGenerator(
      dsModel.runQuery,
      dsModel,
      dsConfig,
      dsModelValues,
      queryName,
      mergedInputVariables,
      {
        transformers: [transformer],
        getNextPage: false,
        paginationMeta: undefined,
        cachePolicy: null,
      },
    );
    const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = response;
    callback({data, rawData, errors, hasError, hasNextPage, paginationMeta});
  };

  const messageListener = (message: any) => {
    try {
      const rawMessageData = JSON.parse(message?.nativeEvent?.data || '{}');
      console.log('\n\n\n\n\n\nmessage', rawMessageData, '\n\n\n\n\n');
      const messageData = rawMessageData?.message;
      if (messageData && rawMessageData.eventKey) {
        handleEvent(
          rawMessageData.eventKey,
          messageData,
          sendMessageToIframe,
          triggerEvent,
          webKey,
          model,
          themeEvaluator,
          modelUpdate,
          runDatasourceQuery,
        );
      }
    } catch (error) {}
  };

  const sendMessageToIframe = (eventKey, status, data, update = false) => {
    if (webViewRef?.current) {
      webViewRef.current.postMessage(
        `${JSON.stringify({
          eventKey,
          status,
          result: {
            eventSDK: update ? APPTILE_EVENTS_KEY.UPDATE_KEY : APPTILE_EVENTS_KEY.RECEIVE_KEY,
            type: 'POST',
            data,
          },
        })}`,
        '*',
      );
    }
  };

  return (
    <View style={[layoutStyles, modelPlatformStyles]} ref={ref}>
      {useHtml ? (
        <WebView
          key={webViewKey}
          ref={webViewRef}
          source={{html: html.replace(webTunnelUrl, base64ApptileWebTunnelSDK)}}
          style={{flex: 1, width: '100%', height: '100%'}}
          injectedJavaScript={injectedJavaScript}
          injectedJavaScriptBeforeContentLoaded={injectedJavaScript}
          onMessage={messageListener}
          onLoadEnd={onLoadEnd}
          onError={onError}
          onNavigationStateChange={handleNavigationStateChange}
        />
      ) : (
        <WebView
          key={webViewKey}
          ref={webViewRef}
          source={{uri: value, headers: requestHeaders}}
          style={{flex: 1, width: '100%', height: '100%'}}
          injectedJavaScript={injectedJavaScript}
          injectedJavaScriptBeforeContentLoaded={injectedJavaScript}
          onMessage={messageListener}
          onLoadEnd={onLoadEnd}
          onError={onError}
          onNavigationStateChange={handleNavigationStateChange}
        />
      )}
      {isEditable && <View style={{backgroundColor: '#0000', height: '100%', width: '100%', position: 'absolute'}} />}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'queryDataSource',
      props: {
        label: 'Query Data Source',
        placeholder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'cart',
      props: {
        label: 'Cart',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'user',
      props: {
        label: 'User',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apptileDetails',
      props: {
        label: 'ApptileDetails',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedCss',
      props: {
        label: 'Injected CSS',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'injectJavaScriptBeforeContentLoad',
      props: {
        label: 'Inject JavaScript Before Content Load',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScriptWhileContentIsLoading',
      props: {
        label: 'Injected JS While Content Is Loading',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'hideSelectors',
      props: {
        label: 'Selectors which you wanna hide in website',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'useHtml',
      props: {
        label: 'USE HTML',
        placeholder: '',
      },
    },
    {
      type: 'radioGroup',
      name: 'snippetMode',
      props: {
        label: 'Snippet Mode',
        placeholder: '',
        options: [
          {text: 'None', value: 'none'},
          ...Object.keys(snippets).map(e => ({
            text: e,
            value: e,
          })),
        ],
      },
    },
    {
      type: 'checkbox',
      name: 'useAppTunnel',
      props: {
        label: 'USE App Tunnel',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'html',
      props: {
        label: 'HTML',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'defaultNavigationScreenName',
      props: {
        label: 'Default Navigation Screen Name',
        placeholder: '',
      },
    },
    {
      type: 'regexInput',
      name: 'navigationalRegex',
      props: {
        label: 'Navigation Based Regex \n (⚠️ DEV ONLY)',
        placeholder: '',
        regexLabel: 'screenName',
        showRecommendation: false,
      },
    },
    {
      type: 'checkbox',
      name: 'incognito',
      props: {
        label: 'Incognito Mode\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'checkbox',
      name: 'domStorageEnabled',
      props: {
        label: 'DomStorage Enabled\n(⚠️ DEV ONLY)',
      },
    },
    {
      type: 'codeInput',
      name: 'userAgent',
      props: {
        label: 'User Agent\n(⚠️ DEV ONLY)',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  reloadWebView: {
    type: TriggerActionIdentifier,
    getValue() {
      return () => {};
    },
  },
  ...Object.fromEntries(Object.keys(eventMapping).map((e: string) => [e, {type: EventTriggerIdentifier}])),
  navigationChanged: {
    type: EventTriggerIdentifier,
  },
  errorReport: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
};
const emptyOnupdate = null;

export default connectWidget('WebViewWidgetV3', WebViewWidgetV3, WebViewWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
