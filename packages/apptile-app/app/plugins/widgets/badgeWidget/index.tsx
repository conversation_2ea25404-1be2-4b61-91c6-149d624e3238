import React from 'react';
import {View, Text, ImageBackground} from 'react-native';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {getPlatformStyles} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {mergeWithDefaultStyles} from 'apptile-core';
import {WidgetStyleEditorOptions} from '@/root/app/styles/types';

const BadgeWidgetConfig = {
  value: 'Text',
  horizontalAlign: 'center',
  verticalAlign: 'center',
  overflowType: 'hidden',
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'badge',
  type: 'widget',
  name: 'Badge',
  description: 'Display a badge on an image',
  layout: {
    width: 50,
    height: 30,
  },
  section: 'Display',
  icon: 'badge',
};

const BadgeWidget = React.forwardRef((props, ref) => {
  const {model, modelStyles, config} = props;

  const value = model.get('value')?.toString();
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 1});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const {color, margin, padding, typography, ...restModelPlatformStyles} = modelPlatformStyles;

  const hasBackgroundImage = model.get('hasBackgroundImage');
  const textStyles = {
    color,
    margin,
    padding,
  };

  const TextComponent = () => (
    <Text ref={ref} style={[textStyles, typography]}>
      {value ? value : ''}
    </Text>
  );

  return value ? (
    <View style={[restModelPlatformStyles, layoutStyles]}>
      {hasBackgroundImage ? (
        <ImageBackground
          source={{
            uri: `${model.get('backgroundURI')}`,
          }}
          resizeMode="cover"
          // eslint-disable-next-line react-native/no-inline-styles
          style={{
            flex: 1,
            justifyContent: 'center',
          }}>
          {TextComponent()}
        </ImageBackground>
      ) : (
        TextComponent()
      )}
    </View>
  ) : (
    <></>
  );
});

export const badgeWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    defaultValue: 'tile.badge.backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    defaultValue: 'tile.badge.color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    defaultValue: 'tile.badge.typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const widgetEditors: PluginEditorsConfig<any> = {
  basic: defaultEditors.basic,
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: true,
      },
    },
  ],
  advanced: [
    {
      type: 'checkbox',
      name: 'hasBackgroundImage',
      props: {
        label: 'Has Background Image',
      },
    },
    {
      type: 'codeInput',
      name: 'backgroundURI',
      props: {
        label: 'Background image',
        placeholder: 'Background Image URI',
      },
      hidden: config => !config.get('hasBackgroundImage'),
    },
  ],
};

const propertySettings: PluginPropertySettings = {};
const emptyOnupdate = null;

export default connectWidget('BadgeWidget', BadgeWidget, BadgeWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(badgeWidgetStyleConfig),
  pluginListing,
  docs,
});
