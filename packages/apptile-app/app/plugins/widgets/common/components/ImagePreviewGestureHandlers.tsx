import {ImageZoom} from '@/root/app/platformSplitters/ImageZoom';
import React from 'react';
import {Dimensions, ImageBackground, StyleSheet} from 'react-native';

const ImagePreviewGestureHandlers: React.FC<{imgSource: string; layoutSize: number}> = ({imgSource}) => {
  return (
    <ImageZoom
      cropWidth={Dimensions.get('window').width}
      cropHeight={Dimensions.get('window').height}
      imageHeight={400}
      imageWidth={Dimensions.get('window').width}
      useNativeDriver={true}
      panToMove={true}>
      <ImageBackground source={{uri: imgSource}} resizeMode="contain" style={styles.image} />
    </ImageZoom>
  );
};

export default ImagePreviewGestureHandlers;

const styles = StyleSheet.create({
  image: {
    flex: 1,
    justifyContent: 'center',
  },
});
