"{\"data\":{\"title\":\"New App\",\"pages\":{\"data\":{\"Offers\":{\"data\":{\"title\":\"Offers\",\"pageId\":\"Offers\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{\"SearchButton\":{\"data\":{\"id\":\"SearchButton\",\"type\":\"widget\",\"subtype\":\"ButtonWidget\",\"config\":{\"data\":{\"value\":\"Go to Search\",\"submit\":\"\",\"onSubmit\":\"\",\"events\":{\"data\":[{\"data\":{\"label\":\"onSubmit\",\"type\":\"page\",\"method\":\"navigate\",\"pluginId\":\"ShopifyQuery1\",\"isGlobalPlugin\":false,\"screenName\":\"Search\",\"params\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"auto\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ListView1\":{\"data\":{\"id\":\"ListView1\",\"type\":\"widget\",\"subtype\":\"ListViewWidget\",\"config\":{\"data\":{\"instances\":\"{{ShopifyQuery1.data.length}}\",\"data\":\"\",\"horizontal\":true,\"itemWidth\":\"300\",\"itemHeight\":\"\",\"numColumns\":\"2\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"auto\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"LVItemContainer\":{\"data\":{\"id\":\"LVItemContainer\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":true,\"style\":{\"data\":{\"margin\":\"\",\"padding\":\"5\",\"borderRadius\":\"\",\"elevation\":\"\",\"background\":\"rgba(255, 255, 255, 0)\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ListView1\",\"alignContent\":\"center\",\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":\"column\",\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":\"nowrap\",\"height\":\"\",\"justifyContent\":\"center\",\"overflow\":\"hidden\",\"width\":\"\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ShopifyQuery1\":{\"data\":{\"id\":\"ShopifyQuery1\",\"type\":\"query\",\"subtype\":\"QueryPlugin\",\"config\":{\"data\":{\"runOnPageLoadDelay\":\"\",\"onSuccess\":\"\",\"datasource\":\"shopify\",\"runWhenModelUpdates\":true,\"runOnModelUpdate\":false,\"onError\":\"\",\"data\":null,\"inputVariables\":{\"data\":{\"collectionHandle\":\"bestseller\",\"first\":10,\"sortKey\":\"BEST_SELLING\",\"presentmentCurrencies\":[\"INR\"]},\"__serializedType__\":\"ImmutableMap\"},\"metadata\":null,\"errorTransformer\":\"\",\"isFetching\":false,\"rawData\":null,\"executeQuery\":\"\",\"queryName\":\"GetCollection\",\"enableErrorTransformer\":false,\"timestamp\":0,\"enableTransformer\":false,\"runWhenPageLoads\":true,\"transformer\":\"\",\"transformers\":\"TransformCollectionProducts\",\"runOnPageLoad\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"LVCard\":{\"data\":{\"id\":\"LVCard\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":true,\"hasBorder\":\"\",\"style\":{\"data\":{\"background\":\"rgb(255, 255, 255)\",\"borderRadius\":\"10\",\"elevation\":\"2\",\"backgroundColor\":\"primaryBackground\",\"margin\":\"5\",\"shadowColor\":\"shadowColor\"},\"__serializedType__\":\"ImmutableMap\"},\"events\":{\"data\":[{\"data\":{\"label\":\"onPress\",\"type\":\"page\",\"method\":\"navigate\",\"pluginId\":\"ShopifyQuery1\",\"isGlobalPlugin\":false,\"screenName\":\"Product\",\"params\":{\"data\":{\"productId\":\"{{ShopifyQuery1.data[i].id}}\",\"varientId\":\"{{ShopifyQuery1.data[i].varientId}}\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVItemContainer\",\"alignContent\":\"stretch\",\"alignItems\":\"flex-start\",\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":\"column\",\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":\"nowrap\",\"height\":{\"$jsan\":\"u\"},\"justifyContent\":\"center\",\"overflow\":\"visible\",\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"container1\":{\"data\":{\"id\":\"container1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"listview1\",\"alignContent\":\"center\",\"alignItems\":\"center\",\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":\"auto\",\"flexDirection\":\"column\",\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"320\",\"justifyContent\":\"center\",\"overflow\":\"hidden\",\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"LVCardImage\":{\"data\":{\"id\":\"LVCardImage\",\"type\":\"widget\",\"subtype\":\"ImageWidget\",\"config\":{\"data\":{\"value\":\"{{ShopifyQuery1.data[i].image}}\",\"aspectRatio\":1,\"resizeMode\":\"cover\",\"loadingIcon\":\"\",\"height\":\"auto\",\"width\":\"\",\"flex\":1,\"style\":{\"data\":{\"borderRadius\":\"10\",\"elevation\":\"\",\"shadowColor\":\"shadowColor\",\"margin\":\"\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"280\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"280\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextCardTitle\":{\"data\":{\"id\":\"TextCardTitle\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{ShopifyQuery1.data[i].title}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"background\":\"rgba(0, 0, 0, 0)\",\"textColor\":\"rgb(0, 0, 0)\",\"padding\":\"5\",\"fontSize\":\"18\",\"elevation\":\"\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"30\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"30\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"text2\":{\"data\":{\"id\":\"text2\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"₹ {{ShopifyQuery1.data[i].presentmentPriceRanges.edges[0].node.minVariantPrice.amount}} - {{ShopifyQuery1.data[i].presentmentPriceRanges.edges[0].node.maxVariantPrice.amount}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"padding\":\"\",\"margin\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextCardDesc\":{\"data\":{\"id\":\"TextCardDesc\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{_.truncate(ShopifyQuery1.data[i].description,{length:100})}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"padding\":\"10\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"54\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"listview1\":{\"data\":{\"id\":\"listview1\",\"type\":\"widget\",\"subtype\":\"ListViewWidget\",\"config\":{\"data\":{\"instances\":\"4\",\"data\":\"\",\"horizontal\":false,\"itemWidth\":\"\",\"itemHeight\":\"320\",\"numColumns\":1},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"auto\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"1\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"image1\":{\"data\":{\"id\":\"image1\",\"type\":\"widget\",\"subtype\":\"ImageWidget\",\"config\":{\"data\":{\"value\":\"https://wallpaperaccess.com/full/154009.jpg\",\"aspectRatio\":1,\"resizeMode\":\"cover\",\"loadingIcon\":\"\",\"style\":{\"data\":{\"margin\":\"10\",\"padding\":\"\",\"elevation\":\"4\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"auto\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"280\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"text1\":{\"data\":{\"id\":\"text1\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"Cat-D\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"fontSize\":\"18\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":\"auto\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"1\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"myModule\":{\"data\":{\"id\":\"myModule\",\"type\":\"widget\",\"subtype\":\"ModuleInstance\",\"config\":{\"data\":{\"childNamespace\":\"module1\",\"moduleUUID\":\"4dbe72a6-4dbe-43ea-b67f-105740404763\",\"moduleName\":\"FirstModule\",\"productItem\":\"{{ShopifyQuery1.data[i]}}\",\"textValue\":\"{{textValue.value}}\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"module1::Container1\":{\"data\":{\"id\":\"module1::Container1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"namespace\":[\"module1\"],\"pluginId\":\"myModule\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"module1::textPlugin1\":{\"data\":{\"id\":\"module1::textPlugin1\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{productItem.value.handle}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"padding\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"module1::Container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"namespace\":[\"module1\"],\"pluginId\":\"myModule\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"module1::textInput1\":{\"data\":{\"id\":\"module1::textInput1\",\"type\":\"widget\",\"subtype\":\"TextInputWidget\",\"config\":{\"data\":{\"labelAlign\":\"\",\"value\":\"\",\"labelWidth\":\"33\",\"placeholder\":\"Enter value\",\"label\":\"Label\",\"labelWidthUnit\":\"%\",\"onSubmit\":\"\",\"submit\":\"\",\"hasLabel\":false,\"labelPosition\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"module1::Container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"namespace\":[\"module1\"],\"pluginId\":\"myModule\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"module1::productItem\":{\"data\":{\"id\":\"module1::productItem\",\"type\":\"state\",\"subtype\":\"ModuleProperty\",\"config\":{\"data\":{\"value\":\"{{myModule.productItem}}\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"namespace\":[\"module1\"],\"pluginId\":\"myModule\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"module1::textValue\":{\"data\":{\"id\":\"module1::textValue\",\"type\":\"state\",\"subtype\":\"ModuleOutput\",\"config\":{\"data\":{\"value\":\"{{textInput1.value}}\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"LVCard\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"namespace\":[\"module1\"],\"pluginId\":\"myModule\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Settings\":{\"data\":{\"title\":\"Settings\",\"pageId\":\"Settings\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{\"ContainerWidget1\":{\"data\":{\"id\":\"ContainerWidget1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":\"\",\"style\":{\"data\":{\"elevation\":\"4\",\"borderRadius\":\"5\",\"margin\":\"5\",\"padding\":\"15\",\"background\":\"rgb(255, 255, 255)\",\"backgroundColor\":\"primaryBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":\"center\",\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"\",\"flexDirection\":\"column\",\"flexGrow\":\"\",\"flexShrink\":\"\",\"flexWrap\":\"nowrap\",\"height\":\"\",\"justifyContent\":\"flex-start\",\"overflow\":\"visible\",\"width\":\"\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ButtonWidget3\":{\"data\":{\"id\":\"ButtonWidget3\",\"type\":\"widget\",\"subtype\":\"ButtonWidget\",\"config\":{\"data\":{\"value\":\"Search\",\"submit\":\"\",\"onSubmit\":\"\",\"style\":{\"data\":{\"margin\":\"5\"},\"__serializedType__\":\"ImmutableMap\"},\"events\":{\"data\":[{\"data\":{\"label\":\"onSubmit\",\"type\":\"page\",\"method\":\"navigate\",\"pluginId\":\"ShopifyQueryGetCart4\",\"isGlobalPlugin\":true,\"screenName\":\"Search\",\"params\":{\"routeParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"}}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"\",\"flexShrink\":\"\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ButtonWidget2\":{\"data\":{\"id\":\"ButtonWidget2\",\"type\":\"widget\",\"subtype\":\"ButtonWidget\",\"config\":{\"data\":{\"value\":\"Login\",\"submit\":\"\",\"onSubmit\":\"\",\"style\":{\"data\":{\"margin\":\"5\"},\"__serializedType__\":\"ImmutableMap\"},\"events\":{\"data\":[{\"data\":{\"label\":\"onSubmit\",\"type\":\"page\",\"method\":\"navigate\",\"pluginId\":\"ShopifyQueryGetCart4\",\"isGlobalPlugin\":true,\"screenName\":\"Accounts\",\"params\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"\",\"flexShrink\":\"\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Accounts\":{\"data\":{\"title\":\"Accounts\",\"pageId\":\"Accounts\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Home\":{\"data\":{\"title\":\"Home\",\"pageId\":\"Home\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Search\":{\"data\":{\"title\":\"Search\",\"pageId\":\"Search\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{\"TextInputWidget2\":{\"data\":{\"id\":\"TextInputWidget2\",\"type\":\"widget\",\"subtype\":\"TextInputWidget\",\"config\":{\"data\":{\"labelAlign\":\"\",\"value\":\"\",\"style\":{\"data\":{\"margin\":\"\",\"padding\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"labelWidth\":\"33\",\"placeholder\":\"Enter value\",\"label\":\"Label\",\"labelWidthUnit\":\"%\",\"onSubmit\":\"\",\"submit\":\"\",\"events\":{\"data\":[{\"data\":{\"label\":\"onSubmit\",\"type\":\"query\",\"method\":\"executeQuery\",\"pluginId\":\"ShopifyQuery2\",\"isGlobalPlugin\":false,\"screenName\":\"\",\"params\":{\"routeParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"}}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"},\"hasLabel\":false,\"labelPosition\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"\",\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"80\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ShopifyQuery2\":{\"data\":{\"id\":\"ShopifyQuery2\",\"type\":\"query\",\"subtype\":\"QueryPlugin\",\"config\":{\"data\":{\"runOnPageLoadDelay\":\"\",\"onSuccess\":\"\",\"datasource\":\"shopify\",\"runOnModelUpdate\":false,\"onError\":\"\",\"data\":null,\"inputVariables\":{\"data\":{\"query\":\"{{TextInputWidget2.value}}\",\"first\":\"20\",\"sortKey\":\"BEST_SELLING\",\"presentmentCurrencies\":\"INR\"},\"__serializedType__\":\"ImmutableMap\"},\"metadata\":null,\"errorTransformer\":\"\",\"isFetching\":false,\"rawData\":null,\"executeQuery\":\"\",\"queryName\":\"SearchProducts\",\"enableErrorTransformer\":false,\"timestamp\":0,\"enableTransformer\":false,\"runWhenPageLoads\":false,\"transformer\":\"\",\"transformers\":\"TransformSearchProducts\",\"runOnPageLoad\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ShopifyQuery3\":{\"data\":{\"id\":\"ShopifyQuery3\",\"type\":\"query\",\"subtype\":\"QueryPlugin\",\"config\":{\"data\":{\"runOnPageLoadDelay\":\"\",\"onSuccess\":\"\",\"datasource\":\"shopify\",\"runOnModelUpdate\":false,\"onError\":\"\",\"data\":null,\"inputVariables\":{\"data\":{\"cartInput\":{\"lines\":[{\"quantity\":1,\"merchandiseId\":\"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MTk3MzA5NjY3NzYyMg==\"}]}},\"__serializedType__\":\"ImmutableMap\"},\"metadata\":null,\"errorTransformer\":\"\",\"isFetching\":false,\"rawData\":null,\"executeQuery\":\"\",\"queryName\":\"CreateShoppingCart\",\"enableErrorTransformer\":false,\"timestamp\":0,\"enableTransformer\":false,\"transformer\":\"\",\"events\":{\"data\":{\"ev1\":{\"data\":{\"label\":\"onSuccess\",\"type\":\"query\",\"method\":\"trigger\",\"pluginId\":\"ShopifyQueryGetCart4\",\"isGlobalPlugin\":true,\"screenName\":\"\",\"params\":{\"routeParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"}}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}},\"__serializedType__\":\"ImmutableMap\"},\"runOnPageLoad\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ListViewSearch1\":{\"data\":{\"id\":\"ListViewSearch1\",\"type\":\"widget\",\"subtype\":\"ListViewWidget\",\"config\":{\"data\":{\"instances\":\"{{ShopifyQuery2.data.length}}\",\"data\":\"\",\"horizontal\":false,\"itemWidth\":\"\",\"itemHeight\":\"250\",\"numColumns\":\"2\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ContainerSearch1\":{\"data\":{\"id\":\"ContainerSearch1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":true,\"hasBorder\":true,\"style\":{\"data\":{\"background\":\"rgb(255, 255, 255)\",\"shadowColor\":\"rgb(0, 0, 0)\",\"elevation\":\"0\",\"padding\":\"\",\"borderRadius\":\"\",\"margin\":\"4\",\"backgroundColor\":\"primaryBackground\"},\"__serializedType__\":\"ImmutableMap\"},\"events\":{\"data\":[{\"data\":{\"label\":\"onPress\",\"type\":\"page\",\"method\":\"navigate\",\"pluginId\":\"ShopifyQuery2\",\"isGlobalPlugin\":false,\"screenName\":\"Product\",\"params\":{\"data\":{\"productId\":\"{{ShopifyQuery2.data[i].id}}\",\"varientId\":\"{{ShopifyQuery2.data[i].varientId}}\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":6}],\"__serializedType__\":\"ImmutableList\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ListViewSearch1\",\"alignContent\":\"stretch\",\"alignItems\":\"center\",\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"auto\",\"flexDirection\":\"column\",\"flexGrow\":\"1\",\"flexShrink\":\"0\",\"flexWrap\":\"nowrap\",\"height\":{\"$jsan\":\"u\"},\"justifyContent\":\"center\",\"overflow\":\"visible\",\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ImageWidget1\":{\"data\":{\"id\":\"ImageWidget1\",\"type\":\"widget\",\"subtype\":\"ImageWidget\",\"config\":{\"data\":{\"value\":\"{{ShopifyQuery2.data[i].image}}\",\"aspectRatio\":1,\"resizeMode\":\"cover\",\"loadingIcon\":\"\",\"style\":{\"data\":{\"elevation\":\"4\",\"borderRadius\":\"75\",\"margin\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerSearch1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"150\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"0\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"150\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"150\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"text1\":{\"data\":{\"id\":\"text1\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"₹ {{ShopifyQuery2.data[i].presentmentPriceRanges.edges[0].node.minVariantPrice.amount}} - {{ShopifyQuery2.data[i].presentmentPriceRanges.edges[0].node.maxVariantPrice.amount}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerSearch1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextWidgetSearch1\":{\"data\":{\"id\":\"TextWidgetSearch1\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{ShopifyQuery2.data[i].title}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"fontSize\":\"fontSize14\",\"margin\":\"2\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerSearch1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Page1\":{\"data\":{\"title\":\"New Page\",\"pageId\":\"Page1\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{\"ContainerWidget1\":{\"data\":{\"id\":\"ContainerWidget1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":\"center\",\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":\"100%\",\"flexDirection\":\"column\",\"flexGrow\":\"1\",\"flexShrink\":\"1\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"100%\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextWidget2\":{\"data\":{\"id\":\"TextWidget2\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"New Page\",\"horizontalAlign\":\"center\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"padding\":\"10\",\"fontSize\":\"24\",\"elevation\":\"4\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1},\"Product\":{\"data\":{\"title\":\"New Page\",\"pageId\":\"Product\",\"pageKey\":{\"$jsan\":\"u\"},\"pageParams\":{\"data\":{\"productId\":{\"data\":{\"name\":\"productId\",\"isRequired\":true,\"defaultValue\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":9},\"varientId\":{\"data\":{\"name\":\"varientId\",\"isRequired\":true,\"defaultValue\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":9}},\"__serializedType__\":\"ImmutableMap\"},\"plugins\":{\"data\":{\"ContainerWidget4\":{\"data\":{\"id\":\"ContainerWidget4\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":\"\",\"style\":{\"data\":{\"padding\":\"\",\"elevation\":\"0\",\"backgroundColor\":\"primaryBackground\",\"borderRadius\":\"\",\"margin\":\"\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":\"auto\",\"flexDirection\":\"column\",\"flexGrow\":\"\",\"flexShrink\":\"\",\"flexWrap\":\"nowrap\",\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":\"visible\",\"width\":\"\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ContainerWidget2\":{\"data\":{\"id\":\"ContainerWidget2\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":\"\",\"style\":{\"data\":{\"margin\":\"15\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget1\",\"alignContent\":\"center\",\"alignItems\":\"center\",\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":\"row\",\"flexGrow\":\"1\",\"flexShrink\":\"0\",\"flexWrap\":\"nowrap\",\"height\":{\"$jsan\":\"u\"},\"justifyContent\":\"space-between\",\"overflow\":\"hidden\",\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ContainerWidget1\":{\"data\":{\"id\":\"ContainerWidget1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{\"isClickable\":false,\"hasBorder\":\"\",\"style\":{\"data\":{\"backgroundColor\":\"transparentBackground\",\"padding\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":\"column\",\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextPrice\":{\"data\":{\"id\":\"TextPrice\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"$ {{QueryProduct.data.product.priceRange.minVariantPrice.amount}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"fontSize\":\"18\",\"margin\":\"\",\"elevation\":\"\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget2\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"CTAButton\":{\"data\":{\"id\":\"CTAButton\",\"type\":\"widget\",\"subtype\":\"ButtonWidget\",\"config\":{\"data\":{\"value\":\"Add to Cart\",\"submit\":\"\",\"onSubmit\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget2\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextDescription\":{\"data\":{\"id\":\"TextDescription\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{QueryProduct.data.product.description}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"padding\":\"\",\"margin\":\"\",\"fontSize\":\"14\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"TextTitle\":{\"data\":{\"id\":\"TextTitle\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{QueryProduct.data.product.title}}\",\"horizontalAlign\":\"left\",\"verticalAlign\":\"center\",\"overflowType\":\"hidden\",\"style\":{\"data\":{\"fontSize\":\"fontSize24\",\"margin\":\"10\",\"backgroundColor\":\"transparentBackground\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget4\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":1,\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ImageWidget3\":{\"data\":{\"id\":\"ImageWidget3\",\"type\":\"widget\",\"subtype\":\"ImageWidget\",\"config\":{\"data\":{\"value\":\"{{QueryProduct.data.product.images.edges[0].node.originalSrc}}\",\"aspectRatio\":1,\"resizeMode\":\"cover\",\"loadingIcon\":\"\",\"style\":{\"data\":{\"margin\":\"\",\"padding\":\"\",\"borderRadius\":\"\",\"elevation\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"ContainerWidget4\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":\"1\",\"flexBasis\":\"400\",\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":\"\",\"flexShrink\":\"0\",\"flexWrap\":{\"$jsan\":\"u\"},\"height\":\"400\",\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":\"100%\"},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"QueryProduct\":{\"data\":{\"id\":\"QueryProduct\",\"type\":\"query\",\"subtype\":\"QueryPlugin\",\"config\":{\"data\":{\"runOnPageLoadDelay\":\"\",\"onSuccess\":\"\",\"datasource\":\"shopify\",\"runWhenModelUpdates\":true,\"runOnModelUpdate\":false,\"onError\":\"\",\"data\":null,\"inputVariables\":{\"data\":{\"productId\":\"{{currentPage.params.productId}}\"},\"__serializedType__\":\"ImmutableMap\"},\"metadata\":null,\"errorTransformer\":\"\",\"isFetching\":false,\"rawData\":null,\"executeQuery\":\"\",\"queryName\":\"GetProduct\",\"enableErrorTransformer\":false,\"timestamp\":0,\"enableTransformer\":false,\"runWhenPageLoads\":true,\"transformer\":\"\",\"runOnPageLoad\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":1}},\"__serializedType__\":\"ImmutableMap\"},\"navigation\":{\"data\":{\"rootNavigator\":{\"data\":{\"type\":\"navigator\",\"name\":\"Root\",\"navigatorType\":\"stack\",\"screens\":{\"data\":{\"Main\":{\"data\":{\"type\":\"navigator\",\"name\":\"Main\",\"navigatorType\":\"tab\",\"screens\":{\"data\":{\"Home\":{\"data\":{\"type\":\"screen\",\"name\":\"Home\",\"screen\":\"Offers\",\"title\":\"Home\",\"showTitleBar\":true,\"isModal\":false},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7},\"Settings\":{\"data\":{\"type\":\"screen\",\"name\":\"Settings\",\"screen\":\"Settings\",\"title\":\"Settings\",\"showTitleBar\":true,\"isModal\":false},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7},\"Accounts\":{\"data\":{\"type\":\"screen\",\"name\":\"Accounts\",\"screen\":\"Accounts\",\"title\":\"Accounts\",\"showTitleBar\":true,\"isModal\":false},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":8},\"Search\":{\"data\":{\"type\":\"screen\",\"name\":\"Search\",\"screen\":\"Search\",\"title\":\"Search Products\",\"showTitleBar\":true,\"isModal\":false},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7},\"Product\":{\"data\":{\"type\":\"screen\",\"name\":\"Product\",\"screen\":\"Product\",\"title\":\"View Product\",\"showTitleBar\":true,\"isModal\":true},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7},\"Page1\":{\"data\":{\"type\":\"screen\",\"name\":\"Page1\",\"screen\":\"Page1\",\"title\":\"Page\",\"showTitleBar\":true,\"isModal\":false},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":7}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":8}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":2},\"plugins\":{\"data\":{\"shopify\":{\"data\":{\"id\":\"shopify\",\"type\":\"datasource\",\"subtype\":\"Shopify\",\"config\":{\"data\":{\"storefrontApiUrl\":\"https://apptile-trishona.myshopify.com/api/2021-10/graphql.json\",\"storefrontAccessToken\":\"25aa394a6fd3c925344b30f4b2c56ac1\",\"pageLoad\":true,\"currency\":\"INR\",\"runWhenPageLoads\":true},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"ShopifyQueryGetCart4\":{\"data\":{\"id\":\"ShopifyQueryGetCart4\",\"type\":\"query\",\"subtype\":\"QueryPlugin\",\"config\":{\"data\":{\"runOnPageLoadDelay\":\"\",\"onSuccess\":\"\",\"datasource\":\"shopify\",\"runOnModelUpdate\":false,\"onError\":\"\",\"data\":null,\"inputVariables\":{\"data\":{\"cartId\":\"Z2lkOi8vc2hvcGlmeS9DYXJ0L2JlNThiOTkzZTYwNDEzMjE0YTAyMGJiOGI1YTNjNzBl\"},\"__serializedType__\":\"ImmutableMap\"},\"metadata\":null,\"errorTransformer\":\"\",\"isFetching\":false,\"rawData\":null,\"executeQuery\":\"\",\"queryName\":\"GetShoppingCart\",\"enableErrorTransformer\":false,\"timestamp\":0,\"enableTransformer\":false,\"transformer\":\"\",\"runOnPageLoad\":false},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableMap\"},\"modules\":{\"data\":{\"4dbe72a6-4dbe-43ea-b67f-105740404763\":{\"data\":{\"moduleUUID\":\"4dbe72a6-4dbe-43ea-b67f-105740404763\",\"moduleSaveId\":\"0\",\"moduleName\":\"FirstModule\",\"inputs\":[\"productItem\"],\"outputs\":[\"textValue\"],\"queries\":[],\"moduleConfig\":{\"data\":{\"Container1\":{\"data\":{\"id\":\"Container1\",\"type\":\"widget\",\"subtype\":\"ContainerWidget\",\"config\":{\"data\":{},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"textPlugin1\":{\"data\":{\"id\":\"textPlugin1\",\"type\":\"widget\",\"subtype\":\"TextWidget\",\"config\":{\"data\":{\"value\":\"{{productItem.value.handle}}\",\"style\":{\"data\":{\"padding\":\"5\"},\"__serializedType__\":\"ImmutableMap\"}},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"Container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"textInput1\":{\"data\":{\"id\":\"textInput1\",\"type\":\"widget\",\"subtype\":\"TextInputWidget\",\"config\":{\"data\":{\"value\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"Container1\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"productItem\":{\"data\":{\"id\":\"productItem\",\"type\":\"state\",\"subtype\":\"ModuleProperty\",\"config\":{\"data\":{\"value\":\"\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0},\"textValue\":{\"data\":{\"id\":\"textValue\",\"type\":\"state\",\"subtype\":\"ModuleOutput\",\"config\":{\"data\":{\"value\":\"{{textInput1.value}}\"},\"__serializedType__\":\"ImmutableMap\"},\"layout\":{\"data\":{\"container\":\"\",\"alignContent\":{\"$jsan\":\"u\"},\"alignItems\":{\"$jsan\":\"u\"},\"alignSelf\":{\"$jsan\":\"u\"},\"flex\":{\"$jsan\":\"u\"},\"flexBasis\":{\"$jsan\":\"u\"},\"flexDirection\":{\"$jsan\":\"u\"},\"flexGrow\":{\"$jsan\":\"u\"},\"flexShrink\":{\"$jsan\":\"u\"},\"flexWrap\":{\"$jsan\":\"u\"},\"height\":{\"$jsan\":\"u\"},\"justifyContent\":{\"$jsan\":\"u\"},\"overflow\":{\"$jsan\":\"u\"},\"width\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":4},\"namespace\":{\"$jsan\":\"u\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":0}},\"__serializedType__\":\"ImmutableOrderedMap\"}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":11}},\"__serializedType__\":\"ImmutableMap\"},\"theme\":{\"data\":{\"isDark\":true,\"isDarkModeSupported\":true,\"theme\":{\"dark\":false,\"transparentBackground\":\"transparent\",\"primaryBackground\":\"#FFFFFF\",\"secondaryBackground\":\"#FFFFFF\",\"surfaceBackground\":\"#FFFFFF\",\"onSurfaceBackground\":\"#000000\",\"primaryForeground\":\"#71717a\",\"secondaryForeground\":\"#71717a\",\"primaryTextColor\":\"#000000\",\"secondaryTextColor\":\"#000000\",\"disabledColor\":\"#71717a\",\"primaryHighlight\":\"#0077e6\",\"placeholderColor\":\"#71717a\",\"primaryBorderColor\":\"#18181b\",\"secondaryBorderColor\":\"#3f3f46\",\"successPrimary\":\"#22c55e\",\"errorPrimary\":\"#ef4444\",\"infoPrimary\":\"#0ea5e9\",\"warningPrimary\":\"#eab308\",\"fontFamily\":{\"regular\":{\"fontFamily\":\"System\",\"fontWeight\":\"400\"},\"medium\":{\"fontFamily\":\"System\",\"fontWeight\":\"500\"},\"light\":{\"fontFamily\":\"System\",\"fontWeight\":\"300\"},\"thin\":{\"fontFamily\":\"System\",\"fontWeight\":\"100\"}},\"borderRadius0\":0,\"opacity0\":0,\"borderWidth0\":0,\"fontWeight100\":100,\"fontSize10\":10,\"spacing0\":0},\"modes\":{\"light\":{\"transparentBackground\":\"transparent\",\"primaryBackground\":\"#FFFFFF\",\"shadowColor\":\"#000000\",\"secondaryBackground\":\"#FFFFFF\",\"surfaceBackground\":\"#FFFFFF\",\"onSurfaceBackground\":\"#000000\",\"primaryTextColor\":\"#000000\",\"secondaryTextColor\":\"#000000\"},\"dark\":{\"transparentBackground\":\"rgba(0,0,0,0)\",\"primaryBackground\":\"#000000\",\"secondaryBackground\":\"#000000\",\"surfaceBackground\":\"#000000\",\"onSurfaceBackground\":\"#FFFFFF\",\"primaryTextColor\":\"#FFFFFF\",\"secondaryTextColor\":\"#FFFFFF\"}},\"platform\":{\"ios\":{\"fontFamily\":{\"regular\":{\"fontFamily\":\"System\",\"fontWeight\":\"400\"},\"medium\":{\"fontFamily\":\"System\",\"fontWeight\":\"500\"},\"light\":{\"fontFamily\":\"System\",\"fontWeight\":\"300\"},\"thin\":{\"fontFamily\":\"System\",\"fontWeight\":\"100\"}}},\"android\":{\"fontFamily\":{\"regular\":{\"fontFamily\":\"sans-serif\",\"fontWeight\":\"normal\"},\"medium\":{\"fontFamily\":\"sans-serif-medium\",\"fontWeight\":\"normal\"},\"light\":{\"fontFamily\":\"sans-serif-light\",\"fontWeight\":\"normal\"},\"thin\":{\"fontFamily\":\"sans-serif-thin\",\"fontWeight\":\"normal\"}}},\"web\":{\"fontFamily\":{\"regular\":{\"fontFamily\":\"Roboto, \\\"Helvetica Neue\\\", Helvetica, Arial, sans-serif\",\"fontWeight\":\"400\"},\"medium\":{\"fontFamily\":\"Roboto, \\\"Helvetica Neue\\\", Helvetica, Arial, sans-serif\",\"fontWeight\":\"500\"},\"light\":{\"fontFamily\":\"Roboto, \\\"Helvetica Neue\\\", Helvetica, Arial, sans-serif\",\"fontWeight\":\"300\"},\"thin\":{\"fontFamily\":\"Roboto, \\\"Helvetica Neue\\\", Helvetica, Arial, sans-serif\",\"fontWeight\":\"100\"}}}}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":5}},\"__serializedType__\":\"ImmutableRecord\",\"__serializedRef__\":3}"