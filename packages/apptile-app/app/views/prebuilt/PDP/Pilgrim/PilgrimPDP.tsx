import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable, Share, Platform, InteractionManager} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import _ from 'lodash';

import {
  CurrentScreenContext,
  ApptileScrollView,
  datasourceTypeModelSel,
  selectPluginStageModel,
  shopifyProductCacheSelector,
  useTheme,
  generateTypographyByPlatform,
  useLoadedFonts,
  EmbeddedAppPageContainer,
  selectPluginConfig,
  selectPlugin, 
  triggerAction, 
  sendAnalyticsEvent,
  navigateToScreen,
  createDeepEqualSelector,
  MaterialCommunityIcons, Ionicons,
  performHapticFeedback,
  selectAppSettingsForKey,
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY,
  SettingsConfig
} from 'apptile-core';

import {registerNativePage} from '../../prebuilt';

import {getProductImages} from '@/root/app/plugins/state/ShopifyPDP/variantSelectionStrategies';
import PilgrimHeader from '../../custom/Pilgrim/PilgrimHeader';
import {activeNavigationSelector} from '@/root/app/selectors/ActiveNavigationSelectors';

// import FastImage from 'react-native-fast-image';
import FlatListSlider from '@/root/app/plugins/widgets/ImageSliderWidget/FlatListSlider';
import {getProductDefaultVariantOptions, getProductDerivedData} from '@/root/app/plugins/state/ShopifyPDP/actions';
import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const orderLimiterModelSel = state => datasourceTypeModelSel(state, 'orderLimiter');

const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, (shopifyDS): string | unknown => {
  return shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']);
});
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');
const shopifyProductCacheSel = state => shopifyProductCacheSelector(state, 'shopifyV_22_10');
const makePDPHelperModelSel = pageKey => {
  return state => selectPluginStageModel(state, pageKey, 'PDP');
};
const defaultPageParams = {};
const emptyProduct = undefined;
const metafieldDefinitions = [
  {key: 'g_bestsellertag', namespace: 'custom'},
  {key: 'after_atc_test_ingrdients', namespace: 'my_fields'},
  {key: 'after_atc_single_line_text', namespace: 'my_fields'},
  {key: 'rich_text_skin_type_batc', namespace: 'my_fields'},
  {key: 'product_size', namespace: 'my_fields'},
  {key: 'ingredients1_url', namespace: 'my_fields'},
  {key: 'ingredients2_url', namespace: 'my_fields'},
  {key: 'ingredients3_url', namespace: 'my_fields'},
  {key: 'featured-product-final', namespace: 'custom'},
  {key: 'how_to_use', namespace: 'my_fields'},
  {key: 'test_benefit_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit2_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit3_url', namespace: 'my_fields'},
  {key: 'product_label_1', namespace: 'custom'},
  {key: 'product_label_2', namespace: 'custom'},
  {key: 'subtitle', namespace: 'descriptors'},
  {key: 'key_benefits_heading', namespace: 'custom'},
  {key: 'key_benefits', namespace: 'custom'},
];

const initialState = {
  product: undefined,
  variantCount: 1,
  productOptions: [],
  optionNames: [],
  selectedOptions: {},
  displayOptions: {},
  activeVariant: {},
  variantImages: [],
  productHandle: '',
  selectVariantOption: 'action',
};

function PDPReducer(state, action) {
  switch (action.type) {
    case 'SET_PRODUCT':
      const product = action.product;
      if (product) {
        const derivedData = getProductDerivedData(product);
        const defaultOptionsData = getProductDefaultVariantOptions(product);
        const variantImages = getProductImages(product);
        return {...state, ...derivedData, ...defaultOptionsData, product, variantImages};
      }
      return {...state, product};
    case 'SET_ACTIVE_VARIANT':
      const variant = action.variant;
      if (variant && state.product) {
        // const variantImages = getActiveVariantImages(state.product, variant);
        const variantImages = getProductImages(state.product);
        return {...state, activeVariant: variant, variantImages};
      }
      return state;
    default:
      return state;
  }
}

const PilgrimPDP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const primaryButtonTheme = themeEvaluator('tile.button.primary');
  const textColor = themeEvaluator('colors.onBackground');
  const primaryColor = themeEvaluator('colors.primary');
  const secondaryColor = themeEvaluator('colors.secondary');
  const {
    typography: primaryButtonTypo,
    color: primaryButtonTextColor,
    disabledColor,
    disabledBackgroundColor,
    backgroundColor,
    ...buttonStyles
  } = primaryButtonTheme;
  const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  const OrderLimiterDSModel = useSelector(orderLimiterModelSel);
  const orderLimiterPluginId = OrderLimiterDSModel?.get('id');
  const OrderLimiterDSConfig = orderLimiterPluginId
    ? useSelector(state => selectPluginConfig(state, null, orderLimiterPluginId))
    : null;

  const [product, setProduct] = useState({});
  const productCacheByHandle = useSelector(shopifyProductCacheSel);
  const PDPModelSel = useRef(makePDPHelperModelSel(pageKey));
  const PDPHelperModel = useSelector(PDPModelSel.current);
  const [PDP, PDPDispatch] = useReducer(PDPReducer, initialState);
  const [cartItems, setCartItems] = useState([]);
  const [isInCart, setIsInCart] = useState(false);
  const [cartQuantity, setCartQuantity] = useState(0);
  const [initEmbed, setInitEmbed] = useState(false);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const currentAspectRatio = Number(pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY) ?? '1');
  const pdpImageResize = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY) ?? 'cover';
  const pdpHideWishlist = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius = Number(pdpSettings.getSettingValue(PDP_BORDER_RADIUS) ?? '5');

  const currProduct =
    (PDPHelperModel?.get('product') || (productCacheByHandle && productCacheByHandle[params.productHandle])) ??
    emptyProduct;
  const currVariant =
    PDPHelperModel?.get('activeVariant') ||
    (productCacheByHandle &&
      productCacheByHandle[params.productHandle] &&
      productCacheByHandle[params.productHandle]?.activeVariant);
  useEffect(() => {
    if (product !== currProduct && !_.isEmpty(currProduct)) {
      setProduct(currProduct);
      setRatingVal(currProduct?.metafields[0]?.value?.value);
      setRatingCount(currProduct?.metafields[1]?.value);

      setAtcText(currProduct?.metafields[26]?.value);
      setProductLabel1(currProduct?.metafields[20]?.value);
      setProductLabel2(currProduct?.metafields[21]?.value);
      setSubtitle(currProduct?.metafields[27]?.value);
      setBenefitsHeading(currProduct?.metafields[28]?.value || 'Key Benefits');
      setBenefitsString(currProduct?.metafields[2]?.value);
      setBenefitsList(currProduct?.metafields[2]?.value?.split('•'));

      PDPDispatch({type: 'SET_PRODUCT', product: currProduct});
      if (currProduct?.title) navigation.setOptions({title: currProduct?.title});
    }
    InteractionManager.runAfterInteractions(() => setInitEmbed(true));
  }, [currProduct, navigation, product]);
  useEffect(() => {
    if (PDP.activeVariant !== currVariant && !_.isEmpty(currVariant)) {
      PDPDispatch({type: 'SET_ACTIVE_VARIANT', variant: currVariant});
    }
  }, [PDP.activeVariant, currVariant]);
  // useEffect(() => {
  //   if (PDP.variantImages?.length) {
  //     FastImage.preload(
  //       PDP.variantImages?.map(img => {
  //         return {
  //           uri: img,
  //         };
  //       }),
  //     );
  //   }
  // }, [PDP.variantImages]);

  /****************************************
   * Handle Metafields.
   ***********************************/

  const [metafields, setMetaFields] = useState([]);
  const [atcText, setAtcText] = useState('');
  // const [skinType, setSkinType] = useState('');
  // const [productSize, setProductSize] = useState('');
  // const [ingredients, setIngredients] = useState([]);
  // const [howToUse, setHowToUse] = useState('');
  // const [benefits, setBenefits] = useState([]);
  const [productLabel1, setProductLabel1] = useState('');
  const [productLabel2, setProductLabel2] = useState('');
  const [subtitle, setSubtitle] = useState('');
  const [ratingVal, setRatingVal] = useState('');
  const [ratingCount, setRatingCount] = useState('');
  const [benefitsHeading, setBenefitsHeading] = useState('');
  const [benefitsString, setBenefitsString] = useState('');
  const [benefitsList, setBenefitsList] = useState([]);
  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchMetaFields = async () => {
      const queryResponse = await queryRunner.runQuery('query', ProductGqls.GET_PRODUCT_METAFIELDS_BY_HANDLE, {
        productHandle: params.productHandle,
        identifiers: metafieldDefinitions,
      });
      setMetaFields(_.get(queryResponse?.data, ['productByHandle', 'metafields']));
      // setInitEmbed(true);
    };
    fetchMetaFields();
    return () => {};
  }, []);
  useEffect(() => {
    if (!_.isEmpty(metafields) && _.isArray(metafields)) {
      setAtcText(metafields[2]?.value);
      // setSkinType(metafields[3]?.value);
      // setProductSize(metafields[4]?.value);
      // setIngredients([metafields[5]?.value, metafields[6]?.value, metafields[7]?.value]);
      // setHowToUse(metafields[9]?.value);
      // setBenefits([metafields[10]?.value, metafields[11]?.value, metafields[12]?.value]);
      setProductLabel1(metafields[13]?.value);
      setProductLabel2(metafields[14]?.value);
      setSubtitle(metafields[15]?.value);
      // setRatingVal(metafields[0]?.value?.value);
      // setRatingCount(metafields[1]?.value);
      setBenefitsHeading(metafields[16]?.value || 'Key Benefits');
      setBenefitsString(metafields[17]?.value);
      setBenefitsList(metafields[17]?.value?.split('•'));
    }
  }, [metafields]);

  /****************************************
   * Handle Cart.
   ***********************************/
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);
  useEffect(() => {
    const cartEntry = cartItems?.filter(cartItem => cartItem?.merchandiseId === PDP.activeVariant?.id);
    if (!_.isEmpty(cartEntry)) {
      const cq = _.first(cartEntry)?.displayQuantity;
      if (cq !== 0) setIsInCart(true);
      else setIsInCart(false);
      setCartQuantity(cq);
    } else {
      setIsInCart(false);
      setCartQuantity(0);
    }
  }, [PDP.activeVariant?.id, cartItems, ShopifyDSModel]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);
  const [isInWishlist, setIsInWishlist] = useState(false);
  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);
  useEffect(() => {
    const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id == product?.id?.split('/')?.pop());
    if (!_.isEmpty(wlEntry)) {
      setIsInWishlist(true);
    } else {
      setIsInWishlist(false);
    }
  }, [product?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const storeCurrency = useSelector(shopifyStoreCurrencySel);
  const addToCartCallback = useCallback(() => {
    if (
      PDP.activeVariant &&
      PDP.product &&
      PDP.activeVariant?.availableForSale &&
      PDP.activeVariant?.salePrice &&
      PDP.activeVariant?.id &&
      PDP.activeVariant?.title &&
      PDP.product?.title &&
      PDP.product?.productType &&
      PDP.product?.id
    ) {
      const increaseCartLineItemActionMeta = {
        pluginConfig: orderLimiterPluginId ? OrderLimiterDSConfig : ShopifyDSConfig,
        pluginModel: orderLimiterPluginId ? OrderLimiterDSModel : ShopifyDSModel,
        pluginSelector: orderLimiterPluginId ? [orderLimiterPluginId] : ['shopify'],
      };

      dispatch(
        triggerAction({
          pluginConfig: increaseCartLineItemActionMeta.pluginConfig,
          pluginModel: increaseCartLineItemActionMeta.pluginModel,
          pluginSelector: increaseCartLineItemActionMeta.pluginSelector,
          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: true,
              sellingPlanId: null,
              itemPrice: PDP.activeVariant.salePrice,
              successToastText: 'Product added to Cart',
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP.activeVariant.salePrice,
          productId: PDP.product.id,
          productType: PDP.product.productType,
          quantity: 1,
          title: PDP.product.title,
          variantId: PDP.activeVariant.id,
          variantTitle: PDP.activeVariant.title,
          brand: PDP.product?.vendor,
        }),
      );
      performHapticFeedback('tap');
      // toast.show('Product added to Cart', {
      //   type: 'success',
      //   placement: 'bottom',
      //   duration: 2000,
      //   style: {marginBottom: 80},
      // });
    }
  }, [
    PDP.activeVariant,
    PDP.product,
    orderLimiterPluginId,
    OrderLimiterDSConfig,
    ShopifyDSConfig,
    OrderLimiterDSModel,
    ShopifyDSModel,
    dispatch,
    storeCurrency,
  ]);

  const increaseCartCallback = useCallback(() => {
    const increaseCartLineItemActionMeta = {
      pluginConfig: orderLimiterPluginId ? OrderLimiterDSConfig : ShopifyDSConfig,
      pluginModel: orderLimiterPluginId ? OrderLimiterDSModel : ShopifyDSModel,
      pluginSelector: orderLimiterPluginId ? [orderLimiterPluginId] : ['shopify'],
    };
    if (
      PDP.activeVariant &&
      PDP.product &&
      PDP.activeVariant?.availableForSale &&
      PDP.activeVariant?.salePrice &&
      PDP.activeVariant?.id &&
      PDP.activeVariant?.title &&
      PDP.product?.title &&
      PDP.product?.productType &&
      PDP.product?.id
    ) {
      dispatch(
        triggerAction({
          pluginConfig: increaseCartLineItemActionMeta.pluginConfig,
          pluginModel: increaseCartLineItemActionMeta.pluginModel,
          pluginSelector: increaseCartLineItemActionMeta.pluginSelector,

          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: true,
              sellingPlanId: null,
              itemPrice: PDP.activeVariant.salePrice,
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP.activeVariant.salePrice,
          productId: PDP.product.id,
          productType: PDP.product.productType,
          quantity: 1,
          title: PDP.product.title,
          variantId: PDP.activeVariant.id,
          variantTitle: PDP.activeVariant.title,
          brand: PDP.product?.vendor,
        }),
      );
      performHapticFeedback('tick');
    }
  }, [
    orderLimiterPluginId,
    OrderLimiterDSConfig,
    ShopifyDSConfig,
    OrderLimiterDSModel,
    ShopifyDSModel,
    PDP.activeVariant,
    PDP.product,
    dispatch,
    storeCurrency,
  ]);
  const decreaseCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale && PDP.product) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'decreaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: true,
              sellingPlanId: null,
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'removeFromCart', {
          currency: storeCurrency,
          price: PDP.activeVariant.salePrice,
          productId: PDP.product.id,
          productType: PDP.product.productType,
          quantity: 1,
          title: PDP.product.title,
          variantId: PDP.activeVariant.id,
          variantTitle: PDP.activeVariant.title,
          brand: PDP.product.vendor,
        }),
      );

      performHapticFeedback('tick');
    }
  }, [PDP.activeVariant, PDP.product, ShopifyDSConfig, ShopifyDSModel, dispatch, storeCurrency]);

  const goToCartCallback = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const addToWishList = useCallback(() => {
    if (PDP.product) {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'addProductToWishlist',
            params: {
              productId: PDP.product?.id?.split('/')?.pop(),
              productHandle: PDP.product?.handle,
            },
          },
        }),
      );
      setIsInWishlist(true);
      performHapticFeedback('tap');
      toast.show('Product added to Wishlist', {
        type: 'info',
        placement: 'top',
        duration: 2000,
        style: {marginTop: 20},
      });

      dispatch(
        sendAnalyticsEvent('track', 'addToWishlist', {
          productId: PDP.product?.id?.split('/')?.pop(),
          productHandle: PDP.product?.handle,
          productType: PDP.product?.productType,
          currency: storeCurrency,
          price: PDP.product?.variants[0]?.salePrice,
          quantity: 1,
          title: PDP.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP.product?.vendor,
        }),
      );
    }
  }, [LWConfig, LWModel, PDP, dispatch, storeCurrency]);

  const removeFromWishList = useCallback(() => {
    dispatch(
      triggerAction({
        pluginConfig: LWConfig,
        pluginModel: LWModel,
        pluginSelector: ['localWishlist'],
        eventModelJS: {
          value: 'removeProductFromWishlist',
          params: {
            productId: product?.id?.split('/')?.pop(),
            productHandle: product?.handle,
          },
        },
      }),
    );
    performHapticFeedback('tap');
    toast.show('Product removed from Wishlist', {
      type: 'info',
      placement: 'top',
      duration: 2000,
      style: {marginTop: 20},
    });
    dispatch(
      sendAnalyticsEvent('track', 'removeFromWishlist', {
        productId: product?.id?.split('/')?.pop(),
        productHandle: product?.handle,
      }),
    );
  }, [LWConfig, LWModel, dispatch, product]);

  const shareProduct = useCallback(() => {
    Share.share(
      Platform.select({
        android: {
          message: `https://discoverpilgrim.com/products/${product?.handle}`,
          url: `https://discoverpilgrim.com/products/${product?.handle}`,
          title: product?.title,
        },
        ios: {
          // message: `https://discoverpilgrim.com/products/${product?.handle}`,
          url: `https://discoverpilgrim.com/products/${product?.handle}`,
          title: product?.title,
        },
      }),
    );
  }, [product]);

  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}} edges={['top', 'bottom', 'left', 'right']}>
        <PilgrimHeader hideWishListIcon={true} />
        <ApptileScrollView
          contentInsetAdjustmentBehavior="automatic"
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: '#fff', flex: 1}}>
          <View>
            <FlatListSlider
              styles={{width: '100%', minHeight: 400}}
              data={PDP.variantImages}
              timer={0}
              autoscroll={false}
              height={400}
              aspectRatio={currentAspectRatio}
              resizeMode={pdpImageResize}
            />
            <View style={[fixedStyles.sliderButtonsContainer]}>
              <Pressable style={[fixedStyles.sliderButtons]} onPress={shareProduct}>
                <Ionicons size={22} name="share-social-outline" color="#333" />
              </Pressable>
              {!pdpHideWishlist && (
                <>
                  {isInWishlist ? (
                    <Pressable style={[fixedStyles.sliderButtons]} onPress={removeFromWishList}>
                      <MaterialCommunityIcons size={22} name="heart" color="#e65a4c" />
                    </Pressable>
                  ) : (
                    <Pressable style={[fixedStyles.sliderButtons]} onPress={addToWishList}>
                      <MaterialCommunityIcons size={22} name="heart-outline" color="#333" />
                    </Pressable>
                  )}
                </>
              )}
            </View>
          </View>
          <View style={[fixedStyles.productDetailsContainer]}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {productLabel1 ? (
                <View style={[{flexDirection: 'row'}]}>
                  <View style={[fixedStyles.productLabelContainer1]}>
                    {/* <MaterialCommunityIcons size={14} name="lightning-bolt-outline" color="#FD4873" /> */}
                    <Text style={[bodyStyles, fixedStyles.productLabelText1]}>{productLabel1}</Text>
                  </View>
                </View>
              ) : (
                <></>
              )}
              {productLabel2 ? (
                <View style={[{flexDirection: 'row'}]}>
                  <View style={[fixedStyles.productLabelContainer2]}>
                    {/* <MaterialCommunityIcons size={14} name="lightning-bolt-outline" color="#FD4873" /> */}
                    <Text style={[bodyStyles, fixedStyles.productLabelText2]}>{productLabel2}</Text>
                  </View>
                </View>
              ) : (
                <></>
              )}
            </View>

            <View style={[fixedStyles.titleContainer]}>
              <Text style={[headingStyles, {color: textColor}]}>{product?.title}</Text>
              <Text style={[bodyStyles, {color: '#00000088', fontSize: 14}]}>{subtitle}</Text>
              {/* <Text style={[bodyStyles, {color: '#00000088'}]}>{atcText}</Text> */}
            </View>
            {/* <View style={[fixedStyles.detailsSectionContainer]}>
              <View style={[fixedStyles.detailTextContainer]}>
                <Text style={[bodyStyles, {color: textColor}]}>{atcText}</Text>
              </View>
            </View> */}
            <View style={[fixedStyles.pricingSection]}>
              <Text style={[bodyStyles, {color: '#00000055', fontSize: 10, textTransform: 'uppercase'}]}>
                MRP inclusive of all taxes
              </Text>
              <View style={[fixedStyles.priceContainer]}>
                <Text style={[subHeadingStyles, fixedStyles.mainPrice]}>{PDP?.activeVariant?.displaySalePrice}</Text>
                {PDP?.activeVariant?.displaySalePrice !== PDP?.activeVariant?.displayPrice ? (
                  <>
                    <Text
                      style={[
                        bodyStyles,
                        fixedStyles.strikeOffPrice,
                        {color: '#0005', textDecorationLine: 'line-through'},
                      ]}>
                      {PDP?.activeVariant?.displayPrice}
                    </Text>
                    <Text style={[bodyStyles, {color: '#388E3C'}]}>
                      (
                      {_.round(
                        ((PDP?.activeVariant?.price - PDP?.activeVariant?.salePrice) * 100) / PDP?.activeVariant?.price,
                      )}
                      % off)
                    </Text>
                  </>
                ) : (
                  <></>
                )}
              </View>
            </View>
            {ratingVal && ratingCount ? (
              <View style={[fixedStyles.ratingSection]}>
                <View style={[fixedStyles.ratingsContainer]}>
                  <View style={[fixedStyles.ratingsBadge]}>
                    <MaterialCommunityIcons size={12} name="star" color="#FFF" />
                    <Text style={[bodyStyles, {fontSize: 12, color: '#fff'}]}>{ratingVal}</Text>
                  </View>
                  <View style={[fixedStyles.ratingsCount]}>
                    <MaterialCommunityIcons size={18} name="check-decagram" color="#00AEEF" />
                    <Text style={[bodyStyles, {fontSize: 12, color: '#000', marginLeft: 4}]}>{ratingCount}</Text>
                    <Text style={[bodyStyles, {fontSize: 12, color: '#000'}]}> Verified reviews</Text>
                  </View>
                </View>
              </View>
            ) : (
              <></>
            )}
            {benefitsString ? (
              <View style={[fixedStyles.benefitsSectionContainer]}>
                <View style={[fixedStyles.benefitsHeadingContainer]}>
                  <Text style={[headingStyles, {fontSize: 16, lineHeight: 20, color: textColor}]}>
                    {benefitsHeading}
                  </Text>
                </View>
                {benefitsList.map((bVal, idx) => {
                  return (
                    <View key={idx} style={[{flexDirection: 'row', alignItems: 'center'}]}>
                      <MaterialCommunityIcons size={12} name="creation" color="#FFC105" />
                      <Text style={[bodyStyles, {fontSize: 12, color: '#000'}]}>{_.trim(bVal)}</Text>
                    </View>
                  );
                })}
              </View>
            ) : (
              <></>
            )}
            {/* <View style={[fixedStyles.detailsSectionContainer]}>
              <View style={[fixedStyles.detailTextContainer]}>
                <Text style={[bodyStyles, {color: textColor}]}>Skin Type: </Text>
                <Text style={[bodyStyles, {color: textColor}]}>{skinType}</Text>
              </View>
              <View style={[fixedStyles.detailTextContainer]}>
                <Text style={[bodyStyles, {color: textColor}]}>Product size: </Text>
                <Text style={[bodyStyles, {color: textColor}]}>{productSize}</Text>
              </View>
            </View> */}
          </View>
          {/* <View style={[fixedStyles.carouselContainer]}>
            <Text style={[headingStyles, fixedStyles.carouselTitleText, {color: textColor}]}>Key Benefits</Text>
            <FlatListSlider
              styles={{width: '100%', height: 380}}
              data={benefits}
              timer={5000}
              autoscroll={false}
              height={360}
              resizeMode="contain"
              // onPress={onPress}
            />
          </View>
          <View style={[fixedStyles.carouselContainer]}>
            <Text style={[headingStyles, , {color: textColor}, fixedStyles.carouselTitleText]}>
              Powerful Ingredients
            </Text>
            <FlatListSlider
              styles={{width: '100%', height: 320}}
              data={ingredients}
              timer={5000}
              autoscroll={false}
              height={300}
              resizeMode="contain"
              // onPress={onPress}
            />
          </View> */}
          {initEmbed ? <EmbeddedAppPageContainer {...screenProps} /> : <></>}
        </ApptileScrollView>
        <View style={[fixedStyles.bottomBar]}>
          {PDP.activeVariant && PDP.activeVariant?.availableForSale === false ? (
            <View style={[fixedStyles.ctaButton]}>
              <Text style={[headingStyles, {color: textColor}]}>Out of Stock</Text>
            </View>
          ) : isInCart ? (
            <View style={[fixedStyles.bottomPanel]}>
              <View style={[fixedStyles.bottomControls, fixedStyles.quantityControlBar]}>
                <Pressable style={[fixedStyles.bottomControls]} onPress={decreaseCartCallback}>
                  <Text style={[primaryButtonTextStyles, {color: textColor}]}>-</Text>
                </Pressable>
                <View style={[fixedStyles.bottomControls]}>
                  <Text style={[primaryButtonTextStyles, {color: textColor}]}>{cartQuantity}</Text>
                </View>
                <Pressable style={[fixedStyles.bottomControls]} onPress={increaseCartCallback}>
                  <Text style={[primaryButtonTextStyles, {color: textColor}]}>+</Text>
                </Pressable>
              </View>
              <Pressable
                style={[fixedStyles.bottomControls, buttonStyles, {backgroundColor, borderRadius: pdpBorderRadius}]}
                onPress={goToCartCallback}>
                <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Go to cart</Text>
              </Pressable>
            </View>
          ) : (
            <View style={[fixedStyles.bottomPanel]}>
              <View
                style={[
                  {
                    flex: 0,
                    marginRight: 12,
                    flexBasis: 'auto',
                    flexGrow: 0,
                    width: 'auto',
                    flexDirection: 'row',
                    alignItems: 'center',
                  },
                ]}>
                <Text style={[subHeadingStyles, fixedStyles.mainPrice]}>{PDP?.activeVariant?.displaySalePrice}</Text>
              </View>
              <Pressable
                style={[fixedStyles.ctaButton, buttonStyles, {backgroundColor, borderRadius: pdpBorderRadius}]}
                onPress={addToCartCallback}>
                <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Add to cart</Text>
              </Pressable>
            </View>
          )}
        </View>
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const fixedStyles = StyleSheet.create({
  titleContainer: {
    paddingVertical: 8,
  },
  productDetailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    flexDirection: 'column',
  },
  productLabelContainer1: {
    width: 'auto',
    paddingHorizontal: 12,
    paddingVertical: 4,
    flexDirection: 'row',
    borderRadius: 20,
    backgroundColor: '#27A0CF33',
    alignItems: 'center',
    marginRight: 5,
  },
  productLabelContainer2: {
    width: 'auto',
    paddingHorizontal: 12,
    paddingVertical: 4,
    flexDirection: 'row',
    borderRadius: 20,
    backgroundColor: '#f7d2db',
    alignItems: 'center',
  },
  productLabelText1: {
    fontSize: 12,
    textTransform: 'uppercase',
    color: '#27A0CF',
  },
  productLabelText2: {
    fontSize: 12,
    textTransform: 'uppercase',
    color: '#fd4873',
  },
  pricingSection: {
    paddingVertical: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 18,
    lineHeight: 24,
    color: '#000',
  },
  strikeOffPrice: {
    marginHorizontal: 4,
  },
  ratingSection: {
    flexDirection: 'row',
  },
  ratingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingsBadge: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: '#388E3C',
    alignItems: 'center',
  },
  ratingsCount: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 2,
    marginHorizontal: 4,
    borderLeftWidth: 1,
    borderColor: '#0003',
    alignItems: 'center',
  },
  benefitsSectionContainer: {
    backgroundColor: 'rgba(255, 193, 5, 0.10)',
    paddingHorizontal: 12,
    paddingBottom: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: 'rgba(255, 193, 5, 0.50)',
    borderRadius: 24,
    marginTop: 38,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  benefitsHeadingContainer: {
    backgroundColor: '#FFC105',
    alignItems: 'center',
    height: 24,
    paddingHorizontal: 12,
    borderRadius: 14,
    position: 'relative',
    top: -12,
    flexBasis: 'auto',
    flexGrow: 0,
    width: 'auto',
  },
  detailsSectionContainer: {
    paddingHorizontal: 12,
  },
  detailTextContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  carouselContainer: {
    marginVertical: 8,
    paddingVertical: 4,
    backgroundColor: '#fff',
  },
  carouselTitleText: {
    marginVertical: 8,
    marginHorizontal: 12,
  },
  bottomBar: {
    backgroundColor: '#fff',
    height: 60,
    flexBasis: 60,
    flex: 0,
    padding: 8,
    overflow: 'hidden',
  },
  ctaButton: {
    flex: 1,
    flexGrow: 1,
    flexShrink: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
  },
  bottomControls: {
    flex: 1,
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityControlBar: {
    borderWidth: 1,
    borderColor: '#FFC105',
    borderRadius: 6,
  },
  sliderButtonsContainer: {
    position: 'absolute',
    bottom: 36,
    right: 16,
  },
  sliderButtons: {
    width: 32,
    height: 32,
    flex: 0,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginBottom: 12,
  },
});

export default registerNativePage('PilgrimPDP', PilgrimPDP, {}, {});
