import _ from 'lodash';
import React, {useCallback, useEffect, useReducer, useRef, useState, useMemo} from 'react';
import {Platform, Pressable, StyleSheet, Text, View, Image, ActivityIndicator} from 'react-native';
import {gestureHandlerRootHOC} from 'react-native-gesture-handler';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';

import {
  navigateToScreen,
  sendAnalyticsEvent,
  triggerAction,
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY,
  SettingsConfig,
  createDeepEqualSelector,
  useLoadedFonts,
  MaterialCommunityIcons,
  performHapticFeedback,
  editableTags,
  selectPluginConfig,
  datasourceTypeModelSel,
  selectPluginStageModel,
  shopifyProductCacheSelector,
  selectAppSettingsForK<PERSON>,
  generateTypographyByPlatform,
  isTypographyStyleSheet,
  useTheme,
  CurrentScreenContext,
  EmbeddedAppPageContainer,
  ApptileScrollView,
} from 'apptile-core';

import {RichTextDisplay} from '@/root/app/plugins/widgets/RichTextWidget';
import RforrabbitHeader from '../../custom/Rforrabbit/RforrabbitHeader';
import {registerNativePage} from '../../prebuilt';
import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import FlatListSlider from '@/root/app/plugins/widgets/ImageSliderV2/FlatListSlider';
import {
  getProductDefaultVariantOptions,
  getProductDerivedData,
  setActiveSellingPlan,
} from '@/root/app/plugins/state/ShopifyPDP/actions';
import {
  getProductImages,
  getVariantImagesBySelectedOption,
} from '@/root/app/plugins/state/ShopifyPDP/variantSelectionStrategies';

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const orderLimiterModelSel = state => datasourceTypeModelSel(state, 'advanceOrderLimiter');
const recurpayModelSel = state => datasourceTypeModelSel(state, 'Recurpay');

const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, (shopifyDS): string | unknown => {
  return shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']);
});
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');
const shopifyProductCacheSel = state => shopifyProductCacheSelector(state, 'shopifyV_22_10');
const makePDPHelperModelSel = pageKey => {
  return state => selectPluginStageModel(state, pageKey, 'PDP');
};
const BuyNowProductsStateModelSel = pageKey => {
  return state => selectPluginStageModel(state, pageKey, 'BuyNowProductsState');
};

const getTagStylesAndFonts = (tags: string[], styles: Record<string, any>) => {
  const tagsStyles = {};
  const fonts = new Set<string>([]);

  _.forEach(tags, entry => {
    const tagName = entry.toLowerCase();

    _.forEach(styles, (styleVal, styleKey) => {
      if (_.startsWith(styleKey, `${tagName}_`)) {
        if (isTypographyStyleSheet(styleVal)) {
          if (styleVal.fontFamily) {
            fonts.add(styleVal.fontFamily);
          }
          _.set(tagsStyles, tagName, {..._.get(tagsStyles, tagName, {}), ...styleVal});
        } else {
          _.set(tagsStyles, _.replace(styleKey, '_', '.'), styleVal);
        }
      }
    });
  });

  return {tagsStyles, fonts: Array.from(fonts)};
};

const defaultPageParams = {};
const emptyProduct = undefined;
const metafieldDefinitions = [
  {
    key: 'only_few_sets_remaining',
    namespace: 'sf_only_few_sets_rem',
  },
];

const initialState = {
  product: undefined,
  variantCount: 1,
  productOptions: [],
  optionNames: [],
  selectedOptions: {},
  displayOptions: {},
  activeVariant: {},
  variantImages: [],
  productHandle: '',
  selectVariantOption: 'action',
};

function PDPReducer(state, action) {
  switch (action.type) {
    case 'SET_PRODUCT':
      const product = action.product;
      if (product) {
        const derivedData = getProductDerivedData(product);
        const defaultOptionsData = getProductDefaultVariantOptions(product);
        const variantImages = getProductImages(product);
        return {...state, ...derivedData, ...defaultOptionsData, product, variantImages};
      }
      return {...state, product};
    case 'SET_ACTIVE_VARIANT':
      const variant = action.variant;
      if (variant && state.product) {
        // const variantImages = getActiveVariantImages(state.product, variant);
        // const variantImages = getActiveVariantImages(state.product, variant);
        const {variantImageFilterByOptionName, variantImageSelectionStrategy} = action;
        const variantImages = getVariantImagesBySelectedOption(
          variantImageSelectionStrategy,
          variant,
          state.product,
          variant?.variantOptions,
          variantImageFilterByOptionName,
        );
        return {...state, activeVariant: variant, variantImages, variantSet: true, variantImagesReady: true};
      }
      return state;
    default:
      return state;
  }
}

const RforrabbitPDP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const [isLoading, setIsLoading] = useState(false);

  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const primaryButtonTheme = themeEvaluator('tile.button.primary');
  const textColor = themeEvaluator('colors.onBackground');
  const primaryColor = themeEvaluator('colors.primary');
  const secondaryColor = themeEvaluator('colors.secondary');
  const mrpColor = themeEvaluator('colors.mrpColor');
  const discountColor = themeEvaluator('colors.discountColor');
  const outlineButtonTheme = themeEvaluator('tile.button.outline');
  const {color: outlineButtonTextColor} = outlineButtonTheme;

  const {
    typography: primaryButtonTypo,
    color: primaryButtonTextColor,
    disabledColor,
    disabledBackgroundColor,
    backgroundColor,
    ...buttonStyles
  } = primaryButtonTheme;
  const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  useEffect(() => {
    const syncingCartStatus = ShopifyDSModel?.get('syncingCartStatus');
    setIsLoading(syncingCartStatus);
  }, [ShopifyDSModel]);

  const OrderLimiterDSModel = useSelector(orderLimiterModelSel);
  const orderLimiterPluginId = OrderLimiterDSModel?.get('id');
  const OrderLimiterDSConfig = orderLimiterPluginId
    ? useSelector(state => selectPluginConfig(state, null, orderLimiterPluginId))
    : null;

  const RecurpayDSModel = useSelector(recurpayModelSel);
  const RecurpayPluginId = RecurpayDSModel?.get('id');
  const RecurpayDSConfig = RecurpayPluginId
    ? useSelector(state => selectPluginConfig(state, null, RecurpayPluginId))
    : null;

  const [product, setProduct] = useState({});
  const productCacheByHandle = useSelector(shopifyProductCacheSel);
  const PDPModelSel = useRef(makePDPHelperModelSel(pageKey));
  const PDPHelperModel = useSelector(PDPModelSel.current);
  const [PDP, PDPDispatch] = useReducer(PDPReducer, initialState);

  const BuyNowProductsSel = useRef(BuyNowProductsStateModelSel(pageKey));
  const BuyNowProductsModel = useSelector(BuyNowProductsSel.current);

  const [cartItems, setCartItems] = useState([]);
  const [isInCart, setIsInCart] = useState(false);
  const [cartQuantity, setCartQuantity] = useState(0);
  const [initEmbed, setInitEmbed] = useState(Platform.OS === 'web');

  const [variantImagesReady, setVariantImagesReady] = useState(false);

  /******************************
   * Recurpay Subscriptions
   *******************************/
  const [subscribeQuantity, setSubscribeQuantity] = useState(1);
  const [isCheckoutReady, setIsCheckoutReady] = useState(false);
  const [orderType, setOrderType] = useState('');
  const [selectedPlan, setSelectedPlan] = useState({
    plan_id: '',
    selling_plan_id: '',
    selling_plan_name: '',
    description: '',
    status: '',
    discount_type: 'percentage',
    discount_value: 0,
    frequency: '',
    interval: '',
  });

  const currentorderTyppe = PDPHelperModel?.get('orderType');
  const currentActiveSellingPlan = PDPHelperModel?.get('activeSellingPlan');
  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const currentAspectRatio = Number(pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY) ?? '1');
  const pdpImageResize = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY) ?? 'cover';
  const pdpHideWishlist = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius = Number(pdpSettings.getSettingValue(PDP_BORDER_RADIUS) ?? '2');

  const currProduct =
    (PDPHelperModel?.get('product') || (productCacheByHandle && productCacheByHandle[params.productHandle])) ??
    emptyProduct;

  const currVariant =
    PDPHelperModel?.get('activeVariant') ||
    (productCacheByHandle &&
      productCacheByHandle[params.productHandle] &&
      productCacheByHandle[params.productHandle]?.activeVariant);

  useEffect(() => {
    if (product !== currProduct && !_.isEmpty(currProduct)) {
      setProduct(currProduct);
      const ratingRawValue = _.round(currProduct?.metafields?.[0]?.value?.value, 2);
      setRatingVal(ratingRawValue);
      setRatingCount(currProduct?.metafields[1]?.value);

      // setProductLabel1(currProduct?.metafields[20]?.value);
      // setProductLabel2(currProduct?.metafields[21]?.value);

      PDPDispatch({type: 'SET_PRODUCT', product: currProduct});
      if (currProduct?.title) navigation.setOptions({title: currProduct?.title});
    }
  }, [currProduct, navigation, product]);

  //*** To fix the lag in variant images in PDP ***/
  useEffect(() => {
    if (PDP.activeVariant !== currVariant && !_.isEmpty(currVariant)) {
      const variantImageFilterByOptionName = PDPHelperModel?.get('variantImageFilterByOptionName');
      const variantImageSelectionStrategy = PDPHelperModel?.get('variantImageSelectionStrategy');

      PDPDispatch({
        type: 'SET_ACTIVE_VARIANT',
        variant: currVariant,
        variantImageFilterByOptionName: variantImageFilterByOptionName,
        variantImageSelectionStrategy: variantImageSelectionStrategy,
      });
      setVariantImagesReady(true);
    }
  }, [PDP.activeVariant, currVariant, PDPHelperModel]);
  //*****************************//

  useEffect(() => {
    if (orderType !== currentorderTyppe) {
      const changedordertype = PDPHelperModel?.get('orderType');
      setOrderType(changedordertype);
    }
    if (selectedPlan?.plan_id !== currentActiveSellingPlan?.plan_id) {
      const currentselectedPlan = PDPHelperModel?.get('activeSellingPlan');
      setSelectedPlan(currentselectedPlan);
    }
    if (PDP.activeVariant !== currVariant && !_.isEmpty(currVariant)) {
      const variantImageFilterByOptionName = PDPHelperModel?.get('variantImageFilterByOptionName');
      const variantImageSelectionStrategy = PDPHelperModel?.get('variantImageSelectionStrategy');

      PDPDispatch({
        type: 'SET_ACTIVE_VARIANT',
        variant: currVariant,
        variantImageFilterByOptionName: variantImageFilterByOptionName,
        variantImageSelectionStrategy: variantImageSelectionStrategy,
      });
      setVariantImagesReady(true);
    }
  }, [PDP.activeVariant, currVariant, PDPHelperModel]);

  //*** To fix the lag in variant images in PDP ***/
  useEffect(() => {
    if (PDP.activeVariant) {
      const variantImageFilterByOptionName = PDPHelperModel?.get('variantImageFilterByOptionName');
      const variantImageSelectionStrategy = PDPHelperModel?.get('variantImageSelectionStrategy');

      PDPDispatch({
        type: 'SET_VARIANT_IMAGES',
        variant: PDP.activeVariant,
        variantImageFilterByOptionName: variantImageFilterByOptionName,
        variantImageSelectionStrategy: variantImageSelectionStrategy,
      });
    }
  }, [PDP.activeVariant, PDPHelperModel]);

  useEffect(() => {
    if (PDP.variantSet || !_.isEmpty(currVariant)) {
      setVariantImagesReady(true);
    }
  }, [PDP.variantSet, currVariant]);
  //*****************************//

  /****************************************
   * Handle Metafields.
   ***********************************/

  const [metafields, setMetaFields] = useState([]);
  const [atcText, setAtcText] = useState('');
  const [ratingVal, setRatingVal] = useState('');
  const [ratingCount, setRatingCount] = useState('');

  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchMetaFields = async () => {
      const queryResponse = await queryRunner.runQuery('query', ProductGqls.GET_PRODUCT_METAFIELDS_BY_HANDLE, {
        productHandle: params.productHandle,
        identifiers: metafieldDefinitions,
      });
      setMetaFields(_.get(queryResponse?.data, ['productByHandle', 'metafields']));
      setInitEmbed(true);
    };
    fetchMetaFields();
    return () => {};
  }, []);
  useEffect(() => {
    if (!_.isEmpty(metafields) && _.isArray(metafields)) {
      setAtcText(_.find(metafields, m => m?.key === 'only_few_sets_remaining')?.value);
      // setProductLabel1(metafields[13]?.value);
      // setProductLabel2(metafields[14]?.value);
    }
  }, [metafields]);

  /****************************************
   * Handle Cart.
   ***********************************/
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);
  useEffect(() => {
    const cartEntry = cartItems?.filter(cartItem => cartItem?.merchandiseId === PDP.activeVariant?.id);
    if (!_.isEmpty(cartEntry)) {
      const cq = _.first(cartEntry)?.displayQuantity;
      if (cq !== 0) setIsInCart(true);
      else setIsInCart(false);
      setCartQuantity(cq);
    } else {
      setIsInCart(false);
      setCartQuantity(0);
    }
  }, [PDP.activeVariant?.id, cartItems, ShopifyDSModel]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);
  const [isInWishlist, setIsInWishlist] = useState(false);
  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);
  useEffect(() => {
    const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id == product?.id?.split('/')?.pop());
    if (!_.isEmpty(wlEntry)) {
      setIsInWishlist(true);
    } else {
      setIsInWishlist(false);
    }
  }, [product?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const storeCurrency = useSelector(shopifyStoreCurrencySel);

  const handleCheakoutReady = () => {
    setIsCheckoutReady(true);
  };

  const handleIncreaseSubscribeQuantity = () => {
    setSubscribeQuantity(prevQuantity => prevQuantity + 1);
  };

  const handleDecreaseSubscribeQuantity = () => {
    setSubscribeQuantity(prevQuantity => {
      if (prevQuantity > 1) {
        return prevQuantity - 1;
      }
      setIsCheckoutReady(false);
      return prevQuantity;
    });
  };

  const initiateCheckout = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      const customertoken = ShopifyDSModel?.get('loggedInUserAccessToken');
      dispatch(
        triggerAction({
          pluginConfig: RecurpayDSConfig,
          pluginModel: RecurpayDSModel,
          pluginSelector: ['recurpay'],

          eventModelJS: {
            value: 'initiateCheckout',
            params: {
              lineItems: [
                {
                  quantity: subscribeQuantity,
                  variant_id: PDP.activeVariant.id.split('/').pop(),
                  properties: [{name: 'Recurpay', value: 'Subscription'}],
                  plan: {id: selectedPlan?.plan_id},
                },
              ],
              note: '',
              customerAccessToken: customertoken ? customertoken.accessToken : '',
              loggedInNavigateTo: 'RecurpayCheckout',
              nonLoggedInNavigateTo: 'Login',
            },
          },
        }),
      );
    }
  }, [
    PDP.activeVariant,
    ShopifyDSConfig,
    ShopifyDSModel,
    dispatch,
    storeCurrency,
    RecurpayDSConfig,
    RecurpayDSModel,
    selectedPlan,
    orderType,
    subscribeQuantity,
  ]);

  const addToCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      const increaseCartLineItemActionMeta = {
        pluginConfig: orderLimiterPluginId ? OrderLimiterDSConfig : ShopifyDSConfig,
        pluginModel: orderLimiterPluginId ? OrderLimiterDSModel : ShopifyDSModel,
        pluginSelector: orderLimiterPluginId ? [orderLimiterPluginId] : ['shopify'],
      };

      dispatch(
        triggerAction({
          pluginConfig: increaseCartLineItemActionMeta.pluginConfig,
          pluginModel: increaseCartLineItemActionMeta.pluginModel,
          pluginSelector: increaseCartLineItemActionMeta.pluginSelector,
          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
              itemPrice: PDP.activeVariant.salePrice,
              successToastText: 'Product added to Cart',
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP.activeVariant.salePrice,
          productId: PDP.product.id.split('/').pop(),
          productType: PDP.product.productType,
          quantity: 1,
          title: PDP.product.title,
          variantId: PDP.activeVariant.id.split('ProductVariant/')[1],
          variantTitle: PDP.activeVariant.title,
        }),
      );

      performHapticFeedback('tap');
      // toast.show('Product added to Cart', {
      //   type: 'success',
      //   placement: 'bottom',
      //   duration: 2000,
      //   style: {marginBottom: 80},
      // });
    }
  }, [
    PDP.activeVariant,
    ShopifyDSConfig,
    ShopifyDSModel,
    orderLimiterPluginId,
    OrderLimiterDSConfig,
    OrderLimiterDSModel,
    dispatch,
    storeCurrency,
  ]);

  const increaseCartCallback = useCallback(() => {
    const increaseCartLineItemActionMeta = {
      pluginConfig: orderLimiterPluginId ? OrderLimiterDSConfig : ShopifyDSConfig,
      pluginModel: orderLimiterPluginId ? OrderLimiterDSModel : ShopifyDSModel,
      pluginSelector: orderLimiterPluginId ? [orderLimiterPluginId] : ['shopify'],
    };
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: increaseCartLineItemActionMeta.pluginConfig,
          pluginModel: increaseCartLineItemActionMeta.pluginModel,
          pluginSelector: increaseCartLineItemActionMeta.pluginSelector,

          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
              itemPrice: PDP.activeVariant.salePrice,
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP.activeVariant.salePrice,
          productId: PDP.product.id.split('/').pop(),
          productType: PDP.product.productType,
          quantity: 1,
          title: PDP.product.title,
          variantId: PDP.activeVariant.id.split('ProductVariant/')[1],
          variantTitle: PDP.activeVariant.title,
        }),
      );

      performHapticFeedback('tick');
    }
  }, [
    PDP.activeVariant,
    ShopifyDSConfig,
    ShopifyDSModel,
    orderLimiterPluginId,
    OrderLimiterDSConfig,
    OrderLimiterDSModel,
    dispatch,
    storeCurrency,
  ]);
  const decreaseCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'decreaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
            },
          },
        }),
      );
      performHapticFeedback('tick');
    }
  }, [PDP.activeVariant, ShopifyDSConfig, ShopifyDSModel, dispatch]);

  const goToCartCallback = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const addToWishList = useCallback(() => {
    const productObj = getWishListItemFromProduct(product);
    if (productObj) {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'addProductToWishlist',
            params: {
              productId: product?.id,
              productHandle: product?.handle,
            },
          },
        }),
      );
      setIsInWishlist(true);
      performHapticFeedback('tap');
      toast.show('Product added to Wishlist', {
        type: 'normal',
        placement: 'bottom',
        duration: 2000,
        style: {marginTop: 20},
      });
      dispatch(
        sendAnalyticsEvent('track', 'addToWishlist', {
          productId: PDP?.product?.id?.split('/')?.pop(),
          productHandle: PDP?.product?.handle,
          productType: PDP?.product?.productType,
          currency: storeCurrency,
          price: PDP?.activeVariant?.salePrice,
          quantity: 1,
          title: PDP?.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP?.product?.vendor,
        }),
      );
    }
  }, [LWConfig, LWModel, dispatch, product]);

  const removeFromWishList = useCallback(() => {
    dispatch(
      triggerAction({
        pluginConfig: LWConfig,
        pluginModel: LWModel,
        pluginSelector: ['localWishlist'],
        eventModelJS: {
          value: 'removeProductFromWishlist',
          params: {
            productId: product?.id,
            productHandle: product?.handle,
          },
        },
      }),
    );
    performHapticFeedback('tap');
    toast.show('Product removed from Wishlist', {
      type: 'normal',
      placement: 'bottom',
      duration: 2000,
      style: {marginTop: 20},
    });
  }, [LWConfig, LWModel, dispatch, product]);

  /****************************************
   * RichText
   ***********************************/
  const {tagsStyles, fonts: RichTextConfigFonts} = getTagStylesAndFonts(
    editableTags,
    themeEvaluator('tile.richTextV2.default'),
  );
  const ratingBg = themeEvaluator('colors.ratingBg');
  const placeholderImage = require('../../../../assets/image-placeholder.png');

  //*** To fix the lag in variant images in PDP  ***//
  const getVariantImages = useMemo(() => {
    if (PDP.variantImages && Array.isArray(PDP.variantImages)) {
      return PDP.variantImages;
    } else if (PDP.activeVariant && PDP.activeVariant.images && Array.isArray(PDP.activeVariant.images)) {
      return PDP.activeVariant.images;
    }
    return [];
  }, [PDP.variantImages, PDP.activeVariant, PDP.activeVariant?.images]);

  const transformData = useMemo(() => {
    return getVariantImages.map(url => ({
      image: typeof url === 'string' ? url : url?.src || '',
      resizeMode: pdpImageResize,
    }));
  }, [getVariantImages, PDP.activeVariant, pdpImageResize]);
  //*****************************//

  const commonIndicatorStyles = {
    indicatorTopMargin: '90%',
    indicatorInactiveSize: 6,
    indicatorActiveSize: 8,
    indicatorBorderRadius: 8,
    indicatorInactiveColor: '#bdc3c7',
    indicatorActiveColor: '#333',
  };

  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1, backgroundColor: '#fff'}} edges={['top', 'bottom', 'left', 'right']}>
        <RforrabbitHeader product={product} />
        <ApptileScrollView
          contentInsetAdjustmentBehavior="automatic"
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: '#fff', flex: 1}}
          overScrollMode="never">
          <View>
            {PDP.variantImagesReady && transformData.length > 0 ? (
              <FlatListSlider
                key={PDP.activeVariant?.id} // Add this line
                resizeMode={pdpImageResize as string}
                aspectRatio={(_.isFinite(_.toNumber(currentAspectRatio)) ? currentAspectRatio : 1) as number}
                containerWidth="100%"
                data={transformData}
                indicator={true}
                autoscroll={false}
                onPress={undefined}
                sliderBackgroundColor={'transparent'}
                commonIndicatorStyles={commonIndicatorStyles}
              />
            ) : (
              <Image style={[{width: '100%', height: 320}]} source={placeholderImage} />
            )}
            <View style={[fixedStyles.sliderButtonsContainer]}>
              {!pdpHideWishlist && (
                <>
                  {isInWishlist ? (
                    <Pressable style={[fixedStyles.sliderButtons]} onPress={removeFromWishList}>
                      <MaterialCommunityIcons size={22} name="heart" color="#333" />
                    </Pressable>
                  ) : (
                    <Pressable style={[fixedStyles.sliderButtons]} onPress={addToWishList}>
                      <MaterialCommunityIcons size={22} name="heart-outline" color="#333" />
                    </Pressable>
                  )}
                </>
              )}
            </View>
          </View>
          <View style={[fixedStyles.productDetailsContainer]}>
            <View style={[fixedStyles.titleContainer]}>
              <Text style={[subHeadingStyles, {color: textColor}]}>{product?.title}</Text>
              <RichTextDisplay
                html={atcText}
                systemFonts={Platform.OS === 'web' ? RichTextConfigFonts : Object.values(loadedFonts)}
                baseStyle={bodyStyles}
                tagsStyles={tagsStyles}
              />
            </View>

            <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <View style={[fixedStyles.pricingSection]}>
                <View style={[fixedStyles.priceContainer]}>
                  <Text style={[subHeadingStyles, fixedStyles.mainPrice]}>{PDP?.activeVariant?.displaySalePrice}</Text>
                  {PDP?.activeVariant?.displayPrice &&
                  PDP?.activeVariant?.price &&
                  PDP?.activeVariant?.displaySalePrice !== PDP?.activeVariant?.displayPrice ? (
                    <>
                      <Text
                        style={[
                          bodyStyles,
                          fixedStyles.strikeOffPrice,
                          {color: mrpColor, textDecorationLine: 'line-through'},
                        ]}>
                        {PDP?.activeVariant?.displayPrice}
                      </Text>
                      <Text
                        style={[
                          bodyStyles,
                          {
                            color: discountColor,
                            paddingLeft: 4,
                            paddingRight: 4,
                            borderRadius: 5,
                          },
                        ]}>
                        (
                        {_.round(
                          ((PDP?.activeVariant?.price - PDP?.activeVariant?.salePrice) * 100) /
                            PDP?.activeVariant?.price,
                        )}
                        % off)
                      </Text>
                    </>
                  ) : (
                    <></>
                  )}
                </View>
                <Text style={[bodyStyles]}>Inclusive of all taxes</Text>
              </View>

              {ratingVal && ratingCount ? (
                <View style={[fixedStyles.ratingSection]}>
                  <View style={[fixedStyles.ratingsContainer]}>
                    <View style={[fixedStyles.ratingsBadge, {backgroundColor: ratingBg ? ratingBg : '#CCCC'}]}>
                      <MaterialCommunityIcons size={12} name="star" color={primaryColor} />
                      <Text style={[bodyStyles, {color: primaryColor}]}>{ratingVal}</Text>
                    </View>
                    <View style={[fixedStyles.ratingsCount]}>
                      <Text style={[bodyStyles, {color: '#000', marginLeft: 4}]}>({ratingCount})</Text>
                    </View>
                  </View>
                </View>
              ) : (
                <></>
              )}
            </View>
          </View>

          {initEmbed ? <EmbeddedAppPageContainer {...screenProps} /> : <></>}
        </ApptileScrollView>
        <View style={[fixedStyles.bottomBar]}>
          {isLoading ? (
            <View style={[fixedStyles.loaderContainer]}>
              <ActivityIndicator size="large" color={primaryColor} />
            </View>
          ) : PDP.activeVariant && PDP.activeVariant?.availableForSale === false ? (
            <View style={[fixedStyles.ctaButton]}>
              <Text style={[headingStyles, {color: textColor}]}>Out of Stock</Text>
            </View>
          ) : orderType === 'buy-once' ? (
            isInCart ? (
              <View style={[fixedStyles.bottomPanel]}>
                <View
                  style={[
                    fixedStyles.bottomControls,
                    fixedStyles.quantityControlBar,
                    {margin: 4, color: outlineButtonTextColor},
                  ]}>
                  <Pressable style={[fixedStyles.bottomControls]} onPress={decreaseCartCallback}>
                    <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>-</Text>
                  </Pressable>
                  <View style={[fixedStyles.bottomControls]}>
                    <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>{cartQuantity}</Text>
                  </View>
                  <Pressable style={[fixedStyles.bottomControls]} onPress={increaseCartCallback}>
                    <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>+</Text>
                  </Pressable>
                </View>
                <Pressable
                  style={[
                    fixedStyles.bottomControls,
                    buttonStyles,
                    {backgroundColor, margin: 4, borderRadius: pdpBorderRadius},
                  ]}
                  onPress={goToCartCallback}>
                  <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor, fontSize: 18}]}>
                    GO TO CART
                  </Text>
                </Pressable>
              </View>
            ) : (
              <View style={[fixedStyles.bottomPanel]}>
                <Pressable
                  style={[
                    fixedStyles.ctaButton,
                    buttonStyles,
                    {backgroundColor, margin: 4, borderRadius: pdpBorderRadius, width: '100%'},
                  ]}
                  onPress={addToCartCallback}>
                  <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor, fontSize: 18}]}>
                    ADD TO CART
                  </Text>
                </Pressable>
              </View>
            )
          ) : isCheckoutReady ? (
            <View style={[fixedStyles.bottomPanel]}>
              <View
                style={[
                  fixedStyles.bottomControls,
                  fixedStyles.quantityControlBar,
                  {margin: 4, color: outlineButtonTextColor},
                ]}>
                <Pressable style={[fixedStyles.bottomControls]} onPress={handleDecreaseSubscribeQuantity}>
                  <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>-</Text>
                </Pressable>
                <View style={[fixedStyles.bottomControls]}>
                  <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>{subscribeQuantity}</Text>
                </View>
                <Pressable style={[fixedStyles.bottomControls]} onPress={handleIncreaseSubscribeQuantity}>
                  <Text style={[primaryButtonTextStyles, {color: textColor, fontSize: 18}]}>+</Text>
                </Pressable>
              </View>
              <Pressable
                style={[
                  fixedStyles.bottomControls,
                  buttonStyles,
                  {backgroundColor, margin: 4, borderRadius: pdpBorderRadius},
                ]}
                onPress={initiateCheckout}>
                <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor, fontSize: 14}]}>
                  GO TO CHECKOUT
                </Text>
              </Pressable>
            </View>
          ) : (
            <View style={[fixedStyles.bottomPanel]}>
              <Pressable
                style={[
                  fixedStyles.ctaButton,
                  buttonStyles,
                  {backgroundColor, margin: 4, borderRadius: pdpBorderRadius, width: '100%'},
                ]}
                onPress={handleCheakoutReady}>
                <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor, fontSize: 18}]}>
                  SUBSCRIBE NOW
                </Text>
              </Pressable>
            </View>
          )}
        </View>
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const fixedStyles = StyleSheet.create({
  titleContainer: {
    paddingVertical: 8,
  },
  productDetailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'column',
  },
  productLabelContainer: {
    width: 'auto',
    paddingHorizontal: 12,
    paddingVertical: 4,
    flexDirection: 'row',
    borderRadius: 20,
    backgroundColor: '#FD487333',
    alignItems: 'center',
  },
  productLabelText: {
    fontSize: 14,
    textTransform: 'uppercase',
    color: '#FD4873',
  },
  pricingSection: {
    paddingTop: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 18,
    lineHeight: 24,
    color: '#000',
  },
  strikeOffPrice: {
    marginHorizontal: 4,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingSection: {
    flexDirection: 'row',
    paddingBottom: 8,
    paddingTop: 8,
  },
  ratingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingsBadge: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    alignItems: 'center',
  },
  ratingsCount: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderColor: '#0003',
    alignItems: 'center',
  },
  benefitsSectionContainer: {
    backgroundColor: 'rgba(255, 193, 5, 0.10)',
    paddingHorizontal: 12,
    paddingBottom: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: 'rgba(255, 193, 5, 0.50)',
    borderRadius: 24,
    marginTop: 38,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  benefitsHeadingContainer: {
    backgroundColor: '#FFC105',
    alignItems: 'center',
    height: 24,
    paddingHorizontal: 12,
    borderRadius: 14,
    position: 'relative',
    top: -12,
    flexBasis: 'auto',
    flexGrow: 0,
    width: 'auto',
  },
  detailsSectionContainer: {
    paddingHorizontal: 12,
  },
  detailTextContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  carouselContainer: {
    marginVertical: 8,
    paddingVertical: 4,
    backgroundColor: '#fff',
  },
  carouselTitleText: {
    marginVertical: 8,
    marginHorizontal: 12,
  },
  bottomBar: {
    backgroundColor: '#fff',
    height: 80,
    flexBasis: 80,
    flex: 0,
    borderWidth: 1,
    borderColor: '#0001',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 14,
    overflow: 'hidden',
  },
  ctaButton: {
    width: '108%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
  bottomControls: {
    flex: 1,
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityControlBar: {
    borderWidth: 1,
    borderColor: '#000000',
    // borderRadius: 6,
  },
  sliderButtonsContainer: {
    position: 'absolute',
    bottom: 36,
    right: 16,
  },
  sliderButtons: {
    flex: 0,
    borderRadius: 50,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    padding: 12,
  },
});

export default registerNativePage('RforrabbitPDP', gestureHandlerRootHOC(RforrabbitPDP), {}, {});
