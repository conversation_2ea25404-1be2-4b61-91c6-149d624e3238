import {navigateToScreen, triggerAction} from 'apptile-core';
import {useFocusEffect} from '@react-navigation/native';
import _ from 'lodash';
import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, Pressable, StyleSheet, Text, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';

import {
  createDeepEqualSelector,
  useLoadedFonts,
  performHapticFeedback,
  getShadowStyle,
  selectPluginConfig,
  datasourceTypeModelSel,
  generateTypographyByPlatform,
  useTheme,
  CurrentScreenContext,
  selectAppSettingsForKey,
  PLP_CARD_HEIGHT_KEY,
  PLP_IMAGE_ASPECT_RATIO_KEY,
  PLP_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
  SettingsConfig,
  apptileSetActivePage
} from 'apptile-core';

import {activeNavigationSelector} from '@/root/app/selectors/ActiveNavigationSelectors';
import {registerNativePage} from '../../prebuilt';
import JolenePLPCollectionCarousel from './JolenePLPCollectionCarousel';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {
  TransformCollectionDetailsByHandle,
  TransformGetCollectionProductsQuery,
  TransformProductFilters,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {ImageComponent} from 'apptile-core';

const ITEM_VMARGIN = 8;
const LOADING_IMAGE_URL =
  'https://cdn.apptile.io/83529966-6875-46fc-b09e-7a51f7242238/d0e45284-3f00-4549-8b16-c0d3ddae3f1b/original.gif';

const NO_RESULT_IMAGE_URL =
  'https://cdn.apptile.io/967d0b61-ceaa-4cae-8bb7-2b9a89995966/946bc4d8-7ac7-443f-9bf6-bd91448d1700/original.jpeg';

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const defaultPageParams = {};

const initialState = {
  collection: undefined,
  products: [],
  sortKey: 'BEST_SELLING',
  reverse: false,
  collectionFilters: [],
  selectedFilters: [],
  filters: [],
  paginationDetails: {},
  queryHasNextPage: false,
  loading: false,
};

function PLPReducer(state, action) {
  switch (action.type) {
    case 'SET_COLLECTION':
      console.log('PLPReducer: SET_COLLECTION');
      const collection = action.collection;
      return {...state, collection};
    case 'FETCH_COLLECTION_PRODUCTS':
      console.log('PLPReducer: FETCH_COLLECTION_PRODUCTS');
      return {...state, loading: true};
    case 'SET_COLLECTION_PRODUCTS':
      console.log('PLPReducer: SET_COLLECTION_PRODUCTS');
      const products = action.products;
      const queryHasNextPage = action?.queryHasNextPage;
      if (products) {
        return {
          ...state,
          loading: false,
          products,
          queryHasNextPage,
          paginationDetails: {...action?.paginationDetails},
        };
      }
      return state;
    case 'SET_SORT_ORDER':
      console.log('PLPReducer: SET_SORT_ORDER');
      const {sortKey, reverse} = action;
      return {...state, sortKey, reverse};
    case 'SET_COLLECTION_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {collectionFilters} = action;
      return {...state, collectionFilters};
    case 'SET_SELECTED_FILTERS':
      console.log('PLPReducer: SET_SELECTED_FILTERS');
      const {selectedFilters} = action;
      return {...state, selectedFilters};
    case 'SET_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {filters} = action;
      return {...state, filters};
    default:
      return state;
  }
}

const JolenePLP: React.FC = screenProps => {
  const {navigation, route, screen} = screenProps;
  const dispatch = useDispatch();

  const pageParams = route.params ?? defaultPageParams;
  const pageId = screen?.screen;
  const pageKey = route.key;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const ITEM_HEIGHT = Number(plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY) ?? '350');
  const NUM_COLUMNS = Number(plpSettings.getSettingValue(PLP_NUM_COLS_KEY) ?? '2');
  const IMAGE_ASPECT_RATIO = Number(plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY) ?? '0.6');

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const productMetafields = ShopifyDSModel?.get('productMetafields', []);
  const variantMetafields = ShopifyDSModel?.get('variantMetafields', []);
  const collectionMetafields = ShopifyDSModel?.get('collectionMetafields', []);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);
  const [PLP, PLPDispatch] = useReducer(PLPReducer, initialState);

  const [lookImages, setLookImages] = useState<any[]>([]);
  const [lookCollections, setLookCollections] = useState<any[]>([]);

  /****************************************
   * Handle Collection Details Fetch.
   ***********************************/
  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollDetails = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_BY_HANDLE, {
        collectionHandle: params.collectionHandle,
        collectionMetafields,
      });
      var {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformCollectionDetailsByHandle},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      navigation.setOptions({title: transformedData?.title});
      PLPDispatch({type: 'SET_COLLECTION', collection: transformedData});

      const collectionLookImages = _.filter(
        transformedData?.metafields,
        mField => mField?.namespace === 'apptile_collection_look',
      );

      setLookImages(collectionLookImages);

      const lookCollectionsMetaFields = _.filter(
        transformedData?.metafields,
        mField => mField?.namespace === 'apptile_look_collection',
      );

      setLookCollections(lookCollectionsMetaFields);
    };
    fetchCollDetails();
    return () => {};
  }, [ShopifyDSModel, collectionMetafields, navigation, params.collectionHandle]);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/
  const fetchProducts = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProducts = async () => {
      PLPDispatch({
        type: 'FETCH_COLLECTION_PRODUCTS',
      });
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        filters: PLP.filters,
        first: 12,
      });
      var {transformedData, queryHasNextPage, paginationDetails} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {
          transformer: TransformGetCollectionProductsQuery,
          isPaginated: true,
          paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
            const {after} = paginationMeta;
            return {...inputVariables, after};
          },
        },
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      PLPDispatch({type: 'SET_COLLECTION_PRODUCTS', products: transformedData, paginationDetails, queryHasNextPage});
    };
    fetchCollProducts();
  }, [
    PLP.filters,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);
  useEffect(() => {
    fetchProducts();
    return () => {};
  }, [fetchProducts]);

  useEffect(() => {
    fetchProducts();
    return () => {};
  }, [PLP.sortKey, PLP.reverse, PLP.filters, fetchProducts]);

  const getNextPage = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');

    const fetchCollProductsNextPage = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        first: 24,
        after: PLP.paginationDetails?.after,
      });
      var {transformedData, queryHasNextPage, paginationDetails} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {
          transformer: TransformGetCollectionProductsQuery,
          isPaginated: true,
          paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
            const {after} = paginationMeta;
            return {...inputVariables, after};
          },
        },
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      PLPDispatch({
        type: 'SET_COLLECTION_PRODUCTS',
        products: PLP.products?.concat(transformedData),
        paginationDetails,
        queryHasNextPage,
      });
    };
    if (PLP.queryHasNextPage) {
      PLPDispatch({
        type: 'FETCH_COLLECTION_PRODUCTS',
      });
      fetchCollProductsNextPage();
    }
    return () => {};
  }, [
    PLP.paginationDetails?.after,
    PLP.products,
    PLP.queryHasNextPage,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/

  // /****************************************
  //  * Handle Wishlist.
  //  ***********************************/
  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);

  /******************************
   * CALLBACKS
   *******************************/

  const addToCartCallback = useCallback(
    variandId => {
      if (variandId) {
        dispatch(
          triggerAction({
            pluginConfig: ShopifyDSConfig,
            pluginModel: ShopifyDSModel,
            pluginSelector: ['shopify'],
            eventModelJS: {
              value: 'increaseCartLineItemQuantity',
              params: {
                merchandiseId: variandId,
                quantity: 1,
                syncWithShopify: false,
                sellingPlanId: null,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Cart', {
          type: 'success',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 10},
        });
      }
    },
    [ShopifyDSConfig, ShopifyDSModel, dispatch],
  );

  const goToProduct = useCallback(
    handle => {
      if (handle) {
        performHapticFeedback('tap');
        dispatch(navigateToScreen('Product', {productHandle: handle}));
      }
    },
    [dispatch],
  );

  const addToWishList = useCallback(
    product => {
      const productObj = getWishListItemFromProduct(product);
      if (productObj) {
        dispatch(
          triggerAction({
            pluginConfig: LWConfig,
            pluginModel: LWModel,
            pluginSelector: ['localWishlist'],
            eventModelJS: {
              value: 'addProductToWishlist',
              params: {
                productObj,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Wishlist', {
          type: 'normal',
          placement: 'top',
          duration: 2000,
          style: {marginTop: 20},
        });
      }
    },
    [LWConfig, LWModel, dispatch],
  );

  const removeFromWishList = useCallback(
    product => {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'removeProductFromWishlist',
            params: {
              productId: product?.id,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product removed from Wishlist', {
        type: 'normal',
        placement: 'top',
        duration: 2000,
        style: {marginTop: 20},
      });
    },
    [LWConfig, LWModel, dispatch],
  );

  /******************************
   * ListView Methods
   *******************************/

  const onEndReached = useCallback(() => {
    console.log('PLP END REACHED');
    getNextPage();
  }, [getNextPage]);
  const renderItem = useCallback(
    ({item, index}) => {
      logger.info(`ListView RenderItem ${index}`);
      return (
        <ItemComponent
          {...{
            item,
            index,
            lookImages: lookImages,
            lookCollections: lookCollections,
            onProductClick: goToProduct,
            onAddToCart: addToCartCallback,
            onAddToWishlist: addToWishList,
            onRemoveFromWishlist: removeFromWishList,
            imageAspectRatio: IMAGE_ASPECT_RATIO,
            itemHeight: ITEM_HEIGHT,
            noOfCols: NUM_COLUMNS,
          }}
        />
      );
    },
    [
      lookImages,
      lookCollections,
      goToProduct,
      addToCartCallback,
      addToWishList,
      removeFromWishList,
      IMAGE_ASPECT_RATIO,
      ITEM_HEIGHT,
      NUM_COLUMNS,
    ],
  );
  const getItemLayout = useCallback((data, index) => {
    const totalHeight = ITEM_HEIGHT + 2 * ITEM_VMARGIN;
    const layout = {
      length: totalHeight,
      offset: totalHeight * Math.floor(index / NUM_COLUMNS),
      index,
    };
    return layout;
  }, []);

  const collectionFeaturedImage = _.find(
    PLP.collection?.metafields,
    mField => mField?.key === 'collection_featured_image',
  )?.value;

  function groupElements(data) {
    const chunkSize = NUM_COLUMNS;
    const newArray = [];
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      newArray.push(chunk);
    }
    return newArray;
  }

  const groupedProducts = groupElements(PLP.products);

  const focused = useRef(false);
  const pageBootFocusCallback = useCallback(() => {
    const doFocusAsync = async () => {
      logger.info('[LIFECYCLE] Set ACTIVE PAGE');
      setTimeout(() => dispatch(apptileSetActivePage(pageKey, pageId)), 0);
      focused.current = true;
    };
    if (!focused.current) doFocusAsync();
    return () => {
      focused.current = false;
    };
  }, [dispatch, pageId, pageKey]);
  useFocusEffect(pageBootFocusCallback);
  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={fixedStyles.safeAreaInset} edges={['bottom', 'left', 'right']}>
        {PLP.loading && !PLP.products.length ? (
          <View style={fixedStyles.loadingImageCont}>
            <ImageComponent
              style={[fixedStyles.loadingImage]}
              source={{
                uri: LOADING_IMAGE_URL,
              }}
              resizeMode={'contain'}
            />
          </View>
        ) : (
          <FlatList
            key={NUM_COLUMNS}
            disableVirtualization={true}
            style={[{flex: 1}]}
            scrollEnabled={true}
            horizontal={false}
            initialScrollIndex={0}
            numColumns={1}
            height="100%"
            width="100%"
            data={groupedProducts}
            extraData={PLP.products?.length + PLP.sortKey + PLP.reverse}
            ListHeaderComponent={() => {
              return collectionFeaturedImage && PLP.products.length > 0 ? (
                <View style={[fixedStyles.featuredImageBox, fixedStyles.verticalSpacing, {marginTop: 0}]}>
                  <ImageComponent
                    style={[fixedStyles.collectionFeaturedImage]}
                    source={{
                      uri: collectionFeaturedImage,
                    }}
                    resizeMode={'cover'}
                  />
                  <Text
                    style={[
                      headingStyles,
                      fixedStyles.editorialHeadingSpacing,
                      fixedStyles.verticalSpacing,
                      fixedStyles.collectionTitle,
                      {paddingTop: 12, paddingBottom: 12},
                    ]}>
                    {PLP.collection?.title}
                  </Text>
                </View>
              ) : null;
            }}
            ListFooterComponent={() => {
              return PLP?.loading ? (
                <View style={[fixedStyles.pageLoadingIndicator]}>
                  <ActivityIndicator size={28} />
                </View>
              ) : (
                <></>
              );
            }}
            getItemLayout={getItemLayout}
            ListEmptyComponent={
              <View style={[fixedStyles.NoResultCont]}>
                <ImageComponent
                  style={[fixedStyles.noResultImage]}
                  source={{
                    uri: NO_RESULT_IMAGE_URL,
                  }}
                  resizeMode="cover"
                />
              </View>
            }
            keyExtractor={item => item?.id}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            onEndReachedThreshold={2}
            onEndReached={onEndReached}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            initialNumToRender={10}
            windowSize={11}
            scrollEventThrottle={16}
          />
        )}
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const ItemComponent = React.memo(props => {
  const {
    item: itemList,
    index,
    onProductClick,
    onAddToCart,
    onAddToWishlist,
    onRemoveFromWishlist,
    lookImages,
    lookCollections,
    imageAspectRatio,
    itemHeight,
    noOfCols,
  } = props as any;

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const textColor = themeEvaluator('colors.onBackground');

  const currentLookIndex = index / (6 / noOfCols);
  const currentLookImage = _.get(lookImages, currentLookIndex);
  const currentLookCollection = _.get(lookCollections, currentLookIndex);
  const productItemStyle = {
    width: `${noOfCols == '2' ? 48 : 100}%`,
    height: itemHeight,
    maxHeight: itemHeight,
    overflow: 'hidden',
    marginVertical: ITEM_VMARGIN,
    backgroundColor: 'white',
  };

  return (
    <>
      {index % (6 / noOfCols) === 0 && (
        <View style={[fixedStyles.lookImageBox]}>
          {currentLookImage?.value ? (
            <View style={[fixedStyles.verticalSpacing, {marginBottom: 16}]}>
              <ImageComponent
                style={[fixedStyles.lookImageStyle]}
                source={{
                  uri: currentLookImage.value,
                }}
                resizeMode={'cover'}
              />
            </View>
          ) : (
            <></>
          )}

          {currentLookCollection?.value ? (
            <>
              <JolenePLPCollectionCarousel
                {...{
                  collectionId: currentLookCollection?.value,
                  onProductClick,
                  onAddToCart,
                  onAddToWishlist,
                  onRemoveFromWishlist,
                }}
              />
            </>
          ) : (
            <></>
          )}
        </View>
      )}
      <View style={[fixedStyles.pairedProductCont]}>
        {itemList.map(item => (
          <View style={[productItemStyle]}>
            <Pressable style={[{flex: 1}]} onPress={() => onProductClick(item?.handle)}>
              <ImageComponent
                style={[fixedStyles.productImage, {aspectRatio: imageAspectRatio}]}
                source={{
                  uri: item?.featuredImage,
                }}
                resizeMode="cover"
              />
              <View style={[fixedStyles.detailsContainer]}>
                <Text
                  style={[bodyStyles, {color: textColor}, fixedStyles.productTitle]}
                  numberOfLines={1}
                  minimumFontScale={1}>
                  {item?.title}
                </Text>
                <View style={[fixedStyles.priceContainer]}>
                  <Text style={[bodyStyles, fixedStyles.mainPrice]}>{item?.displayMinSalePrice}</Text>
                </View>
              </View>
            </Pressable>
          </View>
        ))}
      </View>
    </>
  );
});

const fixedStyles = StyleSheet.create({
  verticalSpacing: {marginTop: 4, marginBottom: 0},
  lookImageBox: {padding: 0},
  editorialHeadingSpacing: {
    padding: 0,
  },
  featuredImageBox: {flex: 1},
  detailsContainer: {
    flex: 1,
    marginTop: 8,
  },
  ratingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  ratingStarStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#50ac0a',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 4,
  },
  mainPrice: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  strikeOffPrice: {
    fontSize: 10,
    marginHorizontal: 4,
  },
  ctaActionsContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  wishlistButton: {
    margin: 4,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
  },
  bottomBar: {
    height: 68,
    flexBasis: 68,
    flex: 0,
    padding: 0,
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
    paddingLeft: 12,
    paddingRight: 12,
    paddingBottom: 20,
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControlsIcon: {fontSize: 24, paddingRight: 4},
  sortModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  sortModalButtons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    margin: 12,
  },
  filterModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  filterEntryContainer: {
    flex: 1,
    paddingVertical: 4,
    marginBottom: 8,
  },
  filterTitleText: {
    fontSize: 16,
    lineHeight: 20,
    paddingTop: 8,
    paddingBottom: 8,
  },
  filterValuesContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 4,
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  filterValueButtons: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 4,
  },
  NoResultCont: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  loadingImage: {width: '100%', height: '100%'},
  loadingImageCont: {flex: 1},
  safeAreaInset: {flex: 1, paddingHorizontal: 16},
  collectionFeaturedImage: {width: '100%', aspectRatio: 1},
  pageLoadingIndicator: {width: '100%', minHeight: 100, alignItems: 'center', justifyContent: 'center'},
  collectionTitle: {textAlignVertical: 'center'},
  noResultImage: {width: '70%', aspectRatio: 1},
  lookImageStyle: {width: '100%', aspectRatio: 0.65, color: '#e5e5e5'},
  pairedProductCont: {flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignContent: 'space-between'},
  productImage: {width: '100%'},
  productTitle: {fontSize: 14, lineHeight: 18},
});

export default registerNativePage('JolenePLP', JolenePLP, {}, {});
