import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable, FlatList, ScrollView} from 'react-native';
import {useDispatch, useSelector, useStore} from 'react-redux';
import _ from 'lodash';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';

import {
  CurrentScreenContext, 
  setNavigationContextForApp,
  datasourceTypeModelSel,
  useTheme,
  generateTypographyByPlatform,
  useLoadedFonts,
  selectPluginConfig,
  triggerAction, 
  sendAnalyticsEvent,
  navigateToScreen,
  createDeepEqualSelector,
  MaterialCommunityIcons,
  performHapticFeedback,
  selectAppSettingsForKey,
  PLP_CARD_HEIGHT_KEY,
  PLP_IMAGE_ASPECT_RATIO_KEY,
  PLP_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
  SettingsConfig,
 getShadowStyle,
 CustomModal,
 apptileSetActivePage
} from 'apptile-core';

import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {ImageComponent} from 'apptile-core';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {
  TransformCollectionDetailsByHandle,
  TransformGetCollectionProductsQuery,
  TransformProductFilters,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';


import {registerNativePage} from '../../prebuilt';
import PilgrimHeader from '../../custom/Pilgrim/PilgrimHeader';

import {activeNavigationSelector} from '@/root/app/selectors/ActiveNavigationSelectors';

const ITEM_VMARGIN = 10;

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const orderLimiterModelSel = state => datasourceTypeModelSel(state, 'orderLimiter');

const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, (shopifyDS): string | unknown => {
  return shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']);
});
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const defaultPageParams = {};
const emptyProduct = undefined;
const metafieldDefinitions = [
  {key: 'g_bestsellertag', namespace: 'custom'},
  {key: 'after_atc_test_ingrdients', namespace: 'my_fields'},
  {key: 'after_atc_single_line_text', namespace: 'my_fields'},
  {key: 'rich_text_skin_type_batc', namespace: 'my_fields'},
  {key: 'product_size', namespace: 'my_fields'},
  {key: 'ingredients1_url', namespace: 'my_fields'},
  {key: 'ingredients2_url', namespace: 'my_fields'},
  {key: 'ingredients3_url', namespace: 'my_fields'},
  {key: 'featured-product-final', namespace: 'custom'},
  {key: 'how_to_use', namespace: 'my_fields'},
  {key: 'test_benefit_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit2_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit3_url', namespace: 'my_fields'},
];

const initialState = {
  collection: undefined,
  products: [],
  sortKey: 'BEST_SELLING',
  reverse: false,
  collectionFilters: [],
  selectedFilters: [],
  filters: [],
  paginationDetails: {},
  isLoading: false,
  queryHasNextPage: false,
};

function PLPReducer(state, action) {
  switch (action.type) {
    case 'SET_COLLECTION':
      console.log('PLPReducer: SET_COLLECTION');
      const collection = action.collection;
      return {...state, collection};
    case 'SET_COLLECTION_PRODUCTS':
      console.log('PLPReducer: SET_COLLECTION_PRODUCTS');
      const products = action.products;
      const queryHasNextPage = action?.queryHasNextPage;
      if (products) {
        return {...state, products, queryHasNextPage, paginationDetails: {...action?.paginationDetails}};
      }
      return state;
    case 'SET_SORT_ORDER':
      console.log('PLPReducer: SET_SORT_ORDER');
      const {sortKey, reverse} = action;
      return {...state, sortKey, reverse};
    case 'SET_COLLECTION_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {collectionFilters} = action;
      return {...state, collectionFilters};
    case 'SET_SELECTED_FILTERS':
      console.log('PLPReducer: SET_SELECTED_FILTERS');
      const {selectedFilters} = action;
      return {...state, selectedFilters};
    case 'SET_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {filters} = action;
      return {...state, filters};
    case 'SET_LOADING':
      console.log('PLPReducer: SET_LOADING');
      const isLoading = action.isLoading;
      return {...state, isLoading};
    default:
      return state;
  }
}

const PilgrimPLP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  useEffect(() => {
    dispatch(
      sendAnalyticsEvent('page', 'pageView', {
        pageName: 'Collection',
        pageId,
        ...params,
      }),
    );
  }, [dispatch, pageId, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const primaryButtonTheme = themeEvaluator('tile.button.primary');
  const outlineButtonTheme = themeEvaluator('tile.button.outline');
  const secondaryButtonTheme = themeEvaluator('tile.button.secondary');
  const textColor = themeEvaluator('colors.onBackground');
  const primaryColor = themeEvaluator('colors.primary');
  const secondaryColor = themeEvaluator('colors.secondary');
  const shadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(10));
  const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(4));
  const {
    typography: primaryButtonTypo,
    // color,
    disabledColor,
    disabledBackgroundColor,
    // backgroundColor,
    ...buttonStyles
  } = primaryButtonTheme;
  const primaryButtonTextColor = primaryButtonTheme?.color;
  const primaryButtonStyles = _.omit(primaryButtonTheme, ['typography', 'disabledColor', 'disabledBackgroundColor']);
  const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

  const outlineButtonTextColor = outlineButtonTheme?.color;
  const outlinebuttonStyles = _.omit(outlineButtonTheme, [
    'typography',
    // 'color',
    'disabledColor',
    'disabledBackgroundColor',
    // 'backgroundColor',
  ]);
  const outlineButtonTextStyles = generateTypographyByPlatform(outlineButtonTheme?.typography, loadedFonts);

  const secondaryButtonTextColor = secondaryButtonTheme?.color;
  const secondaryButtonStyles = _.omit(secondaryButtonTheme, [
    'typography',
    // 'color',
    'disabledColor',
    'disabledBackgroundColor',
    // 'backgroundColor',
  ]);
  const secondaryButtonTextStyles = generateTypographyByPlatform(secondaryButtonTheme?.typography, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const productMetafields = ShopifyDSModel?.get('productMetafields', []);
  const variantMetafields = ShopifyDSModel?.get('variantMetafields', []);
  const collectionMetafields = ShopifyDSModel?.get('collectionMetafields', []);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  const OrderLimiterDSModel = useSelector(orderLimiterModelSel);
  const orderLimiterPluginId = OrderLimiterDSModel?.get('id');
  const OrderLimiterDSConfig = orderLimiterPluginId
    ? useSelector(state => selectPluginConfig(state, null, orderLimiterPluginId))
    : null;

  const [PLP, PLPDispatch] = useReducer(PLPReducer, initialState);
  const [showSortModal, setShowSortModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const ITEM_HEIGHT = Number(plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY) ?? '380');
  const NUM_COLUMNS = Number(plpSettings.getSettingValue(PLP_NUM_COLS_KEY) ?? '2');
  const IMAGE_ASPECT_RATIO = Number(plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY) ?? '1');

  /****************************************
   * Handle Collection Details Fetch.
   ***********************************/
  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollDetails = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_BY_HANDLE, {
        collectionHandle: params.collectionHandle,
        collectionMetafields,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {transformer: TransformCollectionDetailsByHandle},
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      navigation.setOptions({title: transformedData?.title});
      PLPDispatch({type: 'SET_COLLECTION', collection: transformedData});
    };
    fetchCollDetails();
    return () => {};
  }, []);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/
  const fetchProducts = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProducts = async () => {
      try {
        PLPDispatch({type: 'SET_LOADING', isLoading: true});
        const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
          collectionHandle: params.collectionHandle,
          productMetafields,
          variantMetafields,
          sortKey: PLP.sortKey,
          reverse: PLP.reverse,
          filters: PLP.filters,
          first: 24,
        });
        var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
          processShopifyGraphqlQueryResponse(
            queryResponse,
            {
              transformer: TransformGetCollectionProductsQuery,
              isPaginated: true,
              paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
                const {after} = paginationMeta;
                return {...inputVariables, after};
              },
            },
            ShopifyDSModel?.get('shop'),
            ShopifyDSModel,
          );
        PLPDispatch({type: 'SET_COLLECTION_PRODUCTS', products: transformedData, paginationDetails, queryHasNextPage});

        dispatch(
          sendAnalyticsEvent('track', 'collectionView', {
            listName: params.collectionHandle,
            items: _.map(transformedData ?? [], item => {
              return {
                productId: item.id,
                productHandle: item.handle,
                productType: item.productType,
                currency: storeCurrency,
                price: item.variants[0]?.salePrice,
                title: item.title,
                variantId: item.variants[0]?.id,
                variantTitle: item.variants[0]?.title,
                brand: item.vendor,
                listName: params.collectionHandle,
              };
            }),
          }),
        );

        PLPDispatch({type: 'SET_LOADING', isLoading: false});
      } catch (err) {
        PLPDispatch({type: 'SET_LOADING', isLoading: false});
      }
    };
    fetchCollProducts();
  }, [
    PLP.filters,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    dispatch,
    params.collectionHandle,
    productMetafields,
    storeCurrency,
    variantMetafields,
  ]);
  useEffect(() => {
    fetchProducts();
    return () => {};
  }, []);

  useEffect(() => {
    fetchProducts();
    return () => {};
  }, [PLP.sortKey, PLP.reverse, PLP.filters]);

  const getNextPage = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProductsNextPage = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        first: 24,
        after: PLP.paginationDetails?.after,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {
            transformer: TransformGetCollectionProductsQuery,
            isPaginated: true,
            paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
              const {after} = paginationMeta;
              return {...inputVariables, after};
            },
          },
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      PLPDispatch({
        type: 'SET_COLLECTION_PRODUCTS',
        products: PLP.products?.concat(transformedData),
        paginationDetails,
        queryHasNextPage,
      });

      dispatch(
        sendAnalyticsEvent('track', 'collectionView', {
          listName: params.collectionHandle,
          items: _.map(transformedData ?? [], item => {
            return {
              productId: item.id,
              productHandle: item.handle,
              productType: item.productType,
              currency: storeCurrency,
              price: item.variants[0]?.salePrice,
              title: item.title,
              variantId: item.variants[0]?.id,
              variantTitle: item.variants[0]?.title,
              brand: item.vendor,
              listName: params.collectionHandle,
            };
          }),
        }),
      );
    };
    if (PLP.queryHasNextPage) fetchCollProductsNextPage();
    return () => {};
  }, [
    PLP.paginationDetails?.after,
    PLP.products,
    PLP.queryHasNextPage,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    dispatch,
    params.collectionHandle,
    productMetafields,
    storeCurrency,
    variantMetafields,
  ]);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/

  const fetchFilters = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollFilters = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_PRODUCT_FILTERS, {
        collectionHandle: params.collectionHandle,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {
            transformer: TransformProductFilters,
          },
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      PLPDispatch({type: 'SET_COLLECTION_FILTERS', collectionFilters: transformedData});
    };
    fetchCollFilters();
  }, [ShopifyDSModel, params.collectionHandle]);

  const fetchCollectionFilters = useCallback(() => {
    if (_.isEmpty(PLP.collectionFilters)) fetchFilters();
  }, [PLP.collectionFilters, fetchFilters]);

  // /****************************************
  //  * Handle Wishlist.
  //  ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  const LocalWishListItems = useSelector(wishlistItemsSel);
  // const [wishlistItems, setWishlistItems] = useState([]);
  // const [isInWishlist, setIsInWishlist] = useState(false);
  // useEffect(() => {
  //   if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
  //     setWishlistItems(LocalWishListItems);
  //   }
  // }, [LocalWishListItems, wishlistItems]);
  // useEffect(() => {
  //   const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id === collection?.id);
  //   if (!_.isEmpty(wlEntry)) {
  //     setIsInWishlist(true);
  //   } else {
  //     setIsInWishlist(false);
  //   }
  // }, [collection?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/
  const storeCurrency = useSelector(shopifyStoreCurrencySel);

  const addToCartCallback = useCallback(
    (variandId, variantTitle, itemPrice, productId, productType, productTitle, vendor) => {
      if (variandId && variantTitle && itemPrice && productId && productType) {
        const increaseCartLineItemActionMeta = {
          pluginConfig: orderLimiterPluginId ? OrderLimiterDSConfig : ShopifyDSConfig,
          pluginModel: orderLimiterPluginId ? OrderLimiterDSModel : ShopifyDSModel,
          pluginSelector: orderLimiterPluginId ? [orderLimiterPluginId] : ['shopify'],
        };

        dispatch(
          triggerAction({
            pluginConfig: increaseCartLineItemActionMeta.pluginConfig,
            pluginModel: increaseCartLineItemActionMeta.pluginModel,
            pluginSelector: increaseCartLineItemActionMeta.pluginSelector,
            eventModelJS: {
              value: 'increaseCartLineItemQuantity',
              params: {
                merchandiseId: variandId,
                quantity: 1,
                syncWithShopify: true,
                sellingPlanId: null,
                itemPrice: itemPrice,
                successToastText: 'Product added to Cart',
              },
            },
          }),
        );

        dispatch(
          sendAnalyticsEvent('track', 'addToCart', {
            currency: storeCurrency,
            price: itemPrice,
            productId: productId,
            productType: productType,
            quantity: 1,
            title: productTitle,
            variantId: variandId,
            variantTitle: variantTitle,
            brand: vendor,
            listName: params.collectionHandle,
          }),
        );

        performHapticFeedback('tap');
        // toast.show('Product added to Cart', {
        //   type: 'success',
        //   placement: 'bottom',
        //   duration: 2000,
        //   style: {marginBottom: 10},
        // });
      }
    },
    [
      orderLimiterPluginId,
      OrderLimiterDSConfig,
      ShopifyDSConfig,
      OrderLimiterDSModel,
      ShopifyDSModel,
      dispatch,
      storeCurrency,
      params.collectionHandle,
    ],
  );

  const goToProduct = useCallback(
    (handle, product) => {
      if (handle) {
        performHapticFeedback('tap');
        dispatch(navigateToScreen('Product', {productHandle: handle}));

        dispatch(
          sendAnalyticsEvent('track', 'selectProduct', {
            productId: product?.id,
            productHandle: product?.handle,
            productType: product?.productType,
            currency: storeCurrency,
            price: product?.variants[0]?.salePrice,
            title: product?.title,
            variantId: product?.variants[0]?.id,
            variantTitle: product?.variants[0]?.title,
            brand: product?.vendor,
            listName: params.collectionHandle,
          }),
        );
      }
    },
    [dispatch, params.collectionHandle, storeCurrency],
  );

  const addToWishList = useCallback(
    product => {
      const productObj = getWishListItemFromProduct(product);
      if (productObj) {
        dispatch(
          triggerAction({
            pluginConfig: LWConfig,
            pluginModel: LWModel,
            pluginSelector: ['localWishlist'],
            eventModelJS: {
              value: 'addProductToWishlist',
              params: {
                productId: product?.id,
                productHandle: product?.handle,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Wishlist', {
          type: 'info',
          placement: 'top',
          duration: 2000,
          style: {marginTop: 20},
        });
        dispatch(
          sendAnalyticsEvent('track', 'addToWishlist', {
            productId: product?.id,
            productHandle: product?.handle,
            productType: product?.productType,
            currency: storeCurrency,
            price: product?.variants[0]?.salePrice,
            quantity: 1,
            title: product?.title,
            variantId: product?.variants[0]?.id,
            variantTitle: product?.variants[0]?.title,
            brand: product?.vendor,
            listName: params.collectionHandle,
          }),
        );
      }
    },
    [LWConfig, LWModel, dispatch, params.collectionHandle, storeCurrency],
  );

  const removeFromWishList = useCallback(
    product => {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'removeProductFromWishlist',
            params: {
              productId: product?.id,
              productHandle: product?.handle,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product removed from Wishlist', {
        type: 'info',
        placement: 'top',
        duration: 2000,
        style: {marginTop: 20},
      });

      dispatch(
        sendAnalyticsEvent('track', 'removeFromWishlist', {
          productId: product?.id,
          productHandle: product?.handle,
        }),
      );
    },
    [LWConfig, LWModel, dispatch],
  );

  const setSortKeys = useCallback(
    (sortKey: string, reverse: boolean = false) => {
      if (sortKey !== PLP.sortKey || reverse !== PLP.reverse) {
        PLPDispatch({type: 'SET_SORT_ORDER', sortKey, reverse});
        PLPDispatch({
          type: 'SET_COLLECTION_PRODUCTS',
          products: [],
          paginationDetails: undefined,
          queryHasNextPage: false,
        });
      }
    },
    [PLP.reverse, PLP.sortKey],
  );

  const hasSelectedFilter = useCallback(
    filterValue => {
      return !!_.find(PLP.selectedFilters, fVal => fVal.id === filterValue?.id);
    },
    [PLP.selectedFilters],
  );
  const selectFilter = useCallback(
    filterValue => {
      if (hasSelectedFilter(filterValue)) return;
      PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: PLP.selectedFilters.concat([filterValue])});
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const removeFilter = useCallback(
    filterValue => {
      if (hasSelectedFilter(filterValue)) {
        PLPDispatch({
          type: 'SET_SELECTED_FILTERS',
          selectedFilters: _.filter(PLP.selectedFilters, fVal => fVal.id !== filterValue?.id),
        });
      }
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const applyFilters = useCallback(() => {
    const filters = PLP.selectedFilters.map(fValue => {
      try {
        return JSON.parse(fValue?.input);
      } catch {
        return undefined;
      }
    });
    PLPDispatch({type: 'SET_FILTERS', filters});
  }, [PLP.selectedFilters]);

  const resetFilters = useCallback(() => {
    PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: []});
    PLPDispatch({type: 'SET_FILTERS', filters: []});
  }, []);

  /******************************
   * ListView Methods
   *******************************/

  const onEndReached = useCallback(() => {
    console.log('PLP END REACHED');
    getNextPage();
  }, [getNextPage]);

  const renderItem = useCallback(
    ({item, index}) => {
      logger.info(`ListView RenderItem ${index}`);
      return (
        <ItemComponent
          {...{
            item,
            index,
            wishlistProducts: LocalWishListItems,
            onProductClick: goToProduct,
            onAddToCart: addToCartCallback,
            onAddToWishlist: addToWishList,
            onRemoveFromWishlist: removeFromWishList,
            imageAspectRatio: IMAGE_ASPECT_RATIO,
            itemHeight: ITEM_HEIGHT,
            noOfCols: NUM_COLUMNS,
          }}
        />
      );
    },
    [
      IMAGE_ASPECT_RATIO,
      ITEM_HEIGHT,
      LocalWishListItems,
      NUM_COLUMNS,
      addToCartCallback,
      addToWishList,
      goToProduct,
      removeFromWishList,
    ],
  );
  const getItemLayout = useCallback((data, index) => {
    const totalHeight = ITEM_HEIGHT + 2 * ITEM_VMARGIN;
    const layout = {
      length: totalHeight,
      offset: totalHeight * Math.floor(index / NUM_COLUMNS),
      index,
    };
    return layout;
  }, []);

  const focused = useRef(false);
  const pageBootFocusCallback = useCallback(() => {
    const doFocusAsync = async () => {
      logger.info('[LIFECYCLE] Set ACTIVE PAGE');
      setTimeout(() => dispatch(apptileSetActivePage(pageKey, pageId)), 0);
      focused.current = true;
    };
    if (!focused.current) doFocusAsync();
    return () => {
      focused.current = false;
    };
  }, [dispatch, pageId, pageKey]);
  useFocusEffect(pageBootFocusCallback);

  let PLPRenderElement;
  if (PLP.isLoading === true) {
    PLPRenderElement = (
      <View style={{flex: 1}}>
        <ImageComponent
          style={[{width: '100%', height: '100%'}]}
          source={{
            uri: 'https://cdn.apptile.io/83529966-6875-46fc-b09e-7a51f7242238/d0e45284-3f00-4549-8b16-c0d3ddae3f1b/original.gif',
          }}
          resizeMode={'contain'}
        />
      </View>
    );
  } else if (PLP.products?.length === 0 && PLP.isLoading === false) {
    PLPRenderElement = (
      <View style={fixedStyles.noProductsContainer}>
        <Text
          style={[bodyStyles, {color: textColor, fontSize: 14, lineHeight: 18}]}
          ellipsizeMode="tail"
          numberOfLines={2}>
          No Products Found
        </Text>
      </View>
    );
  } else {
    PLPRenderElement = (
      <>
        <FlatList
          key={NUM_COLUMNS}
          disableVirtualization={true}
          style={[{flex: 1}]}
          scrollEnabled={true}
          horizontal={false}
          initialScrollIndex={0}
          numColumns={NUM_COLUMNS}
          height="100%"
          width="100%"
          data={PLP.products}
          extraData={PLP.products?.length + PLP.sortKey + PLP.reverse}
          ListHeaderComponent={() => {
            return PLP.collection?.featuredImage ? (
              <View style={[{flex: 1}]}>
                <ImageComponent
                  style={[{width: '100%', flexBasis: 120, height: 120}]}
                  source={{
                    uri: PLP.collection?.featuredImage,
                  }}
                  resizeMode={'contain'}
                />
              </View>
            ) : null;
          }}
          getItemLayout={getItemLayout}
          // ListEmptyComponent={showPlaceholder ? <EmptyPlaceholder /> : <></>}
          // overScrollMode="never"
          // onViewableItemsChanged={onViewableItemsChanged}
          // onLayout={onLayout}
          keyExtractor={item => item?.id}
          renderItem={renderItem}
          // keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={2}
          onEndReached={onEndReached}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          initialNumToRender={10}
          windowSize={11}
          scrollEventThrottle={16}
        />
        <View style={[fixedStyles.bottomBar]}>
          <View style={[fixedStyles.bottomPanel]}>
            <Pressable
              style={[secondaryButtonStyles, fixedStyles.bottomControls]}
              onPress={() => {
                setShowFilterModal(true);
                fetchCollectionFilters();
              }}>
              <Text style={[secondaryButtonTextStyles, {color: secondaryButtonTextColor}]}>Filters</Text>
            </Pressable>
            <Pressable
              style={[secondaryButtonStyles, fixedStyles.bottomControls]}
              onPress={() => {
                setShowSortModal(true);
              }}>
              <Text style={[secondaryButtonTextStyles, {color: secondaryButtonTextColor}]}>Sort By</Text>
            </Pressable>
          </View>
        </View>
        {showSortModal ? (
          <CustomModal
            position="bottom"
            onClose={() => {
              setShowSortModal(false);
            }}>
            <ScrollView style={[fixedStyles.sortModalContainer]}>
              <Pressable
                style={[
                  PLP.sortKey === 'BEST_SELLING' ? primaryButtonStyles : outlinebuttonStyles,
                  fixedStyles.sortModalButtons,
                ]}
                onPress={() => {
                  setSortKeys('BEST_SELLING');
                  setShowSortModal(false);
                }}>
                <Text
                  style={[
                    PLP.sortKey === 'BEST_SELLING' ? primaryButtonTextStyles : outlineButtonTextStyles,
                    {color: PLP.sortKey === 'BEST_SELLING' ? primaryButtonTextColor : outlineButtonTextColor},
                  ]}>
                  BestSellers
                </Text>
              </Pressable>
              <Pressable
                style={[
                  PLP.sortKey === 'CREATED' && !PLP.reverse ? primaryButtonStyles : outlinebuttonStyles,
                  fixedStyles.sortModalButtons,
                ]}
                onPress={() => {
                  setSortKeys('CREATED');
                  setShowSortModal(false);
                }}>
                <Text
                  style={[
                    PLP.sortKey === 'CREATED' ? primaryButtonTextStyles : outlineButtonTextStyles,
                    {
                      color:
                        PLP.sortKey === 'CREATED' && !PLP.reverse ? primaryButtonTextColor : outlineButtonTextColor,
                    },
                  ]}>
                  Newly added
                </Text>
              </Pressable>
              <Pressable
                style={[
                  PLP.sortKey === 'PRICE' && PLP.reverse ? primaryButtonStyles : outlinebuttonStyles,
                  fixedStyles.sortModalButtons,
                ]}
                onPress={() => {
                  setSortKeys('PRICE', true);
                  setShowSortModal(false);
                }}>
                <Text
                  style={[
                    PLP.sortKey === 'PRICE' && PLP.reverse ? primaryButtonTextStyles : outlineButtonTextStyles,
                    {
                      color: PLP.sortKey === 'PRICE' && PLP.reverse ? primaryButtonTextColor : outlineButtonTextColor,
                    },
                  ]}>
                  Price - high to low
                </Text>
              </Pressable>
              <Pressable
                style={[
                  PLP.sortKey === 'PRICE' && !PLP.reverse ? primaryButtonStyles : outlinebuttonStyles,
                  fixedStyles.sortModalButtons,
                ]}
                onPress={() => {
                  setSortKeys('PRICE');
                  setShowSortModal(false);
                }}>
                <Text
                  style={[
                    PLP.sortKey === 'PRICE' && !PLP.reverse ? primaryButtonTextStyles : outlineButtonTextStyles,
                    {
                      color: PLP.sortKey === 'PRICE' && !PLP.reverse ? primaryButtonTextColor : outlineButtonTextColor,
                    },
                  ]}>
                  Price - low to high
                </Text>
              </Pressable>
            </ScrollView>
          </CustomModal>
        ) : (
          <></>
        )}
        {showFilterModal ? (
          <CustomModal
            position="bottom"
            onClose={() => {
              setShowFilterModal(false);
            }}>
            <View style={[fixedStyles.filterModalContainer]}>
              {_.isEmpty(PLP.collectionFilters) ? (
                <></>
              ) : (
                <>
                  <ScrollView style={{maxHeight: 600}}>
                    {PLP.collectionFilters.map((collFilter, filterIndex) => {
                      if (collFilter?.type !== 'PRICE_RANGE')
                        return (
                          <View style={[fixedStyles.filterEntryContainer]} key={filterIndex}>
                            <Text
                              style={[subHeadingStyles, fixedStyles.filterTitleText, {color: primaryButtonTextColor}]}>
                              {collFilter?.label}
                            </Text>
                            <View style={[fixedStyles.filterValuesContainer]}>
                              {collFilter?.values?.map((filterValue, fValueIdx) => {
                                const isSelected = !!_.find(PLP.selectedFilters, obj => obj.id === filterValue.id);
                                return (
                                  <Pressable
                                    key={fValueIdx}
                                    style={[
                                      hasSelectedFilter(filterValue) ? primaryButtonStyles : outlinebuttonStyles,
                                      fixedStyles.filterValueButtons,
                                      smallShadowStyles,
                                    ]}
                                    onPress={() => {
                                      hasSelectedFilter(filterValue)
                                        ? removeFilter(filterValue)
                                        : selectFilter(filterValue);
                                    }}>
                                    <Text
                                      style={[
                                        isSelected ? primaryButtonTextStyles : outlineButtonTextStyles,
                                        {color: isSelected ? primaryButtonTextColor : outlineButtonTextColor},
                                      ]}>
                                      {filterValue.label}
                                    </Text>
                                  </Pressable>
                                );
                              })}
                            </View>
                          </View>
                        );
                      else return null;
                    })}
                  </ScrollView>
                  <View style={[fixedStyles.bottomPanel]}>
                    <Pressable
                      style={[fixedStyles.bottomControls, outlinebuttonStyles]}
                      onPress={() => {
                        resetFilters();
                        setShowFilterModal(false);
                      }}>
                      <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Reset</Text>
                    </Pressable>
                    <Pressable
                      style={[fixedStyles.bottomControls, primaryButtonTheme, {margin: 0}]}
                      onPress={() => {
                        applyFilters();
                        setShowFilterModal(false);
                      }}>
                      <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Apply</Text>
                    </Pressable>
                  </View>
                </>
              )}
            </View>
          </CustomModal>
        ) : (
          <></>
        )}
      </>
    );
  }

  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1}} edges={['top', 'bottom', 'left', 'right']}>
        <PilgrimHeader>
          <Text
            style={[
              headingStyles,
              {flex: 1, textAlignVertical: 'center', color: textColor, fontSize: 22, lineHeight: 24},
            ]}
            ellipsizeMode="tail"
            adjustsFontSizeToFit={true}
            minimumFontScale={0.7}
            numberOfLines={1}>
            {PLP.collection?.title}
          </Text>
        </PilgrimHeader>
        {PLPRenderElement}
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const ItemComponent = React.memo(
  ({
    item,
    index,
    onProductClick,
    onAddToCart,
    onAddToWishlist,
    onRemoveFromWishlist,
    wishlistProducts,
    imageAspectRatio,
    itemHeight,
    noOfCols,
  }) => {
    const {themeEvaluator} = useTheme();
    const {loadedFonts} = useLoadedFonts();
    const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
    const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
    const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
    const primaryButtonTheme = themeEvaluator('tile.button.primary');
    const outlineButtonTheme = themeEvaluator('tile.button.outline');
    const textColor = themeEvaluator('colors.onBackground');
    const primaryColor = themeEvaluator('colors.primary');
    const secondaryColor = themeEvaluator('colors.secondary');
    const shadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(10));
    const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(4));
    const {
      typography: primaryButtonTypo,
      color: primaryButtonTextColor,
      disabledColor,
      disabledBackgroundColor,
      backgroundColor,
      ...buttonStyles
    } = primaryButtonTheme;
    const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

    const outlinebuttonStyles = _.omit(outlineButtonTheme, [
      'typography',
      'color',
      'disabledColor',
      'disabledBackgroundColor',
      'backgroundColor',
    ]);
    const outlineButtonTextColor = outlineButtonTheme?.color;
    const outlineButtonTextStyles = generateTypographyByPlatform(outlineButtonTheme?.typography, loadedFonts);

    const [inWL, setInWL] = useState(false);
    useEffect(() => {
      if (wishlistProducts && wishlistProducts.find(wlItem => wlItem.handle === item?.handle)) {
        if (!inWL) setInWL(true);
      } else {
        if (inWL) setInWL(false);
      }
    }, [inWL, item?.handle, wishlistProducts]);

    return (
      <View
        style={[
          {
            flex: 1,
            width: `${noOfCols == '2' ? 50 : 100}%`,
            maxWidth: `${noOfCols == '2' ? 50 : 100}%`,
            height: itemHeight + 2 * ITEM_VMARGIN,
            maxHeight: itemHeight + 2 * ITEM_VMARGIN,
            backgroundColor: 'white',
          },
        ]}>
        <View
          style={[{flex: 1, marginVertical: ITEM_VMARGIN, marginHorizontal: 5, borderRadius: 12}, smallShadowStyles]}>
          <View
            style={[
              {
                flex: 1,
                overflow: 'hidden',
                borderRadius: 12,
                backgroundColor: 'white',
              },
            ]}>
            <Pressable style={[{flex: 1}]} onPress={() => onProductClick(item?.handle, item)}>
              <ImageComponent
                style={[{width: '100%', aspectRatio: imageAspectRatio}]}
                source={{
                  uri: item?.featuredImage,
                }}
                resizeMode="cover"
              />
              <View style={[fixedStyles.detailsContainer]}>
                <Text
                  style={[bodyStyles, {color: textColor, fontSize: 14, lineHeight: 18}]}
                  ellipsizeMode="tail"
                  numberOfLines={2}>
                  {item?.title}
                </Text>
                <Text style={[bodyStyles, fixedStyles.benefitsText]} ellipsizeMode="tail" numberOfLines={2}>
                  {item?.metafields[2]?.value}
                </Text>
                {item?.metafields[0]?.value?.value ? (
                  <View style={[fixedStyles.ratingContainer]}>
                    <View style={[fixedStyles.ratingBadge]}>
                      <MaterialCommunityIcons style={[fixedStyles.ratingStarStyle]} name="star" />
                      <Text style={[bodyStyles, fixedStyles.ratingStyle]}>{item?.metafields[0]?.value?.value}</Text>
                    </View>
                    <Text style={[bodyStyles, fixedStyles.ratingCount, {color: '#333'}]}>
                      ({item?.metafields[1]?.value})
                    </Text>
                  </View>
                ) : (
                  <></>
                )}
                <View style={[fixedStyles.priceContainer]}>
                  <Text style={[headingStyles, fixedStyles.mainPrice]}>{item?.displayMinSalePrice}</Text>
                  {item?.displayMinSalePrice !== item?.displayMinPrice ? (
                    <>
                      <Text
                        style={[
                          bodyStyles,
                          fixedStyles.strikeOffPrice,
                          {color: '#333', textDecorationLine: 'line-through'},
                        ]}>
                        {item?.displayMinPrice}
                      </Text>
                      <Text style={[bodyStyles, {color: '#388E3C'}]}>
                        ({_.round(((item?.minPrice - item?.minSalePrice) * 100) / item?.minPrice)}% off)
                      </Text>
                    </>
                  ) : (
                    <></>
                  )}
                </View>
              </View>
            </Pressable>
            <View style={fixedStyles.BadgeWrapper}>
              <View style={[fixedStyles.badgeContainer]}>
                {item?.metafields[20]?.value ? (
                  <View style={[fixedStyles.productBadge1]}>
                    <Text style={[bodyStyles, {color: '#27A0CF', fontSize: 12, textTransform: 'uppercase'}]}>
                      {item?.metafields[20]?.value}
                    </Text>
                  </View>
                ) : (
                  <></>
                )}
              </View>
              <View style={[fixedStyles.badgeContainer]}>
                {item?.metafields[21]?.value ? (
                  <View style={[fixedStyles.productBadge2]}>
                    <Text style={[bodyStyles, {color: '#fd4873', fontSize: 12, textTransform: 'uppercase'}]}>
                      {item?.metafields[21]?.value}
                    </Text>
                  </View>
                ) : (
                  <></>
                )}
              </View>
            </View>

            <View style={[fixedStyles.ctaActionsContainer]}>
              <Pressable
                style={[fixedStyles.wishlistButton]}
                onPress={() => {
                  inWL ? onRemoveFromWishlist(item) : onAddToWishlist(item);
                  setInWL(!inWL);
                }}>
                <MaterialCommunityIcons
                  size={24}
                  name={inWL ? 'heart' : 'heart-outline'}
                  color={inWL ? '#e65a4c' : '#333'}
                />
              </Pressable>
              {item?.availableForSale ? (
                <Pressable
                  style={[buttonStyles, fixedStyles.ctaButton, {backgroundColor}]}
                  onPress={() => {
                    onAddToCart(
                      item?.variants[0]?.id,
                      item?.variants[0]?.title,
                      item?.variants[0]?.salePrice,
                      item?.id,
                      item?.productType,
                      item?.title,
                      item?.vendor,
                    );
                  }}>
                  <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Add to Cart</Text>
                </Pressable>
              ) : (
                <Pressable style={[outlinebuttonStyles, fixedStyles.ctaButton]}>
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Out of stock</Text>
                </Pressable>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  },
);

const fixedStyles = StyleSheet.create({
  detailsContainer: {
    flex: 1,
    marginHorizontal: 10,
    marginTop: 8,
  },
  BadgeWrapper: {
    position: 'absolute',
    flex: 1,
    flexBasis: 'auto',
    top: 0,
    left: 0,
    right: 0,
  },
  badgeContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  productBadge1: {
    flexDirection: 'row',
    flexBasis: 'auto',
    width: 'auto',
    padding: 4,
    backgroundColor: '#c4e5f2',
    alignItems: 'center',
    borderBottomRightRadius: 8,
    marginBottom: 10,
  },
  productBadge2: {
    flexDirection: 'row',
    flexBasis: 'auto',
    width: 'auto',
    padding: 4,
    backgroundColor: '#f7d2db',
    alignItems: 'center',
    borderBottomRightRadius: 8,
  },
  benefitsText: {
    color: '#00000064',
    fontSize: 12,
    lineHeight: 14,
    marginTop: 4,
    overflow: 'hidden',
  },
  ratingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#388E3C',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 4,
  },
  ratingStyle: {
    fontSize: 10,
    lineHeight: 14,
    color: '#FFF',
  },
  ratingStarStyle: {
    fontSize: 10,
    lineHeight: 14,
    color: '#FFF',
  },
  ratingCount: {
    fontSize: 10,
    lineHeight: 14,
  },
  priceContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  strikeOffPrice: {
    fontSize: 10,
    marginHorizontal: 4,
  },
  ctaActionsContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  wishlistButton: {
    margin: 4,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
  },
  bottomBar: {
    height: 60,
    flexBasis: 60,
    flex: 0,
    padding: 6,
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
    paddingBottom: 0,
    paddingTop: 0,
    paddingLeft: 0,
    paddingRight: 0,
  },
  sortModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  sortModalButtons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    margin: 12,
  },
  filterModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  filterEntryContainer: {
    flex: 1,
    paddingVertical: 4,
    marginBottom: 8,
  },
  filterTitleText: {
    fontSize: 16,
    lineHeight: 20,
  },
  filterValuesContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 4,
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  filterValueButtons: {
    padding: 0,
    paddingTop: 8,
    paddingBottom: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noProductsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default registerNativePage('PilgrimPLP', PilgrimPLP, {}, {});
