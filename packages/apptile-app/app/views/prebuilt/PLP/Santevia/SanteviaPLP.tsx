import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable, FlatList} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';
import _ from 'lodash';

import {
  CurrentScreenContext,
  datasourceTypeModelSel,
  selectPluginStageModel,
  shopifyProductCacheSelector,
  useTheme,
  generateTypographyByPlatform,
  useLoadedFonts,
  selectPluginConfig,
  triggerAction,
  navigateToScreen,
  createDeepEqualSelector,
  MaterialCommunityIcons,
  performHapticFeedback,
  getShadowStyle,
  CustomModal,
  apptileSetActivePage,
  PLP_CARD_HEIGHT_KEY,
  PLP_IMAGE_ASPECT_RATIO_KEY,
  PLP_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
  selectAppSettings<PERSON><PERSON><PERSON><PERSON>,
  SettingsConfig
} from 'apptile-core';

import ApptileScrollView from '../../../screen/ApptileScrollView';
import FlatListSlider from '@/root/app/plugins/widgets/ImageSliderWidget/FlatListSlider';
import {EmbeddedAppPageContainer} from '../../../screen/AppScreenContainer';
import {
  getActiveVariantImages,
  getProductDefaultVariantOptions,
  getProductDerivedData,
} from '@/root/app/plugins/state/ShopifyPDP/actions';

import {
  TransformCollectionDetailsByHandle,
  TransformGetCollectionProductsQuery,
  TransformProductFilters,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {ParsedNativeComponent} from '@/root/app/plugins/widgets/RichTextWidget/richTextConvertor';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';

import {registerNativePage} from '../../prebuilt';
import {activeNavigationSelector} from '@/root/app/selectors/ActiveNavigationSelectors';
import {ImageComponent} from 'apptile-core';

const ITEM_VMARGIN = 10;

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const defaultPageParams = {};
const emptyProduct = undefined;
const metafieldDefinitions = [
  {key: 'g_bestsellertag', namespace: 'custom'},
  {key: 'after_atc_test_ingrdients', namespace: 'my_fields'},
  {key: 'after_atc_single_line_text', namespace: 'my_fields'},
  {key: 'rich_text_skin_type_batc', namespace: 'my_fields'},
  {key: 'product_size', namespace: 'my_fields'},
  {key: 'ingredients1_url', namespace: 'my_fields'},
  {key: 'ingredients2_url', namespace: 'my_fields'},
  {key: 'ingredients3_url', namespace: 'my_fields'},
  {key: 'featured-product-final', namespace: 'custom'},
  {key: 'how_to_use', namespace: 'my_fields'},
  {key: 'test_benefit_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit2_url', namespace: 'my_fields'},
  {key: 'after_atc_benefit3_url', namespace: 'my_fields'},
];

const initialState = {
  collection: undefined,
  products: [],
  sortKey: 'BEST_SELLING',
  reverse: false,
  collectionFilters: [],
  selectedFilters: [],
  filters: [],
  paginationDetails: {},
  queryHasNextPage: false,
};

function PLPReducer(state, action) {
  switch (action.type) {
    case 'SET_COLLECTION':
      console.log('PLPReducer: SET_COLLECTION');
      const collection = action.collection;
      return {...state, collection};
    case 'SET_COLLECTION_PRODUCTS':
      console.log('PLPReducer: SET_COLLECTION_PRODUCTS');
      const products = action.products;
      const queryHasNextPage = action?.queryHasNextPage;
      if (products) {
        return {...state, products, queryHasNextPage, paginationDetails: {...action?.paginationDetails}};
      }
      return state;
    case 'SET_SORT_ORDER':
      console.log('PLPReducer: SET_SORT_ORDER');
      const {sortKey, reverse} = action;
      return {...state, sortKey, reverse};
    case 'SET_COLLECTION_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {collectionFilters} = action;
      return {...state, collectionFilters};
    case 'SET_SELECTED_FILTERS':
      console.log('PLPReducer: SET_SELECTED_FILTERS');
      const {selectedFilters} = action;
      return {...state, selectedFilters};
    case 'SET_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {filters} = action;
      return {...state, filters};
    default:
      return state;
  }
}

const SanteviaPLP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const buttonTheme = themeEvaluator('tile.button.primary');
  const outlineButtonTheme = themeEvaluator('tile.button.outline');
  const primaryButtonTheme = themeEvaluator('tile.button.primary');
  const textColor = themeEvaluator('colors.onBackground');
  const primaryColor = themeEvaluator('colors.primary');
  const secondaryColor = themeEvaluator('colors.secondary');
  const shadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(10));
  const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(4));
  const {
    typography: primaryButtonTypo,
    // color,
    disabledColor,
    disabledBackgroundColor,
    // backgroundColor,
    ...buttonStyles
  } = buttonTheme;
  const color = buttonTheme?.color;
  const backgroundColor = buttonTheme?.backgroundColor;
  const outlinebuttonStyles = _.omit(outlineButtonTheme, [
    'typography',
    // 'color',
    'disabledColor',
    'disabledBackgroundColor',
    // 'backgroundColor',
  ]);
  const outlineButtonTypo = outlineButtonTheme?.typography;
  const outlineButtonTextColor = outlineButtonTheme?.color;
  const outlineButtonTextStyles = generateTypographyByPlatform(outlineButtonTypo, loadedFonts);

  const primaryButtonStyles = _.omit(primaryButtonTheme, ['typography', 'disabledColor', 'disabledBackgroundColor']);
  const primaryButtonTextColor = primaryButtonTheme?.color;
  const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const productMetafields = ShopifyDSModel?.get('productMetafields', []);
  const variantMetafields = ShopifyDSModel?.get('variantMetafields', []);
  const collectionMetafields = ShopifyDSModel?.get('collectionMetafields', []);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);
  const [PLP, PLPDispatch] = useReducer(PLPReducer, initialState);
  const [showSortModal, setShowSortModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const ITEM_HEIGHT = Number(plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY) ?? '540');
  const NUM_COLUMNS = Number(plpSettings.getSettingValue(PLP_NUM_COLS_KEY) ?? '1');
  const IMAGE_ASPECT_RATIO = Number(plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY) ?? '1.2');

  /****************************************
   * Handle Collection Details Fetch.
   ***********************************/
  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollDetails = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_BY_HANDLE, {
        collectionHandle: params.collectionHandle,
        collectionMetafields,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {transformer: TransformCollectionDetailsByHandle},
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      navigation.setOptions({title: transformedData?.title});
      PLPDispatch({type: 'SET_COLLECTION', collection: transformedData});
    };
    fetchCollDetails();
    return () => {};
  }, []);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/
  const fetchProducts = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProducts = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        filters: PLP.filters,
        first: 24,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {
            transformer: TransformGetCollectionProductsQuery,
            isPaginated: true,
            paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
              const {after} = paginationMeta;
              return {...inputVariables, after};
            },
          },
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      PLPDispatch({type: 'SET_COLLECTION_PRODUCTS', products: transformedData, paginationDetails, queryHasNextPage});
    };
    fetchCollProducts();
  }, [
    PLP.filters,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);
  useEffect(() => {
    fetchProducts();
    return () => {};
  }, []);

  useEffect(() => {
    fetchProducts();
    return () => {};
  }, [PLP.sortKey, PLP.reverse, PLP.filters]);

  const getNextPage = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProductsNextPage = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        first: 24,
        after: PLP.paginationDetails?.after,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {
            transformer: TransformGetCollectionProductsQuery,
            isPaginated: true,
            paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
              const {after} = paginationMeta;
              return {...inputVariables, after};
            },
          },
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      PLPDispatch({
        type: 'SET_COLLECTION_PRODUCTS',
        products: PLP.products?.concat(transformedData),
        paginationDetails,
        queryHasNextPage,
      });
    };
    if (PLP.queryHasNextPage) fetchCollProductsNextPage();
    return () => {};
  }, [
    PLP.paginationDetails?.after,
    PLP.products,
    PLP.queryHasNextPage,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/

  const fetchFilters = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollFilters = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_PRODUCT_FILTERS, {
        collectionHandle: params.collectionHandle,
      });
      var {rawData, transformedData, transformedHasError, transformedError, queryHasNextPage, paginationDetails} =
        processShopifyGraphqlQueryResponse(
          queryResponse,
          {
            transformer: TransformProductFilters,
          },
          ShopifyDSModel?.get('shop'),
          ShopifyDSModel,
        );
      PLPDispatch({type: 'SET_COLLECTION_FILTERS', collectionFilters: transformedData});
    };
    fetchCollFilters();
  }, [ShopifyDSModel, params.collectionHandle]);

  const fetchCollectionFilters = useCallback(() => {
    if (_.isEmpty(PLP.collectionFilters)) fetchFilters();
  }, [PLP.collectionFilters, fetchFilters]);

  // /****************************************
  //  * Handle Wishlist.
  //  ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  // const LocalWishListItems = useSelector(wishlistItemsSel);
  // const [wishlistItems, setWishlistItems] = useState([]);
  // const [isInWishlist, setIsInWishlist] = useState(false);
  // useEffect(() => {
  //   if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
  //     setWishlistItems(LocalWishListItems);
  //   }
  // }, [LocalWishListItems, wishlistItems]);
  // useEffect(() => {
  //   const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id === collection?.id);
  //   if (!_.isEmpty(wlEntry)) {
  //     setIsInWishlist(true);
  //   } else {
  //     setIsInWishlist(false);
  //   }
  // }, [collection?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const addToCartCallback = useCallback(
    variandId => {
      if (variandId) {
        dispatch(
          triggerAction({
            pluginConfig: ShopifyDSConfig,
            pluginModel: ShopifyDSModel,
            pluginSelector: ['shopify'],
            eventModelJS: {
              value: 'increaseCartLineItemQuantity',
              params: {
                merchandiseId: variandId,
                quantity: 1,
                syncWithShopify: false,
                sellingPlanId: null,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Cart', {
          type: 'success',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 10},
        });
      }
    },
    [ShopifyDSConfig, ShopifyDSModel, dispatch],
  );

  const goToProduct = useCallback(
    handle => {
      if (handle) {
        performHapticFeedback('tap');
        dispatch(navigateToScreen('Product', {productHandle: handle}));
      }
    },
    [dispatch],
  );

  const addToWishList = useCallback(
    product => {
      const productObj = getWishListItemFromProduct(product);
      if (productObj) {
        dispatch(
          triggerAction({
            pluginConfig: LWConfig,
            pluginModel: LWModel,
            pluginSelector: ['localWishlist'],
            eventModelJS: {
              value: 'addProductToWishlist',
              params: {
                productObj,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Wishlist', {
          type: 'normal',
          placement: 'top',
          duration: 2000,
          style: {marginTop: 20},
        });
      }
    },
    [LWConfig, LWModel, dispatch],
  );

  const removeFromWishList = useCallback(
    product => {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'removeProductFromWishlist',
            params: {
              productId: product?.id,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product removed from Wishlist', {
        type: 'normal',
        placement: 'top',
        duration: 2000,
        style: {marginTop: 20},
      });
    },
    [LWConfig, LWModel, dispatch],
  );

  const setSortKeys = useCallback((sortKey: string, reverse: boolean = false) => {
    PLPDispatch({type: 'SET_SORT_ORDER', sortKey, reverse});
    // PLPDispatch({
    //   type: 'SET_COLLECTION_PRODUCTS',
    //   products: [],
    //   paginationDetails: undefined,
    //   queryHasNextPage: false,
    // });
  }, []);

  const hasSelectedFilter = useCallback(
    filterValue => {
      return !!_.find(PLP.selectedFilters, fVal => fVal.id === filterValue?.id);
    },
    [PLP.selectedFilters],
  );
  const selectFilter = useCallback(
    filterValue => {
      if (hasSelectedFilter(filterValue)) return;
      PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: PLP.selectedFilters.concat([filterValue])});
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const removeFilter = useCallback(
    filterValue => {
      if (hasSelectedFilter(filterValue)) {
        PLPDispatch({
          type: 'SET_SELECTED_FILTERS',
          selectedFilters: _.filter(PLP.selectedFilters, fVal => fVal.id !== filterValue?.id),
        });
      }
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const applyFilters = useCallback(() => {
    const filters = PLP.selectedFilters.map(fValue => {
      try {
        return JSON.parse(fValue?.input);
      } catch {
        return undefined;
      }
    });
    PLPDispatch({type: 'SET_FILTERS', filters});
  }, [PLP.selectedFilters]);

  const resetFilters = useCallback(() => {
    PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: []});
    PLPDispatch({type: 'SET_FILTERS', filters: []});
  }, []);

  /******************************
   * ListView Methods
   *******************************/

  const onEndReached = useCallback(() => {
    console.log('PLP END REACHED');
    getNextPage();
  }, [getNextPage]);
  const renderItem = useCallback(
    ({item, index}) => {
      logger.info(`ListView RenderItem ${index}`);
      return (
        <ItemComponent
          {...{
            item,
            index,
            onProductClick: goToProduct,
            onAddToCart: addToCartCallback,
            onAddToWishlist: addToWishList,
            onRemoveFromWishlist: removeFromWishList,
            imageAspectRatio: IMAGE_ASPECT_RATIO,
            itemHeight: ITEM_HEIGHT,
            noOfCols: NUM_COLUMNS,
          }}
        />
      );
    },
    [IMAGE_ASPECT_RATIO, ITEM_HEIGHT, NUM_COLUMNS, addToCartCallback, addToWishList, goToProduct, removeFromWishList],
  );
  const getItemLayout = useCallback((data, index) => {
    const totalHeight = ITEM_HEIGHT + 2 * ITEM_VMARGIN;
    const layout = {
      length: totalHeight,
      offset: totalHeight * Math.floor(index / NUM_COLUMNS),
      index,
    };
    return layout;
  }, []);

  const focused = useRef(false);
  const pageBootFocusCallback = useCallback(() => {
    const doFocusAsync = async () => {
      logger.info('[LIFECYCLE] Set ACTIVE PAGE');
      setTimeout(() => dispatch(apptileSetActivePage(pageKey, pageId)), 0);
      focused.current = true;
    };
    if (!focused.current) doFocusAsync();
    return () => {
      focused.current = false;
    };
  }, [dispatch, pageId, pageKey]);
  useFocusEffect(pageBootFocusCallback);
  return (
    <CurrentScreenContext.Provider value={screen}>
      {PLP.products?.length === 0 ? (
        <View style={{flex: 1}}>
          <ImageComponent
            style={[{width: '100%', height: '100%'}]}
            source={{
              uri: 'https://cdn.apptile.io/83529966-6875-46fc-b09e-7a51f7242238/d0e45284-3f00-4549-8b16-c0d3ddae3f1b/original.gif',
            }}
            resizeMode={'contain'}
          />
        </View>
      ) : (
        <SafeAreaView
          style={{flex: 1, alignItems: 'center', backgroundColor: '#f6f6f6'}}
          edges={['bottom', 'left', 'right']}>
          <FlatList
            key={NUM_COLUMNS}
            disableVirtualization={true}
            style={[{flex: 1}]}
            scrollEnabled={true}
            horizontal={false}
            initialScrollIndex={0}
            numColumns={NUM_COLUMNS}
            height="100%"
            width="100%"
            data={PLP.products}
            extraData={PLP.products?.length + PLP.sortKey + PLP.reverse}
            // ListHeaderComponent={() => {
            //   return PLP.collection?.featuredImage ? (
            //     <View style={[{flex: 1}]}>
            //       <ImageComponent
            //         style={[{width: '100%', flexBasis: 80, height: 80}]}
            //         source={{
            //           uri: PLP.collection?.featuredImage,
            //         }}
            //         resizeMode={'contain'}
            //       />
            //     </View>
            //   ) : null;
            // }}
            getItemLayout={getItemLayout}
            // ListEmptyComponent={showPlaceholder ? <EmptyPlaceholder /> : <></>}
            // overScrollMode="never"
            // onViewableItemsChanged={onViewableItemsChanged}
            // onLayout={onLayout}
            keyExtractor={item => item?.id}
            renderItem={renderItem}
            // keyExtractor={keyExtractor}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            onEndReachedThreshold={2}
            onEndReached={onEndReached}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            initialNumToRender={10}
            windowSize={11}
            scrollEventThrottle={16}
          />

          {/* <EmbeddedAppPageContainer {...screenProps} /> */}
          <View style={[fixedStyles.bottomBar, {width: '100%'}]}>
            <View style={[fixedStyles.bottomPanel]}>
              <Pressable
                style={[fixedStyles.bottomControls, outlinebuttonStyles, {paddingTop: 16, paddingBottom: 16}]}
                onPress={() => {
                  setShowFilterModal(true);
                  fetchCollectionFilters();
                }}>
                <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Filters</Text>
              </Pressable>
              <Pressable
                style={[fixedStyles.bottomControls, outlinebuttonStyles, {paddingTop: 16, paddingBottom: 16}]}
                onPress={() => {
                  setShowSortModal(true);
                }}>
                <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Sort By</Text>
              </Pressable>
            </View>
          </View>
          {showSortModal ? (
            <CustomModal
              position="bottom"
              onClose={() => {
                setShowSortModal(false);
              }}>
              <View style={[fixedStyles.sortModalContainer]}>
                <Pressable
                  style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                  onPress={() => {
                    setSortKeys('BEST_SELLING');
                    setShowSortModal(false);
                  }}>
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>BestSellers</Text>
                </Pressable>
                <Pressable
                  style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                  onPress={() => {
                    setSortKeys('CREATED');
                    setShowSortModal(false);
                  }}>
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Newly added</Text>
                </Pressable>
                <Pressable
                  style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                  onPress={() => {
                    setSortKeys('PRICE', true);
                    setShowSortModal(false);
                  }}>
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Price - high to low</Text>
                </Pressable>
                <Pressable
                  style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                  onPress={() => {
                    setSortKeys('PRICE');
                    setShowSortModal(false);
                  }}>
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Price - low to high</Text>
                </Pressable>
              </View>
            </CustomModal>
          ) : (
            <></>
          )}
          {showFilterModal ? (
            <CustomModal
              position="bottom"
              onClose={() => {
                setShowFilterModal(false);
              }}>
              <View style={[fixedStyles.filterModalContainer]}>
                {_.isEmpty(PLP.collectionFilters) ? (
                  <></>
                ) : (
                  <>
                    {PLP.collectionFilters.map((collFilter, filterIndex) => {
                      if (collFilter?.type !== 'PRICE_RANGE')
                        return (
                          <View style={[fixedStyles.filterEntryContainer]} key={filterIndex}>
                            <Text style={[subHeadingStyles, fixedStyles.filterTitleText, {color}]}>
                              {collFilter?.label}
                            </Text>
                            <View style={[fixedStyles.filterValuesContainer]}>
                              {collFilter?.values?.map((filterValue, fValueIdx) => {
                                const isSelected = !!_.find(PLP.selectedFilters, obj => obj.id === filterValue.id);
                                return (
                                  <Pressable
                                    key={fValueIdx}
                                    style={[
                                      hasSelectedFilter(filterValue) ? primaryButtonStyles : outlinebuttonStyles,
                                      fixedStyles.filterValueButtons,
                                      smallShadowStyles,
                                    ]}
                                    onPress={() => {
                                      hasSelectedFilter(filterValue)
                                        ? removeFilter(filterValue)
                                        : selectFilter(filterValue);
                                    }}>
                                    <Text
                                      style={[
                                        primaryButtonTextStyles,
                                        {
                                          color: hasSelectedFilter(filterValue)
                                            ? primaryButtonTextColor
                                            : outlineButtonTextColor,
                                        },
                                        smallShadowStyles,
                                      ]}>
                                      {filterValue.label}
                                    </Text>
                                  </Pressable>
                                );
                              })}
                            </View>
                          </View>
                        );
                      else return null;
                    })}
                    <View style={[fixedStyles.bottomPanel]}>
                      <Pressable
                        style={[fixedStyles.bottomControls, outlinebuttonStyles]}
                        onPress={() => {
                          resetFilters();
                          setShowFilterModal(false);
                        }}>
                        <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Reset</Text>
                      </Pressable>
                      <Pressable
                        style={[fixedStyles.bottomControls, buttonTheme, {margin: 0}]}
                        onPress={() => {
                          applyFilters();
                          setShowFilterModal(false);
                        }}>
                        <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Apply</Text>
                      </Pressable>
                    </View>
                  </>
                )}
              </View>
            </CustomModal>
          ) : (
            <></>
          )}
        </SafeAreaView>
      )}
    </CurrentScreenContext.Provider>
  );
};

const ItemComponent = React.memo(
  ({
    item,
    index,
    onProductClick,
    onAddToCart,
    onAddToWishlist,
    onRemoveFromWishlist,
    imageAspectRatio,
    itemHeight,
    noOfCols,
  }) => {
    const {themeEvaluator} = useTheme();
    const {loadedFonts} = useLoadedFonts();
    const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
    const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
    const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
    const buttonTheme = themeEvaluator('tile.button.primary');
    const outlineButtonTheme = themeEvaluator('tile.button.outline');
    const textColor = themeEvaluator('colors.onBackground');
    const primaryColor = themeEvaluator('colors.primary');
    const secondaryColor = themeEvaluator('colors.secondary');
    const shadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(10));
    const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(4));
    const {
      typography: buttonTypo,
      color,
      disabledColor,
      disabledBackgroundColor,
      backgroundColor,
      ...buttonStyles
    } = buttonTheme;
    const outlinebuttonStyles = _.omit(outlineButtonTheme, [
      'typography',
      'color',
      'disabledColor',
      'disabledBackgroundColor',
      'backgroundColor',
    ]);
    const buttonTextStyles = generateTypographyByPlatform(buttonTypo, loadedFonts);
    const [inWL, setInWL] = useState(false);
    return (
      <View
        style={[
          {
            flex: 1,
            width: `${noOfCols == '2' ? 50 : 100}%`,
            maxWidth: `${noOfCols == '2' ? 50 : 100}%`,
            height: itemHeight + 2 * ITEM_VMARGIN,
            maxHeight: itemHeight + 2 * ITEM_VMARGIN,
            overflow: 'hidden',
            paddingHorizontal: 8,
          },
        ]}>
        <View
          style={[
            {
              flex: 1,
              width: '100%',
              maxWidth: '100%',
              height: '100%',
              maxHeight: '100%',
              overflow: 'hidden',
              borderRadius: 12,
              marginVertical: ITEM_VMARGIN,
              backgroundColor: 'white',
            },
            shadowStyles,
          ]}>
          <Pressable style={[{flex: 1}]} onPress={() => onProductClick(item?.handle)}>
            <ImageComponent
              style={[{width: '100%', aspectRatio: imageAspectRatio}]}
              source={{
                uri: item?.featuredImage,
              }}
              resizeMode="contain"
            />
            <View style={[fixedStyles.detailsContainer]}>
              <Text
                style={[
                  subHeadingStyles,
                  {color: textColor, fontSize: 20, lineHeight: 20, marginBottom: 8, paddingTop: 8},
                ]}
                numberOfLines={2}
                minimumFontScale={1}>
                {item?.title}
              </Text>
              <View style={[fixedStyles.ratingContainer]}>
                <Text style={[bodyStyles, fixedStyles.ratingStyle]}>4.5</Text>
                {_.range(0, 5).map(idx => (
                  <MaterialCommunityIcons
                    style={[fixedStyles.ratingStarStyle]}
                    name={idx == 4 ? 'star-half-full' : 'star'}
                  />
                ))}
              </View>
              <View style={[fixedStyles.priceContainer]}>
                <Text
                  style={[headingStyles, fixedStyles.mainPrice, {fontSize: 20, lineHeight: 20, color: primaryColor}]}>
                  {item?.displayMinSalePrice}
                </Text>
                {item?.displayMinSalePrice !== item?.displayMinPrice ? (
                  <Text
                    style={[
                      bodyStyles,
                      fixedStyles.strikeOffPrice,
                      {color: '#333', textDecorationLine: 'line-through'},
                    ]}>
                    {item?.displayMinPrice}
                  </Text>
                ) : (
                  <></>
                )}
              </View>
            </View>
          </Pressable>
          <View style={[fixedStyles.ctaActionsContainer]}>
            {item?.availableForSale ? (
              <Pressable
                style={[buttonStyles, fixedStyles.ctaButton, {backgroundColor}]}
                onPress={() => {
                  onAddToCart(item?.variants[0]?.id);
                }}>
                <Text style={[buttonTextStyles, {color}]}>Add to Cart</Text>
              </Pressable>
            ) : (
              <Pressable style={[outlinebuttonStyles, fixedStyles.ctaButton]}>
                <Text style={[buttonTextStyles, {color}]}>Out of stock</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    );
  },
);

const fixedStyles = StyleSheet.create({
  detailsContainer: {
    flex: 1,
    marginHorizontal: 10,
    marginTop: 8,
  },
  ratingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  ratingStarStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#50ac0a',
  },
  priceContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  strikeOffPrice: {
    fontSize: 10,
    marginHorizontal: 4,
  },
  ctaActionsContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  wishlistButton: {
    margin: 4,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
  },
  bottomBar: {
    padding: 6,
  },
  bottomPanel: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  sortModalButtons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    margin: 12,
  },
  filterModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  filterEntryContainer: {
    flex: 1,
    paddingVertical: 4,
    marginBottom: 8,
  },
  filterTitleText: {
    fontSize: 16,
    lineHeight: 20,
  },
  filterValuesContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 4,
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  filterValueButtons: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 4,
  },
});

export default registerNativePage('SanteviaPLP', SanteviaPLP, {}, {});
