import _ from 'lodash';
import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {ActivityIndicator, FlatList, Pressable, ScrollView, StyleSheet, Text, View} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';

import {
  navigateToScreen,
  triggerAction,
  useLoadedFonts,
  MaterialCommunityIcons,
  performHapticFeedback,
  CustomModal,
  getShadowStyle,
  selectPluginConfig,
  datasourceByIdModelSel,
  datasourceTypeModelSel,
  generateTypographyByPlatform,
  useTheme,
  CurrentScreenContext,
  SettingsConfig,
  PLP_CARD_HEIGHT_KEY,
  PLP_IMAGE_ASPECT_RATIO_KEY,
  PLP_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
  selectAppSettings<PERSON><PERSON><PERSON><PERSON>,
  apptileSetActivePage,
  createDeepEqualSelector,
  sendAnalyticsEvent,
} from 'apptile-core';

import {registerNativePage} from '../../prebuilt';
import SparklHeader from '../../custom/Sparkl/SparklHeader';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {
  TransformCollectionDetailsByHandle,
  TransformGetCollectionProductsQuery,
  TransformProductFilters,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {ImageComponent} from 'apptile-core';

const ITEM_VMARGIN = 2;

const localWishlistModelSel = (state: any) => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = (state: any) => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
  return {};
}

const shopifyModelSel = (state: any) => datasourceTypeModelSel(state, 'shopifyV_22_10');

const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, (shopifyDS): string | unknown => {
  return shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']);
});

const collectionDefaultImageModelSel = (state: any) => datasourceByIdModelSel(state, 'CollectionDefaultImage');

const defaultPageParams = {};

const initialState = {
  collection: undefined,
  products: [],
  sortKey: 'COLLECTION_DEFAULT',
  reverse: false,
  collectionFilters: [],
  selectedFilters: [],
  filters: [],
  paginationDetails: {},
  queryHasNextPage: false,
  loadingProducts: true,
};

function PLPReducer(
  state: any,
  action: {
    type?: any;
    collection?: any;
    products?: any;
    queryHasNextPage?: any;
    paginationDetails?: any;
    sortKey?: any;
    reverse?: any;
    collectionFilters?: any;
    selectedFilters?: any;
    filters?: any;
  },
) {
  switch (action.type) {
    case 'SET_COLLECTION':
      console.log('PLPReducer: SET_COLLECTION');
      const collection = action.collection;
      return {...state, collection};
    case 'FETCH_COLLECTION_PRODUCTS':
      console.log('PLPReducer: FETCH_COLLECTION_PRODUCTS');
      return {...state, loadingProducts: true};
    case 'SET_COLLECTION_PRODUCTS':
      console.log('PLPReducer: SET_COLLECTION_PRODUCTS');
      const products = action.products;
      const queryHasNextPage = action?.queryHasNextPage;
      if (products) {
        return {
          ...state,
          products,
          loadingProducts: false,
          queryHasNextPage,
          paginationDetails: {...action?.paginationDetails},
        };
      }
      return {...state, loadingProducts: false};
    case 'SET_SORT_ORDER':
      console.log('PLPReducer: SET_SORT_ORDER');
      const {sortKey, reverse} = action;
      return {...state, sortKey, reverse};
    case 'SET_COLLECTION_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {collectionFilters} = action;
      return {...state, collectionFilters};
    case 'SET_SELECTED_FILTERS':
      console.log('PLPReducer: SET_SELECTED_FILTERS');
      const {selectedFilters} = action;
      return {...state, selectedFilters};
    case 'SET_FILTERS':
      console.log('PLPReducer: SET_FILTERS');
      const {filters} = action;
      return {...state, filters};
    default:
      return state;
  }
}

const SparklPLP: React.FC = screenProps => {
  const {navigation, route, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const settingsSelector = (settingsKey: string) => (state: any) => selectAppSettingsForKey(state, settingsKey);
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const ITEM_HEIGHT = Number(plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY) ?? '350');
  const NUM_COLUMNS = Number(plpSettings.getSettingValue(PLP_NUM_COLS_KEY) ?? '2');
  const IMAGE_ASPECT_RATIO = Number(plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY) ?? '1');
  const NO_RESULT_IMAGE_URL =
    'https://cdn.apptile.io/967d0b61-ceaa-4cae-8bb7-2b9a89995966/946bc4d8-7ac7-443f-9bf6-bd91448d1700/original.jpeg';

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('typography.heading'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const buttonTheme = themeEvaluator('tile.button.primary');
  const outlineButtonTheme = themeEvaluator('tile.button.outline');
  const primaryButtonTheme = themeEvaluator('tile.button.primary');
  const primaryColor = themeEvaluator('colors.primary');
  const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(0));
  const {typography: primaryButtonTypo} = buttonTheme;
  const outlinebuttonStyles = _.omit(outlineButtonTheme, [
    'typography',
    // 'color',
    'disabledColor',
    'disabledBackgroundColor',
    // 'backgroundColor',
  ]);
  const outlineButtonTypo = outlineButtonTheme?.typography;
  const outlineButtonTextColor = outlineButtonTheme?.color;
  const outlineButtonTextStyles = generateTypographyByPlatform(outlineButtonTypo, loadedFonts);

  const primaryButtonStyles = _.omit(primaryButtonTheme, ['typography', 'disabledColor', 'disabledBackgroundColor']);
  const primaryButtonTextColor = primaryButtonTheme?.color;
  const primaryButtonTextStyles = generateTypographyByPlatform(primaryButtonTypo, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const collectionDefaultImageModel = useSelector(collectionDefaultImageModelSel);

  const productMetafields = ShopifyDSModel?.get('productMetafields', []);
  const variantMetafields = ShopifyDSModel?.get('variantMetafields', []);
  const collectionMetafields = ShopifyDSModel?.get('collectionMetafields', []);
  const [PLP, PLPDispatch] = useReducer(PLPReducer, initialState);
  const [showSortModal, setShowSortModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const storeCurrency: string = useSelector(shopifyStoreCurrencySel);
  const LocalWishListItems = useSelector(wishlistItemsSel);
  /****************************************
   * Handle Collection Details Fetch.
   ***********************************/
  useEffect(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollDetails = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_BY_HANDLE, {
        collectionHandle: params.collectionHandle,
        collectionMetafields,
      });
      var {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformCollectionDetailsByHandle},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      navigation.setOptions({title: transformedData?.title});
      PLPDispatch({type: 'SET_COLLECTION', collection: transformedData});
    };
    fetchCollDetails();
    return () => {};
  }, []);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/
  const fetchProducts = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProducts = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        filters: PLP.filters,
        first: 24,
      });
      var {transformedData, queryHasNextPage, paginationDetails} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {
          transformer: TransformGetCollectionProductsQuery,
          isPaginated: true,
          paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
            const {after} = paginationMeta;
            return {...inputVariables, after};
          },
        },
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      PLPDispatch({type: 'SET_COLLECTION_PRODUCTS', products: transformedData, paginationDetails, queryHasNextPage});
    };
    fetchCollProducts();
  }, [
    PLP.filters,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);
  useEffect(() => {
    fetchProducts();
    return () => {};
  }, []);

  useEffect(() => {
    fetchProducts();
    return () => {};
  }, [PLP.sortKey, PLP.reverse, PLP.filters]);

  const getNextPage = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollProductsNextPage = async () => {
      PLPDispatch({
        type: 'FETCH_COLLECTION_PRODUCTS',
      });
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: params.collectionHandle,
        productMetafields,
        variantMetafields,
        sortKey: PLP.sortKey,
        reverse: PLP.reverse,
        first: 24,
        after: PLP.paginationDetails?.after,
      });
      var {transformedData, queryHasNextPage, paginationDetails} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {
          transformer: TransformGetCollectionProductsQuery,
          isPaginated: true,
          paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
            const {after} = paginationMeta;
            return {...inputVariables, after};
          },
        },
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      PLPDispatch({
        type: 'SET_COLLECTION_PRODUCTS',
        products: PLP.products?.concat(transformedData),
        paginationDetails,
        queryHasNextPage,
      });
    };
    if (PLP.queryHasNextPage) {
      PLPDispatch({
        type: 'FETCH_COLLECTION_PRODUCTS',
      });
      fetchCollProductsNextPage();
    }
    return () => {};
  }, [
    PLP.paginationDetails?.after,
    PLP.products,
    PLP.queryHasNextPage,
    PLP.reverse,
    PLP.sortKey,
    ShopifyDSModel,
    params.collectionHandle,
    productMetafields,
    variantMetafields,
  ]);

  /****************************************
   * Handle Collection Products Fetch.
   ***********************************/

  const fetchFilters = useCallback(() => {
    const queryRunner = ShopifyDSModel?.get('queryRunner');
    const fetchCollFilters = async () => {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_PRODUCT_FILTERS, {
        collectionHandle: params.collectionHandle,
      });
      var {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {
          transformer: TransformProductFilters,
        },
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      PLPDispatch({type: 'SET_COLLECTION_FILTERS', collectionFilters: transformedData});
    };
    fetchCollFilters();
  }, [ShopifyDSModel, params.collectionHandle]);

  const fetchCollectionFilters = useCallback(() => {
    if (_.isEmpty(PLP.collectionFilters)) fetchFilters();
  }, [PLP.collectionFilters, fetchFilters]);

  // /****************************************
  //  * Handle Wishlist.
  //  ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  // const LocalWishListItems = useSelector(wishlistItemsSel);
  // const [wishlistItems, setWishlistItems] = useState([]);
  // const [isInWishlist, setIsInWishlist] = useState(false);
  // useEffect(() => {
  //   if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
  //     setWishlistItems(LocalWishListItems);
  //   }
  // }, [LocalWishListItems, wishlistItems]);
  // useEffect(() => {
  //   const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id === collection?.id);
  //   if (!_.isEmpty(wlEntry)) {
  //     setIsInWishlist(true);
  //   } else {
  //     setIsInWishlist(false);
  //   }
  // }, [collection?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const goToProduct = useCallback(
    (handle: any) => {
      if (handle) {
        performHapticFeedback('tap');
        dispatch(navigateToScreen('Product', {productHandle: handle}));
      }
    },
    [dispatch],
  );

  const addToWishList = useCallback(
    (product: Record<string, any>) => {
      const productObj = getWishListItemFromProduct(product);
      if (productObj) {
        dispatch(
          triggerAction({
            pluginConfig: LWConfig,
            pluginModel: LWModel,
            pluginSelector: ['localWishlist'],
            eventModelJS: {
              value: 'addProductToWishlist',
              params: {
                productObj,
                productId: product?.id,
              },
            },
          }),
        );
        performHapticFeedback('tap');
        toast.show('Product added to Wishlist', {
          type: 'success',
          placement: 'bottom',
          duration: 2000,
        });
        dispatch(
          sendAnalyticsEvent('track', 'addToWishlist', {
            productId: product?.id,
            productHandle: product?.handle,
            productType: product?.productType,
            currency: storeCurrency,
            price: product?.variants[0]?.salePrice,
            quantity: '1',
            title: product?.title,
            variantId: product?.variants[0]?.id,
            variantTitle: product?.variants[0]?.title,
            brand: product?.vendor,
            listName: params.collectionHandle,
          }),
        );
      }
    },
    [LWConfig, LWModel, dispatch, params.collectionHandle, storeCurrency],
  );

  const removeFromWishList = useCallback(
    product => {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'removeProductFromWishlist',
            params: {
              productId: product?.id,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product removed from Wishlist', {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
      });
    },
    [LWConfig, LWModel, dispatch],
  );

  const setSortKeys = useCallback((sortKey: string, reverse: boolean = false) => {
    PLPDispatch({type: 'SET_SORT_ORDER', sortKey, reverse});
    // PLPDispatch({
    //   type: 'SET_COLLECTION_PRODUCTS',
    //   products: [],
    //   paginationDetails: undefined,
    //   queryHasNextPage: false,
    // });
  }, []);

  const hasSelectedFilter = useCallback(
    (filterValue: {id: any}) => {
      return !!_.find(PLP.selectedFilters, fVal => fVal.id === filterValue?.id);
    },
    [PLP.selectedFilters],
  );
  const selectFilter = useCallback(
    (filterValue: any) => {
      if (hasSelectedFilter(filterValue)) return;
      PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: PLP.selectedFilters.concat([filterValue])});
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const removeFilter = useCallback(
    (filterValue: {id: any}) => {
      if (hasSelectedFilter(filterValue)) {
        PLPDispatch({
          type: 'SET_SELECTED_FILTERS',
          selectedFilters: _.filter(PLP.selectedFilters, fVal => fVal.id !== filterValue?.id),
        });
      }
    },
    [PLP.selectedFilters, hasSelectedFilter],
  );
  const applyFilters = useCallback(() => {
    const filters = PLP.selectedFilters.map((fValue: {input: string}) => {
      try {
        return JSON.parse(fValue?.input);
      } catch {
        return undefined;
      }
    });
    PLPDispatch({type: 'SET_FILTERS', filters});
  }, [PLP.selectedFilters]);

  const resetFilters = useCallback(() => {
    PLPDispatch({type: 'SET_SELECTED_FILTERS', selectedFilters: []});
    PLPDispatch({type: 'SET_FILTERS', filters: []});
  }, []);

  /******************************
   * ListView Methods
   *******************************/

  const onEndReached = useCallback(() => {
    console.log('PLP END REACHED');
    getNextPage();
  }, [getNextPage]);
  const renderItem = useCallback(
    ({item, index}) => {
      logger.info(`ListView RenderItem ${index}`);
      return (
        <ItemComponent
          {...{
            item,
            index,
            onProductClick: goToProduct,
            wishlistProducts: LocalWishListItems,
            onAddToWishlist: addToWishList,
            onRemoveFromWishlist: removeFromWishList,
            imageAspectRatio: IMAGE_ASPECT_RATIO,
            itemHeight: ITEM_HEIGHT,
            noOfCols: NUM_COLUMNS,
          }}
        />
      );
    },
    [IMAGE_ASPECT_RATIO, ITEM_HEIGHT, NUM_COLUMNS, addToWishList, goToProduct, removeFromWishList],
  );
  const getItemLayout = useCallback((data: any, index: number) => {
    const totalHeight = ITEM_HEIGHT + 2 * ITEM_VMARGIN;
    const layout = {
      length: totalHeight,
      offset: totalHeight * Math.floor(index / NUM_COLUMNS),
      index,
    };
    return layout;
  }, []);
  let collectionBannerImage = PLP?.collection?.featuredImage;

  if (!collectionBannerImage) {
    collectionBannerImage = collectionDefaultImageModel?.get('value');
  }

  const focused = useRef(false);
  const pageBootFocusCallback = useCallback(() => {
    const doFocusAsync = async () => {
      logger.info('[LIFECYCLE] Set ACTIVE PAGE');
      setTimeout(() => dispatch(apptileSetActivePage(pageKey, pageId)), 0);
      focused.current = true;
    };
    if (!focused.current) doFocusAsync();
    return () => {
      focused.current = false;
    };
  }, [dispatch, pageId, pageKey]);
  useFocusEffect(pageBootFocusCallback);
  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1}} edges={['top', 'bottom', 'left', 'right']}>
        <SparklHeader>
          <View style={fixedStyles.pageHeaderCont}>
            {PLP?.collection?.title ? (
              <Text style={[subHeadingStyles, fixedStyles.pageHeaderText, {color: primaryColor}]}>
                {PLP?.collection?.title}
              </Text>
            ) : (
              <></>
            )}
          </View>
        </SparklHeader>

        {PLP.loadingProducts && !PLP.products.length ? (
          <View style={fixedStyles.loadingImageCont}>
            <ImageComponent
              style={[fixedStyles.loadingImage]}
              source={{
                uri: 'https://cdn.apptile.io/af01dd96-78d6-4f9a-8f3c-c26dec85ce36/2378245f-ff2d-4a54-bc49-edd023499cf8/original.gif',
              }}
              resizeMode={'contain'}
            />
          </View>
        ) : (
          <>
            {PLP.products.length > 0}
            <FlatList
              key={NUM_COLUMNS}
              disableVirtualization={true}
              style={[{flex: 1}]}
              scrollEnabled={true}
              horizontal={false}
              initialScrollIndex={0}
              numColumns={NUM_COLUMNS}
              height="100%"
              width="100%"
              data={PLP.products}
              extraData={PLP.products?.length + PLP.sortKey + PLP.reverse}
              ListHeaderComponent={() => {
                if (!collectionBannerImage) return <></>;
                return (
                  <View style={[{flex: 1}]}>
                    <ImageComponent
                      style={[{width: '100%', aspectRatio: 2}]}
                      source={{
                        uri: collectionBannerImage,
                      }}
                      resizeMode={'cover'}
                    />
                    <View style={[fixedStyles.bannerGradientOverlay]} />
                    <Text style={[headingStyles, fixedStyles.bannerOverlayText]}>{PLP.collection?.title}</Text>
                  </View>
                );
              }}
              ListFooterComponent={() => {
                return PLP?.loadingProducts ? (
                  <View style={[fixedStyles.pageLoadingIndicator]}>
                    <ActivityIndicator size={14} />
                  </View>
                ) : (
                  <></>
                );
              }}
              getItemLayout={getItemLayout}
              ListEmptyComponent={
                <View style={[fixedStyles.NoResultCont]}>
                  <ImageComponent
                    style={[fixedStyles.noResultImage]}
                    source={{
                      uri: NO_RESULT_IMAGE_URL,
                    }}
                    resizeMode="cover"
                  />
                </View>
              }
              // ListEmptyComponent={showPlaceholder ? <EmptyPlaceholder /> : <></>}
              // overScrollMode="never"
              // onViewableItemsChanged={onViewableItemsChanged}
              // onLayout={onLayout}
              keyExtractor={item => item?.id}
              renderItem={renderItem}
              // keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              onEndReachedThreshold={2}
              onEndReached={onEndReached}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              initialNumToRender={10}
              windowSize={11}
              scrollEventThrottle={16}
            />

            <View style={[fixedStyles.bottomBar]}>
              <View style={[fixedStyles.bottomPanel]}>
                <Pressable
                  style={[fixedStyles.bottomControls, outlinebuttonStyles, {borderWidth: 0}]}
                  onPress={() => {
                    setShowFilterModal(true);
                    fetchCollectionFilters();
                  }}>
                  <MaterialCommunityIcons style={fixedStyles.bottomControlsIcon} name={'filter'} />
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Filters</Text>
                </Pressable>
                <Pressable
                  style={[fixedStyles.bottomControls, outlinebuttonStyles, {borderWidth: 0}]}
                  onPress={() => {
                    setShowSortModal(true);
                  }}>
                  <MaterialCommunityIcons style={[fixedStyles.bottomControlsIcon]} name={'sort'} />
                  <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Sort By</Text>
                </Pressable>
              </View>
            </View>
            {showSortModal ? (
              <CustomModal
                isDismissible={true}
                position="bottom"
                onClose={() => {
                  setShowSortModal(false);
                }}>
                <View style={[fixedStyles.sortModalContainer]}>
                  <Pressable
                    style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                    onPress={() => {
                      setSortKeys('BEST_SELLING');
                      setShowSortModal(false);
                    }}>
                    <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>BestSellers</Text>
                  </Pressable>
                  <Pressable
                    style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                    onPress={() => {
                      setSortKeys('CREATED');
                      setShowSortModal(false);
                    }}>
                    <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Newly added</Text>
                  </Pressable>
                  <Pressable
                    style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                    onPress={() => {
                      setSortKeys('PRICE', true);
                      setShowSortModal(false);
                    }}>
                    <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Price - high to low</Text>
                  </Pressable>
                  <Pressable
                    style={[outlinebuttonStyles, fixedStyles.sortModalButtons]}
                    onPress={() => {
                      setSortKeys('PRICE');
                      setShowSortModal(false);
                    }}>
                    <Text style={[outlineButtonTextStyles, {color: outlineButtonTextColor}]}>Price - low to high</Text>
                  </Pressable>
                </View>
              </CustomModal>
            ) : (
              <></>
            )}
            {showFilterModal ? (
              <CustomModal
                position="bottom"
                isDismissible={true}
                onClose={() => {
                  setShowFilterModal(false);
                }}>
                <View style={[fixedStyles.filterModalContainer]}>
                  {_.isEmpty(PLP.collectionFilters) ? (
                    <></>
                  ) : (
                    <>
                      <ScrollView>
                        {PLP.collectionFilters.map(
                          (
                            collFilter: {type: string; label: any; values: any[]},
                            filterIndex: React.Key | null | undefined,
                          ) => {
                            if (collFilter?.type !== 'PRICE_RANGE')
                              return (
                                <View style={[fixedStyles.filterEntryContainer]} key={filterIndex}>
                                  <Text style={[subHeadingStyles, fixedStyles.filterTitleText, {color: '#000000'}]}>
                                    {collFilter?.label}
                                  </Text>
                                  <View style={[fixedStyles.filterValuesContainer]}>
                                    {collFilter?.values?.map(
                                      (filterValue: {label: any}, fValueIdx: React.Key | null | undefined) => {
                                        return (
                                          <Pressable
                                            key={fValueIdx}
                                            style={[
                                              hasSelectedFilter(filterValue)
                                                ? primaryButtonStyles
                                                : outlinebuttonStyles,
                                              fixedStyles.filterValueButtons,
                                              smallShadowStyles,
                                              {borderColor: '#E4E4E7', margin: 4},
                                            ]}
                                            onPress={() => {
                                              hasSelectedFilter(filterValue)
                                                ? removeFilter(filterValue)
                                                : selectFilter(filterValue);
                                            }}>
                                            <Text
                                              style={[
                                                primaryButtonTextStyles,
                                                {
                                                  color: hasSelectedFilter(filterValue)
                                                    ? primaryButtonTextColor
                                                    : outlineButtonTextColor,
                                                },
                                                smallShadowStyles,
                                              ]}>
                                              {filterValue.label}
                                            </Text>
                                          </Pressable>
                                        );
                                      },
                                    )}
                                  </View>
                                </View>
                              );
                            else return null;
                          },
                        )}
                      </ScrollView>
                      <View style={[fixedStyles.bottomPanel, {minHeight: 70, paddingTop: 8}]}>
                        <Pressable
                          style={[fixedStyles.bottomControls, outlineButtonTheme, {margin: 4}]}
                          onPress={() => {
                            resetFilters();
                            setShowFilterModal(false);
                          }}>
                          <Text
                            style={[
                              outlineButtonTextStyles,
                              {color: outlineButtonTextColor, textTransform: 'uppercase'},
                            ]}>
                            Reset
                          </Text>
                        </Pressable>
                        <Pressable
                          style={[fixedStyles.bottomControls, buttonTheme, {margin: 4}]}
                          onPress={() => {
                            applyFilters();
                            setShowFilterModal(false);
                          }}>
                          <Text style={[primaryButtonTextStyles, {color: primaryButtonTextColor}]}>Apply</Text>
                        </Pressable>
                      </View>
                    </>
                  )}
                </View>
              </CustomModal>
            ) : (
              <></>
            )}
          </>
        )}
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const ItemComponent = React.memo(
  ({item, onProductClick, onAddToWishlist,wishlistProducts, onRemoveFromWishlist, imageAspectRatio, itemHeight, noOfCols}) => {
    const {themeEvaluator} = useTheme();
    const {loadedFonts} = useLoadedFonts();
    const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
    const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
    const onBackgroundColor = themeEvaluator('colors.onBackground');
    const onPrimaryColor = themeEvaluator('colors.onPrimary');
    const [inWL, setInWL] = useState(false);
    useEffect(() => {
      if (wishlistProducts && wishlistProducts.find(wlItem => wlItem.handle === item?.handle)) {
        if (!inWL) setInWL(true);
      } else {
        if (inWL) setInWL(false);
      }
    }, [inWL, item?.handle, wishlistProducts]);

    return (
      <View
        style={[
          {
            flex: 1,
            width: `${noOfCols == '2' ? 50 : 100}%`,
            maxWidth: `${noOfCols == '2' ? 50 : 100}%`,
            height: itemHeight,
            maxHeight: itemHeight,
            overflow: 'hidden',
            borderRadius: 0,
            marginVertical: ITEM_VMARGIN,
            marginHorizontal: 5,
            padding: 8,
          },
        ]}>
        <Pressable
          style={[{flex: 1, borderColor: '#dddddd', borderWidth: 1}]}
          onPress={() => onProductClick(item?.handle)}>
          {item.availableForSale && item.variants[0].price - item.variants[0].salePrice > 0 && (
            <View style={[fixedStyles.discountTag]}>
              <Text style={[bodyStyles, {fontSize: 12, color: '#fff'}]}>SALE</Text>
            </View>
          )}
          {!item.availableForSale && (
            <View style={[fixedStyles.outOfStockTag]}>
              <Text style={[bodyStyles, {fontSize: 12, color: '#222'}]}>Sold Out</Text>
            </View>
          )}
          <ImageComponent
            style={[
              {
                width: '100%',
                aspectRatio: imageAspectRatio,
              },
            ]}
            source={{
              uri: item?.featuredImage,
            }}
            resizeMode="cover"
          />
          <View style={[fixedStyles.detailsContainer]}>
            <Pressable
              style={[fixedStyles.wishlistButton, {backgroundColor: '#FFFFFF'}]}
              onPress={() => {
                inWL ? onRemoveFromWishlist(item) : onAddToWishlist(item);
                setInWL(!inWL);
              }}>
              <MaterialCommunityIcons size={20} name={inWL ? 'heart' : 'heart-outline'} color="#1e1212ff" />
            </Pressable>
            <Text
              style={[bodyStyles, {color: onBackgroundColor, textAlign: 'center', fontSize: 12}]}
              numberOfLines={2}
              minimumFontScale={1}>
              {item?.title}
            </Text>

            <View style={[fixedStyles.priceContainer]}>
              <Text style={[subHeadingStyles, {color: onBackgroundColor, fontSize: 14}]}>
                {item?.variants[0]?.displaySalePrice}
              </Text>
              {item?.variants[0].salePrice !== item?.variants[0].price && item?.variants[0].price !== 0 ? (
                <Text
                  style={[
                    subHeadingStyles,
                    fixedStyles.strikeOffPrice,
                    {color: '#a3a3a3', textDecorationLine: 'line-through', fontSize: 14},
                  ]}>
                  {item.variants[0]?.displayPrice}
                </Text>
              ) : (
                <></>
              )}
            </View>
          </View>
        </Pressable>
      </View>
    );
  },
);

const fixedStyles = StyleSheet.create({
  pageHeaderText: {fontWeight: '500', textAlignVertical: 'center', textAlign: 'center'},
  bannerOverlayText: {
    fontWeight: '500',
    color: '#ffffff',
    fontSize: 24,
    position: 'absolute',
    top: '50%',
    width: '100%',
    textAlign: 'center',
  },
  bannerGradientOverlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    aspectRatio: 2,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  NoResultCont: {flex: 1, alignItems: 'center', justifyContent: 'center'},
  noResultImage: {width: '70%', aspectRatio: 1},
  loadingImage: {width: '100%', height: '100%'},
  loadingImageCont: {flex: 1},
  pageHeaderCont: {flex: 1, justifyContent: 'center'},
  detailsContainer: {
    flex: 1,
    paddingTop: 8,
    alignItems: 'center',
    borderTopWidth: 1,
    borderColor: '#e3e3e3',
  },
  ratingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#000',
  },
  ratingStarStyle: {
    fontSize: 13,
    lineHeight: 18,
    color: '#50ac0a',
  },
  priceContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 12,
    color: '#000',
  },
  strikeOffPrice: {
    fontSize: 12,
    marginHorizontal: 4,
  },
  ctaActionsContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 2,
  },
  wishlistButton: {
    margin: 0,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
    position: 'absolute',
    right: 10,
    top: -40,
    padding: 6,
    borderRadius: 50,
  },
  discountTag: {
    margin: 0,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
    position: 'absolute',
    right: 6,
    top: 6,
    backgroundColor: '#232323',
    zIndex: 3,
    padding: 4,
  },
  outOfStockTag: {
    margin: 0,
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexBasis: 'auto',
    position: 'absolute',
    right: 6,
    top: 6,
    borderColor: '#d2d2d2',
    borderWidth: 1,
    zIndex: 3,
    padding: 4,
    backgroundColor: '#f6f6f6',
  },
  bottomBar: {
    height: 68,
    flexBasis: 68,
    flex: 0,
    padding: 0,
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: 12,
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControlsIcon: {
    fontSize: 24,
    paddingRight: 4,
  },
  sortModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  sortModalButtons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    margin: 12,
    borderColor: '#E4E4E7',
  },
  filterModalContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
    maxHeight: 600,
    minHeight: 100,
  },
  filterEntryContainer: {
    flex: 1,
    paddingVertical: 4,
    marginBottom: 8,
  },
  filterTitleText: {
    fontSize: 16,
    lineHeight: 20,
    paddingTop: 8,
    paddingBottom: 8,
  },
  filterValuesContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 4,
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  filterValueButtons: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 4,
  },
  ratingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingsBadge: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingVertical: 2,
    alignItems: 'center',
  },
  ratingSection: {
    flexDirection: 'row',
    borderRadius: 5,
  },
  pageLoadingIndicator: {width: '100%', minHeight: 100, alignItems: 'center', justifyContent: 'center'},
});

export default registerNativePage('SparklPLP', SparklPLP, {}, {});
