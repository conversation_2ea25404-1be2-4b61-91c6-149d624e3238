import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';

import {
  navigateToScreen, 
  createDeepEqualSelector,
  Ionicons, MaterialCommunityIcons,
  performHapticFeedback,
  selectPluginConfig,
  datasourceTypeModelSel,
  goBack,
  useTheme,
  generateTypographyByPlatform,
  useLoadedFonts
} from 'apptile-core';

import {ImageComponent} from 'apptile-core';

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);

const KimiricaHeader: React.FC = props => {
  const dispatch = useDispatch();
  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  const [cartItems, setCartItems] = useState([]);
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);

  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);

  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const navigateBack = useCallback(() => {
    performHapticFeedback('tap');
    goBack();
  }, []);

  const navigateSearch = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Search', {}));
  }, [dispatch]);

  const navigateWL = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Wishlist', {}));
  }, [dispatch]);

  const navigateCart = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const textColor = themeEvaluator('colors.onBackground');
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);

  return (
    <View style={[fixedStyles.headerContainer]}>
      <View style={[fixedStyles.leftContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} hitSlop={5} onPress={navigateBack}>
          <MaterialCommunityIcons size={32} name="chevron-left" color="#333" />
        </Pressable>

        {props.children ? (
          props.children
        ) : (
          <ImageComponent
            style={[{height: 28, width: 100}]}
            source={{
              uri: 'https://cdn.apptile.io/3d2ba87a-3ccd-4257-b369-e844afd58498/060b52de-7c76-48e2-8eb2-fdbfd0409f82/original-480x480.png',
            }}
            resizeMode={'contain'}
          />
        )}
      </View>
      <View style={[fixedStyles.rightContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} onPress={navigateSearch}>
          <Ionicons size={22} name="search-outline" color="#333" />
        </Pressable>
        <Pressable style={[fixedStyles.headerButtons]} onPress={navigateWL}>
          <Ionicons size={22} name="heart-outline" color="#333" />
          {wishlistItems?.length ? (
            <View style={[fixedStyles.iconBadge]}>
              <Text style={[fixedStyles.iconBadgeText]}>{wishlistItems?.length}</Text>
            </View>
          ) : (
            <></>
          )}
        </Pressable>
        <Pressable style={[fixedStyles.headerButtons]} onPress={navigateCart}>
          <Ionicons size={22} name="cart-outline" color="#333" />
          {cartItems?.length ? (
            <View style={[fixedStyles.iconBadge]}>
              <Text style={[fixedStyles.iconBadgeText]}>{cartItems ? _.sumBy(cartItems, v => v?.newQuantity) : 0}</Text>
            </View>
          ) : (
            <></>
          )}
        </Pressable>
      </View>
    </View>
  );
};

const fixedStyles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    flexGrow: 0,
    height: 56,
    flexBasis: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: '#fff',
  },

  leftContainer: {
    flexDirection: 'row',
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 4,
    flexGrow: 1,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtons: {
    width: 42,
    height: 42,
    padding: 8,
    borderRadius: 12,
    marginRight: 12,
  },
  iconBadge: {
    position: 'absolute',
    right: 4,
    top: 4,
    width: 12,
    height: 12,
    borderRadius: 12,
    backgroundColor: '#000000',
    flex: 1,
    flexGrow: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBadgeText: {
    fontSize: 9,
    color: '#fff',
  },
});

export default KimiricaHeader;
