import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {
  goBack,
  navigateToScreen, 
  createDeepEqualSelector, 
  Ionicons, 
  MaterialCommunityIcons, 
  performHapticFeedback, 
  selectPluginConfig, 
  datasourceTypeModelSel
} from 'apptile-core';

import {ImageComponent} from 'apptile-core';

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);

type PilgrimHeaderProps = {
  hideWishListIcon?: boolean;
  hideCartIcon?: boolean;
  hideSearchIcon?: boolean;
};

const PilgrimHeader: React.FC<React.PropsWithChildren<PilgrimHeaderProps>> = props => {
  const {hideWishListIcon, hideCartIcon, hideSearchIcon} = props;
  const dispatch = useDispatch();
  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  const [cartItems, setCartItems] = useState([]);
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);

  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);

  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const navigateBack = useCallback(() => {
    performHapticFeedback('tap');
    goBack();
  }, []);

  const navigateSearch = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Search', {}));
  }, [dispatch]);

  const navigateWL = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Wishlist', {}));
  }, [dispatch]);

  const navigateCart = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  return (
    <View style={[fixedStyles.headerContainer]}>
      <View style={[fixedStyles.leftContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} hitSlop={5} onPress={navigateBack}>
          <MaterialCommunityIcons size={28} name="arrow-left" color="#333" />
        </Pressable>
        {props.children ? (
          props.children
        ) : (
          <ImageComponent
            style={[{height: 32, width: 90}]}
            source={{
              uri: 'https://cdn.apptile.io/83529966-6875-46fc-b09e-7a51f7242238/56588087-eb9a-4be9-8108-53a1a759a992/original-720x720.png',
            }}
            resizeMode={'contain'}
          />
        )}
      </View>
      <View style={[fixedStyles.rightContainer]}>
        {hideSearchIcon ? (
          <></>
        ) : (
          <Pressable style={[fixedStyles.headerButtons]} onPress={navigateSearch}>
            <Ionicons size={22} name="search" color="#333" />
          </Pressable>
        )}
        {hideWishListIcon ? (
          <></>
        ) : (
          <Pressable style={[fixedStyles.headerButtons]} onPress={navigateWL}>
            <Ionicons size={22} name="heart-outline" color="#333" />
            {wishlistItems?.length ? (
              <View style={[fixedStyles.iconBadge]}>
                <Text style={[fixedStyles.iconBadgeText]}>{wishlistItems?.length}</Text>
              </View>
            ) : (
              <></>
            )}
          </Pressable>
        )}
        {hideCartIcon ? (
          <></>
        ) : (
          <Pressable style={[fixedStyles.headerButtons]} onPress={navigateCart}>
            <Ionicons size={22} name="cart-outline" color="#333" />
            {cartItems?.length ? (
              <View style={[fixedStyles.iconBadge]}>
                <Text style={[fixedStyles.iconBadgeText]}>{_.sumBy(cartItems, 'displayQuantity') || '0'}</Text>
              </View>
            ) : (
              <></>
            )}
          </Pressable>
        )}
      </View>
    </View>
  );
};

const fixedStyles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    flexGrow: 0,
    height: 56,
    flexBasis: 56,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    backgroundColor: '#fff',
  },
  leftContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 12,
  },
  rightContainer: {
    flexDirection: 'row',
    flexBasis: 'auto',
    width: 'auto',
    flex: 0,
    flexGrow: 0,
    alignItems: 'center',
  },
  headerButtons: {
    width: 32,
    height: 32,
    padding: 4,
    borderRadius: 12,
    marginRight: 12,
  },
  iconBadge: {
    position: 'absolute',
    right: 4,
    top: 4,
    width: 12,
    height: 12,
    borderRadius: 12,
    backgroundColor: '#C40B12',
    flex: 1,
    flexGrow: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBadgeText: {
    fontSize: 9,
    color: '#fff',
  },
});

export default PilgrimHeader;
