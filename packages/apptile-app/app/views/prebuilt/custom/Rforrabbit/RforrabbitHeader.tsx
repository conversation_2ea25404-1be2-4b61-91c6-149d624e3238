import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, Share, StyleSheet, View, Text} from 'react-native';
import {useSelector} from 'react-redux';
import _ from 'lodash';
import {
  createDeepEqualSelector,
  Ionicons,
  MaterialCommunityIcons,
  performHapticFeedback,
  datasourceTypeModelSel,
  goBack,
} from 'apptile-core';

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');

const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);

const RforrabbitHeader: React.FC = props => {
  const {product} = props;
  const ShopifyDSModel = useSelector(shopifyModelSel);

  const [cartItems, setCartItems] = useState([]);
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);

  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const navigateBack = useCallback(() => {
    performHapticFeedback('tap');
    goBack();
  }, []);

  const shareProduct = useCallback(() => {
    Share.share({
      message: `https://rforrabbit.com/products/${product?.handle}`,
      url: `https://rforrabbit.com/products/${product?.handle}`,
      title: product?.title,
    });
  }, [product]);

  return (
    <View style={[fixedStyles.headerContainer]}>
      <View style={[fixedStyles.leftContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} hitSlop={5} onPress={navigateBack}>
          <MaterialCommunityIcons size={32} name="chevron-left" color="#333" />
        </Pressable>

        <Text numberOfLines={2} ellipsizeMode="tail" style={[fixedStyles.truncate, {color: '#333'}]}>
          {product?.title ? product?.title : ''}
        </Text>
      </View>
      <View style={[fixedStyles.rightContainer]}>
        <Pressable style={[fixedStyles.sliderButtons]} onPress={shareProduct}>
          <Ionicons size={22} name="share-social-outline" color="#333" />
        </Pressable>
      </View>
    </View>
  );
};

const fixedStyles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    flexGrow: 0,
    height: 56,
    flexBasis: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: '#fff',
  },

  leftContainer: {
    flexDirection: 'row',
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 4,
    flexGrow: 1,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtons: {
    width: 42,
    height: 42,
    padding: 8,
    borderRadius: 12,
    marginRight: 12,
  },
  iconBadge: {
    position: 'absolute',
    right: 4,
    top: 4,
    width: 12,
    height: 12,
    borderRadius: 12,
    backgroundColor: '#000000',
    flex: 1,
    flexGrow: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBadgeText: {
    fontSize: 9,
    color: '#fff',
  },
  sliderButtonsContainer: {
    position: 'absolute',
    bottom: 36,
    right: 16,
  },
  sliderButtons: {
    flex: 0,
    borderRadius: 50,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    padding: 12,
  },
  truncate: {
    overflow: 'hidden',
    // whiteSpace: 'nowrap',
    maxWidth: '80%',
  },
});

export default RforrabbitHeader;
