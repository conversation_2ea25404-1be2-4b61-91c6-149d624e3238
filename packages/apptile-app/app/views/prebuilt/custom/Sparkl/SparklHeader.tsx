import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';

import {
  navigateToScreen,
  createDeepEqualSelector,
  Ionicons,
  MaterialCommunityIcons,
  performHapticFeedback,
  datasourceTypeModelSel,
  goBack,
} from 'apptile-core';

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);

const SparklHeader: React.FC = props => {
  const dispatch = useDispatch();

  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);

  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const navigateBack = useCallback(() => {
    performHapticFeedback('tap');
    goBack();
  }, []);

  const navigateWL = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Wishlist', {}));
  }, [dispatch]);

  return (
    <View style={[fixedStyles.headerContainer]}>
      <View style={[fixedStyles.leftContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} hitSlop={5} onPress={navigateBack}>
          <MaterialCommunityIcons size={32} name="chevron-left" color="#333" />
        </Pressable>
        {props?.children}
      </View>
      <View style={[fixedStyles.rightContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} onPress={navigateWL}>
          <Ionicons size={22} name="heart-outline" color="#333" />
          {wishlistItems?.length ? (
            <View style={[fixedStyles.iconBadge]}>
              <Text style={[fixedStyles.iconBadgeText]}>{wishlistItems?.length}</Text>
            </View>
          ) : (
            <></>
          )}
        </Pressable>
      </View>
    </View>
  );
};

const fixedStyles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    flexGrow: 0,
    height: 56,
    flexBasis: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: '#fff',
  },

  leftContainer: {
    flexDirection: 'row',
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 4,
    flexGrow: 1,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtons: {
    width: 42,
    height: 42,
    padding: 8,
    borderRadius: 12,
    marginRight: 12,
  },
  iconBadge: {
    position: 'absolute',
    right: 4,
    top: 4,
    width: 12,
    height: 12,
    borderRadius: 12,
    backgroundColor: '#000000',
    flex: 1,
    flexGrow: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBadgeText: {
    fontSize: 9,
    color: '#fff',
  },
});

export default SparklHeader;
