import {Component} from 'react';
import {ImmutableMapType, ScreenConfigParams} from 'apptile-core';
import {PluginEditorsConfig} from '../../common/EditorControlTypes';
import Immutable from 'immutable';

export interface NativePageProps {
  navigation: any;
  route: any;
  isEditable: boolean;
  screen: ScreenConfigParams;
}
export type NativePageInfo = {
  id: string;
  page: Component<NativePageProps>;
  config: (options: Record<string, any>) => ImmutableMapType<any>;
  editors: PluginEditorsConfig<any>;
};

export const NativePageRegistry: {[s: string]: NativePageInfo} = {};
export const RegisteredNativePages = new Set<string>();

const connectNativePageConfig = (config: any) => (options: Record<string, any>) => {
  return Immutable.Map(config).merge(options);
};

export const GetRegisteredNativePageInfo = (id: string): NativePageInfo | undefined => {
  const NPInfo = NativePageRegistry[id];
  return NPInfo ?? undefined;
};

export const GetRegisteredNativePage = (id: string): React.Component<NativePageProps> => {
  const NPInfo = NativePageRegistry[id];
  if (NPInfo) {
    return NPInfo.page;
  }

  logger.error(`Page for the id ${id} not found.`);
  return null!;
};

export function registerNativePage(
  id: string,
  page: any,
  defaultConfig: Record<string, any>,
  editors: PluginEditorsConfig<any>,
) {
  NativePageRegistry[id] = {
    id,
    page,
    config: connectNativePageConfig(defaultConfig),
    editors,
  };

  RegisteredNativePages.add(id);
  return page;
}
