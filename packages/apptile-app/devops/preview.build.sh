#!/bin/bash -e
cd "$(dirname $0)/../"
project_path=$PWD
build_path="$PWD/build"
temp_dir="$PWD/temp"

HOMEBREW_NO_AUTO_UPDATE=1 brew list jq &>/dev/null || brew install jq
HOMEBREW_NO_AUTO_UPDATE=1 brew list xmlstarlet &>/dev/null || brew install xmlstarlet

build_android=$(jq -r '.build_android' "$project_path/devops/preview.config.json")
build_ios=$(jq -r '.build_ios' "$project_path/devops/preview.config.json")



echo -e "\n\n🧹 Cleaning build & temp directories...\n"

rm -rf $build_path/
rm -rf $temp_dir/


echo -e "\n⚙ Building framework bundles...\n"

watchman watch-del-all $project_path
npm i --no-audit

mv .env.json .env.json.backup
echo "{}" > .env.json
mkdir -p $build_path/

if [[ $build_android == "true" ]]
then
    npx --yes react-native bundle --dev false --entry-file index.js --bundle-output $build_path/index.android.bundle --platform android
fi
if [[ $build_ios == "true" ]]
then
    npx --yes react-native bundle --dev false --entry-file index.js --bundle-output $build_path/index.ios.bundle --platform ios
fi

rm .env.json
mv .env.json.backup .env.json


if [[ $build_android == "true" ]]
then
    $project_path/devops/scripts/android/preview.build.sh $project_path
else
    echo "Skipping android build..."
fi


if [[ $build_ios == "true" ]]
then
    $project_path/devops/scripts/ios/preview.build.sh $project_path
else
    echo "Skipping ios build..."
fi
