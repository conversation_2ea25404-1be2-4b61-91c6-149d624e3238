#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Check if at least one feature argument was provided
if (process.argv.length < 3) {
  console.log('No Features were Added in Manisfest during build Runtime');
  process.exit(0);
}

const MANIFEST_FILE = process.argv[2];
// Define the features based on type
const FEATURES = {
  opengl: '<uses-feature android:glEsVersion="0x00020000" android:required="true"/>',
  camera: '<uses-feature android:name="android.hardware.camera"/>',
  autofocusCamera: '<uses-feature android:name="android.hardware.camera.autofocus"/>',
};

// Collect features to add based on CLI arguments
const FEATURES_TO_ADD = process.argv.slice(3).map(featureType => {
  if (!FEATURES[featureType]) {
    console.error(`Invalid feature type: ${featureType}`);
    process.exit(1);
  }
  return FEATURES[featureType];
});

// Read the AndroidManifest.xml file
fs.readFile(MANIFEST_FILE, 'utf8', (err, data) => {
  if (err) {
    console.error(`Error reading ${MANIFEST_FILE}:`, err);
    process.exit(1);
  }

  let modified = false;

  // Add each feature if it does not already exist in the file
  FEATURES_TO_ADD.forEach(feature => {
    if (!data.includes(feature.trim())) {
      // Insert the feature before the closing </manifest> tag
      data = data.replace(/<\/manifest>/, `    ${feature}\n</manifest>`);
      console.log(`Added: ${feature}`);
      modified = true;
    } else {
      console.log(`Already exists: ${feature}`);
    }
  });

  // Write the modified data back to the AndroidManifest.xml file if changes were made
  if (modified) {
    fs.writeFile(MANIFEST_FILE, data, 'utf8', err => {
      if (err) {
        console.error(`Error writing ${MANIFEST_FILE}:`, err);
        process.exit(1);
      }
      console.log(`Features have been added to ${MANIFEST_FILE}.`);
    });
  } else {
    console.log('No modifications were necessary.');
  }
});
