#!/usr/bin/env node

const fs = require('fs');
const plist = require('plist');

// Check if at least one key argument was provided
if (process.argv.length < 3) {
  console.log('No Permissions were added..');
  process.exit(0);
}

const PLIST_FILE = process.argv[2];

// Define the keys and their default values
const KEYS = {
  camera: {
    key: 'NSCameraUsageDescription',
    defaultValue: 'This app requires access to the camera',
  },
  location: {
    key: 'NSLocationWhenInUseUsageDescription',
    defaultValue: 'We use your location to provide location-based services and enhance your app experience.',
  },
  microphone: {
    key: 'NSMicrophoneUsageDescription',
    defaultValue: 'This app requires access to the microphone',
  },
  apptracking: {
    key: 'NSUserTrackingUsageDescription',
    defaultValue:
      'Your privacy matters. We collect usage data to enhance your app experience. Rest assured, your information is handled securely and used solely for improvement',
  },
};

// Read the Info.plist file
fs.readFile(PLIST_FILE, 'utf8', (err, data) => {
  if (err) {
    console.error(`Error reading ${PLIST_FILE}:`, err);
    process.exit(1);
  }

  let plistData;
  try {
    plistData = plist.parse(data);
  } catch (err) {
    console.error(`Error parsing ${PLIST_FILE}:`, err);
    process.exit(1);
  }

  let modified = false;

  // Collect key-value pairs from CLI arguments
  const args = process.argv.slice(3);
  args.forEach(arg => {
    let [type, value] = arg.split('=');
    value = value ? value.replace(/(^"|"$)/g, '') : null; // Remove quotes if present

    if (KEYS[type]) {
      const key = KEYS[type].key;
      const finalValue = value === 'null' || value === undefined || value === '' ? KEYS[type].defaultValue : value;
      if (plistData[key] !== finalValue) {
        plistData[key] = finalValue;
        console.log(`Added/Updated: ${key} = ${finalValue}`);
        modified = true;
      } else {
        console.log(`Already exists: ${key} = ${finalValue}`);
      }
    } else {
      console.error(`Invalid key type: ${type}`);
      console.log(`Valid types: ${Object.keys(KEYS).join(', ')}`);
      process.exit(1);
    }
  });

  // Write the modified data back to the Info.plist file if changes were made
  if (modified) {
    const newData = plist.build(plistData);
    fs.writeFile(PLIST_FILE, newData, 'utf8', err => {
      if (err) {
        console.error(`Error writing ${PLIST_FILE}:`, err);
        process.exit(1);
      }
      console.log(`Keys and values have been added to ${PLIST_FILE}.`);
    });
  } else {
    console.log('No modifications were necessary.');
  }
});
