//
//  ApplePayManager.m
//  ShopifyApplePay
//
//  Created by <PERSON><PERSON> on 24/09/22.
//

#import "ApptileApplePay.h"
#import "ReactNativeTSProject-Bridging-Header.h"

@interface RCT_EXTERN_REMAP_MODULE(ApptileApplePay, ApplePayService, RCTEventEmitter)

RCT_EXTERN_METHOD(supportedEvents)
//RCT_EXTERN_METHOD(createSession:(NSDictionary)payCheckout merchantIdentifier:(NSString *)merchantIdentifier)

RCT_EXTERN_METHOD(createSession:(NSString *)merchantIdentifier payCheckout:(NSDictionary *)payCheckout)


RCT_EXTERN_METHOD(initApplePay:(RCTPromiseResolveBlock)resolve
                 withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(canMakePayments:(RCTPromiseResolveBlock)resolve
                 withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(abort:(RCTPromiseResolveBlock)resolve
                 withRejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(updateContactCallback:(NSDictionary *)params)

RCT_EXTERN_METHOD(updateShippingMethodCallback:(NSDictionary *)params)

RCT_EXTERN_METHOD(complete:(RCTPromiseResolveBlock)resolve
                 withRejecter:(RCTPromiseRejectBlock)reject)
@end

