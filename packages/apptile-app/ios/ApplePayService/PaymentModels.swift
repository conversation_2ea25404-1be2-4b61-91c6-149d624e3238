//
//  PaymentModels.swift
//  ShopifyApplePay
//
//  Created by <PERSON><PERSON> on 04/10/22.
//

import Foundation



//public struct PayGiftCard {
//   public let id: String
//   public let balance: Decimal
//   public let amount: Decimal
//   public let lastCharacters: String
//
//   public init(id: String, balance: Decimal, amount: Decimal, lastCharacters: String) {
//       self.id             = id
//       self.balance        = balance
//       self.amount         = amount
//       self.lastCharacters = lastCharacters
//   }
//}
//
//public struct PayDiscount {
//
//   public let code:   String
//   public let amount: Decimal
//
//   public init(code: String, amount: Decimal) {
//       self.code   = code
//       self.amount = amount
//   }
//}
//
//public struct PayLineItem {
//
//   public let price:    Decimal
//   public let quantity: Int
//
//   public init(price: Decimal, quantity: Int) {
//       self.price    = price
//       self.quantity = quantity
//   }
//}


public struct PayPostalAddress {
  
  public let city: String?
  
  /// Country (eg: "Canada")
  public let country: String?
  
  /// ISO country code (eg: "ca")
  public let countryCode: String?
  
  /// Province (eg: "ON" or "Ontario")
  public let province: String?
  
  /// Zip or postal code (eg: "M5V 2J4")
  public let zip: String?
  
  /// True if the zip or postal code has been padded. For example,
  /// if the value passed in was "M5V", this property will return
  /// `true` and `zip` will be "M5V 0Z0"
  public let isPadded: Bool
  
  /// The original, non-padded zip code that was used to create the address
  internal let originalZip: String?
  
  // ----------------------------------
  //  MARK: - Init -
  //
  public init(city: String? = nil,
              country:     String? = nil,
              countryCode: String? = nil,
              province:    String? = nil,
              zip:         String? = nil) {
    
    self.city         = city
    self.country      = country
    self.countryCode  = countryCode
    self.province     = province
    self.originalZip  = zip
    
    
    let countryCode = self.countryCode?.lowercased() ?? ""
    if let zip = zip {
      
      let trimmedZip = zip.trimmingCharacters(in: .whitespacesAndNewlines)
      if trimmedZip.count < 4 {
        let (zip, isPadded) = PayPostalAddress.paddedPostalCode(trimmedZip, for: countryCode)
        self.zip      = zip
        self.isPadded = isPadded
      } else {
        self.zip      = trimmedZip
        self.isPadded = false
      }
      
    } else {
      self.zip      = nil
      self.isPadded = false
    }
  }
  
  
  private static func paddedPostalCode(_ postalCode: String, for countryCode: String) -> (postalCode: String, isModified: Bool) {
    switch countryCode {
    case "ca":
      return ("\(postalCode) 0Z0", true)
      
    case "gb":
      return ("\(postalCode) 0ZZ", true)
      
    default:
      return (postalCode, false)
    }
  }
}


public struct PayAddress {
  
  public let addressLine1: String?
  
  public let addressLine2: String?
  
  public let city: String?
  
  public let country: String?
  
  public let province: String?
  
  public let zip: String?
  
  public let firstName: String?
  
  public let lastName: String?
  
  public let phone: String?
  
  public let email: String?
  
  public init(addressLine1: String? = nil,
              addressLine2: String? = nil,
              city:         String? = nil,
              country:      String? = nil,
              province:     String? = nil,
              zip:          String? = nil,
              
              firstName:    String? = nil,
              lastName:     String? = nil,
              phone:        String? = nil,
              email:        String? = nil) {
    
    self.addressLine1 = addressLine1
    self.addressLine2 = addressLine2
    self.city         = city
    self.country      = country
    self.province     = province
    self.zip          = zip
    
    self.firstName    = firstName
    self.lastName     = lastName
    self.phone        = phone
    self.email        = email
  }
}

#if canImport(PassKit)

import PassKit


internal extension PayPostalAddress {
  
  init(with address: CNPostalAddress) {
    self.init(
      city:        address.city,
      country:     address.country,
      countryCode: address.isoCountryCode,
      province:    address.state,
      zip:         address.postalCode
    )
  }
}

internal extension PayAddress {
  
  init(with contact: PKContact) {
    
    var line1: String?
    var line2: String?
    
    if let address = contact.postalAddress {
      let street = address.street
      if !street.isEmpty {
        let lines  = street.components(separatedBy: .newlines)
        line1      = lines.count > 0 ? lines[0] : nil
        line2      = lines.count > 1 ? lines[1] : nil
      }
    }
    
    self.init(
      addressLine1: line1,
      addressLine2: line2,
      city:         contact.postalAddress?.city,
      country:      contact.postalAddress?.country,
      province:     contact.postalAddress?.state,
      zip:          contact.postalAddress?.postalCode,
      firstName:    contact.name?.givenName,
      lastName:     contact.name?.familyName,
      phone:        contact.phoneNumber?.stringValue,
      email:        contact.emailAddress
    )
  }
}

#endif


//public struct PayShippingRate {
//
//   /// Represents a `from` and `to` date for the expected delivery time frame. If the `to` date is `nil`, then the expected delivery window is roughly around the `from` date.
//   ///
//   public struct DeliveryRange {
//
//       /// Delivery is expected no earlier than `from` date.
//       public let from: Date
//
//       /// Delivery is expected no later than `to` date.
//       public let to:   Date?
//
//       /// Create a delivery range.
//       ///
//       /// - parameters:
//       ///     - from: A date from which to expect delivery.
//       ///     - to: A date until which to expect delivery.
//       ///
//       public init(from: Date, to: Date? = nil) {
//           self.from = from
//           self.to   = to
//       }
//
//       /// A string that describes how many days until expected delivery (eg: "1 - 2 days").
//       public func descriptionFrom(_ date: Date) -> String {
//           let firstDelta = date.daysUntil(self.from)
//
//           guard let toDate = self.to else {
//               let suffix  = firstDelta == 1 ? "" : "s"
//               return "\(firstDelta) day\(suffix)"
//           }
//
//           let secondDelta = date.daysUntil(toDate)
//           return "\(firstDelta) - \(secondDelta) days"
//       }
//   }
//
//   /// A handle that uniquely identifies this shipping rate.
//   public let handle: String
//
//   /// A human-friendly name for this shipping rate.
//   public let title: String
//
//   /// Shipping rate price.
//   public let price: Decimal
//
//   /// A delivery range that represents a timeframe during which a delivery is expected.
//   public let deliveryRange: DeliveryRange?
//
//   public init(handle: String, title: String, price: Decimal, deliveryRange: DeliveryRange? = nil) {
//       self.handle        = handle
//       self.title         = title
//       self.price         = price
//       self.deliveryRange = deliveryRange
//   }
//}

//private extension Date {
//
//   static let calendar = Calendar.current
//
//   func daysUntil(_ date: Date) -> Int {
//
//       let calendar = Date.calendar
//       let start    = calendar.startOfDay(for: self)
//       let end      = calendar.startOfDay(for: date)
//       let delta    = calendar.dateComponents([.day], from: start, to: end)
//
//       return delta.day!
//   }
//}

//#if canImport(PassKit)

//import PassKit

//
//internal extension PayShippingRate {
//
//   var summaryItem: PKShippingMethod {
//       let item = PKShippingMethod(label: self.title, amount: self.price as NSDecimalNumber)
//
//       if let deliveryRange = self.deliveryRange {
//           item.detail = deliveryRange.descriptionFrom(Date())
//       } else {
//           item.detail = "No delivery estimate provided."
//       }
//       item.identifier = self.handle
//
//       return item
//   }
//}

//internal extension Array where Element == PayShippingRate {
//
//   var summaryItems: [PKShippingMethod] {
//       return self.map {
//           $0.summaryItem
//       }
//   }
//
//   func shippingRateFor(_ shippingMethod: PKShippingMethod) -> Element? {
//       return self.filter {
//           $0.handle == shippingMethod.identifier!
//       }.first
//   }
//}

//#endif

#if canImport(PassKit)
import PassKit

public class PayCheckout: NSObject {
  
  public var checkoutId:               String
  //   public let hasLineItems:     Bool
  public var needsShipping:    Bool
  
  //   public let giftCards:        [PayGiftCard]?
  //   public let discount:         PayDiscount?
  //   public let shippingDiscount: PayDiscount?
  //   public let lineItems:        [PayLineItem]
  public var shippingAddress:  PayAddress?
  //   public let shippingRate:     PayShippingRate?
  
  public var currencyCode:     String
  public var countryCode:     String
  //   public let totalDuties:      Decimal?
  //   public let subtotalPrice:    Decimal
  //   public let totalTax:         Decimal
  public var paymentDue:       Decimal
  
  public init(checkoutId: String, needsShipping:Bool, shippingAddress: PayAddress?, currencyCode: String, paymentDue: Decimal, countryCode:String) {
    
    self.checkoutId               = checkoutId
    self.needsShipping = needsShipping
    
    //       self.lineItems        = lineItems
    self.shippingAddress  = shippingAddress
    //       self.shippingRate     = shippingRate
    
    //       self.giftCards        = giftCards
    //       self.discount         = discount
    //       self.shippingDiscount = shippingDiscount
    
    self.currencyCode     = currencyCode
    self.countryCode      =  countryCode
    //       self.totalDuties      = totalDuties
    //       self.subtotalPrice    = subtotalPrice
    //       self.totalTax         = totalTax
    self.paymentDue       = paymentDue
    //       self.hasLineItems     = !lineItems.isEmpty
  }
  
}


//internal extension PayCheckout {
//
//  func paymentRequest() -> PKPaymentRequest{
//    let request = PKPaymentRequest()
//    let payCheckout = self
//    request.currencyCode = payCheckout.currencyCode
//    request.countryCode = payCheckout.countryCode
//
//    let paymentItem = PKPaymentSummaryItem.init(label: "Total", amount: payCheckout.paymentDue as NSDecimalNumber)
//    let paymentItem1 = PKPaymentSummaryItem.init(label: "SubTotal", amount: payCheckout.paymentDue as NSDecimalNumber)
//    let paymentItem2 = PKPaymentSummaryItem.init(label: "Shipping", amount: payCheckout.paymentDue as NSDecimalNumber)
//    request.paymentSummaryItems = [paymentItem1, paymentItem2, paymentItem]
//
//    request.requiredBillingContactFields  = .all
//    request.requiredShippingContactFields = .all
//    request.merchantCapabilities = [.capability3DS]
//    return request
//  }
//}

public class ApptileSession: NSObject {
  public var checkoutId:               String
  public var payeeName:               String
  public var lineItems:        [ApptileLineItem]
  public var shippingAddress:  PayAddress?
  public var currencyCode:     String
  public var countryCode:     String
  public var paymentDue:       NSDecimalNumber
  public var requiresShipping: Bool
  
  public init(checkoutId: String, payeeName:String, lineItems: [ApptileLineItem], shippingAddress: PayAddress?, currencyCode: String, paymentDue: NSDecimalNumber, countryCode:String, requiresShipping:Bool) {
    self.checkoutId = checkoutId
    self.payeeName = payeeName
    self.lineItems        = lineItems
    self.shippingAddress  = shippingAddress
    self.currencyCode     = currencyCode
    self.countryCode      =  countryCode
    self.paymentDue       = paymentDue
    self.requiresShipping = requiresShipping
  }
  
}


internal extension ApptileSession {
  
  func paymentRequest() -> PKPaymentRequest{
    let request = PKPaymentRequest()
    let session = self
    request.currencyCode = session.currencyCode
    request.countryCode = session.countryCode
    
    
    request.paymentSummaryItems = self.summaryItems()
    
    request.requiredBillingContactFields  = [.emailAddress, .postalAddress]
    request.requiredShippingContactFields = session.requiresShipping ? [.emailAddress, .postalAddress, .phoneNumber]: []
    request.merchantCapabilities = [.capability3DS]
    return request
  }
  
  func summaryItems() -> [PKPaymentSummaryItem] {
    var summaryItems = self.lineItems.summaryItems
//    summaryItems.append(PKPaymentSummaryItem(label: self.payeeName, amount: self.paymentDue as NSDecimalNumber))
    return summaryItems
  }
}

public struct ApptileLineItem {
  public let label: String
  public let price:    NSDecimalNumber
  public let quantity: NSDecimalNumber
  
  public init(label: String, price: NSDecimalNumber, quantity: NSDecimalNumber) {
    self.label = label
    self.price    = price
    self.quantity = quantity
  }
}

internal extension ApptileLineItem {
  var paymentSummaryItem: PKPaymentSummaryItem {
    let lineItem = self
    let amount = (lineItem.quantity as Decimal > 1) ? ((lineItem.price as Decimal) * (lineItem.quantity as Decimal)) as NSDecimalNumber : lineItem.price
    let summary = PKPaymentSummaryItem.init(label: lineItem.label, amount: amount)
    return summary
  }
}

internal extension Array where Element == ApptileLineItem {
   var summaryItems: [PKPaymentSummaryItem] {
       return self.map {
           $0.paymentSummaryItem
       }
   }
}
#endif
