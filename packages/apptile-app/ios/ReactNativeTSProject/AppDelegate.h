// For gif splash enable bot the following flags

// Don't remove these flags. Used by build script
// #define ENABLE_NATIVE_SPLASH
// #define ENABLE_NATIVE_SPLASH_WITH_GIF

#define MIN_SPLASH_DURATION 1.0
#define MAX_SPLASH_DURATION 15.0

#import <RCTAppDelegate.h>
#import <UIKit/UIKit.h>
#import <Firebase.h>
#import <UserNotifications/UNUserNotificationCenter.h>
#import <React/RCTImageView.h>
#import <React/RCTImageSource.h>
#import <React/RCTImageLoader.h>
#import <React/RCTBridge.h>
// ForCleverTap (Don't remove) #import <CleverTapReactManager.h>
// ForCleverTap (Don't remove) #import <CleverTap.h>

#import "RNGetValues.h"


@class RCTRootView;

@interface AppDelegate : RCTAppDelegate <FIRMessagingDelegate, UNUserNotificationCenterDelegate>

@property (nonatomic, strong) NSDictionary *launchOptions;
@property (nonatomic, strong) RCTImageView *splash;
@property BOOL minDurationPassed;
@property BOOL jsLoaded;

@end
