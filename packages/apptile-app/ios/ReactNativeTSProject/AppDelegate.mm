#import "AppDelegate.h"

#import <React/RCTBundleURLProvider.h>
#import <React/RCTLinkingManager.h>
#import <React/RCTI18nUtil.h>
#import <UserNotifications/UserNotifications.h>
#import <RNCPushNotificationIOS.h>
#import "ReactNativeTSProject-Bridging-Header.h"



/* ForFBIntegration (Don't remove) #import <AuthenticationServices/AuthenticationServices.h>
#import <SafariServices/SafariServices.h>
#import <FBSDKCoreKit/FBSDKCoreKit-Swift.h> ForFBIntegrationEnd */
/* MoengageDependency (Don't remove) #import <ReactNativeMoEngage/MoEngageInitializer.h>
#import <MoEngageSDK/MoEngageSDK.h> MoengageDependencyEnd */

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  // ForCleverTap (Don't remove) [CleverTap autoIntegrate];
  // ForCleverTap (Don't remove) [[CleverTapReactManager sharedInstance] applicationDidLaunchWithOptions:launchOptions];
  self.jsLoaded = NO;
  self.minDurationPassed = NO;
  self.moduleName = @"Apptile";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  self.initialProps = @{};

  /* FireworkDependency (Don't remove) 
  [FireworkBridge initFireworkSDK]; 
  FireworkDependencyEnd */ 

  // disable RTL
  [[RCTI18nUtil sharedInstance] allowRTL:NO];
  [[RCTI18nUtil sharedInstance] forceRTL:NO];

  /* ForFBIntegration (Don't remove) [[FBSDKApplicationDelegate sharedInstance] application:application didFinishLaunchingWithOptions:launchOptions];
  [FBSDKApplicationDelegate.sharedInstance initializeSDK]; ForFBIntegrationEnd */

  [FIRApp configure];
  [FIRMessaging messaging].delegate = self;
  /* We need to keep track of these because we may want to reinit the bridge later and
  * will need them then.
  */
  self.launchOptions = launchOptions;

  /* OneSignalRequiresItToRemove */
  if ([UNUserNotificationCenter class] != nil) {
    // iOS 10 or later
    // For iOS 10 display notification (sent via APNS)
    [UNUserNotificationCenter currentNotificationCenter].delegate = self;
    UNAuthorizationOptions authOptions = UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge;
    [[UNUserNotificationCenter currentNotificationCenter]
      requestAuthorizationWithOptions:authOptions
      completionHandler:^(BOOL granted, NSError * _Nullable error) {
        // ...
    }];
  } else {
    // iOS 10 notifications aren't available; fall back to iOS 8-9 notifications.
    UIUserNotificationType allNotificationTypes = (UIUserNotificationTypeSound | UIUserNotificationTypeAlert | UIUserNotificationTypeBadge);
    UIUserNotificationSettings *settings = [UIUserNotificationSettings settingsForTypes:allNotificationTypes categories:nil];
    [application registerUserNotificationSettings:settings];
  }
  [application registerForRemoteNotifications];
  /* OneSignalRequiresItToRemoveEnd */
  
  /* MoengageDependency (Don't remove) MoEngageSDKConfig* sdkConfig = [[MoEngageSDKConfig alloc] initWithAppId:@"<MoEngageAppId>" dataCenter: MoEngageDataCenterData_center_0<MoEngageDatacenter>];
  sdkConfig.consoleLogConfig = [[MoEngageConsoleLogConfig alloc] initWithIsLoggingEnabled:false loglevel:MoEngageLoggerTypeVerbose];
  [[MoEngageInitializer sharedInstance] initializeDefaultSDKConfig:sdkConfig andLaunchOptions:launchOptions]; MoengageDependencyEnd */
  
  BOOL result = [super application:application didFinishLaunchingWithOptions:launchOptions];
  
  [self showNativeSplash];
  
  return result;
}

- (void)showNativeSplash {
#ifdef ENABLE_NATIVE_SPLASH
  // Register for a notification sent from RNApptile that is
  // originated from javascript side in order to remove splash
  NSString *JSReadyNotification = @"JSReadyNotification";
  [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(jsDidLoad:)
                                                 name:JSReadyNotification
                                               object:nil];
  RCTBridge *bridge = self.bridge;
  

  // Load the splash image or first frame of gif from bundle
  NSURL *pngURL = [[NSBundle mainBundle] URLForResource:@"splash" withExtension:@"png"];
  NSURLRequest *requestPng = [NSURLRequest requestWithURL:pngURL];
  RCTImageSource *pngImageSource = [[RCTImageSource alloc] initWithURLRequest:requestPng size:CGSizeZero scale:1.0];
  RCTImageView *rctImageView = [[RCTImageView alloc] initWithBridge:bridge];
  rctImageView.imageSources = @[pngImageSource];
#endif
#ifdef ENABLE_NATIVE_SPLASH_WITH_GIF
  // Load the gif from the bundle
  NSURL *gifURL = [[NSBundle mainBundle] URLForResource:@"splash" withExtension:@"gif"];
  NSURLRequest *request = [NSURLRequest requestWithURL:gifURL];
  RCTImageSource *imageSource = [[RCTImageSource alloc] initWithURLRequest:request size:CGSizeZero scale:1.0];
  
  // Replace first frame with gif after 500ms (required for LaunchScreen.storyboard fadeout animation)
  NSTimeInterval delayInSeconds = 0.5;
  dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
  dispatch_after(popTime, dispatch_get_main_queue(), ^(void){
    if (self.splash != NULL) {
      [self.splash removeFromSuperview];
      [self.splash setImageSources:@[imageSource]];
      [self.window.rootViewController.view addSubview:self.splash];
    }
  });
#endif 
#ifdef ENABLE_NATIVE_SPLASH
  // Attempt to remove splash after minimum play duration
  NSTimeInterval minSplashDuration = MIN_SPLASH_DURATION + 0.5;
  dispatch_time_t minSplashTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(minSplashDuration * NSEC_PER_SEC));
  dispatch_after(minSplashTime, dispatch_get_main_queue(), ^(void){
    self.minDurationPassed = YES;
    if (self.splash != NULL && self.jsLoaded == YES) {
      [self.splash removeFromSuperview];
      self.splash = NULL;
    }
  });
  
  // Remove the splash after max duration if its not removed yet
  NSTimeInterval maxSplashDuration = MAX_SPLASH_DURATION + 0.5;
  dispatch_time_t maxSplashTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(maxSplashDuration * NSEC_PER_SEC));
  dispatch_after(maxSplashTime, dispatch_get_main_queue(), ^(void){
    if (self.splash != NULL) {
      [self.splash removeFromSuperview];
      self.splash = NULL;
    }
  });
  
  // append the splash image or gif to the window
  rctImageView.frame = self.window.frame;
  rctImageView.resizeMode = RCTResizeModeCover;
  self.splash = rctImageView;
  UIView *root = self.window.rootViewController.view;
  [root addSubview:rctImageView];
#endif
}

- (void)jsDidLoad:(NSNotification *) note
{
#ifdef ENABLE_NATIVE_SPLASH
  self.jsLoaded = YES;
  if (self.splash != NULL && self.minDurationPassed == YES) {
    [self.splash removeFromSuperview];
    self.splash = NULL;
  }
#endif
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  NSURL * bundleUrl = [self getBundleURL];
  return bundleUrl;
}

- (NSURL *)getBundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{

  /* ForFBIntegration (Don't remove) if ([[FBSDKApplicationDelegate sharedInstance] application:app openURL:url options:options]) {
    return YES;
  } ForFBIntegrationEnd */

  return [RCTLinkingManager application:app openURL:url options:options];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
  /* ForIntentFilter (Don't remove) if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) {
    NSURL *url = userActivity.webpageURL;
    if (url && ([url.scheme isEqualToString:@"http"] || [url.scheme isEqualToString:@"https"])) {
      NSString *path = url.path;
        if ([path isEqualToString:@"/"] ||
            [path hasPrefix:@"/product"] ||
            [path hasPrefix:@"/collection"]) {
          return [RCTLinkingManager application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
        } else {
          [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
          return YES;
        }
    }
    return NO;
  } ForIntentFilterEnd */
  return [RCTLinkingManager application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
}

- (void)messaging:(FIRMessaging *)messaging didReceiveRegistrationToken:(NSString *)fcmToken
{
  NSLog(@"FCM registration token: %@", fcmToken);
  // Notify about received token.
  NSDictionary *dataDict = [NSDictionary dictionaryWithObject:fcmToken forKey:@"token"];
  [[NSNotificationCenter defaultCenter] postNotificationName: @"FCMToken" object:nil userInfo:dataDict];
  // TODO: If necessary send token to application server.
  // Note: This callback is fired at each app startup and whenever a new token is generated.
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  [FIRMessaging messaging].APNSToken = deviceToken;
  [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];

  /* MoengageDependency (Don't remove) [[MoEngageSDKMessaging sharedInstance] setPushToken:deviceToken]; MoengageDependencyEnd */
}

// Receive displayed notifications for iOS 10 devices.
// Handle incoming notification messages while app is in the foreground.
- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler
{
  NSDictionary *userInfo = notification.request.content.userInfo;

  // With swizzling disabled you must let Messaging know about the message, for Analytics
  // [[FIRMessaging messaging] appDidReceiveMessage:userInfo];

  // ...

  // Print full message.
  NSLog(@"%@", userInfo);

  // Change this to your preferred presentation option
  completionHandler(UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionBadge);
}

// Handle notification messages after display notification is tapped by the user.
- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void(^)(void))completionHandler
{
  NSDictionary *userInfo = response.notification.request.content.userInfo;
  // if (userInfo[kGCMMessageIDKey]) {
  //   NSLog(@"Message ID: %@", userInfo[kGCMMessageIDKey]);
  // }

  // With swizzling disabled you must let Messaging know about the message, for Analytics
  // [[FIRMessaging messaging] appDidReceiveMessage:userInfo];

  // Print full message.
  NSLog(@"%@", userInfo);

  completionHandler();
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];

  /* MoengageDependency (Don't remove) [[MoEngageSDKMessaging sharedInstance] userNotificationCenter:center didReceive:response]; MoengageDependencyEnd */
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  // If you are receiving a notification message while your app is in the background,
  // this callback will not be fired till the user taps on the notification launching the application.
  // TODO: Handle data of notification

  // With swizzling disabled you must let Messaging know about the message, for Analytics
  // [[FIRMessaging messaging] appDidReceiveMessage:userInfo];

  // ...

  // Print full message.
  NSLog(@"%@", userInfo);

  completionHandler(UIBackgroundFetchResultNewData);
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];

  /* MoengageDependency (Don't remove) [[MoEngageSDKMessaging sharedInstance] didReceieveNotificationInApplication:application withInfo:userInfo]; MoengageDependencyEnd */
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
 [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
 
 /* MoengageDependency (Don't remove) [[MoEngageSDKMessaging sharedInstance]didFailToRegisterForPush]; MoengageDependencyEnd */
}

- (NSInteger)compareVersions:(NSString *)version1 withVersion:(NSString *)version2 {
    NSArray *parts1 = [version1 componentsSeparatedByString:@"."];
    NSArray *parts2 = [version2 componentsSeparatedByString:@"."];

    for (NSInteger i = 0; i < MIN(parts1.count, parts2.count); i++) {
        NSInteger part1 = [parts1[i] integerValue];
        NSInteger part2 = [parts2[i] integerValue];

        if (part1 < part2) {
            return -1;
        } else if (part1 > part2) {
            return 1;
        }
    }
    return 0;
}

@end
