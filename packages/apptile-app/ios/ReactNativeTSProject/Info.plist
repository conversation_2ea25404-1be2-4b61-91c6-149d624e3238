<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ANALYTICS_API_ENDPOINT</key>
	<string></string>
	<key>APPTILE_API_ENDPOINT</key>
	<string></string>
	<key>APPTILE_APP_FORK</key>
	<string>main</string>
	<key>APPTILE_APP_HOST</key>
	<string></string>
	<key>APPTILE_APP_HOST_2</key>
	<string></string>
	<key>APPTILE_APP_ID</key>
	<string></string>
	<key>APPTILE_BASE_FRAMEWORK_VERSION</key>
	<string></string>
	<key>APPTILE_BUNDLED_SAVE_ID</key>
	<string></string>
	<key>APPTILE_IS_DISTRIBUTED_APP</key>
	<string></string>
	<key>APPTILE_UPDATE_ENDPOINT</key>
	<string></string>
	<key>APPTILE_URL_SCHEME</key>
	<string>demoapptileprvw</string>
	<key>APPTILE_IS_MULTI_FORK</key>
	<string></string>
	<key>APPTILE_FORK_LIST</key>
	<string></string>
	<key>APPTILE_NEXT_BUNDLE_UPDATE_CHECK_ON_APP_FOCUS</key>
	<string></string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Apptile Dev</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLIconFile</key>
			<string>icon</string>
			<key>CFBundleURLName</key>
			<string>com.apptile.apptilepreviewdemo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>shop.61460152503.app</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<!-- <key>FireworkVideoAppID</key>
	<string>58b0975a6dd2c99ca3d5ee18e895e5f32e667722ef7630392caaf489b07a66c8</string> -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>tel</string>
		<string>https</string>
		<string>paytm</string>
		<string>paytmmp</string>
		<string>tez</string>
		<string>phonepe</string>
		<string>shop.61460152503.app</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
    	<string>12.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<!-- Do not change NSAllowsArbitraryLoads to true, or you will risk app rejection! -->
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Access camera to scan QR Code</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to provide location-based services and enhance your app experience.</string>
	 <!-- <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array> -->
</dict>
</plist>
