#!/bin/bash
# Test variables. Uncomment these to check how the script behaves when testing on local machine
# USE_PROD_KEYS=
# CODEBUILD_WEBHOOK_TRIGGER=pr/234
# CODEBUILD_WEBHOOK_TRIGGER=branch/main
# CODEBUILD_INITIATOR=AWSReservedSSO_AdministratorAccess_5213a54c6f57fa03/<EMAIL>

# BUILDING_FROM= #main "pr", "manual"
USE_PROD_ENV_VARS=false

# echo "CODEBUILD_WEBHOOK_TRIGGER=$CODEBUILD_WEBHOOK_TRIGGER"
# echo "CODEBUILD_INITIATOR=$CODEBUILD_INITIATOR"
# echo "CODEBUILD_WEBHOOK_HEAD_REF$CODEBUILD_WEBHOOK_HEAD_REF"
# echo "Determining if the build will happen from hook or manual trigger"
# if [[ $CODEBUILD_WEBHOOK_TRIGGER == branch/main ]]; then
#   BUILDING_FROM=main
# elif [[ $CODEBUILD_WEBHOOK_TRIGGER == pr/* ]]; then
#   BUILDING_FROM=pr
# elif [[ $CODEBUILD_INITIATOR == *@apptile.io ]]; then
#   BUILDING_FROM=manual
# elif [[ $CODEBUILD_WEBHOOK_HEAD_REF == refs/heads/scion/* ]]; then
#   BUILDING_FROM=branch
# else
#   echo "Cannot identify the trigger for this build! Therefore cannot determine which keys to use!"
#   exit 1
# fi

echo "Starting with BUILDING_FROM: $BUILDING_FROM"
BUILDING_FROM="${BUILDING_FROM}"

echo "Determining which env keys to use for the build"
if [[ $BUILDING_FROM == main ]]; then
  USE_PROD_ENV_VARS=true
elif [[ $BUILDING_FROM == pr ]]; then
  USE_PROD_ENV_VARS=false
elif [[ $BUILDING_FROM == branch ]]; then
  USE_PROD_ENV_VARS=true
elif [[ $BUILDING_FROM == manual ]]; then
  USE_PROD_ENV_VARS=$USE_PROD_KEYS
  if [[ $USE_PROD_ENV_VARS == "" ]]; then
    echo "For manual trigger you have to set the env variable USE_PROD_KEYS to true/false in the settings of 'Start build with overrides' to specify which keys to use in addition to specifying the commit or tag"
    exit 1
  fi
else 
  echo "Cannot determine which keys to use for build!"
  exit 1
fi

if [[ $BUILDING_FROM == notfound ]]; then
  echo "Cannot build when trigger type is not known"
  exit 1
fi

echo "USE_PROD_ENV_VARS: $USE_PROD_ENV_VARS"
 
echo "Setting .env.json for the build"
if [[ $USE_PROD_ENV_VARS == true ]]; then
  export REACT_APP_SHOPIFY_API_KEY=$REACT_APP_SHOPIFY_API_KEY_PROD
  export ENABLE_LOGROCKET=true
  cp .env-ci-prod.json .env.json
else
  export REACT_APP_SHOPIFY_API_KEY=$REACT_APP_SHOPIFY_API_KEY_DEMO
  cp .env-ci.json .env.json
fi

echo "REACT_APP_SHOPIFY_API_KEY: $REACT_APP_SHOPIFY_API_KEY"
echo "ENABLE_LOGROCKET: $ENABLE_LOGROCKET"
echo "Building using: "
cat .env.json

echo "Checking that branch is properly rebased if applicable"
echo "$BUILDING_FROM=|$BUILDING_FROM| |branch|"
REBASE_CHECK_PASSED=false
if [[ $BUILDING_FROM == main ]]; then
  echo "Passing rebase check because building from main branch"
  REBASE_CHECK_PASSED=true
elif [[ $BUILDING_FROM == branch ]]; then
  echo "Passing rebase check because of scion"
  REBASE_CHECK_PASSED=true
elif [[ $BUILDING_FROM == manual ]]; then
  echo "Passing rebase check because building by manual trigger"
  REBASE_CHECK_PASSED=true
else
  echo "Current branch: $CODEBUILD_WEBHOOK_HEAD_REF"
  INTERSECTION_COMMIT=$(git merge-base $CODEBUILD_WEBHOOK_HEAD_REF origin/main)
  LATEST_ON_MAIN=$(git merge-base origin/main origin/main)
  echo "INTERSECTION_COMMIT: $INTERSECTION_COMMIT"
  echo "LASTEST_ON_MAIN: $LATEST_ON_MAIN"
  if [[ "$LATEST_ON_MAIN" == "$INTERSECTION_COMMIT" ]]; then
    REBASE_CHECK_PASSED=true
  fi
fi

echo "REBASE_CHECK_PASSED: $REBASE_CHECK_PASSED"


if [[ "$REBASE_CHECK_PASSED" != true ]]; then
  echo "Branch is not properly rebased! Aborting build"
  exit 1
fi

npm run postinstall

echo "Starting build"
npm run build
# Fail the build if npm run build fails
if [[ "$?" != "0" ]]; then
  export CODEBUILD_BUILD_SUCCEEDING=0
  exit 1;
fi

mkdir -p dist
cp ./scripts/preparebuild.js ./dist

echo "BUILD_DIR_NAME in dobuild.sh=$BUILD_DIR_NAME"

mkdir -p ../../$BUILD_DIR_NAME

if [[ $USE_PROD_ENV_VARS == true ]]; then
  echo "Setting up artifact namespace to prod"
  mv dist ../../$BUILD_DIR_NAME/prod

  export REACT_APP_SHOPIFY_API_KEY=$REACT_APP_SHOPIFY_API_KEY_DEMO
  cp .env-ci.json .env.json
  
  echo "Doing demo build"
  npm run build

  if [[ "$?" != 0 ]]; then
    export CODEBUILD_BUILD_SUCCEEDING=0
    exit 1;
  fi

  cp ./scripts/preparebuild.js ./dist
  mv dist ../../$BUILD_DIR_NAME/demo
else
  echo "Setting up artifact namespace to demo"
  mv dist ../../$BUILD_DIR_NAME/demo
fi

# echo "Show the artifacts"
# cd ../..
# pwd
# ls $BUILD_DIR_NAME
# ls $BUILD_DIR_NAME/prod

