const fs = require('fs');
const path = require('path');

const acornPackage = path.resolve(__dirname, '../node_modules/acorn/package.json');
const acornPkg = JSON.parse(fs.readFileSync(acornPackage));
acornPkg.exports['./dist/acorn'] = './dist/acorn.js';
fs.writeFileSync(acornPackage, JSON.stringify(acornPkg, null, 2));

const acornWalkPackage = path.resolve(__dirname, '../node_modules/acorn-walk/package.json');
const acornWalkPkg = JSON.parse(fs.readFileSync(acornWalkPackage));
acornWalkPkg.exports['./dist/walk'] = './dist/walk.js';
fs.writeFileSync(acornWalkPackage, JSON.stringify(acornWalkPkg, null, 2));

console.log("Acorn has been fixed up");
