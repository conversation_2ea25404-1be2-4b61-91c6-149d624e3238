#!/bin/bash 

# This script is launched by running `npm run watchcore` in apptile-app folder.
# The pwd is /Users/<USER>/apptile-local/ReactNativeTSProjeect/packages/apptile-app

# Setup a apptile-core directory that will be watched by webpack of apptile-app. The apptile-core directory's compilation output will be set to this folder where it will put the index.browser.js, index.browser.js.map, index.mobile.js and index.mobile.js.map along with the types. That will be enough to run apptile-core in watch mode but to support expansion of sourcemaps in the devtools we will also symlink all source files in the place where the .map files expect them to be. This expected location can be found inside the .map file itself.
mkdir -p apptile-core                                              
# Set the target directory                               
target_directory="apptile-core"                               
# This is the list of source files that will be needed to unfold the sourcemaps in the devtools.                                 
source_directories=("actions" "api" "common" "components" "constant" "fonts" "icons" "index.ts" "lib" "platformSplitters" "plugins" "sagas" "selectors" "store" "styles" "views" "web")                   # Loop through the source directories                          
for dir in "${source_directories[@]}"; do                     
    target_link="$target_directory/$dir"                       
    # Check if the symlink already exists                      
    if [ -L "$target_link" ]; then                             
        echo "Removing existing symlink: $target_link"         
        rm "$target_link"                                     
    fi                                                         
    # Create a new symlink                                     
    # This is tricky to parse. The relative path here is not relative to where this script is running from but instead where the final symlink will be. In case its unclear, after creating the symlink just ls one of the symlinks to make sure you are getting the contents of apptile-core folders.
    ln -s "../../apptile-core/$dir" "$target_link"             
done

# modify .env.json to enable WATCH_CORE. This variable will be used by the webpack running for apptile-app.
jq '. + { "WATCH_CORE": true }' .env.json > .env.json.tmp && mv .env.json.tmp .env.json

cd ../apptile-core
jq '.compilerOptions += { "outDir": "../apptile-app/apptile-core/types" }' tsconfig.json > tsconfig.json.tmp && mv tsconfig.json.tmp tsconfig.json 

# Function to cleanup and exit
cleanup() {
  jq '.compilerOptions += { "outDir": "./dist/types" }' tsconfig.json > tsconfig.json.tmp && mv tsconfig.json.tmp tsconfig.json
  cd ../apptile-app
  echo "Cleaning up..."
  jq 'del(.WATCH_CORE)' .env.json > .env.json.tmp && mv .env.json.tmp .env.json
  # Kill webpack processes
  for pid in "$@"; do
    # Check if the process with the given PID exists
    if ps -p "$pid" > /dev/null; then
      echo "Killing process with PID $pid"
      kill "$pid"
    else
      echo "Process with PID $pid not found. Skipping."
    fi
  done
  exit 0
}

# Trap Ctrl+C and call the cleanup function
trap 'cleanup "$pid1" "$pid2" "$pid3"' SIGINT

export WATCH_CORE=true
# Start the first webpack process in watch mode
./node_modules/.bin/webpack --watch --config webpack.browser.js &

# Save the PID of the first process
pid1=$!

# Start the second webpack process in watch mode
./node_modules/.bin/webpack --watch --config webpack.mobile.js &

# Save the PID of the second process
pid2=$!

./node_modules/.bin/tsc -w --emitDeclarationOnly -p ./tsconfig.json &
pid3=$!

# Wait for either process to finish
wait $pid1 $pid2 $pid3
