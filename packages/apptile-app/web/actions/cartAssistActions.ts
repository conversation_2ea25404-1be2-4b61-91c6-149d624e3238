import { IProductVariant } from "@/root/app/plugins/datasource/ShopifyV_22_10/types";
import { ICustomCart, ICustomCartLineItem } from "../views/cartAssist/utils";

export const CART_ASSIST_SET_CUSTOMER = 'CART_ASSIST_SET_CUSTOMER';
export const CART_ASSIST_SET_CART = 'CART_ASSIST_SET_CART'
export const CART_ASSIST_ADD_LINE_ITEM = 'CART_ASSIST_ADD_LINE_ITEM'
export const CART_ASSIST_ADD_LINE_ITEMS = 'CART_ASSIST_ADD_LINE_ITEMS'
export const CART_ASSIST_CLEAR_ALL_LINE_ITEMS = 'CART_ASSIST_CLEAR_ALL_LINE_ITEMS'
export const CART_ASSIST_UPDATE_LINE_ITEM_QUANTITY = 'CART_ASSIST_UPDATE_LINE_ITEM_QUANTITY'
export const CART_ASSIST_UPDATE_LINE_ITEM_VARIANT = 'CART_ASSIST_UPDATE_LINE_ITEM_VARIANT'
export const CART_ASSIST_REMOVE_LINE_ITEM = 'CART_ASSIST_REMOVE_LINE_ITEM'
export const CART_ASSIST_SET_CHANGES_EXIST = 'CART_ASSIST_SET_CHANGES_EXIST'
export const CART_ASSIST_CLEAR_ALL_REMOVED_LINE_ITEMS = 'CART_ASSIST_CLEAR_ALL_REMOVED_LINE_ITEMS'

export enum LINE_ITEM_TYPE { "EXISTING", "NEW" }
export enum QUANTITY_UPDATE_TYPE { "INCREASE", "DECREASE" }
export enum CART_ACTIONS_ENUM { "CREATE", "MODIFY" }

export const setCustomer = (customer: I_CART_ASSIST_CUSTOMER) => {
  return {
    type: CART_ASSIST_SET_CUSTOMER,
    payload: customer,
  };
};

export const setCart = (cart?: ICustomCart) => {
  return {
    type: CART_ASSIST_SET_CART,
    payload: cart
  }
}

export const addLineItem = (lineItem: ICustomCartLineItem, type: LINE_ITEM_TYPE, variant?: IProductVariant) => {
  return {
    type: CART_ASSIST_ADD_LINE_ITEM,
    payload: {
      lineItem,
      type, variant
    }
  }
}

export const addLineItems = (lineItems: ICustomCartLineItem[], type: LINE_ITEM_TYPE) => {
  return {
    type: CART_ASSIST_ADD_LINE_ITEMS,
    payload: {
      lineItems,
      type
    }
  }
}

export const updateLineItemQuantity = (lineItem: ICustomCartLineItem, quantityType: QUANTITY_UPDATE_TYPE, lineType: LINE_ITEM_TYPE) => {
  return {
    type: CART_ASSIST_UPDATE_LINE_ITEM_QUANTITY,
    payload: {
      lineItem,
      quantityType,
      lineType
    }
  }
}

export const updateLineItemVariant = (lineItem: ICustomCartLineItem, variantTitle: string, lineType: LINE_ITEM_TYPE) => {
  return {
    type: CART_ASSIST_UPDATE_LINE_ITEM_VARIANT,
    payload: {
      lineItem,
      variantTitle,
      lineType
    }
  }
}

export const setChangesExist = (changesExist: boolean) => {
  return {
    type: CART_ASSIST_SET_CHANGES_EXIST,
    payload: changesExist,
  }
};

export const clearLineItems = () => {
  return { 
    type: CART_ASSIST_CLEAR_ALL_LINE_ITEMS,
  }
}

export const removeLineItem = (lineItem: ICustomCartLineItem, type: LINE_ITEM_TYPE) => {
  return {
    type: CART_ASSIST_REMOVE_LINE_ITEM,
    payload: {
      lineItem,
      type
    }
  }
}

export const clearAllRemovedLineItems = () => {
  return {
    type: CART_ASSIST_CLEAR_ALL_REMOVED_LINE_ITEMS,
  }
}