export const SET_STORE_DETAILS = 'SET_STORE_DETAILS';
export const UPDATE_STORE_DETAILS = 'UPDATE_STORE_DETAILS';
export const UPDATE_BASIC_APP_INFO = 'UPDATE_BASIC_APP_INFO';
export const UPDATE_ONBOARDING_STATUS_DONE = 'UPDATE_ONBOARDING_STATUS_DONE';
export const WEBFLOW_API_CALLS_START = 'WEBFLOW_API_CALLS_START';
export const WEBFLOW_API_CALLS_FINISHED = 'WEBFLOW_API_CALLS_FINISHED';
export const WEBFLOW_API_CALLS_ERROR = 'WEBFLOW_API_CALLS_ERROR';
export const WEBFLOW_API_CALLS_LOADING = 'WEBFLOW_API_CALLS_LOADING';
export const RESET_MANDATORY_FIELDS = 'RESET_MANDATORY_FIELDS';
export const FILL_MANDATORY_FIELDS = 'FILL_MANDATORY_FIELDS';
export const FILLING_MANDATORY_FINISHED = 'FILLING_MANDATORY_FINISHED';
export const CHANGE_ONBOARDING_METADATA = 'CHANGE_ONBOARDING_METADATA';
export const FETCH_ONBOARDING_METADATA = 'FETCH_ONBOARDING_METADATA';
export const SET_ONBOARDING_METADATA = 'SET_ONBOARDING_METADATA';
export const FETCH_BRAND = 'FETCH_BRAND';
export const START_FETCH_BRAND = 'START_FETCH_BRAND';
export const END_FETCH_BRAND = 'END_FETCH_BRAND';
export const SET_BRAND_DATA = 'SET_BRAND_DATA';
export const CURRENCY_CONVERT_SALES_AMOUNT = 'CURRENCY_CONVERT_SALES_AMOUNT';

export type UpdateStorePayload = {
  storeName?: string;
  orgId?: string;
  appId?: string;
  appSaveId?: string;
};

export const updateStoreDetails = (payload: UpdateStorePayload) => {
  return {
    type: UPDATE_STORE_DETAILS,
    payload,
  };
};

export type UpdateOnboardingPayload = {
  appId: string;
  infoObject: {isOnboarded?: boolean; name?: string; activeBlueprintUUID?: string};
};

export const updateBasicAppInfo = (payload: UpdateOnboardingPayload) => {
  return {
    type: UPDATE_BASIC_APP_INFO,
    payload,
  };
};

export type WebflowCollectionIds = {
  Themes: string;
  Tags: string;
  Stats: string;
  Integrations: string;
  Benifits: string;
  Preview: string;
};

export type WebflowApiCallsPayload = {
  webflowCollectionIds: WebflowCollectionIds;
};

export const callWebflowApis = () => {
  return {
    type: WEBFLOW_API_CALLS_START,
  };
};

export const resetMandtoryFields = () => {
  return {
    type: RESET_MANDATORY_FIELDS,
  };
};

export const fillMandtoryFields = (onboarding = false, appId: any, saveApp = false) => {
  return {
    type: FILL_MANDATORY_FIELDS,
    payload: {onboarding, appId, saveApp},
  };
};

export const fillingMandtoryFinished = (failed = false) => {
  return {
    type: FILLING_MANDATORY_FINISHED,
    payload: {failed},
  };
};

export function changeOnboardingMetadata(metadata: Record<string, any>) {
  return {
    type: CHANGE_ONBOARDING_METADATA,
    payload: {metadata},
  };
}

export function fetchOnboardingMetadata(appId: string) {
  return {
    type: FETCH_ONBOARDING_METADATA,
    payload: appId,
  };
}

export function fetchBrand(domain: string) {
  return {
    type: FETCH_BRAND,
    payload: domain,
  };
}
