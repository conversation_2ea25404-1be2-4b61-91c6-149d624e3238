import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api, currentAppConfigVersion} from './Api';
export default class AppApiV2 {
  static baseURL = '/api/v2/app';
  static getApiUrl() {
    return Api.API_SERVER + AppApiV2.baseURL;
  }

  static fetchAppForks(appUUID: string): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot Fetch app forks for appUUID: ' + appUUID);
    }
    return Api.get(AppApiV2.getApiUrl() + `/${appUUID}/forks`);
  }

  static migrateAppToV2(appUUID: string): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot migrate app for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/migrate?currentFrameworkVersion=${currentAppConfigVersion}`);
  }

  static createAppFork(appUUID: string, forkDetails: any): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot create fork for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/fork`, {forkDetails});
  }

  static createLanguageFork(
    appUUID: string,
    forkDetails: {
      forkFrom: {id: string};
      forkName: string;
      forkTitle: string;
      targetLanguage: string;
    },
  ): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot create language fork for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/language-fork`, forkDetails);
  }

  static fetchForksVisibility(appUUID: string): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot fetch visibility for appUUID: ' + appUUID);
    }
    return Api.get(AppApiV2.getApiUrl() + `/${appUUID}/language-fork-visibility`);
  }

  static updateForkVisibility(appUUID: string, forkId: number, visibility: boolean): AxiosPromise<AppConfigResponse> {
    if (!appUUID) {
      logger.warn('Cannot update visibility for appUUID: ' + appUUID);
    }
    return Api.post(AppApiV2.getApiUrl() + `/${appUUID}/language-fork-visibility`, {visibility, forkId});
  }
}
