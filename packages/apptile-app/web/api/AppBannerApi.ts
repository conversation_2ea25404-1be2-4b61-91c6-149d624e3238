import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {BannerConfig} from '../views/installBanner';

export default class AppBannerApi {
  static shopifyBaseURL = '/shopify-shop-manager/app-banner';
  static baseURL = '/api/app-banner';
  static getShopifyApiUrl() {
    return Api.API_SERVER + AppBannerApi.shopifyBaseURL;
  }

  static getApiUrl() {
    return Api.API_SERVER + AppBannerApi.baseURL;
  }

  static updateBanner(bannerConfig: BannerConfig, appId: string): AxiosPromise<any> {
    return Api.post(
      AppBannerApi.getApiUrl(),
      {bannerConfig},
      {
        headers: {
          'Content-Type': 'application/json',
          'x-app-id': appId,
        },
      },
    );
  }

  static checkShopifyAppBannerBlock(appId: string): AxiosPromise<any> {
    return Api.get(AppBannerApi.getShopifyApiUrl() + `/check-app-embed/`, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }
}
