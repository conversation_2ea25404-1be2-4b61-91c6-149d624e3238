import {AppConfigResponse} from 'apptile-server';
import {AppConfig, getAppConstants, RecordSerializer} from 'apptile-core';
import {AxiosPromise} from 'axios';

// import pkg from '../../package.json';
import {Api, currentAppConfigVersion} from './Api';
import type {IAppBranch, IAppBranchesWithScheduledOta, IAppBranchesWithScheduledOtaResponse} from './ApiTypes';

export type PublishData = {
  publishes: {
    activeAppSaves: Record<string, number>;
    updatedAtOrderDesc: string[];
  };
  targetFrameworkVersion: string;
};

export default class AppConfigApi {
  static baseURL = '/api/app';
  static getApiUrl() {
    return Api.API_SERVER + AppConfigApi.baseURL;
  }

  static fetchAppBranches(appId: string, forkId: number): AxiosPromise<unknown> {
    return Api.get(Api.API_SERVER + `/api/v2/app/${appId}/fork/${forkId}/branches`);
  }

  static fetchAppBranchesWithScheduledOtas(
    appId: string,
    forkId: number,
  ): AxiosPromise<IAppBranchesWithScheduledOtaResponse> {
    return Api.get(Api.API_SERVER + `/api/v2/app/${appId}/fork/${forkId}/branches/scheduled-otas`);
  }

  static async fetchAppManifest(appId: string): Promise<unknown> {
    try {
      const response = await Api.get(Api.API_SERVER + `/api/v2/app/${appId}/manifest`).catch(e => {
        logger.error(`Unable to load App Manifest, ERROR: ${e.message}`, e.response);
        return {};
      });

      return response?.data;
    } catch (e) {
      logger.error(e);
    }
  }

  static async fetchAppBranch(appId: string | number, forkId: number, appBranch: string): Promise<IAppBranch> {
    logger.info('[DEBUG] fetchAppData: Called');
    let fetchAppDataUrl = Api.API_SERVER + '/api/v2/app' + `/${appId}`;
    logger.info('[APPCONFIG] start');
    try {
      const response = await Api.get(fetchAppDataUrl + `/fork/${forkId}/branch/${appBranch}`).catch(e => {
        logger.error(`Unable to load App Branch from ${fetchAppDataUrl}, ERROR: ${e.message}`, e.response);
        return {};
      });

      return response.data as IAppBranch;
    } catch (e) {
      logger.error(e);
    }
  }

  static async deleteAppBranch(appId: string | number, forkId: number, appBranch: string): Promise<any> {
    let deleteAppBranchUrl = Api.API_SERVER + '/api/v2/app' + `/${appId}`;
    try {
      const response = await Api.delete(deleteAppBranchUrl + `/fork/${forkId}/branch/${appBranch}`);
      return response.data as IAppBranch;
    } catch (e) {
      logger.error(e);
    }
  }

  static async fetchAppConfigWeb(appId: string | number, forkId: number, appBranch: string): Promise<AppConfig> {
    logger.info('[DEBUG] fetchAppData: Called');
    let fetchAppDataUrl = Api.API_SERVER + '/api/v2/app' + `/${appId}`;
    logger.info('[APPCONFIG] start');
    try {
      // const response = await Api.get(fetchAppDataUrl + `/${forkId}/${appBranch}`, {withCredentials: false})
      // const appConfig: AppConfig = RecordSerializer.parse(response.data);
      // return appConfig;

      const response = await Api.get(fetchAppDataUrl + `/${forkId}/${appBranch}/noRedirect`);
      const isAppClip = await getAppConstants().IS_APP_CLIP;
      const AppData = isAppClip
        ? await Api.get(fetchAppDataUrl + `/fork/${forkId}/branch/${appBranch}`).catch(e => {
            logger.error(`Unable to load App Branch from ${fetchAppDataUrl}, ERROR: ${e.message}`, e.response);
            return {};
          })
        : {};
      const urlToLoad = isAppClip
        ? response.data?.url.replace(
            AppData.data?.headCommitId.toString(),
            AppData.data?.activePublishedCommitId.toString()
          )
        : response.data?.url;

      if (urlToLoad) {
        const url = new URL(urlToLoad);
        const trailingPartOfUrl = urlToLoad.slice(url.origin.length);
        let proxiedUrl = `/appconfigs${trailingPartOfUrl}`;
        if (__DEV__) {
          proxiedUrl = urlToLoad;
        }
        const configResponse = await Api.get(proxiedUrl, {withCredentials: false});
        const appConfig: AppConfig = RecordSerializer.parse(configResponse.data);
        return appConfig;
      } else {
        throw `No URL returned from ${Api.API_SERVER}/api/v2/app/${appId}/${forkId}/${appBranch}/noRedirect`;
      }
    } catch (e) {
      logger.error(
        `Unable to load App from ${Api.API_SERVER}/api/v2/app/${appId}/${forkId}/${appBranch}, ERROR: ${e?.message}`,
        e?.response,
      );
    }
  }

  static async fetchAppConfigLegacy(appId: string | number): Promise<AppConfig> {
    logger.info('[DEBUG] fetchAppConfigLegacy: Called');
    let fetchAppDataUrl = AppConfigApi.getApiUrl() + `/${appId}`;
    logger.info('[APPCONFIG] start');
    try {
      const response = await Api.get(AppConfigApi.getApiUrl() + `/${appId}`).catch(e =>
        logger.error(`Unable to load App from ${fetchAppDataUrl}, ERROR: ${e.message}`, e.response),
      );
      const appConfig: AppConfig = RecordSerializer.parse(response.data?.currentAppSave?.data);
      return appConfig;
    } catch (e) {
      logger.error(e);
    }
  }

  static fetchAppData(appId: string | number): AxiosPromise<AppConfigResponse> {
    return Api.get(AppConfigApi.getApiUrl() + `/${appId}`);
  }

  static async compress(text: string) {
    const textEncoder = new TextEncoder();
    const data = textEncoder.encode(text);
    const readableStream = new ReadableStream({
      start(controller) {
        controller.enqueue(data);
        controller.close();
      },
    });

    const compressedStream = readableStream.pipeThrough(new CompressionStream('gzip'));

    const chunks = [];
    let byteLength = 0;
    const reader = compressedStream.getReader();
    while (true) {
      const {done, value}: {done: boolean; value: Uint8Array} = await reader.read();
      if (done) {
        break;
      }
      byteLength += value.length;
      chunks.push(value);
    }

    const result = new Uint8Array(byteLength);
    let offset = 0;

    for (let index = 0; index < chunks.length; ++index) {
      result.set(chunks[index], offset);
      offset += chunks[index].length;
    }

    return result;
  }

  static saveAppConfig(
    appId: number,
    forkId: number,
    branchName: string,
    appSaveId: number,
    data: any,
    remark?: string,
    backup?: boolean,
  ): AxiosPromise<AppConfigResponse> {
    if (window.CompressionStream) {
      // const payload = JSON.stringify({
      //   appSaveId,
      //   remark,
      //   backup,
      //   data
      // });
      const payload = JSON.stringify(data);

      logger.info('unzipped size of appconfig: ', payload.length / (1024 * 1024), ' MB');
      return AppConfigApi.compress(payload).then((compressedPayload: Uint8Array) => {
        console.log('zipped size of appconfig: ', compressedPayload.length / (1024 * 1024), ' MB');
        const formData = new FormData();
        formData.append('forkId', forkId + '');
        formData.append('branchName', branchName);
        formData.append('previousCommitId', '' + appSaveId);
        formData.append('remark', remark || '');
        formData.append('files[]', new Blob([compressedPayload.buffer]), 'appConfig.json');

        return Api.post(
          Api.API_SERVER + '/api/v2/app' + `/${appId}?currentFrameworkVersion=${currentAppConfigVersion}`,
          formData,
          {
            headers: {'Content-Type': 'multipart/form-data'},
          },
        );
      });
    } else {
      // logger.info("Using uncompressed upload of appconfig!")
      // const payload =  {
      //   appSaveId,
      //   remark,
      //   backup,
      //   data
      // };
      // return Api.post(
      //   Api.API_SERVER + '/api/v2/app' + `/${appId}?currentFrameworkVersion=${currentAppConfigVersion}`,
      //     payload
      //   );
    }
  }
  static createAppBranch(appId: number, forkId: number, title: string, data: any): AxiosPromise<AppConfigResponse> {
    if (window.CompressionStream) {
      const payload = JSON.stringify(data);

      logger.info('unzipped size of appconfig: ', payload.length / (1024 * 1024), ' MB');
      return AppConfigApi.compress(payload).then((compressedPayload: Uint8Array) => {
        console.log('zipped size of appconfig: ', compressedPayload.length / (1024 * 1024), ' MB');
        const formData = new FormData();
        formData.append('forkId', forkId + '');
        formData.append('title', title);
        formData.append('files[]', new Blob([compressedPayload.buffer]), 'appConfig.json');

        return Api.post(
          Api.API_SERVER + '/api/v2/app' + `/${appId}/branch?currentFrameworkVersion=${currentAppConfigVersion}`,
          formData,
          {
            headers: {'Content-Type': 'multipart/form-data'},
          },
        );
      });
    } else {
      // logger.info("Using uncompressed upload of appconfig!")
      // const payload =  {
      //   appSaveId,
      //   remark,
      //   backup,
      //   data
      // };
      // return Api.post(
      //   Api.API_SERVER + '/api/v2/app' + `/${appId}?currentFrameworkVersion=${currentAppConfigVersion}`,
      //     payload
      //   );
    }
  }

  static publishAppConfig(
    appId: string,
    forkId: number,
    branchName: string,
    data: any,
    previousSaveId: number,
    remark?: string,
  ): AxiosPromise<AppConfigResponse> {
    if (window.CompressionStream) {
      const payload = JSON.stringify(data);

      logger.info('unzipped size of appconfig: ', payload.length / (1024 * 1024), ' MB');
      return AppConfigApi.compress(payload)
        .then((compressedPayload: Uint8Array) => {
          console.log('zipped size of appconfig: ', compressedPayload.length / (1024 * 1024), ' MB');
          const formData = new FormData();
          formData.append('forkId', forkId + '');
          formData.append('branchName', branchName);
          formData.append('previousCommitId', '' + previousSaveId);
          formData.append('remark', remark || '');
          formData.append('files[]', new Blob([compressedPayload.buffer]), 'appConfig.json');

          return Api.post(
            Api.API_SERVER + '/api/v2/app' + `/${appId}/publish?currentFrameworkVersion=${currentAppConfigVersion}`,
            formData,
            {
              headers: {'Content-Type': 'multipart/form-data'},
            },
          );
        })
        .then(result => {
          logger.info('Publishing to Old Config API If Required.');
          return AppConfigApi.publishToOldAPI(appId, forkId, branchName, data, previousSaveId, remark).then(() => {
            return result;
          });
        });
    } else {
      return undefined;
    }
  }

  static async publishToOldAPI(
    appId: string,
    forkId: number,
    branchName: string,
    data: any,
    previousSaveId: number,
    remark?: string,
  ) {
    const oldAppIdsToPublish = [
      '01971ac9-1039-4ce3-86a1-466936a22fc6',
      '03b87bc4-94a8-40ec-9761-9f8dddc1999a',
      '0c0026d7-de89-46b3-be6e-98dc12c5251a',
      '1bd0ba0c-8f6c-41f0-b317-dfb90cb03aa6',
      '82b5c091-81d6-4134-ae21-e7e00b1e9999',
      '9824168f-6827-47ec-a50d-730fd9173876',
      '992ffab6-24f7-46a5-b60b-13a3c8c2e76d',
      'b0e32798-6031-4cc4-8045-f65308e5e462',
      'ce854c5f-2fed-40f9-8efd-ac65d031080f',
      'd0327d44-0d79-4358-aacd-2e1abfbce0e3',
      'd6ac468c-4413-4f19-80c4-60502bd6a6c9',
      'd8332b1e-50f9-4075-8376-34cda7cda219',
      'dc2f1cea-c963-448b-b208-d920c38350c4',
      'f7e4c931-b1e2-403a-80a0-bda18fec1745',
      'f95e9bc8-4f7e-476d-990b-5630ed5d7384',
    ];
    if (oldAppIdsToPublish.includes(appId)) {
      const payload = JSON.stringify({
        appSaveId: null,
        remark,
        backup: false,
        data,
      });

      logger.info('unzipped size of appconfig: ', payload.length / (1024 * 1024), ' MB');
      return AppConfigApi.compress(payload).then((compressedPayload: Uint8Array) => {
        console.log('zipped size of appconfig: ', compressedPayload.length / (1024 * 1024), ' MB');
        return Api.post(
          AppConfigApi.getApiUrl() + `/${appId}?currentFrameworkVersion=${currentAppConfigVersion}`,
          compressedPayload.buffer,
          {
            headers: {
              'Content-Type': 'application/json',
              'Content-Encoding': 'gzip',
            },
          },
        );
      });
    } else {
      return;
    }
  }

  static publishCommit(appId: number, forkId: number, branchName: string, commitId: number) {
    return Api.post(Api.API_SERVER + '/api/v2/app' + `/${appId}/publishCommit`, {
      forkId,
      branchName,
      commitId,
      frameworkVersion: currentAppConfigVersion,
    });
  }

  static fetchAppSaveMeta(appId: number, appSaveId: number): AxiosPromise<AppConfigResponse> {
    return Api.get(AppConfigApi.getApiUrl() + `/${appId}/save-metadata?appSaveId=${appSaveId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  static fetchOrgApps(orgId: number): AxiosPromise<AppConfigResponse> {
    return Api.get(AppConfigApi.getApiUrl() + `/${orgId}`);
  }

  static getAllPublishedIds(appId: number): AxiosPromise<PublishData> {
    return Api.get(AppConfigApi.getApiUrl() + `/${appId}/allPublished`);
  }

  static verifyPublish(
    appId: number,
    previousPlublishes: PublishData,
  ): AxiosPromise<{success: 'passed' | 'failed'; reason: string}> {
    return Api.post(AppConfigApi.getApiUrl() + `/${appId}/verify`, {
      publishes: previousPlublishes,
      targetFrameworkVersion: currentAppConfigVersion,
    });
  }
}
