import axios from "axios";

const axiosInstance = axios.create();

export class AuctionApi {
  private appId: string;
  private authToken: string;
  private static baseURL = `https://api.apptile.io/auction-proxy/auction`;

  constructor(appId: string, authToken: string) {
    this.appId = appId;
    this.authToken = authToken;
  }

  private formHeaders() {
    return {
      'x-shopify-app-id': this.appId,
      Authorization: `Bearer ${this.authToken}`
    };
  }

  /* Pre-Live */
  addAuctionTags<T>(productIds: string[], streamId: string) {
    return axiosInstance.post<T>(
      AuctionApi.baseURL + '/add-tags',
      { productIds, streamId },
      { headers: this.formHeaders() }
    );
  }
  appendLotNumberToProductPages<T>(productIdLotNumberMap: any) {
    return axiosInstance.post<T>(
      AuctionApi.baseURL + '/append-lot-number',
      { productIdLotNumberMap },
      { headers: this.formHeaders() }
    )
  }

  /* During Live */
  putBidWinner<T>(body: any) {
    return axiosInstance.post<T>(
      AuctionApi.baseURL + '/winner',
      { ...body },
      { headers: this.formHeaders() }
    )
  }
  addedProductToStream<T>(productId: string, streamId: string, title: string) {
    return axiosInstance.post<T>(
      AuctionApi.baseURL + '/live/add-product',
      { productId, streamId, title },
      { headers: this.formHeaders() }
    );
  }
  removedProductFromStream<T>(productId: string) {
    return axiosInstance.delete<T>(
      AuctionApi.baseURL + `/live/remove-product/${productId}`,
      { headers: this.formHeaders() }
    );
  }

  /* Post Live */
  removeAuctionTags<T>(productIds: string[], streamId: string) {
    return axiosInstance.post<T>(
      AuctionApi.baseURL + '/remove-tags',
      { productIds, streamId },
      { headers: this.formHeaders() }
    );
  }
  
}