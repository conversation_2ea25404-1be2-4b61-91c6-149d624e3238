import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IBrandResponse} from './ApiTypes';

export default class BannerApi {
  static baseURL = '/api/banner';
  static getApiUrl(times: any) {
    let baseUrl = Api.API_SERVER + BannerApi.baseURL;
    return `${baseUrl}?times=${times}`;
  }

  static fetchBanner(url: any, times: any): AxiosPromise<IBrandResponse> {
    const data = {
      urls: [url],
    };
    return Api.post(BannerApi.getApiUrl(times), data);
  }
}
