import {WEB_API_SERVER_ENDPOINT} from '@/root/.env.json';
import {Api} from '@/root/web/api/Api';

export class AutomatedNotificationApi {
  static automatedNotificationManager = '/apptile-automated-notifications-manager';

  static getCommunicationUrl() {
    return WEB_API_SERVER_ENDPOINT + AutomatedNotificationApi.automatedNotificationManager;
  }
  static getProject<T>(appId: string) {
    return Api.get<T>(AutomatedNotificationApi.getCommunicationUrl() + '/project', {
      headers: {'x-shopify-app-id': appId},
    });
  }
  static createProject<T>(appId: string) {
    return Api.get<T>(AutomatedNotificationApi.getCommunicationUrl() + '/project/create', {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static getAllNotitfications<T>(appId: string) {
    return Api.get<T>(AutomatedNotificationApi.getCommunicationUrl() + '/notification', {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static getANotification<T>(appId: string, notificationId: string) {
    return Api.get<T>(AutomatedNotificationApi.getCommunicationUrl() + '/notification' + `/${notificationId}`, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static createNotification<T>(appId: string, createEntry: any) {
    return Api.post<T>(AutomatedNotificationApi.getCommunicationUrl() + '/notification/create', createEntry, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static publishNotification<T>(appId: string, notificationId: string) {
    return Api.post<T>(
      AutomatedNotificationApi.getCommunicationUrl() + `/notification/publish/${notificationId}`,
      {},
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static updateNotification<T>(appId: string, notificationId: string, updatedEntry: Record<string, any>) {
    return Api.patch<T>(
      AutomatedNotificationApi.getCommunicationUrl() + `/notification/update/${notificationId}`,
      updatedEntry,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static deactivateNotification<T>(appId: string, notificationId: string) {
    return Api.patch<T>(
      AutomatedNotificationApi.getCommunicationUrl() + `/notification/deactivate/${notificationId}`,
      {},
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }
}
