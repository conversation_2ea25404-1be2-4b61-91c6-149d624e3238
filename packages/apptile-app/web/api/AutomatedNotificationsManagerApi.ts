import {WEB_API_SERVER_ENDPOINT} from '@/root/.env.json';
import {Api} from '@/root/web/api/Api';

export interface Campaign {
  appId: string;
  campaignType: string;
  isActive: boolean;
  delayInSeconds: number;
  notificationTitle: string;
  notificationBody: string;
  notificationDeepLink: string | null;
  notificationImageUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCampaignPayload {
  isActive?: boolean;
  delayInSeconds?: number;
  notificationTitle: string;
  notificationBody: string;
  notificationDeepLink?: string;
  notificationImageUrl?: string;
}

export interface UpdateCampaignPayload {
  isActive?: boolean;
  delayInSeconds?: number;
  notificationTitle?: string;
  notificationBody?: string;
  notificationDeepLink?: string;
  notificationImageUrl?: string;
}

export interface ToggleCampaignStatusPayload {
  isActive: boolean;
}

export interface ToggleCampaignStatusResponse {
  message: string;
  campaign: Campaign;
}

export interface PublishCampaignResponse {
  message: string;
  campaign: Campaign;
}

export class AutomatedNotificationsManagerApi {
  static automatedNotificationManager = '/api/automated-notifications/api';

  static getCommunicationUrl() {
    return WEB_API_SERVER_ENDPOINT + AutomatedNotificationsManagerApi.automatedNotificationManager;
  }

  static getCampaignsUrl() {
    return AutomatedNotificationsManagerApi.getCommunicationUrl() + '/campaigns';
  }

  /**
   * List all campaigns for an app
   * GET /campaigns/:appId
   */
  static listCampaigns<T = Campaign[]>(appId: string) {
    return Api.get<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}`, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  /**
   * Get a specific campaign
   * GET /campaigns/:appId/:campaignType
   */
  static getCampaign<T = Campaign>(appId: string, campaignType: string) {
    return Api.get<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}/${campaignType}`, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  /**
   * Create or upsert a campaign
   * POST /campaigns/:appId/:campaignType
   */
  static createOrUpsertCampaign<T = Campaign>(
    appId: string,
    campaignType: string,
    payload: CreateCampaignPayload
  ) {
    return Api.post<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}/${campaignType}`, payload, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  /**
   * Update a campaign
   * PUT /campaigns/:appId/:campaignType
   */
  static updateCampaign<T = Campaign>(
    appId: string,
    campaignType: string,
    payload: UpdateCampaignPayload
  ) {
    return Api.put<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}/${campaignType}`, payload, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  /**
   * Toggle campaign status
   * PATCH /campaigns/:appId/:campaignType/status
   */
  static toggleCampaignStatus<T = ToggleCampaignStatusResponse>(
    appId: string,
    campaignType: string,
    payload: ToggleCampaignStatusPayload
  ) {
    return Api.patch<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}/${campaignType}/status`, payload, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  /**
   * Publish a campaign - Updates database to active AND syncs with DynamoDB
   * PUT /campaigns/:appId/:campaignType/publish
   */
  static publishCampaign<T = PublishCampaignResponse>(
    appId: string,
    campaignType: string
  ) {
    return Api.put<T>(`${AutomatedNotificationsManagerApi.getCampaignsUrl()}/${appId}/${campaignType}/publish`, {}, {
      headers: {'x-shopify-app-id': appId},
    });
  }
}
