import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {FetchGenAiApiResponse} from './ApiTypes';
import {v4 as uuid} from 'uuid';

export default class GenAiApi {
  static baseURL = '/genai/api/';
  static getApiUrl() {
    return Api.API_SERVER + GenAiApi.baseURL;
  }
  static enhanceNotification(prompt: string): AxiosPromise<FetchGenAiApiResponse> {
    const UUID = uuid();
    return Api.post(GenAiApi.getApiUrl() + `chat/notification-prompt/notification-${UUID + '-' + Date.now()}`, {
      prompt,
    });
  }
  static generateCode(
    moduleUUID: string,
    prompt: string,
    code: string,
    availableScreens: string,
  ): AxiosPromise<FetchGenAiApiResponse> {
    return Api.post(GenAiApi.getApiUrl() + `chat/generate-code/module-${moduleUUID}`, {prompt, code, availableScreens});
  }
  static getChatHistory(chatId: string): AxiosPromise<FetchGenAiApiResponse> {
    return Api.get(GenAiApi.getApiUrl() + `chat/history/${chatId}`);
  }
  static getReactCode(chatId: string): AxiosPromise<FetchGenAiApiResponse> {
    return Api.get(GenAiApi.getApiUrl() + `code/${chatId}`);
  }
  static setReactCode(chatId: string, code: string): AxiosPromise<FetchGenAiApiResponse> {
    return Api.put(GenAiApi.getApiUrl() + `code/${chatId}`, {code});
  }
}
