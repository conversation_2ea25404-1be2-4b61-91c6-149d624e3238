import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IFetchIntegrationResponse} from './ApiTypes';

export default class IntegrationsApi {
  static baseURL = '/api';
  static getApiUrl() {
    return Api.API_SERVER + IntegrationsApi.baseURL;
  }

  static fetchIntegration(appId: string, integrationId: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/integrations/${integrationId}`);
  }

  static fetchIntegrationList(category?: string): AxiosPromise<IFetchIntegrationResponse> {
    const categoryParam = category ? `?category=${encodeURIComponent(category)}` : '/';
    return Api.get(IntegrationsApi.getApiUrl() + `/integrations` + categoryParam);
  }

  static fetchIntegrationCategories(): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/integrations/categories`);
  }

  static fetchAppIntegrations(appId: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations`);
  }

  static fetchAppIntegration(appId: string, id: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${id}`);
  }

  static saveAppIntegrationCredentials(
    appId: string,
    id: string,
    credentials: any,
  ): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${id}/credentials`, credentials);
  }

  static createIntegration(appId: string, data: any): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations`, data);
  }

  static fetchAppIntegrationSecrets(appId: string, platformType: string): AxiosPromise<IFetchIntegrationResponse> {
    return Api.get(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${platformType}/credentials`);
  }

  static toggleAppIntegration(appId: string, id: string, isActive: boolean): AxiosPromise<IFetchIntegrationResponse> {
    return Api.post(IntegrationsApi.getApiUrl() + `/apps/${appId}/appIntegrations/${id}/active/${isActive}`);
  }
}
