import {WEB_API_SERVER_ENDPOINT} from '@/root/.env.json';
import {Api} from '@/root/web/api/Api';
import {OutcomeTimeRangeEnum} from './ApiTypes';

export class OneSignalPushNotificationApi {
  static baseURL = '/one-signal-proxy';

  static getCommunicationUrl() {
    return WEB_API_SERVER_ENDPOINT + OneSignalPushNotificationApi.baseURL;
  }

  //For push notification
  static getNotifications<T>(oneSignalAppId: string, appId: string, limit: number, offset: number) {
    return Api.get<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() +
        `/notifications?app_id=${oneSignalAppId}&limit=${limit}&offset=${offset}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static getNotification<T>(
    oneSignalAppId: string,
    notificationId: string,
    appId: string,
    timeRange: OutcomeTimeRangeEnum,
  ) {
    return Api.get<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() +
        `/notifications/${notificationId}?app_id=${oneSignalAppId}&outcome_names=os__click.count,os__session_duration.sum,Purchase.sum&outcome_time_range=${timeRange}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static sendPushNotification<T>(appId: string, createEntry: Record<string, any>) {
    return Api.post<T>(OneSignalPushNotificationApi.getCommunicationUrl() + '/notifications', createEntry, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static sendTestPushNotification<T>(appId: string, subscriberId: string) {
    return Api.post<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() + '/notification-test',
      {notificationSubscriberId: subscriberId},
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static stopPushNotification<T>(appId: string, notificationId: string, oneSignalAppId: string) {
    return Api.delete<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() + `/notifications/${notificationId}?app_id=${oneSignalAppId}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  //For templates/saving notifications
  static createTemplate<T>(appId: string, createEntry: Record<string, any>) {
    return Api.post<T>(OneSignalPushNotificationApi.getCommunicationUrl() + '/templates', createEntry, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static viewTemplates<T>(oneSignalAppId: string, appId: string, limit: number, offset: number) {
    return Api.get<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() +
        `/templates?app_id=${oneSignalAppId}&limit=${limit}&offset=${offset}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static viewTemplate<T>(oneSignalAppId: string, appId: string, templateId: string) {
    return Api.get<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() + `/templates/${templateId}?app_id=${oneSignalAppId}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  static deleteTemplate<T>(oneSignalAppId: string, appId: string, templateId: string) {
    return Api.delete<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() + `/templates/${templateId}?app_id=${oneSignalAppId}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }

  //For outcomes

  static getOutcomes<T>(oneSignalAppId: string, appId: string, timeRange: OutcomeTimeRangeEnum) {
    return Api.get<T>(
      OneSignalPushNotificationApi.getCommunicationUrl() +
        `/apps/${oneSignalAppId}/outcomes?outcome_names=os__click.count,os__session_duration.sum,Purchase.sum&outcome_time_range=${timeRange}`,
      {
        headers: {'x-shopify-app-id': appId},
      },
    );
  }
}
