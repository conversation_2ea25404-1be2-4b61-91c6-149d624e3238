import {AxiosPromise} from 'axios';
import _ from 'lodash';
import {Api} from './Api';
import {ITileSaveInterface, ITilesListResponse, ITileUpdateInterface} from './ApiTypes';

export default class PagesApi {
  static baseURL = '/api/pages';
  static getApiUrl() {
    return Api.API_SERVER + PagesApi.baseURL;
  }

  static createPageTemplate(pageSaveRecord: ITileSaveInterface): AxiosPromise<any> {
    return Api.post(PagesApi.getApiUrl(), pageSaveRecord, {withCredentials: true});
  }

  static getPageTemplate(uuid: string): AxiosPromise<any> {
    return Api.get(PagesApi.getApiUrl() + `/${uuid}`);
  }
  static getPages(tags: string[], offset = 0, limit = 50): AxiosPromise<ITilesListResponse> {
    return Api.get(PagesApi.getApiUrl(), {params: {tags: _.each(tags, _.toUpper).join(','), offset, limit}});
  }

  static updatePageMeta(pageUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {id: uuid, ...rest} = pageUpdateRecord;
    return Api.put(PagesApi.getApiUrl() + `/${uuid}`, rest, {withCredentials: true});
  }
  
  static updatePageTemplate(pageUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {id: uuid} = pageUpdateRecord;
    return Api.post(PagesApi.getApiUrl() + `/${uuid}/version`, pageUpdateRecord, {withCredentials: true});
  }
}
