import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IAppOnlyDiscountAttributes, IAppOnlyDiscountCreateAttributes} from './ApiTypes';

export default class ShopifyDiscountsApi {
  static baseURL = '/api/discount';
  static getApiUrl() {
    return Api.API_SERVER + ShopifyDiscountsApi.baseURL;
  }

  static syncAllDiscounts(): AxiosPromise<string> {
    return Api.get(ShopifyDiscountsApi.getApiUrl() + `/syncDiscounts`);
  }

  static getAllDiscounts(): AxiosPromise<string> {
    return Api.get(ShopifyDiscountsApi.getApiUrl() + `/`);
  }

  static getDiscountById(discountId: string): AxiosPromise<IAppOnlyDiscountAttributes> {
    //Convert discount id from string to base64
    return Api.get(ShopifyDiscountsApi.getApiUrl() + `/` + btoa(discountId));
  }

  static createAppOnlyDiscount(
    createPayload: IAppOnlyDiscountCreateAttributes,
  ): AxiosPromise<IAppOnlyDiscountAttributes> {
    return Api.post(ShopifyDiscountsApi.getApiUrl() + `/`, createPayload);
  }

  static updateAppOnlyDiscount(
    discountId: string,
    updatePayload: IAppOnlyDiscountCreateAttributes,
  ): AxiosPromise<IAppOnlyDiscountAttributes> {
    return Api.put(ShopifyDiscountsApi.getApiUrl() + `/${btoa(discountId)}`, updatePayload);
  }

  static deleteDiscountById(discountId: string): AxiosPromise<IAppOnlyDiscountAttributes> {
    return Api.delete(ShopifyDiscountsApi.getApiUrl() + `/${btoa(discountId)}`);
  }

  static activateDiscountById(discountId: string): AxiosPromise<IAppOnlyDiscountAttributes> {
    return Api.put(ShopifyDiscountsApi.getApiUrl() + `/activate/${btoa(discountId)}`);
  }
}
