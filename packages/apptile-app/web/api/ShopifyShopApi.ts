import {AxiosPromise} from 'axios';
import {Api} from './Api';

export default class ShopifyShopApi {
  static baseURL = '/api/shopify-shop';
  static getApiUrl() {
    return Api.API_SERVER + ShopifyShopApi.baseURL;
  }

  static fetchProducts(appId: string): AxiosPromise<any> {
    return Api.get(ShopifyShopApi.getApiUrl() + `/fetch-products`, {
      headers: {'X-App-Id': appId},
    });
  }

  static fetchCollections(appId: string): AxiosPromise<any> {
    return Api.get(ShopifyShopApi.getApiUrl() + `/fetch-collections`, {
      headers: {'X-App-Id': appId},
    });
  }

  static fetchApptileOrders(appId: string): AxiosPromise<any> {
    return Api.get(ShopifyShopApi.getApiUrl() + `/fetch-apptile-orders`, {
      headers: {'X-App-Id': appId},
    });
  }

  static fetchBulkStatus(appId: string, bulkTaskId: string): AxiosPromise<any> {
    return Api.post(
      ShopifyShopApi.getApiUrl() + `/bulk`,
      {id: bulkTaskId},
      {
        headers: {'X-App-Id': appId},
      },
    );
  }
}
