import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {SnapshotResponse} from './ApiTypes';

export default class SnapshotsApi {
  static baseURL = '/api/ota-scheduler';
  static getApiUrl() {
    return Api.API_SERVER + SnapshotsApi.baseURL;
  }

  static scheduleOta({forkId, branchId, startDate, endDate, targetCommitId}): AxiosPromise<AppConfigResponse> {
    return Api.post(SnapshotsApi.getApiUrl() + '/', {
      forkId,
      branchId,
      startDate,
      endDate,
      targetCommitId,
    });
  }

  static checkOtaOverlap({forkId, startDate}: {forkId: number; startDate: Date}): AxiosPromise<SnapshotResponse> {
    return Api.post(SnapshotsApi.getApiUrl() + `/check-overlapping-otas/${forkId}`, {
      startDate,
    });
  }

  static getScheduledOtas(forkId: number, history?: boolean): AxiosPromise<AppConfigResponse> {
    return Api.get(SnapshotsApi.getApiUrl() + `/${forkId}` + (history ? '?history=true' : ''));
  }
}
