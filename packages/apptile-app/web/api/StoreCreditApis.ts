import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {
  IStoreCreditCreateSignUpEventRuleData,
  IStoreCreditLiveJoinEventRuleData,
  IStoreCreditSignUpEventRuleData,
} from './ApiTypes';

export default class StoreCreditApi {
  static signupBaseURL = '/shopify-shop-manager/store-credit-signup-event-rule';
  static liveJoinBaseURL = '/shopify-shop-manager/store-credit-live-join-event-rule';
  static getSignupApiUrl() {
    return Api.API_SERVER + StoreCreditApi.signupBaseURL;
  }
  static getLiveJoinApiUrl() {
    return Api.API_SERVER + StoreCreditApi.liveJoinBaseURL;
  }

  static fetchSignUpEventRule(appId: string): AxiosPromise<IStoreCreditSignUpEventRuleData> {
    return Api.get(StoreCreditApi.getSignupApiUrl() + `/${appId}`);
  }

  static createSignUpEventRule(
    appId: string,
    data: IStoreCreditCreateSignUpEventRuleData,
  ): AxiosPromise<IStoreCreditSignUpEventRuleData> {
    return Api.post(StoreCreditApi.getSignupApiUrl() + `/${appId}`, data);
  }

  static updateSignUpEventRule(
    appId: string,
    data: IStoreCreditCreateSignUpEventRuleData,
  ): AxiosPromise<IStoreCreditSignUpEventRuleData> {
    return Api.put(StoreCreditApi.getSignupApiUrl() + `/${appId}`, data);
  }

  static deleteSignUpEventRule(appId: string): AxiosPromise<IStoreCreditSignUpEventRuleData> {
    return Api.delete(StoreCreditApi.getSignupApiUrl() + `/${appId}`);
  }

  static getLiveJoinStoreCreditData(appId: string, streamId: string): AxiosPromise<IStoreCreditLiveJoinEventRuleData> {
    return Api.get(StoreCreditApi.getLiveJoinApiUrl() + `/${appId}/${streamId}`);
  }
}
