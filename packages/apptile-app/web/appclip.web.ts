import {shouldInitWebLogrocket} from './globalvariables';
import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import {name as appName} from '../app.json';
import {setAppConstants} from 'apptile-core';
import queryString from 'query-string';
import livelyScript from './lively.txt';
import shoppableFeedScript from './lively-shoppableFeed.txt';
import WebAppClip from './WebAppClip';

function toggle_full_screen() {
  if (
    (document?.fullScreenElement && document?.fullScreenElement !== null) ||
    (!document?.mozFullScreen && !document?.webkitIsFullScreen)
  ) {
    if (document?.documentElement.requestFullScreen) {
      document?.documentElement.requestFullScreen();
    } else if (document?.documentElement.mozRequestFullScreen) {
      /* Firefox */
      document?.documentElement.mozRequestFullScreen();
    } else if (document?.documentElement.webkitRequestFullScreen) {
      /* Chrome, Safari & Opera */
      document?.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
    } else if (document?.msRequestFullscreen) {
      /* IE/Edge */
      document?.documentElement.msRequestFullscreen();
    }
  } else {
    if (document?.cancelFullScreen) {
      document?.cancelFullScreen();
    } else if (document?.mozCancelFullScreen) {
      /* Firefox */
      document?.mozCancelFullScreen();
    } else if (document?.webkitCancelFullScreen) {
      /* Chrome, Safari and Opera */
      document?.webkitCancelFullScreen();
    } else if (document?.msExitFullscreen) {
      /* IE/Edge */
      document?.msExitFullscreen();
    }
  }
}

console.log('glvarcheck', shouldInitWebLogrocket);
const parsedURL = queryString.parseUrl(window.location.href);
const {appId, screenName, params: rawParams, fullScreen, isAppClip} = parsedURL?.query ?? {};

if (fullScreen && fullScreen === 'true') {
  try {
    setTimeout(toggle_full_screen, 1000);
  } catch (error) {
    console.error('Error toggling full screen:', error);
  }
}

setAppConstants({APPTILE_APP_ID: appId, IS_APP_CLIP: isAppClip ? isAppClip === 'true' : true});

const initialProps: Record<string, any> = {};
if (screenName) initialProps.screenName = screenName;

if (rawParams) {
  try {
    initialProps.params = typeof rawParams === 'string' ? JSON.parse(rawParams) : rawParams;
  } catch (error) {
    console.error('Failed to parse params:', error);
    initialProps.params = rawParams;
  }
}

// Register and run the app
AppRegistry.registerComponent(appName, () => WebAppClip);
AppRegistry.runApplication(appName, {
  initialProps,
  rootTag: document.getElementById('pre-root'),
});

window.reloadLively = new Function(livelyScript);
window.reloadShoppableFeeds = new Function(shoppableFeedScript);
