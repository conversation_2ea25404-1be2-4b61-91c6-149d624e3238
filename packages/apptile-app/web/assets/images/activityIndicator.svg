<svg xmlns="http://www.w3.org/2000/svg"  height="100%" viewBox="0 0 32 32" width="100%">
    <circle cx="16" cy="16" fill="none" r="14" stroke-width="4" style="stroke: rgb(25, 118, 210); opacity: 0.2;"></circle>
    <circle cx="16" cy="16" fill="none" r="14" stroke-width="4" style="stroke: rgb(25, 118, 210); stroke-dasharray: 80; stroke-dashoffset: 60;"></circle>
    <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 0 0"
        to="360 0 0"
        dur="0.8s"
        repeatCount="indefinite"
    />
</svg>