/**
 * Currency constants for the application
 * Contains a list of currencies with their code, name, and symbol
 */

export type Currency = {
  code: string;
  name: string;
  symbol: string;
};

export type Country = {
  countryCode: string;
  country: string;
};

export const countriesList: Country[] = [
  {
    countryCode: 'US',
    country: 'United States',
  },
  {
    countryCode: 'AU',
    country: 'Australia',
  },
  {
    countryCode: 'BR',
    country: 'Brazil',
  },
  {
    countryCode: 'CA',
    country: 'Canada',
  },
  {
    countryCode: 'CN',
    country: 'China',
  },
  {
    countryCode: 'DE',
    country: 'Germany',
  },
  {
    countryCode: 'FR',
    country: 'France',
  },
  {
    countryCode: 'GB',
    country: 'United Kingdom',
  },
  {
    countryCode: 'IN',
    country: 'India',
  },
  {
    countryCode: 'JP',
    country: 'Japan',
  },
  {
    countryCode: 'KR',
    country: 'South Korea',
  },
  {
    countryCode: 'RU',
    country: 'Russia',
  },
  {
    countryCode: 'AD',
    country: 'Andorra',
  },
  {
    countryCode: 'AE',
    country: 'United Arab Emirates',
  },
  {
    countryCode: 'AF',
    country: 'Afghanistan',
  },
  {
    countryCode: 'AG',
    country: 'Antigua & Barbuda',
  },
  {
    countryCode: 'AI',
    country: 'Anguilla',
  },
  {
    countryCode: 'AL',
    country: 'Albania',
  },
  {
    countryCode: 'AM',
    country: 'Armenia',
  },
  {
    countryCode: 'AN',
    country: 'Netherlands Antilles',
  },
  {
    countryCode: 'AO',
    country: 'Angola',
  },
  {
    countryCode: 'AR',
    country: 'Argentina',
  },
  {
    countryCode: 'AS',
    country: 'Ascension Island.',
  },
  {
    countryCode: 'AT',
    country: 'Austria',
  },
  {
    countryCode: 'AW',
    country: 'Aruba',
  },
  {
    countryCode: 'AX',
    country: 'Åland Islands',
  },
  {
    countryCode: 'AZ',
    country: 'Azerbaijan',
  },
  {
    countryCode: 'BA',
    country: 'Bosnia & Herzegovina',
  },
  {
    countryCode: 'BB',
    country: 'Barbados',
  },
  {
    countryCode: 'BD',
    country: 'Bangladesh',
  },
  {
    countryCode: 'BE',
    country: 'Belgium',
  },
  {
    countryCode: 'BF',
    country: 'Burkina Faso',
  },
  {
    countryCode: 'BG',
    country: 'Bulgaria',
  },
  {
    countryCode: 'BH',
    country: 'Bahrain',
  },
  {
    countryCode: 'BI',
    country: 'Burundi',
  },
  {
    countryCode: 'BJ',
    country: 'Benin',
  },
  {
    countryCode: 'BL',
    country: 'St. Barthélemy',
  },
  {
    countryCode: 'BM',
    country: 'Bermuda',
  },
  {
    countryCode: 'BN',
    country: 'Brunei',
  },
  {
    countryCode: 'BO',
    country: 'Bolivia',
  },
  {
    countryCode: 'BQ',
    country: 'Caribbean Netherlands',
  },
  {
    countryCode: 'BS',
    country: 'Bahamas',
  },
  {
    countryCode: 'BT',
    country: 'Bhutan',
  },
  {
    countryCode: 'BV',
    country: 'Bouvet Island',
  },
  {
    countryCode: 'BW',
    country: 'Botswana',
  },
  {
    countryCode: 'BY',
    country: 'Belarus',
  },
  {
    countryCode: 'BZ',
    country: 'Belize',
  },
  {
    countryCode: 'CH',
    country: 'Switzerland',
  },
  {
    countryCode: 'CC',
    country: 'Cocos (Keeling) Islands',
  },
  {
    countryCode: 'CD',
    country: 'Congo - Kinshasa',
  },
  {
    countryCode: 'CF',
    country: 'Central African Republic',
  },
  {
    countryCode: 'CG',
    country: 'Congo - Brazzaville',
  },
  {
    countryCode: 'CI',
    country: 'Côte d’Ivoire',
  },
  {
    countryCode: 'CK',
    country: 'Cook Islands',
  },
  {
    countryCode: 'CL',
    country: 'Chile',
  },
  {
    countryCode: 'CM',
    country: 'Cameroon',
  },
  {
    countryCode: 'CO',
    country: 'Colombia',
  },
  {
    countryCode: 'CR',
    country: 'Costa Rica',
  },
  {
    countryCode: 'CU',
    country: 'Cuba',
  },
  {
    countryCode: 'CV',
    country: 'Cape Verde',
  },
  {
    countryCode: 'CW',
    country: 'Curaçao',
  },
  {
    countryCode: 'CX',
    country: 'Christmas Island',
  },
  {
    countryCode: 'CY',
    country: 'Cyprus',
  },
  {
    countryCode: 'CZ',
    country: 'Czechia',
  },
  {
    countryCode: 'DJ',
    country: 'Djibouti',
  },
  {
    countryCode: 'DK',
    country: 'Denmark',
  },
  {
    countryCode: 'DM',
    country: 'Dominica',
  },
  {
    countryCode: 'DO',
    country: 'Dominican Republic',
  },
  {
    countryCode: 'DZ',
    country: 'Algeria',
  },
  {
    countryCode: 'EC',
    country: 'Ecuador',
  },
  {
    countryCode: 'EE',
    country: 'Estonia',
  },
  {
    countryCode: 'EG',
    country: 'Egypt',
  },
  {
    countryCode: 'EH',
    country: 'Western Sahara',
  },
  {
    countryCode: 'ER',
    country: 'Eritrea',
  },
  {
    countryCode: 'ES',
    country: 'Spain',
  },
  {
    countryCode: 'ET',
    country: 'Ethiopia',
  },
  {
    countryCode: 'FI',
    country: 'Finland',
  },
  {
    countryCode: 'FJ',
    country: 'Fiji',
  },
  {
    countryCode: 'FK',
    country: 'Falkland Islands',
  },
  {
    countryCode: 'FO',
    country: 'Faroe Islands',
  },
  {
    countryCode: 'GA',
    country: 'Gabon',
  },
  {
    countryCode: 'GD',
    country: 'Grenada',
  },
  {
    countryCode: 'GE',
    country: 'Georgia',
  },
  {
    countryCode: 'GF',
    country: 'French Guiana',
  },
  {
    countryCode: 'GG',
    country: 'Guernsey',
  },
  {
    countryCode: 'GH',
    country: 'Ghana',
  },
  {
    countryCode: 'GI',
    country: 'Gibraltar',
  },
  {
    countryCode: 'GL',
    country: 'Greenland',
  },
  {
    countryCode: 'GM',
    country: 'Gambia',
  },
  {
    countryCode: 'GN',
    country: 'Guinea',
  },
  {
    countryCode: 'GP',
    country: 'Guadeloupe',
  },
  {
    countryCode: 'GQ',
    country: 'Equatorial Guinea',
  },
  {
    countryCode: 'GR',
    country: 'Greece',
  },
  {
    countryCode: 'GS',
    country: 'South Georgia & South Sandwich Islands',
  },
  {
    countryCode: 'GT',
    country: 'Guatemala',
  },
  {
    countryCode: 'GW',
    country: 'Guinea-Bissau',
  },
  {
    countryCode: 'GY',
    country: 'Guyana',
  },
  {
    countryCode: 'HK',
    country: 'Hong Kong SAR',
  },
  {
    countryCode: 'HM',
    country: 'Heard & McDonald Islands',
  },
  {
    countryCode: 'HN',
    country: 'Honduras',
  },
  {
    countryCode: 'HR',
    country: 'Croatia',
  },
  {
    countryCode: 'HT',
    country: 'Haiti',
  },
  {
    countryCode: 'HU',
    country: 'Hungary',
  },
  {
    countryCode: 'ID',
    country: 'Indonesia',
  },
  {
    countryCode: 'IE',
    country: 'Ireland',
  },
  {
    countryCode: 'IL',
    country: 'Israel',
  },
  {
    countryCode: 'IM',
    country: 'Isle of Man',
  },
  {
    countryCode: 'IO',
    country: 'British Indian Ocean Territory',
  },
  {
    countryCode: 'IQ',
    country: 'Iraq',
  },
  {
    countryCode: 'IR',
    country: 'Iran',
  },
  {
    countryCode: 'IS',
    country: 'Iceland',
  },
  {
    countryCode: 'IT',
    country: 'Italy',
  },
  {
    countryCode: 'JE',
    country: 'Jersey',
  },
  {
    countryCode: 'JM',
    country: 'Jamaica',
  },
  {
    countryCode: 'JO',
    country: 'Jordan',
  },
  {
    countryCode: 'KE',
    country: 'Kenya',
  },
  {
    countryCode: 'KG',
    country: 'Kyrgyzstan',
  },
  {
    countryCode: 'KH',
    country: 'Cambodia',
  },
  {
    countryCode: 'KI',
    country: 'Kiribati',
  },
  {
    countryCode: 'KM',
    country: 'Comoros',
  },
  {
    countryCode: 'KN',
    country: 'St. Kitts & Nevis',
  },
  {
    countryCode: 'KP',
    country: 'North Korea',
  },
  {
    countryCode: 'KW',
    country: 'Kuwait',
  },
  {
    countryCode: 'KY',
    country: 'Cayman Islands',
  },
  {
    countryCode: 'KZ',
    country: 'Kazakhstan',
  },
  {
    countryCode: 'LA',
    country: 'Laos',
  },
  {
    countryCode: 'LB',
    country: 'Lebanon',
  },
  {
    countryCode: 'LC',
    country: 'St. Lucia',
  },
  {
    countryCode: 'LI',
    country: 'Liechtenstein',
  },
  {
    countryCode: 'LK',
    country: 'Sri Lanka',
  },
  {
    countryCode: 'LR',
    country: 'Liberia',
  },
  {
    countryCode: 'LS',
    country: 'Lesotho',
  },
  {
    countryCode: 'LT',
    country: 'Lithuania',
  },
  {
    countryCode: 'LU',
    country: 'Luxembourg',
  },
  {
    countryCode: 'LV',
    country: 'Latvia',
  },
  {
    countryCode: 'LY',
    country: 'Libya',
  },
  {
    countryCode: 'MA',
    country: 'Morocco',
  },
  {
    countryCode: 'MC',
    country: 'Monaco',
  },
  {
    countryCode: 'MD',
    country: 'Moldova',
  },
  {
    countryCode: 'ME',
    country: 'Montenegro',
  },
  {
    countryCode: 'MF',
    country: 'St. Martin',
  },
  {
    countryCode: 'MG',
    country: 'Madagascar',
  },
  {
    countryCode: 'MK',
    country: 'North Macedonia',
  },
  {
    countryCode: 'ML',
    country: 'Mali',
  },
  {
    countryCode: 'MM',
    country: 'Myanmar (Burma)',
  },
  {
    countryCode: 'MN',
    country: 'Mongolia',
  },
  {
    countryCode: 'MO',
    country: 'Macao SAR',
  },
  {
    countryCode: 'MQ',
    country: 'Martinique',
  },
  {
    countryCode: 'MR',
    country: 'Mauritania',
  },
  {
    countryCode: 'MS',
    country: 'Montserrat',
  },
  {
    countryCode: 'MT',
    country: 'Malta',
  },
  {
    countryCode: 'MU',
    country: 'Mauritius',
  },
  {
    countryCode: 'MV',
    country: 'Maldives',
  },
  {
    countryCode: 'MW',
    country: 'Malawi',
  },
  {
    countryCode: 'MX',
    country: 'Mexico',
  },
  {
    countryCode: 'MY',
    country: 'Malaysia',
  },
  {
    countryCode: 'MZ',
    country: 'Mozambique',
  },
  {
    countryCode: 'NA',
    country: 'Namibia',
  },
  {
    countryCode: 'NC',
    country: 'New Caledonia',
  },
  {
    countryCode: 'NE',
    country: 'Niger',
  },
  {
    countryCode: 'NF',
    country: 'Norfolk Island',
  },
  {
    countryCode: 'NG',
    country: 'Nigeria',
  },
  {
    countryCode: 'NI',
    country: 'Nicaragua',
  },
  {
    countryCode: 'NL',
    country: 'Netherlands',
  },
  {
    countryCode: 'NO',
    country: 'Norway',
  },
  {
    countryCode: 'NP',
    country: 'Nepal',
  },
  {
    countryCode: 'NR',
    country: 'Nauru',
  },
  {
    countryCode: 'NU',
    country: 'Niue',
  },
  {
    countryCode: 'NZ',
    country: 'New Zealand',
  },
  {
    countryCode: 'OM',
    country: 'Oman',
  },
  {
    countryCode: 'PA',
    country: 'Panama',
  },
  {
    countryCode: 'PE',
    country: 'Peru',
  },
  {
    countryCode: 'PF',
    country: 'French Polynesia',
  },
  {
    countryCode: 'PG',
    country: 'Papua New Guinea',
  },
  {
    countryCode: 'PH',
    country: 'Philippines',
  },
  {
    countryCode: 'PK',
    country: 'Pakistan',
  },
  {
    countryCode: 'PL',
    country: 'Poland',
  },
  {
    countryCode: 'PM',
    country: 'St. Pierre & Miquelon',
  },
  {
    countryCode: 'PN',
    country: 'Pitcairn Islands',
  },
  {
    countryCode: 'PS',
    country: 'Palestinian Territories',
  },
  {
    countryCode: 'PT',
    country: 'Portugal',
  },
  {
    countryCode: 'PY',
    country: 'Paraguay',
  },
  {
    countryCode: 'QA',
    country: 'Qatar',
  },
  {
    countryCode: 'RE',
    country: 'Réunion',
  },
  {
    countryCode: 'RO',
    country: 'Romania',
  },
  {
    countryCode: 'RS',
    country: 'Serbia',
  },
  {
    countryCode: 'RW',
    country: 'Rwanda',
  },
  {
    countryCode: 'SA',
    country: 'Saudi Arabia',
  },
  {
    countryCode: 'SB',
    country: 'Solomon Islands',
  },
  {
    countryCode: 'SC',
    country: 'Seychelles',
  },
  {
    countryCode: 'SD',
    country: 'Sudan',
  },
  {
    countryCode: 'SE',
    country: 'Sweden',
  },
  {
    countryCode: 'SG',
    country: 'Singapore',
  },
  {
    countryCode: 'SH',
    country: 'St. Helena',
  },
  {
    countryCode: 'SI',
    country: 'Slovenia',
  },
  {
    countryCode: 'SJ',
    country: 'Svalbard & Jan Mayen',
  },
  {
    countryCode: 'SK',
    country: 'Slovakia',
  },
  {
    countryCode: 'SL',
    country: 'Sierra Leone',
  },
  {
    countryCode: 'SM',
    country: 'San Marino',
  },
  {
    countryCode: 'SN',
    country: 'Senegal',
  },
  {
    countryCode: 'SO',
    country: 'Somalia',
  },
  {
    countryCode: 'SR',
    country: 'Suriname',
  },
  {
    countryCode: 'SS',
    country: 'South Sudan',
  },
  {
    countryCode: 'ST',
    country: 'São Tomé & Príncipe',
  },
  {
    countryCode: 'SV',
    country: 'El Salvador',
  },
  {
    countryCode: 'SX',
    country: 'Sint Maarten',
  },
  {
    countryCode: 'SY',
    country: 'Syria',
  },
  {
    countryCode: 'SZ',
    country: 'Eswatini',
  },
  {
    countryCode: 'TA',
    country: 'Tristan da Cunha',
  },
  {
    countryCode: 'TC',
    country: 'Turks & Caicos Islands',
  },
  {
    countryCode: 'TD',
    country: 'Chad',
  },
  {
    countryCode: 'TF',
    country: 'French Southern Territories',
  },
  {
    countryCode: 'TG',
    country: 'Togo',
  },
  {
    countryCode: 'TH',
    country: 'Thailand',
  },
  {
    countryCode: 'TJ',
    country: 'Tajikistan',
  },
  {
    countryCode: 'TK',
    country: 'Tokelau',
  },
  {
    countryCode: 'TL',
    country: 'Timor-Leste',
  },
  {
    countryCode: 'TM',
    country: 'Turkmenistan',
  },
  {
    countryCode: 'TN',
    country: 'Tunisia',
  },
  {
    countryCode: 'TO',
    country: 'Tonga',
  },
  {
    countryCode: 'TR',
    country: 'Türkiye',
  },
  {
    countryCode: 'TT',
    country: 'Trinidad & Tobago',
  },
  {
    countryCode: 'TV',
    country: 'Tuvalu',
  },
  {
    countryCode: 'TW',
    country: 'Taiwan',
  },
  {
    countryCode: 'TZ',
    country: 'Tanzania',
  },
  {
    countryCode: 'UA',
    country: 'Ukraine',
  },
  {
    countryCode: 'UG',
    country: 'Uganda',
  },
  {
    countryCode: 'UM',
    country: 'U.S. Outlying Islands',
  },
  {
    countryCode: 'UY',
    country: 'Uruguay',
  },
  {
    countryCode: 'UZ',
    country: 'Uzbekistan',
  },
  {
    countryCode: 'VA',
    country: 'Vatican City',
  },
  {
    countryCode: 'VC',
    country: 'St. Vincent & Grenadines',
  },
  {
    countryCode: 'VE',
    country: 'Venezuela',
  },
  {
    countryCode: 'VG',
    country: 'British Virgin Islands',
  },
  {
    countryCode: 'VN',
    country: 'Vietnam',
  },
  {
    countryCode: 'VU',
    country: 'Vanuatu',
  },
  {
    countryCode: 'WF',
    country: 'Wallis & Futuna',
  },
  {
    countryCode: 'WS',
    country: 'Samoa',
  },
  {
    countryCode: 'XK',
    country: 'Kosovo',
  },
  {
    countryCode: 'YE',
    country: 'Yemen',
  },
  {
    countryCode: 'YT',
    country: 'Mayotte',
  },
  {
    countryCode: 'ZA',
    country: 'South Africa',
  },
  {
    countryCode: 'ZM',
    country: 'Zambia',
  },
  {
    countryCode: 'ZW',
    country: 'Zimbabwe',
  },
];

export const currencyList: Currency[] = [
  {code: 'USD', name: 'United States Dollar', symbol: '$'},
  {code: 'EUR', name: 'Euro', symbol: '€'},
  {code: 'GBP', name: 'British Pound Sterling', symbol: '£'},
  {code: 'INR', name: 'Indian Rupee', symbol: '₹'},
  {code: 'JPY', name: 'Japanese Yen', symbol: '¥'},
  {code: 'CAD', name: 'Canadian Dollar', symbol: '$'},
  {code: 'AUD', name: 'Australian Dollar', symbol: '$'},
  {code: 'SGD', name: 'Singapore Dollar', symbol: '$'},
  {code: 'CNY', name: 'Chinese Yuan', symbol: '¥'},
  {code: 'BRL', name: 'Brazilian Real', symbol: 'R$'},
  {code: 'ZAR', name: 'South African Rand', symbol: 'R'},
  {code: 'NZD', name: 'New Zealand Dollar', symbol: '$'},
  {code: 'CHF', name: 'Swiss Franc', symbol: 'CHF'},
  {code: 'SEK', name: 'Swedish Krona', symbol: 'kr'},
  {code: 'NOK', name: 'Norwegian Krone', symbol: 'kr'},
  {code: 'DKK', name: 'Danish Krone', symbol: 'kr'},
  {code: 'AED', name: 'United Arab Emirates Dirham', symbol: 'د.إ'},
  {code: 'PKR', name: 'Pakistani Rupee', symbol: '₨'},
  {code: 'XOF', name: 'West African CFA Franc', symbol: 'Fr'},
  {code: 'HKD', name: 'Hong Kong Dollar', symbol: '$'},
  {code: 'MXN', name: 'Mexican Peso', symbol: '$'},
  {code: 'KRW', name: 'South Korean Won', symbol: '₩'},
  {code: 'THB', name: 'Thai Baht', symbol: '฿'},
  {code: 'ILS', name: 'Israeli New Shekel', symbol: '₪'},
  {code: 'PLN', name: 'Polish Zloty', symbol: 'zł'},
  {code: 'RUB', name: 'Russian Ruble', symbol: '₽'},
  {code: 'TRY', name: 'Turkish Lira', symbol: '₺'},
  {code: 'VND', name: 'Vietnamese Dong', symbol: '₫'},
  {code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM'},
  {code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp'},
  {code: 'EGP', name: 'Egyptian Pound', symbol: '£'},
  {code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س'},
  {code: 'ARS', name: 'Argentine Peso', symbol: '$'},
  {code: 'CLP', name: 'Chilean Peso', symbol: '$'},
  {code: 'COP', name: 'Colombian Peso', symbol: '$'},
  {code: 'PEN', name: 'Peruvian Sol', symbol: 'S/'},
  {code: 'TWD', name: 'New Taiwan Dollar', symbol: 'NT$'},
  {code: 'CZK', name: 'Czech Koruna', symbol: 'Kč'},
  {code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft'},
  {code: 'UAH', name: 'Ukrainian Hryvnia', symbol: '₴'},
  {code: 'AFN', name: 'Afghan Afghani', symbol: '؋'},
  {code: 'ALL', name: 'Albanian Lek', symbol: 'L'},
  {code: 'DZD', name: 'Algerian Dinar', symbol: 'د.ج'},
  {code: 'AOA', name: 'Angolan Kwanza', symbol: 'Kz'},
  {code: 'XCD', name: 'East Caribbean Dollar', symbol: '$'},
  {code: 'AMD', name: 'Armenian Dram', symbol: '֏'},
  {code: 'AWG', name: 'Aruban Florin', symbol: 'ƒ'},
  {code: 'AZN', name: 'Azerbaijani Manat', symbol: '₼'},
  {code: 'BSD', name: 'Bahamian Dollar', symbol: '$'},
  {code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب'},
  {code: 'BDT', name: 'Bangladeshi Taka', symbol: '৳'},
  {code: 'BBD', name: 'Barbadian Dollar', symbol: '$'},
  {code: 'BYN', name: 'Belarusian Ruble', symbol: 'Br'},
  {code: 'BZD', name: 'Belize Dollar', symbol: 'BZ$'},
  {code: 'BMD', name: 'Bermudian Dollar', symbol: '$'},
  {code: 'BTN', name: 'Bhutanese Ngultrum', symbol: 'Nu.'},
  {code: 'BOB', name: 'Bolivian Boliviano', symbol: 'Bs.'},
  {code: 'BAM', name: 'Bosnia and Herzegovina Convertible Mark', symbol: 'KM'},
  {code: 'BWP', name: 'Botswana Pula', symbol: 'P'},
  {code: 'BND', name: 'Brunei Dollar', symbol: '$'},
  {code: 'BGN', name: 'Bulgarian Lev', symbol: 'лв'},
  {code: 'BIF', name: 'Burundian Franc', symbol: 'Fr'},
  {code: 'CVE', name: 'Cape Verdean Escudo', symbol: '$'},
  {code: 'KYD', name: 'Cayman Islands Dollar', symbol: '$'},
  {code: 'XAF', name: 'Central African CFA Franc', symbol: 'Fr'},
  {code: 'CDF', name: 'Congolese Franc', symbol: 'FC'},
  {code: 'CRC', name: 'Costa Rican Colón', symbol: '₡'},
  {code: 'HRK', name: 'Croatian Kuna', symbol: 'kn'},
  {code: 'CUP', name: 'Cuban Peso', symbol: '$'},
  {code: 'CUC', name: 'Cuban Convertible Peso', symbol: '$'},
  {code: 'ANG', name: 'Netherlands Antillean Guilder', symbol: 'ƒ'},
  {code: 'DOP', name: 'Dominican Peso', symbol: 'RD$'},
  {code: 'SVC', name: 'Salvadoran Colón', symbol: '₡'},
  {code: 'ERN', name: 'Eritrean Nakfa', symbol: 'Nfk'},
  {code: 'ETB', name: 'Ethiopian Birr', symbol: 'Br'},
  {code: 'FKP', name: 'Falkland Islands Pound', symbol: '£'},
  {code: 'FJD', name: 'Fijian Dollar', symbol: '$'},
  {code: 'GMD', name: 'Gambian Dalasi', symbol: 'D'},
  {code: 'GEL', name: 'Georgian Lari', symbol: '₾'},
  {code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵'},
  {code: 'GIP', name: 'Gibraltar Pound', symbol: '£'},
  {code: 'GTQ', name: 'Guatemalan Quetzal', symbol: 'Q'},
  {code: 'GNF', name: 'Guinean Franc', symbol: 'Fr'},
  {code: 'GYD', name: 'Guyanese Dollar', symbol: '$'},
  {code: 'HTG', name: 'Haitian Gourde', symbol: 'G'},
  {code: 'HNL', name: 'Honduran Lempira', symbol: 'L'},
  {code: 'ISK', name: 'Icelandic Króna', symbol: 'kr'},
  {code: 'IRR', name: 'Iranian Rial', symbol: '﷼'},
  {code: 'IQD', name: 'Iraqi Dinar', symbol: 'ع.د'},
  {code: 'JMD', name: 'Jamaican Dollar', symbol: 'J$'},
  {code: 'JOD', name: 'Jordanian Dinar', symbol: 'د.ا'},
  {code: 'KZT', name: 'Kazakhstani Tenge', symbol: '₸'},
  {code: 'KES', name: 'Kenyan Shilling', symbol: 'Sh'},
  {code: 'KPW', name: 'North Korean Won', symbol: '₩'},
  {code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك'},
  {code: 'KGS', name: 'Kyrgyzstani Som', symbol: 'с'},
  {code: 'LAK', name: 'Lao Kip', symbol: '₭'},
  {code: 'LBP', name: 'Lebanese Pound', symbol: 'ل.ل'},
  {code: 'LSL', name: 'Lesotho Loti', symbol: 'L'},
  {code: 'LRD', name: 'Liberian Dollar', symbol: '$'},
  {code: 'LYD', name: 'Libyan Dinar', symbol: 'ل.د'},
  {code: 'MOP', name: 'Macanese Pataca', symbol: 'P'},
  {code: 'MKD', name: 'Macedonian Denar', symbol: 'ден'},
  {code: 'MGA', name: 'Malagasy Ariary', symbol: 'Ar'},
  {code: 'MWK', name: 'Malawian Kwacha', symbol: 'MK'},
  {code: 'MVR', name: 'Maldivian Rufiyaa', symbol: '.ރ'},
  {code: 'MRU', name: 'Mauritanian Ouguiya', symbol: 'UM'},
  {code: 'MUR', name: 'Mauritian Rupee', symbol: '₨'},
  {code: 'MDL', name: 'Moldovan Leu', symbol: 'L'},
  {code: 'MNT', name: 'Mongolian Tögrög', symbol: '₮'},
  {code: 'MAD', name: 'Moroccan Dirham', symbol: 'د.م.'},
  {code: 'MZN', name: 'Mozambican Metical', symbol: 'MT'},
  {code: 'MMK', name: 'Myanmar Kyat', symbol: 'Ks'},
  {code: 'NAD', name: 'Namibian Dollar', symbol: '$'},
  {code: 'NPR', name: 'Nepalese Rupee', symbol: '₨'},
  {code: 'NIO', name: 'Nicaraguan Córdoba', symbol: 'C$'},
  {code: 'NGN', name: 'Nigerian Naira', symbol: '₦'},
  {code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.'},
  {code: 'PAB', name: 'Panamanian Balboa', symbol: 'B/.'},
  {code: 'PGK', name: 'Papua New Guinean Kina', symbol: 'K'},
  {code: 'PYG', name: 'Paraguayan Guaraní', symbol: '₲'},
  {code: 'PHP', name: 'Philippine Peso', symbol: '₱'},
  {code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق'},
  {code: 'RON', name: 'Romanian Leu', symbol: 'lei'},
  {code: 'RWF', name: 'Rwandan Franc', symbol: 'Fr'},
  {code: 'WST', name: 'Samoan Tala', symbol: 'T'},
  {code: 'STD', name: 'São Tomé and Príncipe Dobra', symbol: 'Db'},
  {code: 'SCR', name: 'Seychellois Rupee', symbol: '₨'},
  {code: 'SLL', name: 'Sierra Leonean Leone', symbol: 'Le'},
  {code: 'SBD', name: 'Solomon Islands Dollar', symbol: '$'},
  {code: 'SOS', name: 'Somali Shilling', symbol: 'Sh'},
  {code: 'SSP', name: 'South Sudanese Pound', symbol: '£'},
  {code: 'LKR', name: 'Sri Lankan Rupee', symbol: 'Rs'},
  {code: 'SDG', name: 'Sudanese Pound', symbol: 'ج.س.'},
  {code: 'SRD', name: 'Surinamese Dollar', symbol: '$'},
  {code: 'SZL', name: 'Eswatini Lilangeni', symbol: 'L'},
  {code: 'SYP', name: 'Syrian Pound', symbol: '£'},
  {code: 'TJS', name: 'Tajikistani Somoni', symbol: 'ЅМ'},
  {code: 'TZS', name: 'Tanzanian Shilling', symbol: 'Sh'},
  {code: 'TOP', name: 'Tongan Paʻanga', symbol: 'T$'},
  {code: 'TTD', name: 'Trinidad and Tobago Dollar', symbol: 'TT$'},
  {code: 'TND', name: 'Tunisian Dinar', symbol: 'د.ت'},
  {code: 'TMT', name: 'Turkmenistani Manat', symbol: 'm'},
  {code: 'UGX', name: 'Ugandan Shilling', symbol: 'Sh'},
  {code: 'UYU', name: 'Uruguayan Peso', symbol: '$'},
  {code: 'UZS', name: 'Uzbekistani Soʻm', symbol: 'лв'},
  {code: 'VUV', name: 'Vanuatu Vatu', symbol: 'Vt'},
  {code: 'VES', name: 'Venezuelan Bolívar Soberano', symbol: 'Bs.S'},
  {code: 'YER', name: 'Yemeni Rial', symbol: '﷼'},
  {code: 'ZMW', name: 'Zambian Kwacha', symbol: 'ZK'},
  {code: 'ZWL', name: 'Zimbabwean Dollar', symbol: 'Z$'},
];

// Helper functions for currency operations
export const getCurrencyByCode = (code: string): Currency | undefined => {
  return currencyList.find(currency => currency.code === code);
};

export const getCurrencyName = (code: string): string => {
  const currency = getCurrencyByCode(code);
  return currency ? `${currency.name} (${currency.code}) ${currency.symbol}` : code;
};

export const getCurrencySymbol = (code: string): string => {
  const currency = getCurrencyByCode(code);
  return currency?.symbol || '';
};
