export interface LanguageOption {
  name: string;
  code: string;
}

export const languageOptions: LanguageOption[] = [
  {name: 'Arabic', code: 'AR'},
  {name: 'Spanish', code: 'ES'},
  {name: 'French', code: 'FR'},
  {name: 'German', code: 'DE'},
  {name: 'Chinese', code: 'ZH'},
  {name: 'Japanese', code: 'JA'},
  {name: 'Portuguese', code: 'PT'},
  {name: 'Italian', code: 'IT'},
  {name: 'Russian', code: 'RU'},
  {name: 'Dutch', code: 'NL'},
  {name: 'Korean', code: 'KO'},
  {name: 'Hindi', code: 'HI'},
  {name: 'Bengali', code: 'BN'},
  {name: 'Indonesian', code: 'ID'},
  {name: 'Turkish', code: 'TR'},
  {name: 'Vietnamese', code: 'VI'},
  {name: 'Polish', code: 'PL'},
  {name: 'Swedish', code: 'SV'},
  {name: 'Norwegian', code: 'NO'},
  {name: 'Thai', code: 'TH'},
  {name: 'Persian', code: 'FA'},
  {name: 'Hebrew', code: 'HE'},
  {name: 'Greek', code: 'EL'},
  {name: 'Czech', code: 'CS'},
  {name: 'Romanian', code: 'RO'},
  {name: 'Hungarian', code: 'HU'},
  {name: 'Filipino', code: 'FIL'},
  {name: 'Malay', code: 'MS'},
  {name: 'Ukrainian', code: 'UK'},
];

export const getLanguageNameByCode = (code: string): string => {
  const language = languageOptions.find(lang => lang.code === code);
  return language ? language.name : code;
};

// TODO:
// - Add support for more languages
// - Add all languages in backed
// - Make deafult language selected
// - Fix API error handling
// - Fix modal close during api
// - Show fork live and draft
