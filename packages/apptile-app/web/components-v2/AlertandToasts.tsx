import {GetRegisteredPluginInfo, pluginConfigUpdatePath, selectGlobalPlugins, selectPluginConfig} from 'apptile-core';
import {useEffect, useState} from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import CollapsiblePanel from '../components/CollapsiblePanel';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import PluginPropertyEditor from '../views/propertyInspector/components/PluginPropertyEditor';
import _ from 'lodash';

const AlertandToasts = () => {
  const dispatch = useDispatch();
  const globalPlugins = useSelector(state => selectGlobalPlugins(state));
  const [closeSectionIndex, setCloseSectionIndex] = useState<String[]>([]);
  const [editors, setEditors] = useState<any>({});
  useEffect(() => {
    if (globalPlugins) {
      const datasources = globalPlugins.filter(e => e.type == 'datasource');
      datasources.map(e => {
        const datasourceEditors = GetRegisteredPluginInfo(e.subtype)?.editors;
        const visibleEditors = Object.keys(datasourceEditors).reduce((acc, key) => {
          let newArray = acc;
          if (datasourceEditors[key]) {
            newArray = [
              ...newArray,
              ...datasourceEditors[key]?.filter(
                (item: any) => item.category === 'Alerts' && item.isVisibleInV2 === true,
              ),
            ];
          }
          return newArray;
        }, []);
        let pluginEditors = [{editors: [], sectionHeader: ''}];
        if (visibleEditors?.[0]?.type == 'editorSectionHeader') pluginEditors = [];
        visibleEditors.forEach((editor: any) => {
          if (editor.type == 'editorSectionHeader' && editor?.props?.label)
            pluginEditors.push({editors: [], sectionHeader: editor?.props?.label});
          else pluginEditors[pluginEditors.length - 1].editors.push(editor);
        });
        if (visibleEditors?.length)
          setEditors(prevEditors => ({
            ...prevEditors,
            [e.id]: pluginEditors,
          }));
      });
    }
  }, [dispatch, globalPlugins]);

  const onValueChange = (name: any, val: any, pluginId: any) => {
    dispatch(
      pluginConfigUpdatePath(pluginId, '', ['config'], {
        [name]: val,
      }),
    );
  };

  // console.log('editors', editors, globalPlugins);

  const onEditorSectionToggle = (sectionKey: string) => {
    if (closeSectionIndex.includes(sectionKey)) {
      setCloseSectionIndex(closeSectionIndex.filter(i => i !== sectionKey));
    } else {
      setCloseSectionIndex([...closeSectionIndex, sectionKey]);
    }
  };
  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <Text style={styles.headingText}>Alerts & Toasts</Text>
      <View style={styles.divider} />
      {Object.keys(editors).map((plugin: any, index: number) => (
        <>
          {editors[plugin].map((editorSections: any, sectionIndex: number) => (
            <>
              {editorSections.sectionHeader ? (
                <CollapsiblePanel
                  key={`${plugin}-${sectionIndex}`}
                  title={editorSections.sectionHeader}
                  isOpen={!closeSectionIndex.includes(`${plugin}-${sectionIndex}`)}
                  backgroundStyle={{borderWidth: 0}}
                  setOpen={() => onEditorSectionToggle(`${plugin}-${sectionIndex}`)}
                  customHeader={
                    <EditorSectionHeader
                      label={editorSections.sectionHeader}
                      name={editorSections.sectionHeader}
                      isPremiumDesabled={false}
                      iconSize={18}
                      icon={!closeSectionIndex.includes(`${plugin}-${sectionIndex}`) ? 'chevron-up' : 'chevron-down'}
                      iconType={'Material Icon'}
                    />
                  }>
                  {editorSections.editors.map((pluginEditor: any, sectionIndex: number) => (
                    <PluginPropertyEditorWrapper
                      key={plugin + sectionIndex + pluginEditor.name}
                      propEditor={pluginEditor}
                      pageId={null}
                      pluginId={plugin}
                      onChange={(newValue: any) => {
                        onValueChange(pluginEditor.name, newValue, plugin);
                      }}
                    />
                  ))}
                </CollapsiblePanel>
              ) : (
                <>
                  {editorSections.editors.map((pluginEditor: any, sectionIndex: number) => (
                    <PluginPropertyEditorWrapper
                      key={plugin + sectionIndex + pluginEditor.name}
                      propEditor={pluginEditor}
                      pageId={null}
                      pluginId={plugin}
                      onChange={(newValue: any) => {
                        onValueChange(pluginEditor.name, newValue, plugin);
                      }}
                    />
                  ))}
                </>
              )}
              <View style={styles.divider} />
            </>
          ))}
        </>
      ))}
    </ScrollView>
  );
};

const PluginPropertyEditorWrapper = ({pageId, pluginId, propEditor, onChange}: any) => {
  const selector = [pluginId, 'config', propEditor.name];

  const pluginConfig = useSelector(state => selectPluginConfig(state, pageId, pluginId));
  if (!propEditor) {
    return null;
  }
  const label = propEditor.props?.label || propEditor.name;

  return (
    <PluginPropertyEditor
      key={pluginConfig?.id + propEditor.name + label}
      editor={propEditor}
      entityConfig={pluginConfig}
      config={pluginConfig?.getIn(['config'])}
      pageId={pageId}
      pluginId={pluginId}
      configPathSelector={selector}
      hideExposePropButton
      onChange={onChange}
      isModule
    />
  );
};

const styles = StyleSheet.create({
  headingText: {
    fontSize: 16,
    fontWeight: '600',
    paddingTop: 16,
    paddingBottom: 16,
    textTransform: 'capitalize',
  },
  divider: {
    backgroundColor: '#E5E5E5',
    marginVertical: 5,
    height: 1,
    width: '100%',
  },
});

export default AlertandToasts;
