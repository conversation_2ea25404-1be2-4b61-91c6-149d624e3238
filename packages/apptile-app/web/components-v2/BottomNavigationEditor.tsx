import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ScrollView, TouchableOpacity, View, StyleSheet, Text, ActivityIndicator} from 'react-native';
import CollapsiblePanel from '../components/CollapsiblePanel';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import {updateColor} from '../actions/themeActions';
import ColorInputControl from '../components/controls/ColorInputControl';
import {
  addNavigationPage,
  Icon,
  navComponentDelete,
  navigationConfigUpdatePath,
  NavigatorConfig,
  navUpdateName,
  useCallbackRef,
  useTheme,
} from 'apptile-core';
import {navConfigSelector} from '../../../apptile-core/selectors/AppConfigSelector';
import IconChooserControl from '../components/controls/IconChooserControl';
import {useDispatch, useSelector} from 'react-redux';
import {selectScreensInNavWithPath} from '../selectors/EditorSelectors';
import {debounce} from 'lodash';
import {makeToast} from '../actions/toastActions';
import {
  EDITOR_SELECT_NAV_COMPONENT,
  EDITOR_SELECTED_PAGE_TYPE,
  navigationReoderingV2,
  softRestartConfig,
} from '../actions/editorActions';
import CodeInput from '../components/codeEditor/codeInput';
import commonStyles from '../styles-v2/commonStyles';
import RadioGroupControlV2 from '../components/controls-v2/RadioGroupControl';
import TileHeader from './TileHeader';
import SortableList from '../components/SortableList';
import _ from 'lodash';
import {getNavigationContext} from 'apptile-core';
import PageIdSelectorControl from '../components/controls/PageIdSelectorControl';

const getTabScreens = (ovj: any, mainTab: any) => {
  const mainScreens = ovj.filter(screen => screen.path[1] === mainTab?.name && screen.path.length === 3);
  return mainScreens;
};

const getTabLength = (ovj: any, mainTab: any) => {
  const bottomTabScreens = ovj.filter(screen => screen.path[1] === mainTab?.name && screen.path.length === 3);
  const topTabScreens = ovj.filter(screen => screen.path[1] === mainTab?.name && screen.path.length === 4);
  let totalLength = bottomTabScreens.length;
  if (topTabScreens.length > 0) totalLength = bottomTabScreens.length + 1;
  return totalLength;
};

const BottomNavigationEditor = () => {
  const navConfigs: NavigatorConfig = useSelector(navConfigSelector)?.get('rootNavigator');
  const mainTab = useMemo(() => {
    if (!navConfigs) return null;
    const navConfigJS = navConfigs.toJS();
    return Object.values(navConfigJS.screens).find(
      screen => screen?.type === 'navigator' && screen?.navigatorType === 'tab',
    );
  }, [navConfigs]);
  const context = getNavigationContext();
  const bottomBars = navConfigs.screens.flatMap(config => {
    if (config.type === 'navigator') {
      return config.screens;
    }
  });
  const bottomNavigationTabs = bottomBars.toJS
    ? Object.entries(bottomBars.toJS()).map(([key, value]) => ({...value, key}))
    : Object.entries(bottomBars).map(([key, value]) => ({...value, key}));

  const dispatch = useDispatch();
  const screens: any = useSelector(selectScreensInNavWithPath(['/']));

  const [showAddNavigationSection, setShowAddNavigationSection] = useState(false);
  const [showUpdateNavigationSection, setShowUpdateNavigationSection] = useState('');
  const [open, setOpen] = useState(true);
  const [closeSectionIndex, setCloseSectionIndex] = useState<Number[]>([]);
  const {themeEvaluator} = useTheme();
  const [iconColor, setIconColor] = useState(themeEvaluator('colors.navText'));
  const [activeIconColor, setActiveIconColor] = useState(themeEvaluator('colors.navPrimary'));
  const [tileColor, setTileColor] = useState(themeEvaluator('colors.navCard'));
  const [currentEditorSection, setCurrentEditorSection] = useState('basics');
  const [isLoading, setIsLoading] = useState(false);

  const tabLength = getTabLength(screens, mainTab);

  const [navigationData, setNavigationData] = useState({
    screen: '',
    title: '',
    iconType: '',
    iconName: '',
  });
  const [updateNavigationData, setUpdatetNavigationData] = useState({
    oldScreen: '',
    screen: '',
    title: '',
    iconType: '',
    iconName: '',
  });

  const [topNavigationName, setTopNavigationName] = useState('');

  const debouncedOnValueChange = debounce(value => handleUpdateChangeNavigationData({name: 'title', value}), 1000);
  const debouncedOnUpdateNavigationValueChange = debounce(value => setTopNavigationName(value), 1000);

  const isAddButtonDisabled = () => {
    return !(navigationData.screen && navigationData.title && navigationData.iconName && navigationData.iconType);
  };
  const handleChangeNavigationData = e => {
    const {name, value} = e;
    setNavigationData(prevData => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleUpdateChangeNavigationData = ({name, value}) => {
    setUpdatetNavigationData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const addPage = useCallbackRef(() => {
    let scrName = navigationData.screen;
    const avalaibleScreens = getTabScreens(screens, mainTab);
    const existed = avalaibleScreens.find(screen => screen.path[1] === mainTab?.name && screen.path[2] === scrName);
    if (existed) {
      dispatch(makeToast({content: `${scrName} screen already Exists.`, duration: 2000, appearances: 'warning'}));
      return;
    }
    const pathSelector = ['/', mainTab?.name];
    const navSelector = ['/', mainTab?.name, scrName];
    dispatch(addNavigationPage(pathSelector, scrName));
    dispatch(navigationConfigUpdatePath(navSelector, [], navigationData));
    setNavigationData({
      screen: '',
      title: '',
      iconType: '',
      iconName: '',
    });
    updateAppConfig();
  });
  const updatePage = useCallbackRef(() => {
    try {
      const navSelector = ['/', mainTab?.name, updateNavigationData.oldScreen];
      const {oldScreen, ...data} = updateNavigationData;
      const avalaibleScreens = getTabScreens(screens, mainTab);
      const scrName = updateNavigationData.screen;
      const existed =
        oldScreen !== scrName &&
        avalaibleScreens.find(screen => screen.path[1] === mainTab?.name && screen.path[2] === scrName);
      if (existed) {
        dispatch(
          makeToast({
            content: `${scrName} screen already Exists.`,
            duration: 2000,
            appearances: 'warning',
          }),
        );
        return;
      }
      dispatch(navigationConfigUpdatePath(navSelector, [], data));
      updateAppConfig();
    } catch (e) {
      console.log(e, 'error in update page');
    }
  });

  function showToaster() {
    dispatch(makeToast({content: 'Minimum 2 navigation items required', duration: 2000, appearances: 'error'}));
  }

  const findIndex = (array: any, key: any, originalArray: any) => {
    const arr = array.map(([_, value]) => value.key || value.name);
    const originalKeyOrder = originalArray.map((item: any) => item.key || item.name);
    const idx = arr.indexOf(key);
    const oldIndex = originalKeyOrder.indexOf(key);
    if (idx === oldIndex) return -1;
    return idx;
  };

  useEffect(() => {
    if (context) context.navigate(mainTab?.name);
  }, []);

  const updateNavigationOrder = useCallback(
    (reorderedList: [any, any][], item: any) => {
      let operation = 'UP';
      let path = ['/', mainTab?.name, item.item.name];
      const index = findIndex(reorderedList, item.item.name, bottomNavigationTabs);
      dispatch(navigationReoderingV2(operation, path, index));
    },
    [bottomNavigationTabs],
  );

  const updateTopNavigationName = item => {
    const navSelector = ['/', mainTab?.name, item?.name];
    dispatch(navUpdateName(navSelector, topNavigationName));
    updateAppConfig();
  };

  function handleLoading(callback, delay = 100) {
    return setTimeout(callback, delay);
  }

  const updateAppConfig = () => {
    setIsLoading(true);
    handleLoading(() => {
      dispatch(softRestartConfig());
      setIsLoading(false);
    }, 100);
  };

  const renderItem = data => {
    const item = data.itemVal;
    return !(showUpdateNavigationSection === item.name) ? (
      <View key={item.name} style={{marginVertical: 4}}>
        <TouchableOpacity
          onPress={() => {
            setShowUpdateNavigationSection(item.name);
            handleUpdateChangeNavigationData({name: 'oldScreen', value: item.name});
            handleUpdateChangeNavigationData({name: 'screen', value: item.screen});
            handleUpdateChangeNavigationData({name: 'title', value: item.title});
            handleUpdateChangeNavigationData({name: 'iconType', value: item.iconType});
            handleUpdateChangeNavigationData({name: 'iconName', value: item.iconName});
          }}>
          <View style={styles.navCardContainer}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Icon iconType={item.iconType} name={item.iconName} size={16} color="#3C3C3C" style={{marginRight: 10}} />
              <Text style={styles.navName}>{item?.title || item?.name}</Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                if (tabLength > 2) {
                  dispatch(navComponentDelete(['/', mainTab?.name, item.name]));
                  dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: ['/', mainTab?.name]});
                  dispatch({
                    type: EDITOR_SELECTED_PAGE_TYPE,
                    payload: screens[0].config.type,
                  });
                } else {
                  showToaster();
                }
              }}>
              <Icon iconType="ApptileWebIcons" name={'close'} size={16} color="#3C3C3C" />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    ) : (
      <View key={item.name} style={styles.navUpdateContainer}>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            marginBottom: 16,
            justifyContent: 'space-between',
          }}>
          <Text style={{fontSize: 11, fontWeight: 600}}>EDITING NAVIGATION ITEM</Text>
          <TouchableOpacity
            onPress={() => {
              setShowUpdateNavigationSection('');
            }}>
            <Icon iconType="ApptileWebIcons" name={'close'} size={16} />
          </TouchableOpacity>
        </View>
        <View style={styles.navTitleCnt}>
          <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>Title</Text>
          <View style={styles.navTitleInput}>
            <CodeInput
              placeholder="value"
              value={updateNavigationData.title || topNavigationName || item?.name}
              onChange={(editor: unknown, data: unknown, value: string) => {
                item.type === 'navigator'
                  ? debouncedOnUpdateNavigationValueChange(value)
                  : debouncedOnValueChange(value);
              }}
            />
          </View>
        </View>
        <IconChooserControl
          value={updateNavigationData.iconName}
          config={{iconType: updateNavigationData.iconType}}
          onBulkValueChange={value => {
            handleUpdateChangeNavigationData({name: 'iconType', value: value.iconType});
            handleUpdateChangeNavigationData({name: 'iconName', value: value.iconName});
          }}
          defaultValue={''}
          label={'Icon'}
        />
        {item?.screen !== 'Home' && !!item?.screen && (
          <PageIdSelectorControl
            value={updateNavigationData.screen}
            defaultValue={updateNavigationData.screen}
            label="Navigate to "
            disableBinding
            onChange={(value: string) => {
              handleUpdateChangeNavigationData({name: 'oldScreen', value: item.name});
              handleUpdateChangeNavigationData({name: 'screen', value: value});
            }}
          />
        )}
        <button
          onClick={() => {
            item?.type === 'navigator' ? updateTopNavigationName(item) : updatePage();
            setShowUpdateNavigationSection('');
          }}
          style={{
            width: '25%',
            borderRadius: 30,
            padding: 10,
            backgroundColor: 'white',
            border: '1px solid #1060E0',
            color: '#1060E0',
            marginTop: 8,
            cursor: 'pointer',
          }}>
          Update
        </button>
      </View>
    );
  };

  return (
    <View style={{flex: 1, marginTop: 20}}>
      <TileHeader isDeletable={false} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{overflow: 'scroll', flex: 1, paddingRight: 4, marginTop: 8}}>
        <View>
          <RadioGroupControlV2
            label=""
            options={[
              {text: 'Basics', value: 'basics'},
              {text: 'Style', value: 'style'},
            ]}
            value={currentEditorSection}
            disableBinding={true}
            onChange={setCurrentEditorSection}
          />
          <View>
            {isLoading ? (
              <ActivityIndicator />
            ) : (
              <View>
                {currentEditorSection === 'basics' ? (
                  <CollapsiblePanel
                    isOpen={open}
                    setOpen={(open: boolean) => {
                      setOpen(open);
                    }}
                    backgroundStyle={{borderWidth: 0}}
                    title="section"
                    customHeader={
                      <EditorSectionHeader
                        label={'BOTTOM NAVIGATION ITEMS'}
                        name={'bottom naivgation items'}
                        isPremiumDesabled={false}
                        icon={open ? 'chevron-up' : 'chevron-down'}
                        iconSize={18}
                        iconType={'Material Icon'}
                      />
                    }>
                    <View>
                      <View style={styles.navigationContainer}>
                        <Text style={styles.navSubHeading}>Must have between 2–5 navigation items</Text>
                      </View>
                      {Array.isArray(bottomNavigationTabs) && (
                        <SortableList
                          dragKey={`1-list-editor`}
                          data={_.toPairs(bottomNavigationTabs)}
                          onChange={updateNavigationOrder}
                          itemComponent={renderItem}
                          componentProps={{
                            totalItems: bottomNavigationTabs?.length,
                            minLength: parseInt('1', 10),
                          }}
                        />
                      )}
                      {!showAddNavigationSection && (
                        <button
                          disabled={tabLength >= 5}
                          onClick={() => {
                            setShowAddNavigationSection(true);
                          }}
                          style={tabLength >= 5 ? styles.enabledAddNavButton : styles.disabledAddNavButton}>
                          {tabLength < 5 ? ' + Add new navigation' : 'Limit reached'}
                        </button>
                      )}
                      {showAddNavigationSection && (
                        <View
                          style={{
                            border: '1px solid #E5E5E5',
                            borderRadius: '8px',
                            paddingVertical: 12,
                            paddingHorizontal: 8,
                            marginTop: 8,
                          }}>
                          <View
                            style={{
                              display: 'flex',
                              flexDirection: 'row',
                              marginBottom: 12,
                              justifyContent: 'space-between',
                            }}>
                            <Text style={{fontSize: 11, fontWeight: 600}}>ADD NAVIGATION ITEM</Text>
                            <TouchableOpacity
                              onPress={() => {
                                setShowAddNavigationSection(false);
                                setNavigationData({
                                  screen: '',
                                  title: '',
                                  iconType: '',
                                  iconName: '',
                                });
                              }}>
                              <Icon iconType="ApptileWebIcons" name={'close'} size={16} />
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flex: 1,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              marginVertical: 4,
                              marginHorizontal: 0,
                            }}>
                            <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>Title</Text>
                            <View
                              style={{
                                flex: 1,
                                borderRadius: 5,
                                backgroundColor: '#F3F3F3',
                                overflow: 'hidden',
                              }}>
                              <CodeInput
                                placeholder="value"
                                value={''}
                                onChange={(editor: unknown, data: unknown, value: string) =>
                                  handleChangeNavigationData({name: 'title', value: value})
                                }
                              />
                            </View>
                          </View>
                          <IconChooserControl
                            value={navigationData.iconName}
                            config={{iconType: navigationData.iconType}}
                            onBulkValueChange={value => {
                              handleChangeNavigationData({name: 'iconType', value: value.iconType});
                              handleChangeNavigationData({name: 'iconName', value: value.iconName});
                            }}
                            defaultValue={''}
                            label={'Icon'}
                          />{' '}
                          <PageIdSelectorControl
                            value={navigationData.screen}
                            defaultValue={navigationData.screen}
                            label="Navigate to "
                            disableBinding
                            onChange={(value: string) => {
                              handleChangeNavigationData({name: 'screen', value: value});
                            }}
                          />
                          <button
                            disabled={isAddButtonDisabled()}
                            onClick={() => {
                              addPage();
                              setShowAddNavigationSection(false);
                              setNavigationData({
                                screen: '',
                                title: '',
                                iconType: '',
                                iconName: '',
                              });
                            }}
                            style={
                              isAddButtonDisabled()
                                ? {
                                    width: '20%',
                                    borderRadius: 30,
                                    padding: 10,
                                    backgroundColor: 'white',
                                    border: '1px solid gray',
                                    color: 'gray',
                                    marginTop: 8,
                                    cursor: 'not-allowed',
                                  }
                                : {
                                    width: '20%',
                                    borderRadius: 30,
                                    padding: 10,
                                    backgroundColor: 'white',
                                    border: '1px solid #1060E0',
                                    color: '#1060E0',
                                    marginTop: 8,
                                    cursor: 'pointer',
                                  }
                            }>
                            Add
                          </button>
                        </View>
                      )}
                    </View>
                  </CollapsiblePanel>
                ) : currentEditorSection === 'settings' ? (
                  <View />
                ) : (
                  <View>
                    <CollapsiblePanel
                      isOpen={!closeSectionIndex.includes(1)}
                      setOpen={(open: boolean) => {
                        if (closeSectionIndex.includes(1)) closeSectionIndex.splice(closeSectionIndex.indexOf(1), 1);
                        else closeSectionIndex.push(1);
                        setCloseSectionIndex([...closeSectionIndex]);
                      }}
                      backgroundStyle={{borderWidth: 0}}
                      title="section"
                      customHeader={
                        <EditorSectionHeader
                          label={'COLOR'}
                          name={'color'}
                          isPremiumDesabled={false}
                          icon={!closeSectionIndex.includes(1) ? 'chevron-up' : 'chevron-down'}
                          iconSize={18}
                          iconType={'Material Icon'}
                        />
                      }>
                      <View>
                        <ColorInputControl
                          inTheme
                          label="Active Icon & Text"
                          name="navPrimary"
                          value={activeIconColor}
                          onChange={(value: string) => {
                            setActiveIconColor(value);
                            dispatch(
                              updateColor({
                                colorName: 'navPrimary',
                                colorCode: value,
                                mode: 'light',
                              }),
                            );
                          }}
                        />
                        <ColorInputControl
                          inTheme
                          label="Icon & Text"
                          name="navText"
                          value={iconColor}
                          onChange={(value: string) => {
                            setIconColor(value);
                            dispatch(
                              updateColor({
                                colorName: 'navText',
                                colorCode: value,
                                mode: 'light',
                              }),
                            );
                          }}
                        />
                      </View>
                    </CollapsiblePanel>
                    <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} />
                    <CollapsiblePanel
                      isOpen={!closeSectionIndex.includes(2)}
                      setOpen={(open: boolean) => {
                        if (closeSectionIndex.includes(2)) closeSectionIndex.splice(closeSectionIndex.indexOf(2), 1);
                        else closeSectionIndex.push(2);
                        setCloseSectionIndex([...closeSectionIndex]);
                      }}
                      backgroundStyle={{borderWidth: 0}}
                      title="section"
                      customHeader={
                        <EditorSectionHeader
                          label={'BACKGROUND COLOR'}
                          name={'background color'}
                          isPremiumDesabled={false}
                          icon={!closeSectionIndex.includes(2) ? 'chevron-up' : 'chevron-down'}
                          iconSize={18}
                          iconType={'Material Icon'}
                        />
                      }>
                      <View>
                        <ColorInputControl
                          inTheme
                          label="Background"
                          name="navCardColor"
                          value={tileColor}
                          onChange={(value: string) => {
                            setTileColor(value);
                            dispatch(
                              updateColor({
                                colorName: 'navCard',
                                colorCode: value,
                                mode: 'light',
                              }),
                            );
                          }}
                        />
                      </View>
                    </CollapsiblePanel>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
        <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%', marginBottom: '100px'}} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  navigationContainer: {marginBottom: 14},
  navHeading: {fontSize: 11, fontWeight: '600'},
  navSubHeading: {fontSize: 12, marginTop: '-4', color: '#535353', fontWeight: '400'},
  navCardContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'rgb(243, 243, 243);',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  navName: {fontSize: 13, color: '#3C3C3C', fontWeight: '400'},
  navUpdateContainer: {
    border: '1px solid #E5E5E5',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  navTitleCnt: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 4,
    marginHorizontal: 0,
  },
  navTitleInput: {
    flex: 1,
    borderWidth: 0,
    borderRadius: 5,
    backgroundColor: '#F3F3F3',
    overflow: 'hidden',
  },
  enabledAddNavButton: {
    width: '50%',
    fontSize: 13,
    borderRadius: 30,
    padding: 10,
    fontWeight: '500',
    backgroundColor: 'white',
    border: '1px solid #00000033',
    color: 'rgba(0, 0, 0, 0.2)',
    marginTop: 8,
    cursor: 'not-allowed',
    marginBottom: 8,
  },
  disabledAddNavButton: {
    width: '50%',
    borderRadius: 30,
    fontSize: 13,
    padding: 10,
    fontWeight: '500',
    backgroundColor: 'white',
    border: '1px solid #1060E0',
    color: '#1060E0',
    marginTop: 8,
    cursor: 'pointer',
    marginBottom: 8,
  },
  horizontalLine: {
    borderWidth: 0,
    backgroundColor: '#E5E5E5',
    margin: '10px 0',
    width: '100%',
  },
});

export default BottomNavigationEditor;
