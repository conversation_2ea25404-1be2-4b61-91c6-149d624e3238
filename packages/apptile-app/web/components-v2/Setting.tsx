import {useEffect, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Button from './base/Button';
import {EDITOR_SELECT_SETTING_COMPONENT} from './../actions/editorActions';
import { selectSettingtSelector } from './../selectors/EditorSelectors';

const Setting = () => {
  const dispatch = useDispatch();
  const selectedSettings: any = useSelector(state => selectSettingtSelector(state));
  useEffect(() => {
    dispatch({type: EDITOR_SELECT_SETTING_COMPONENT, payload: 'Toasts'});
    return () => {
      dispatch({type: EDITOR_SELECT_SETTING_COMPONENT, payload: ''});
    };
  }, [dispatch]);

  return (
    <View>
      <Text style={styles.headingText}>Settings</Text>
      <View style={styles.divider} />
      <View style={styles.navContainer}>
        <Button
          containerStyles={
            selectedSettings === 'Toasts'
              ? [styles.buttonBaseContainer, styles.activeNavButton]
              : [styles.buttonBaseContainer, styles.inactiveNavButton]
          }
          textStyles={styles.buttonText}
          onPress={() => {
            dispatch({type: EDITOR_SELECT_SETTING_COMPONENT, payload: 'Toasts'});
          }}>
          <View style={[styles.buttonRowBottom, {gap: 76}]}>
            <Text style={selectedSettings === 'Toasts' ? styles.bottomNavText : [styles.bottomNavText, {color: '#3C3C3C'}]}>
              Alerts & Toasts
            </Text>

            <View style={styles.iconWrapper}>
              {selectedSettings === 'Toasts' ? <SvgArrow fill="#1060E0" /> : <SvgArrow fill="#3C3C3C" />}
            </View>
          </View>
        </Button>

          <Button
          containerStyles={
            selectedSettings === 'AppSettings'
              ? [styles.buttonBaseContainer, styles.activeNavButton]
              : [styles.buttonBaseContainer, styles.inactiveNavButton]
          }
          textStyles={styles.buttonText}
          onPress={() => {
            dispatch({type: EDITOR_SELECT_SETTING_COMPONENT, payload: 'AppSettings'});
          }}>
          <View style={[styles.buttonRowBottom, {gap: 84}]}>
            <Text style={selectedSettings === 'AppSettings' ? styles.bottomNavText : [styles.bottomNavText, {color: '#3C3C3C'}]}>
              App Settings
            </Text>

            <View style={styles.iconWrapper}>
              {selectedSettings === 'AppSettings' ? <SvgArrow fill="#1060E0" /> : <SvgArrow fill="#3C3C3C" />}
            </View>
          </View>
        </Button>
      </View>
    </View>
  );
};

const SvgArrow = ({fill}: {fill: string}) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.4712 7.52867C10.5962 7.65369 10.6664 7.82323 10.6664 8.00001C10.6664 8.17678 10.5962 8.34632 10.4712 8.47134L6.6999 12.2427C6.63841 12.3063 6.56484 12.3571 6.48351 12.3921C6.40217 12.427 6.31469 12.4454 6.22617 12.4462C6.13765 12.4469 6.04986 12.4301 5.96793 12.3966C5.886 12.363 5.81157 12.3135 5.74897 12.2509C5.68638 12.1883 5.63688 12.1139 5.60336 12.032C5.56983 11.95 5.55297 11.8623 5.55374 11.7737C5.55451 11.6852 5.5729 11.5977 5.60784 11.5164C5.64277 11.4351 5.69356 11.3615 5.75724 11.3L9.05724 8.00001L5.75724 4.7C5.6358 4.57427 5.5686 4.40587 5.57012 4.23107C5.57164 4.05627 5.64175 3.88906 5.76536 3.76546C5.88896 3.64185 6.05617 3.57174 6.23097 3.57022C6.40577 3.5687 6.57417 3.6359 6.6999 3.75734L10.4712 7.52867Z"
      fill={fill}
    />
  </svg>
);

const styles = StyleSheet.create({
  headingText: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  divider: {
    backgroundColor: '#E5E5E5',
    height: 1,
    width: '100%',
  },
  navContainer: {
    display: 'flex',
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 16,
  },
  buttonBaseContainer: {
    display: 'flex',
    paddingVertical: 12,
    paddingHorizontal: 12,
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderRadius: 8,
  },
  activeNavButton: {
    backgroundColor: '#F3F7FD',
    borderWidth: 1,
    borderColor: '#BAD1F6',
  },
  inactiveNavButton: {
    backgroundColor: '#F3F3F3',
    borderWidth: 0,
  },
  buttonText: {
    color: '#1060E0',
    fontFamily: 'Work Sans',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  bottomNavText: {
    color: '#1060E0',
    fontFamily: 'Work Sans',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
  },
  topNavText: {
    color: '#1060E0',
    fontFamily: 'Work Sans',
    fontSize: 13,
    fontWeight: '400',
    lineHeight: 16,
  },
  buttonRowBottom: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 130,
  },
  buttonRowTop: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 76,
  },
  iconWrapper: {
    width: 16,
    height: 16,
  },
});

export default Setting;
