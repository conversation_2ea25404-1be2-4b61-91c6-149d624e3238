import React, {useCallback, useEffect, useState} from 'react';
import {ScrollView, StyleSheet, View, Text} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _, {debounce} from 'lodash';

// apptile-core imports
import {
  selectAppSettingsForKey,
  BrandSettingsTypes,
  HeaderSettingsTypes,
  modelUpdateAction,
  updateSettingsValue,
  updateSettingsValueBulk,
  SettingsConfig,
} from 'apptile-core';

// Local component imports
import CollapsiblePanel from '../components/CollapsiblePanel';
import CheckboxControl from '../components/controls/CheckboxControl';
import {softRestartConfig} from '../actions/editorActions';
import {selectScreensInNav} from '../selectors/EditorSelectors';
import DropDownControl from '../components/controls/DropDownControl';
import IconChooserControl from '../components/controls/IconChooserControl';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import RangeSliderControl from '../components/controls/RangeSliderControl';

const BRAND_LOGO_WIDTH = BrandSettingsTypes.BRAND_LOGO_WIDTH,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_TILE_TAG = BrandSettingsTypes.BRAND_TILE_TAG,
  BRAND_TILE_TAG_DISPLAY = BrandSettingsTypes.BRAND_TILE_TAG_DISPLAY,
  BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES;
const {
  HEADER_SETTINGS_KEY,
  HEADER_TEMPLATE_ID,
  HEADER_LOGO_RESIZE,
  HEADER_LEFT_ICON,
  HEADER_LEFT_ICON_SHOW,
  HEADER_LEFT_ICON_NAVIGATION,
  HEADER_RIGHT_ICON,
  HEADER_RIGHT_ICON_SHOW,
  HEADER_RIGHT_ICON_NAVIGATION,
  HEADER_CART_ICON_SHOW,
  HEADER_WISHLIST_ICON_SHOW,
  HEADER_CART_ICON,
  HEADER_WISHLIST_ICON,
  HEADER_CART_ICON_NAVIGATION,
  HEADER_WISHLIST_ICON_NAVIGATION,
  HEADER_ICON_SIZE,
} = HeaderSettingsTypes;

const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
interface TopHeaderProps {
  currentEditorSection: string;
}
const TopHeader: React.FC<TopHeaderProps> = ({currentEditorSection}: any) => {
  // Brand Settings
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandLogoSize: string | null = brandSettings.getSettingValue(BRAND_LOGO_WIDTH);
  const brandTileTag: string | null = brandSettings.getSettingValue(BRAND_TILE_TAG);
  const brandTileTagDisplay: string | null = brandSettings.getSettingValue(BRAND_TILE_TAG_DISPLAY);
  const brandExposedTiles: string | null = brandSettings.getSettingValue(BRAND_EXPOSED_TILES);

  // Header Settings
  const headerSettings: SettingsConfig = useSelector(settingsSelector(HEADER_SETTINGS_KEY));
  const isHeaderNotEnabled = Object.keys(headerSettings.toJS()?.values).length === 0;
  const headerTemplateId: number = headerSettings.getSettingValue(HEADER_TEMPLATE_ID) ?? 0;
  const headerLogoResize: string | null = headerSettings.getSettingValue(HEADER_LOGO_RESIZE) ?? 'contain';
  const headerLeftIcon: string | null = headerSettings.getSettingValue(HEADER_LEFT_ICON) ?? '';
  const headerLeftIconShow: boolean = headerSettings.getSettingValue(HEADER_LEFT_ICON_SHOW) ?? false;
  const headerLeftIconNavigation: string | null = headerSettings.getSettingValue(HEADER_LEFT_ICON_NAVIGATION);
  const headerRightIcon: string | null = headerSettings.getSettingValue(HEADER_RIGHT_ICON) ?? '';
  const headerRightIconShow: boolean = headerSettings.getSettingValue(HEADER_RIGHT_ICON_SHOW) ?? false;
  const headerRightIconNavigation: string | null = headerSettings.getSettingValue(HEADER_RIGHT_ICON_NAVIGATION);
  const headerCartIconShow: boolean = headerSettings.getSettingValue(HEADER_CART_ICON_SHOW) ?? false;
  const headerWishlistIconShow: boolean = headerSettings.getSettingValue(HEADER_WISHLIST_ICON_SHOW) ?? false;
  const headerCartIcon: string | null = headerSettings.getSettingValue(HEADER_CART_ICON) ?? '';
  const headerWishlistIcon: string | null = headerSettings.getSettingValue(HEADER_WISHLIST_ICON) ?? '';
  const headerCartIconNavigation: string | null = headerSettings.getSettingValue(HEADER_CART_ICON_NAVIGATION);
  const headerWishlistIconNavigation: string | null = headerSettings.getSettingValue(HEADER_WISHLIST_ICON_NAVIGATION);
  const headerIconSize: string | null = headerSettings.getSettingValue(HEADER_ICON_SIZE) ?? '20';

  const dispatch = useDispatch();

  const [brandLogoWidth, setBrandLogoWidth] = useState(brandLogoSize);
  const [brandTileSpecificTag, setBrandTileSpecificTag] = useState(brandTileTag);
  const [brandTileSpecificTagDisplay, setBrandTileSpecificTagDisplay] = useState(brandTileTagDisplay);
  const [brandSpecificTiles, setBrandSpecificTiles] = useState(brandExposedTiles);
  const [headerTemplateValue, setHeaderTemplate] = useState(headerTemplateId);
  const [headerLogoResizeValue, setHeaderLogoResize] = useState(headerLogoResize);
  const [headerLeftIconValue, setHeaderLeftIcon] = useState(headerLeftIcon);
  const [headerLeftIconShowValue, setHeaderLeftIconShow] = useState(headerLeftIconShow);
  const [headerLeftIconNavigationValue, setHeaderLeftIconNavigation] = useState(headerLeftIconNavigation);
  const [headerRightIconValue, setHeaderRightIcon] = useState(headerRightIcon);
  const [headerRightIconShowValue, setHeaderRightIconShow] = useState(headerRightIconShow);
  const [headerRightIconNavigationValue, setHeaderRightIconNavigation] = useState(headerRightIconNavigation);
  const [headerCartIconShowValue, setHeaderCartIconShow] = useState(headerCartIconShow);
  const [headerWishlistIconShowValue, setHeaderWishlistIconShow] = useState(headerWishlistIconShow);
  const [headerCartIconValue, setHeaderCartIcon] = useState(headerCartIcon);
  const [headerWishlistIconValue, setHeaderWishlistIcon] = useState(headerWishlistIcon);
  const [headerCartIconNavigationValue, setHeaderCartIconNavigation] = useState(headerCartIconNavigation);
  const [headerWishlistIconNavigationValue, setHeaderWishlistIconNavigation] = useState(headerWishlistIconNavigation);
  const [headerIconSizeValue, setHeaderIconSize] = useState(headerIconSize);
  const [closeSectionIndex, setCloseSectionIndex] = useState<String[]>([]);
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    if (brandLogoWidth !== brandLogoSize) setBrandLogoWidth(brandLogoSize);
  }, [brandLogoSize]);
  useEffect(() => {
    if (brandTileSpecificTag !== brandTileTag) setBrandTileSpecificTag(brandTileTag);
  }, [brandTileTag]);
  useEffect(() => {
    if (brandTileSpecificTagDisplay !== brandTileTagDisplay) setBrandTileSpecificTagDisplay(brandTileTagDisplay);
  }, [brandTileTagDisplay]);
  useEffect(() => {
    if (brandSpecificTiles !== brandExposedTiles) setBrandSpecificTiles(brandExposedTiles);
  }, [brandExposedTiles]);
  useEffect(() => {
    if (headerTemplateValue !== headerTemplateId) setHeaderTemplate(headerTemplateId);
  }, [headerTemplateId]);
  useEffect(() => {
    if (headerLogoResizeValue !== headerLogoResize) setHeaderLogoResize(headerLogoResize);
  }, [headerLogoResize]);
  useEffect(() => {
    if (headerLeftIconValue !== headerLeftIcon) setHeaderLeftIcon(headerLeftIcon);
  }, [headerLeftIcon]);
  useEffect(() => {
    if (headerLeftIconShowValue !== headerLeftIconShow) setHeaderLeftIconShow(headerLeftIconShow);
  }, [headerLeftIconShow]);
  useEffect(() => {
    if (headerLeftIconNavigationValue !== headerLeftIconNavigation)
      setHeaderLeftIconNavigation(headerLeftIconNavigation);
  }, [headerLeftIconNavigation]);
  useEffect(() => {
    if (headerRightIconValue !== headerRightIcon) setHeaderRightIcon(headerRightIcon);
  }, [headerRightIcon]);
  useEffect(() => {
    if (headerRightIconShowValue !== headerRightIconShow) setHeaderRightIconShow(headerRightIconShow);
  }, [headerRightIconShow]);
  useEffect(() => {
    if (headerRightIconNavigationValue !== headerRightIconNavigation)
      setHeaderRightIconNavigation(headerRightIconNavigation);
  }, [headerRightIconNavigation]);
  useEffect(() => {
    if (headerCartIconShowValue !== headerCartIconShow) setHeaderCartIconShow(headerCartIconShow);
  }, [headerCartIconShow]);
  useEffect(() => {
    if (headerWishlistIconShowValue !== headerWishlistIconShow) setHeaderWishlistIconShow(headerWishlistIconShow);
  }, [headerWishlistIconShow]);
  useEffect(() => {
    if (headerCartIconValue !== headerCartIcon) setHeaderCartIcon(headerCartIcon);
  }, [headerCartIcon]);
  useEffect(() => {
    if (headerWishlistIconValue !== headerWishlistIcon) setHeaderWishlistIcon(headerWishlistIcon);
  }, [headerWishlistIcon]);
  useEffect(() => {
    if (headerCartIconNavigationValue !== headerCartIconNavigation)
      setHeaderCartIconNavigation(headerCartIconNavigation);
  }, [headerCartIconNavigation]);
  useEffect(() => {
    if (headerWishlistIconNavigationValue !== headerWishlistIconNavigation)
      setHeaderWishlistIconNavigation(headerWishlistIconNavigation);
  }, [headerWishlistIconNavigation]);
  useEffect(() => {
    if (headerIconSizeValue !== headerIconSize) setHeaderIconSize(headerIconSize);
  }, [headerIconSize]);

  useEffect(() => {
    const array = [
      {key: HEADER_LEFT_ICON_SHOW, value: true},
      {key: HEADER_LEFT_ICON, value: {iconName: 'menu', iconType: 'Feather'}},
      {key: HEADER_LEFT_ICON_NAVIGATION, value: 'Menu'},
      {key: HEADER_CART_ICON_SHOW, value: true},
      {key: HEADER_CART_ICON, value: {iconName: 'cart-outline', iconType: 'Material Icon'}},
      {key: HEADER_CART_ICON_NAVIGATION, value: 'Cart'},
      {key: HEADER_WISHLIST_ICON_SHOW, value: true},
      {key: HEADER_WISHLIST_ICON, value: {iconName: 'heart-outline', iconType: 'Material Icon'}},
      {key: HEADER_WISHLIST_ICON_NAVIGATION, value: 'Wishlist'},
    ];
    if (isHeaderNotEnabled) {
      dispatch(updateSettingsValueBulk(HEADER_SETTINGS_KEY, array));
      
      dispatch(softRestartConfig());
    }
  }, [dispatch]);

  const onBrandLogoSizeUpdate = _.debounce(value => {
    setBrandLogoWidth(value);
    dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_LOGO_WIDTH, value));
    const modelUpdate = [
      {
        selector: ['Apptile', 'brandLogoSize'],
        newValue: value,
      },
    ];
    dispatch(modelUpdateAction(modelUpdate, undefined, true));
    dispatch(softRestartConfig());
  }, 400);

  const onHeaderTemplateUpdate = useCallback(
    value => {
      setHeaderTemplate(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_TEMPLATE_ID, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderLogoResizeUpdate = useCallback(
    value => {
      setHeaderLogoResize(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LOGO_RESIZE, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderLeftIconUpdate = useCallback(
    value => {
      setHeaderLeftIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderLeftIconShowUpdate = useCallback(
    value => {
      setHeaderLeftIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON_SHOW, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderLeftIconNavigationUpdate = useCallback(
    value => {
      setHeaderLeftIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON_NAVIGATION, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderRightIconUpdate = useCallback(
    value => {
      setHeaderRightIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderRightIconShowUpdate = useCallback(
    value => {
      setHeaderRightIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON_SHOW, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderRightIconNavigationUpdate = useCallback(
    value => {
      setHeaderRightIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON_NAVIGATION, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderCartIconShowUpdate = useCallback(
    value => {
      setHeaderCartIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON_SHOW, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderWishlistIconShowUpdate = useCallback(
    value => {
      setHeaderWishlistIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON_SHOW, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderCartIconUpdate = useCallback(
    value => {
      setHeaderCartIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderWishlistIconUpdate = useCallback(
    value => {
      setHeaderWishlistIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderCartIconNavigationUpdate = useCallback(
    value => {
      setHeaderCartIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON_NAVIGATION, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderWishlistIconNavigationUpdate = useCallback(
    value => {
      setHeaderWishlistIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON_NAVIGATION, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );
  const onHeaderIconSizeUpdate = useCallback(
    value => {
      setHeaderIconSize(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_ICON_SIZE, value));
      dispatch(softRestartConfig());
    },
    [dispatch],
  );

  const debouncedOnValueChange = debounce(value => onHeaderIconSizeUpdate(value), 500);

  const onEditorSectionToggle = (sectionKey: string) => {
    if (closeSectionIndex.includes(sectionKey)) {
      setCloseSectionIndex(closeSectionIndex.filter(i => i !== sectionKey));
    } else {
      setCloseSectionIndex([...closeSectionIndex, sectionKey]);
    }
  };

  const screens = (useSelector(selectScreensInNav) || []).map(s => ({
    name: s.title,
    value: s.screen,
  }));

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={{overflow: 'scroll', paddingTop: 8}}>
      {currentEditorSection === 'basics' ? (
        <CollapsiblePanel
          title="top navigation items"
          customHeader={
            <EditorSectionHeader
              label={'TOP NAVIGATION ITEMS'}
              name={'top navigation items'}
              isPremiumDesabled={false}
              iconSize={18}
              icon={isOpen ? 'chevron-up' : 'chevron-down'}
              iconType={'Material Icon'}
            />
          }
          isOpen={isOpen}
          setOpen={() => setIsOpen(!isOpen)}
          backgroundStyle={{borderWidth: 0}}>
          <View>
            <Text style={styles.navSubHeading}>Must have 1 left icon and between 1-3 right icons</Text>
          </View>
          <View style={{flex: 1, flexDirection: 'row', width: '100%', gap: 8}}>
            <View style={{width: '30%', justifyContent: 'center'}}>
              <Text style={styles.iconLabel}>Left Icon </Text>
            </View>
            <View style={{width: '20%'}}>
              <IconChooserControl
                value={headerLeftIconValue?.iconName ?? ''}
                config={{iconType: headerLeftIconValue?.iconType}}
                onBulkValueChange={onHeaderLeftIconUpdate}
                defaultValue={''}
                isTextHide={true}
              />
            </View>
            <DropDownControl
              defaultValue={headerLeftIconNavigationValue}
              value={headerLeftIconNavigationValue}
              options={screens}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
              onChange={onHeaderLeftIconNavigationUpdate}
            />
          </View>
          <View style={{flex: 1, flexDirection: 'row', width: '100%', gap: 10}}>
            <View style={{width: '30%', justifyContent: 'center'}}>
              <Text style={styles.iconLabel}>Right Icon 1 </Text>
            </View>
            <View style={{width: '20%'}}>
              <IconChooserControl
                value={headerWishlistIconValue?.iconName ?? ''}
                config={{iconType: headerWishlistIconValue?.iconType}}
                onBulkValueChange={onHeaderWishlistIconUpdate}
                defaultValue={''}
                isTextHide={true}
              />
            </View>
            <DropDownControl
              defaultValue={headerWishlistIconNavigationValue}
              value={headerWishlistIconNavigationValue}
              options={screens}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
              onChange={onHeaderWishlistIconNavigationUpdate}
            />
          </View>
          <View style={{flex: 1, flexDirection: 'row', width: '100%', gap: 10}}>
            <View style={{width: '30%', justifyContent: 'center'}}>
              <Text style={styles.iconLabel}>Right Icon 2 </Text>
            </View>
            <View style={{width: '20%'}}>
              <IconChooserControl
                value={headerCartIconValue?.iconName ?? ''}
                config={{iconType: headerCartIconValue?.iconType}}
                onBulkValueChange={onHeaderCartIconUpdate}
                defaultValue={''}
                isTextHide={true}
              />
            </View>
            <DropDownControl
              defaultValue={headerCartIconNavigationValue}
              value={headerCartIconNavigationValue}
              options={screens}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
              onChange={onHeaderCartIconNavigationUpdate}
            />
          </View>
          <View style={{flex: 1, flexDirection: 'row', width: '100%', gap: 10}}>
            <View style={{width: '30%', justifyContent: 'center'}}>
              <Text style={styles.iconLabel}>Right Icon 3 </Text>
            </View>
            <View style={{width: '20%'}}>
              <IconChooserControl
                value={headerRightIconValue?.iconName ?? ''}
                config={{iconType: headerRightIconValue?.iconType}}
                onBulkValueChange={onHeaderRightIconUpdate}
                defaultValue={''}
                isTextHide={true}
              />
            </View>
            <DropDownControl
              defaultValue={headerRightIconNavigationValue}
              value={headerRightIconNavigationValue}
              options={screens}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
              onChange={onHeaderRightIconNavigationUpdate}
            />
          </View>
        </CollapsiblePanel>
      ) : currentEditorSection === 'settings' ? (
        <CollapsiblePanel
          title="VISIBILITY"
          customHeader={
            <EditorSectionHeader
              label={'VISIBILITY'}
              name={'visibility'}
              isPremiumDesabled={false}
              iconSize={18}
              icon={!closeSectionIndex.includes('VISIBILITY') ? 'chevron-up' : 'chevron-down'}
              iconType={'Material Icon'}
            />
          }
          isOpen={!closeSectionIndex.includes('VISIBILITY')}
          setOpen={() => onEditorSectionToggle('VISIBILITY')}
          backgroundStyle={{borderWidth: 0}}>
          <View style={{overflow: 'scroll', paddingTop: 8}}>
            <CheckboxControl
              value={headerLeftIconShowValue}
              onChange={onHeaderLeftIconShowUpdate}
              label={'Show Left Icon'}
            />
            <CheckboxControl
              value={headerWishlistIconShowValue}
              onChange={onHeaderWishlistIconShowUpdate}
              label={'Show Right Icon 1'}
            />
            <CheckboxControl
              value={headerCartIconShowValue}
              onChange={onHeaderCartIconShowUpdate}
              label={'Show Right Icon 2'}
            />
            <CheckboxControl
              value={headerRightIconShowValue}
              onChange={onHeaderRightIconShowUpdate}
              label={'Show Right Icon 3'}
            />
          </View>
        </CollapsiblePanel>
      ) : (
        <CollapsiblePanel
          title="ICON"
          customHeader={
            <EditorSectionHeader
              label={'ICON'}
              name={'ICON'}
              isPremiumDesabled={false}
              iconSize={18}
              icon={!closeSectionIndex.includes('ICON') ? 'chevron-up' : 'chevron-down'}
              iconType={'Material Icon'}
            />
          }
          isOpen={!closeSectionIndex.includes('ICON')}
          setOpen={() => onEditorSectionToggle('ICON')}
          backgroundStyle={{borderWidth: 0}}>
          <View style={{overflow: 'scroll', paddingTop: 8}}>
            <RangeSliderControl
              defaultValue="20"
              label={'Icon Size'}
              value={headerIconSizeValue ?? ''}
              onChange={debouncedOnValueChange}
              minRange="12"
              maxRange="32"
            />
          </View>
        </CollapsiblePanel>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
  divider: {
    backgroundColor: '#E5E5E5',
    marginVertical: 5,
    height: 1,
    width: '100%',
  },
  navSubHeading: {fontSize: 12, marginTop: '-4', color: '#535353', fontWeight: '400'},
  iconLabel: {fontSize: 12},
});
export default TopHeader;