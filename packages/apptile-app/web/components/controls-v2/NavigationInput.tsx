import _, {debounce} from 'lodash';
import React, {useCallback, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {useSelector} from 'react-redux';
import {ScreenConfigParams} from 'apptile-core';
import {selectScreensInNav} from '../../selectors/EditorSelectors';
import DropDownControl from '../../components/controls/DropDownControl';
import standardScreens, {editableScreens} from '../../common/screenConstants';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {ShopifyItemHandlePicker} from '../../integrations/shopify/components/ShopifyItemPicker';

export interface NavigationEditorProps {
  onChange: (data: {navigateTo: string; navigateEntityId: string}) => void;
  children?: React.ReactNode;
  value: {navigateTo: string; navigateEntityId: string};
  screens: ScreenConfigParams[];
  label: string;
}

const NavigationEditor: React.FC<NavigationEditorProps> = props => {
  const {onChange, value, label} = props;
  const [newValue, setNewValue] = useState(value);
  const [method, setMethod] = useState(
    value?.navigateTo == 'Product' || value?.navigateTo == 'Collection' ? 'Item' : 'Screen',
  );
  const navScreens = useSelector(selectScreensInNav);
  const newEditableScreens: ScreenConfigParams[] = Array(editableScreens.length);
  navScreens
    .filter((s: ScreenConfigParams) => editableScreens.includes(s.name))
    .map(s => newEditableScreens.splice(editableScreens.indexOf(s.name), 1, s));
  const screens = [
    ...newEditableScreens
      .filter((s: ScreenConfigParams) => s)
      .map((s: ScreenConfigParams) => ({
        name: s.screen == 'Product' ? 'Product' : s.screen == 'Collection' ? 'Collection' : s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
    ...navScreens
      .filter((s: ScreenConfigParams) => !standardScreens.includes(s.name))
      .map((s: ScreenConfigParams) => ({
        name: s.screen == 'Product' ? 'Product' : s.screen == 'Collection' ? 'Collection' : s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
  ];

  const updateEvent = debounce(data => {
    setMethod(data?.navigateTo == 'Product' || data?.navigateTo == 'Collection' ? 'Item' : 'Screen');
    setNewValue(data);
    onChange(data);
  }, 200);

  return (
    <View style={styles.eventListItem}>
      <View style={styles.editorWindow}>
        <Text style={[commonStyles.heading]}>{label}</Text>
        <View style={styles.rowLayout}>
          <DropDownControl
            label="Navigate to"
            defaultValue={method == 'Item' ? newValue?.navigateTo : newValue?.navigateEntityId}
            value={method == 'Item' ? newValue?.navigateTo : newValue?.navigateEntityId}
            options={screens}
            nameKey={'name'}
            valueKey={'value'}
            iconKey={'icon'}
            disableBinding={true}
            onChange={function (value: string): void {
              updateEvent({
                navigateTo: value == 'Product' || value == 'Collection' ? value : 'Screen',
                navigateEntityId: value == 'Product' || value == 'Collection' ? '' : value,
              });
            }}
          />
        </View>
        {method == 'Item' && (
          <View style={styles.rowLayout}>
            <ShopifyItemHandlePicker
              name={newValue?.navigateTo}
              value={newValue?.navigateEntityId}
              label={newValue?.navigateTo}
              itemType={newValue?.navigateTo == 'Product' ? 'product' : 'collection'}
              onChange={value => {
                updateEvent({navigateTo: newValue?.navigateTo, navigateEntityId: value});
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

const NavigationInput: React.FC<any> = (props) => {
  const {value, onChange, configProps} = props;
  const screens = useSelector(selectScreensInNav);

  const onUpdate = useCallback(
    (data: any) => {
      onChange(data);
    },
    [onChange],
  );

  return (
    <View style={[{flex: 1}, commonStyles.controlContainer]}>
      <NavigationEditor value={value} screens={screens} onChange={onUpdate} label={configProps.label} />
    </View>
  );
};

const styles = StyleSheet.create({
  eventListItem: {
    flex: 1,
    flexDirection: 'column',
    flexBasis: 'auto',
    flexGrow: 1,
    overflow: 'hidden',
    marginBottom: 5,
  },
  eventListingLabel: {
    alignContent: 'center',
    padding: 4,
    flex: 1,
    flexBasis: 'auto',
    fontSize: 10,
    color: '#fff',
    backgroundColor: '#0091bc',
    borderRadius: 4,
    marginHorizontal: 4,
    flexWrap: 'wrap',
    flexGrow: 0,
  },
  eventListingMethod: {
    flex: 1,
    flexGrow: 0,
    fontSize: 10,
    lineHeight: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  eventListingDisplayMethod: {
    justifyContent: 'center',
    flex: 1,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  addEventRow: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
    justifyContent: 'flex-end',
  },
  eventListingBox: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
  },
  eventListingLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    overflow: 'scroll',
  },
  actionLinkText: {
    textAlign: 'right',
    flex: 1,
    flexGrow: 0,
    padding: 5,
    color: '#3c92dc',
    flexBasis: 'auto',
  },
  deleteButton: {margin: 2, padding: 2},
  upIcon: {
    borderRadius: 40,
  },
  downIcom: {
    borderRadius: 40,
    marginLeft: 4,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventItemBox: {flexDirection: 'row'},
  editorWindow: {
    paddingTop: 5,
    flex: 1,
    backgroundColor: '#fff',
    flexBasis: 'auto',
    flexDirection: 'column',
    flexShrink: 0,
  },
  editorInputItem: {flex: 1, flexDirection: 'column'},
  rowLayout: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 11,
    color: '#333',
    marginRight: 5,
  },
});

export default NavigationInput;
