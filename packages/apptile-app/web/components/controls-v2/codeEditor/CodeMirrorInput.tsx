import React, {forwardRef, useEffect} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import theme from '../../../styles-v2/theme';
import CodeEditor from './codeEditor';
import _ from 'lodash';
import commonStyles from '@/root/web/styles-v2/commonStyles';

type CodeMirrorInputProps = {
  value?: unknown;
  defaultValue?: unknown;
  placeholder?: unknown;
  label?: string;
  onChange: (value: string) => void;
  noOfLines: number;
  language: string;
  ignoreBinding?: boolean;
  theme?: string;
  editorStyles?: StyleSheet.NamedStyles<any>;
};

const CodeMirrorInput = forwardRef<{isFocused: () => boolean}, CodeMirrorInputProps>(
  (
    {
      value,
      defaultValue,
      placeholder,
      label,
      propertyName,
      onChange,
      noOfLines = 1,
      language,
      ignoreBinding,
      theme = 'material-palenight',
      editorStyles,
    },
    ref,
  ) => {
    const [valueState, setValueState] = React.useState(defaultValue ? `${defaultValue}` : value ? `${value}` : '');
    const debounceValue = _.debounce(newValue => onChange(newValue), 300);
    useEffect(() => {
      debounceValue(valueState);
    }, [valueState]);
    return (
      <View style={{flex: 1, marginVertical: theme.PRIMARY_MARGIN, minHeight: theme.PRIMARY_HEIGHT + 5}}>
        {label && (
          <View style={{width: '100%', marginBottom: 5}}>
            <Text style={{...commonStyles.labelText, fontWeight: 'bold'}}>{label}</Text>
          </View>
        )}
        <CodeEditor
          {...{
            onChange: (editor: unknown, data: unknown, value: string) => {
              if (ignoreBinding) {
                setValueState(value.replace(/\{\{/gim, '{ {'));
              } else {
                setValueState(value);
              }
            },
            defaultValue: valueState,
            // value: value ? `${value}` : '',
            options: {
              theme,
              mode: `text/${language}`,
              lineNumbers: true,
              autoCloseBrackets: true,
              matchBrackets: true,
              autohint: true,
              highlightSelectionMatches: true,
              extraKeys: {'Ctrl-Space': 'autocomplete'},
              viewportMargin: Infinity,
              autoRefresh: true,
            },
            editorSize: [
              '100%',
              noOfLines > 0 ? `${(theme.LINE_HEIGHT + (noOfLines == 1 ? 5 : 3)) * noOfLines + 16}px` : '100%',
            ],
            placeholder: placeholder ? `${placeholder}` : '',
            editorStyles,
            forwardedRef: ref,
          }}
        />
      </View>
    );
  },
);

const styles = StyleSheet.create({
  previewValue: {
    overflow: 'scroll',
    borderTopColor: '#cee1da',
    borderTopWidth: 0.2,
    paddingTop: 5,
    paddingHorizontal: 6,
    maxWidth: 200,
    maxHeight: 300,
  },
  previewText: {
    color: theme.PRIMARY_COLOR,
    fontSize: 10,
    fontWeight: '200',
    fontFamily: 'monospace',
    marginTop: 4,
  },
});

export default CodeMirrorInput;
