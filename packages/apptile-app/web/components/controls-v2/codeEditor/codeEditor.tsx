import * as codemirror from 'codemirror';
import 'codemirror/addon/display/autorefresh';
import 'codemirror/addon/display/placeholder';
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/edit/matchbrackets';
import 'codemirror/addon/hint/anyword-hint';
import 'codemirror/addon/hint/javascript-hint';
import 'codemirror/addon/hint/css-hint';
import 'codemirror/addon/hint/html-hint';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/sql-hint';
import 'codemirror/addon/mode/multiplex';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/mode/jsx/jsx';
import 'codemirror/mode/css/css';
import 'codemirror/mode/htmlmixed/htmlmixed';
import 'codemirror/mode/sql/sql';
import './theme/material-palenight.css';
import './theme/material-ocean.css';
import './theme/juejin.css';
import './theme/monokai.css';
import isEqual from 'lodash-es/isEqual';
import {forwardRef, default as React, SyntheticEvent} from 'react';
import {StyleSheet, View} from 'react-native';

import _ from 'lodash';
import theme from '../../../styles-v2/theme';

export const CodeMirrorEmpty = `
.CodeMirror {
  height: auto;
  min-height: 28px;
  padding: 0px;
  font-family: ${theme.FONT_FAMILY};
  font-size: ${theme.FONT_SIZE}px;
  line-height: ${theme.LINE_HEIGHT + 2}px;
}
.CodeMirror *::-webkit-scrollbar {
  background-color:#0000;
  width: 4px;
  height: 4px;
}
/* scrollbar itself */
.CodeMirror *::-webkit-scrollbar-thumb {
  border-radius: 6px;
}
/* set button(top and bottom of the scrollbar) */
.CodeMirror *::-webkit-scrollbar-button {display:none;height: 0; width: 0;}
.CodeMirror-lines { padding: 8px 4px;}
.CodeMirror-line { padding-right: 4px;}
.CodeMirror pre.CodeMirror-line, .CodeMirror pre.CodeMirror-line-like{
  padding: 0px 6px 0px 4px;
}
.codeEditorV2  .CodeMirror-linenumber {
    padding: 0 !important;
}
`;

// Create stylesheet
const style = document.createElement('style');
style.type = 'text/css';
if (style.styleSheet) {
  style.styleSheet.cssText = CodeMirrorEmpty;
} else {
  style.appendChild(document.createTextNode(CodeMirrorEmpty));
}

// Inject stylesheet
document.head.appendChild(style);

export type CodemirrorEditor = codemirror.Editor;
export type CodemirrorEditorChange = codemirror.EditorChange;
export type CodemirrorScrollInfo = codemirror.ScrollInfo;
export type CodemirrorPosition = codemirror.Position;
export type CodemirrorLineHandle = codemirror.LineHandle;

export interface EditorEvent {
  (editor: CodemirrorEditor, event?: SyntheticEvent): void;
}

export interface EditorChangeEvent {
  (editor: CodemirrorEditor, changeObj: CodemirrorEditorChange): void;
}

export interface KeyHandledEvent {
  (editor: CodemirrorEditor, name: string, event: SyntheticEvent): void;
}

export interface ICodeMirrorActions {
  onChange?(editor: CodemirrorEditor, data: CodemirrorEditorChange, value: unknown): void;
  onCursorActivity?(editor: CodemirrorEditor): void;
  onFocusChange?(e: boolean): void;
  onScroll?(editor: CodemirrorEditor, data: CodemirrorScrollInfo): void;

  onBlur?: EditorEvent;
  onContextMenu?: EditorEvent;
  onCopy?: EditorEvent;
  onCursor?: (editor: CodemirrorEditor, data: CodemirrorPosition) => void;
  onCut?: EditorEvent;
  onDblClick?: EditorEvent;
  onDragEnter?: EditorEvent;
  onDragLeave?: EditorEvent;
  onDragOver?: EditorEvent;
  onDragStart?: EditorEvent;
  onDrop?: EditorEvent;
  onFocus?: EditorEvent;
  onGutterClick?: (editor: CodemirrorEditor, lineNumber: number, gutter: string, event: Event) => void;
  onInputRead?: EditorChangeEvent;
  onKeyDown?: EditorEvent;
  onKeyHandled?: KeyHandledEvent;
  onKeyPress?: EditorEvent;
  onKeyUp?: EditorEvent;
  onMouseDown?: EditorEvent;
  onPaste?: EditorEvent;
  onRenderLine?: (editor: CodemirrorEditor, line: CodemirrorLineHandle, element: HTMLElement) => void;
  onSelection?: (editor: CodemirrorEditor, data: Record<string, unknown>) => void;
  onTouchStart?: EditorEvent;
  onUpdate?: (editor: CodemirrorEditor) => void;
  onViewportChange?: (editor: CodemirrorEditor, start: number, end: number) => void;
}

export interface ICustomHints {
  tables: Record<string, Array<string>> | Array<string>;
}

export interface IcodeEditorProps extends ICodeMirrorActions {
  autoFocus?: boolean;
  className?: string;
  editorInstance?: CodemirrorEditor;
  defaultValue?: string;
  name?: string;
  options: Record<string, unknown>;
  path?: string;
  value?: string;
  preserveScrollPosition?: boolean;
  editorSize?: Array<string>;
  placeholder?: string;
  forwardedRef?: React.MutableRefObject<{isFocused: () => boolean}>;
  editorStyles?: StyleSheet.NamedStyles<any>;
}

interface codeEditorState {
  isFocused: boolean;
}
class CodeEditor extends React.Component<IcodeEditorProps, codeEditorState> {
  editor: codemirror.Editor;
  textareaNode = React.createRef<HTMLTextAreaElement>();
  defaultEditorSize = [600, 100];

  constructor(props: IcodeEditorProps) {
    super(props);

    this.state = {
      isFocused: false,
    };
  }

  getCodeMirrorInstance(): codemirror.Editor {
    return this.props.editorInstance || codemirror;
  }

  componentDidMount(): void {
    const value = this.props.defaultValue || this.props.value;
    const codeMirrorInstance = this.getCodeMirrorInstance();
    this.editor = codeMirrorInstance.fromTextArea(this.textareaNode, this.props.options);
    if (value) this.editor.setValue(value);
    const editorSize = this.props.editorSize || this.defaultEditorSize || [];
    this.editor.setSize(...editorSize);

    if (this.props.forwardedRef) {
      this.props.forwardedRef.current = {
        isFocused: () => {
          return this.state.isFocused;
        },
      };
    }

    this.editor.on('focus', (cm, event) => {
      this.editor.setCursor({line: this.editor.lineCount() || 1, ch: 0});
      this.editor.refresh();
    });

    this.editor.on('keyup', function (cm, event) {
      if (
        !cm.state.completionActive &&
        (event.keyCode || event.which) != 13 &&
        (event.keyCode || event.which) > 64 &&
        (event.keyCode || event.which) < 91 &&
        !event.altKey &&
        !event.ctrlKey
      ) {
        cm.showHint({completeSingle: false, alignWithWord: false});
      }
    });
    this.editor.on('change', (doc, change) => {
      if (this.props.onChange && change.origin !== 'setValue') {
        this.props.onChange(this.getCodeMirrorInstance(), change, doc.getValue());
      }
    });
  }

  componentWillUnmount(): void {
    // FIXME: on Unmount release refernces
    if (this.editor) {
      this.editor.toTextArea();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps: IcodeEditorProps): void {
    if (this.editor && nextProps.value !== undefined && nextProps.value !== this.props.value) {
      if (this.props.preserveScrollPosition) {
        const prevScrollPosition = this.editor.getScrollInfo();
        this.editor.setValue(nextProps.value);
        this.editor.scrollTo(prevScrollPosition.left, prevScrollPosition.top);
      } else {
        this.editor.setValue(nextProps.value);
      }
    }
    if (typeof nextProps.options === 'object') {
      for (const optionName in nextProps.options) {
        if (nextProps.options.hasOwnProperty(optionName)) {
          this.setOptionIfChanged(optionName, nextProps.options[optionName]);
        }
      }
    }
  }

  setOptionIfChanged(optionName: string, newValue: unknown): void {
    const oldValue = this.editor.getOption(optionName);
    if (!isEqual(oldValue, newValue)) {
      this.editor.setOption(optionName, newValue);
    }
  }

  render(): React.ReactNode {
    return (
      <div style={{...styles.codeEditor, ...this.props?.editorStyles}} className="codeEditorV2">
        <textarea
          ref={ref => (this.textareaNode = ref)}
          name={this.props.name || this.props.path}
          defaultValue={this.props.value}
          autoComplete="on"
          autoFocus={this.props.autoFocus}
          placeholder={this.props.placeholder ? this.props.placeholder : ''}
        />
      </div>
    );
  }
}

export default CodeEditor;

const styles = StyleSheet.create({
  codeEditorFocused: {
    flex: 1,
    flexDirection: 'row',
    borderRadius: 10,
    overflow: 'hidden',
  },
  codeEditor: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 10,
    overflow: 'hidden',
  },
});
