/* Based on Sublime Text's Monokai theme */

.codeEditorV2 .cm-s-monokai.CodeMirror { background: #272822 !important; color: #f8f8f2; }
.codeEditorV2 .cm-s-monokai div.CodeMirror-selected { background: #49483E !important; }
.codeEditorV2 .cm-s-monokai .CodeMirror-line::selection, .codeEditorV2 .cm-s-monokai .CodeMirror-line > span::selection, .codeEditorV2 .cm-s-monokai .CodeMirror-line > span > span::selection { background: rgba(73, 72, 62, .99) !important; }
.codeEditorV2 .cm-s-monokai .CodeMirror-line::-moz-selection, .codeEditorV2 .cm-s-monokai .CodeMirror-line > span::-moz-selection, .codeEditorV2 .cm-s-monokai .CodeMirror-line > span > span::-moz-selection { background: rgba(73, 72, 62, .99) !important; }
.codeEditorV2 .cm-s-monokai .CodeMirror-gutters { background: #272822 !important; border-right: 0px; }
.codeEditorV2 .cm-s-monokai .CodeMirror-guttermarker { color: white; }
.codeEditorV2 .cm-s-monokai .CodeMirror-guttermarker-subtle { color: #d0d0d0; }
.codeEditorV2 .cm-s-monokai .CodeMirror-linenumber { color: #d0d0d0; }
.codeEditorV2 .cm-s-monokai .CodeMirror-cursor { border-left: 1px solid #f8f8f0; }

.codeEditorV2 .cm-s-monokai span.cm-comment { color: #75715e; }
.codeEditorV2 .cm-s-monokai span.cm-atom { color: #ae81ff; }
.codeEditorV2 .cm-s-monokai span.cm-number { color: #ae81ff; }

.codeEditorV2 .cm-s-monokai span.cm-comment.cm-attribute { color: #97b757; }
.codeEditorV2 .cm-s-monokai span.cm-comment.cm-def { color: #bc9262; }
.codeEditorV2 .cm-s-monokai span.cm-comment.cm-tag { color: #bc6283; }
.codeEditorV2 .cm-s-monokai span.cm-comment.cm-type { color: #5998a6; }

.codeEditorV2 .cm-s-monokai span.cm-property, .codeEditorV2 .cm-s-monokai span.cm-attribute { color: #a6e22e; }
.codeEditorV2 .cm-s-monokai span.cm-keyword { color: #f92672; }
.codeEditorV2 .cm-s-monokai span.cm-builtin { color: #66d9ef; }
.codeEditorV2 .cm-s-monokai span.cm-string { color: #e6db74; }

.codeEditorV2 .cm-s-monokai span.cm-variable { color: #f8f8f2; }
.codeEditorV2 .cm-s-monokai span.cm-variable-2 { color: #9effff; }
.codeEditorV2 .cm-s-monokai span.cm-variable-3, .codeEditorV2 .cm-s-monokai span.cm-type { color: #66d9ef; }
.codeEditorV2 .cm-s-monokai span.cm-def { color: #fd971f; }
.codeEditorV2 .cm-s-monokai span.cm-bracket { color: #f8f8f2; }
.codeEditorV2 .cm-s-monokai span.cm-tag { color: #f92672; }
.codeEditorV2 .cm-s-monokai span.cm-header { color: #ae81ff; }
.codeEditorV2 .cm-s-monokai span.cm-link { color: #ae81ff; }
.codeEditorV2 .cm-s-monokai span.cm-error { background: #f92672 !important; color: #f8f8f0; }

.codeEditorV2 .cm-s-monokai .CodeMirror-activeline-background { background: #373831 !important; }
.codeEditorV2 .cm-s-monokai .CodeMirror-matchingbracket {
  text-decoration: underline;
  color: white !important;
}
