
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';

export interface IAutoFetchCard {
  image: string;
  selectedImage: string;
  previouslySelectedImage: string;
  onSelectAsset: (assetId: string, url?: string) => void;
  index: number;
}

const AutoFetchCard: React.FC<IAutoFetchCard> = props => {
  const {image, selectedImage, onSelectAsset, previouslySelectedImage, index} = props;
  const [thumburl, setThumburl] = useState(selectedImage ? image : undefined);
  const isSelected = image === selectedImage;
  const isPreviouslySelected = previouslySelectedImage === image;

  useEffect(() => {
    //For preselecting the first image in asset upload! also kept it in useEffect to wait for the component to mount
    if (index === 0 && thumburl) {
      onSelectAsset(image, thumburl);
    }
  }, [index, thumburl]);

  return (
    <TouchableOpacity
      style={[
        styles.CardWrapper,
        isSelected || (selectedImage && isPreviouslySelected && isSelected) || (!selectedImage && isPreviouslySelected)
          ? styles.selectedImageCard
          : {},
      ]}
      onPress={() => onSelectAsset(image, thumburl)}>
      {!thumburl && (
        <>
          <Image style={styles.cardImage} resizeMode="contain" source={image} onError={() => console.log('error')} />
          {isSelected && (
            <View style={{position: 'absolute', top: 0, left: 0, height: 112, width: 112, backgroundColor: '#0008'}} />
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

export default AutoFetchCard;

const styles = StyleSheet.create({
  CardWrapper: {
    width: 112,
    height: 112,
    borderRadius: 4,
    overflow: 'hidden',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  selectedImageCard: {
    borderWidth: 2,
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  cardImage: {
    height: 112,
    width: 112,
  },
  cardActiveColor: {color: '#fff', textAlign: 'center'},
});
