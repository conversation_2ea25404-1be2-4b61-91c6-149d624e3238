import theme from '@/root/web/styles-v2/theme';
import {default as React, useEffect, useState} from 'react';
import {ActivityIndicator, FlatList, StyleSheet, Text, View} from 'react-native';
import AutoFetchCard from './autoFetchCard';
import Banner<PERSON><PERSON> from '../../../api/AutoFetchBannerApi';
import {datasourceTypeModelSel} from 'apptile-core';
import {getShopifyObjectCache} from '@/root/web/integrations/shopify/ShopifyObjectCache';

interface IAutoFetchLibrary {
  apptileState: apptileState;
  updateSelectedImage: (url?: string) => void;
  onNewAsset: () => void;
}

const AutoFetchLibrary: React.FC<IAutoFetchLibrary> = props => {
  const {updateSelectedImage} = props;
  const [data, setData] = useState<any[]>([]);
  const [previouslySelectedImage, setpreviouslySelectedImage] = useState(data[0]?.url);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [isFetching, setIsFetching] = useState(false);
  const [loadMore, setLoadMore] = useState(1);

  const onSelectAsset = (url: string) => {
    setSelectedImage(url);
    updateSelectedImage(url);
  };

  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  const storefrontApiUrl = ShopifyDSModel?.get('storefrontApiUrl');
  const baseUrl = new URL(storefrontApiUrl).origin;

  const preprocessingAutoFetchedImages = data => {
    const images = data?.map(image => image.url) || [];
    const uniqueImages = [...new Set(images)];
    return uniqueImages;
  };

  useEffect(() => {
    setIsFetching(true);
    const getImageBanner = async () => {
      return await (await getShopifyObjectCache()).getImagesList();
    };
    getImageBanner().then((res: any) => {
      setData(res);
      setIsFetching(false);
    });
    // BannerApi.fetchBanner(baseUrl, loadMore)
    //   .then(response => {
    //     const data: any = response.data;
    //     console.log(data, 'apio');
    //     setData(data);
    //     setIsFetching(false);
    //   })
    //   .catch(error => console.log('error while fetching banners', error));
  }, [previouslySelectedImage, loadMore]);

  const RenderItem = ({item, index}: any) => {
    return <AutoFetchCard {...{selectedImage, image: item, onSelectAsset, previouslySelectedImage}} index={index} />;
  };

  return (
    <View style={styles.container}>
      {isFetching && <ActivityIndicator />}
      {!isFetching && data.length === 0 && <Text>No Images Found</Text>}
      {data.length !== 0 ? (
        <View style={{flex: 1}}>
          <FlatList
            data={preprocessingAutoFetchedImages(data)}
            renderItem={RenderItem}
            numColumns={5}
            keyExtractor={(item, index) => String(index)}
            ListFooterComponent={() => {
              if (isFetching) {
                return <ActivityIndicator />;
              } else {
                return null;
              }
            }}
            horizontal={false}
            onEndReachedThreshold={0}
            contentContainerStyle={styles.list}
            columnWrapperStyle={styles.column}
          />
          {/* {preprocessingAutoFetchedImages(data).length % 20 === 0 &&
            preprocessingAutoFetchedImages(data).length !== 0 && (
              <button
                style={{
                  marginTop: 10,
                  backgroundColor: 'rgb(64, 156, 255)',
                  width: '20%',
                  border: 'none',
                  padding: 6,
                  color: 'white',
                  borderRadius: 10,
                }}
                onClick={() => {
                  setLoadMore(val => val + 1);
                }}>
                Load More
              </button>
            )} */}
        </View>
      ) : (
        <></>
      )}
    </View>
  );
};

export default AutoFetchLibrary;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    flex: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  column: {
    flexShrink: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  listItemWrapper: {
    width: '22%',
    borderWidth: 2,
    borderRadius: 8,
    borderColor: '#ccc',
    overflow: 'hidden',
    margin: 10,
    padding: 4,
  },
  listItemSelected: {
    borderWidth: 2,
    borderColor: '#2196f3',
  },
  listItemImage: {
    width: '100%',
    minHeight: 150,
  },
  headingContainer: {
    flexDirection: 'row',
  },
  heading: {
    flex: 1,
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '700',
    fontSize: 15,
    lineHeight: 17,
    marginTop: 8,
    marginBottom: 16,
  },
});
