import React, {useEffect, useState, useCallback} from 'react';
import {Pressable, StyleSheet, View, Text} from 'react-native';
import {
  allAvailablePlans,
  deletePageConfig,
  Icon,
  navComponentDelete,
  pluginConfigUpdate,
  selectModuleByUUID,
} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {initApptileIsEditable, initApptileIsPreview} from 'apptile-core';
import {selectScreensInNav, selectSelectedPluginConfig} from '../../selectors/EditorSelectors';
import Button from '../../components-v2/base/Button';
import {changeOnboardingMetadata} from '../../actions/onboardingActions';
import {getOnboardingMetadataWithKey} from '../../selectors/OnboardingSelector';
import {APP_PREVIEW_CLICKED} from '../../common/onboardingConstants';
import {EditorRootState} from '../../store/EditorRootState';
import {useNavigate, useParams} from '../../routing.web';
import {useIsPreview} from 'apptile-core';
import {ScreenConfig} from 'apptile-core';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import Analytics from '@/root/web/lib/segment';
import theme from '../../styles-v2/theme';
import _, {set} from 'lodash';
import {checkApptileEmailSelector, currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {AI_TILE_PAGE_ID} from '../../common/tileConstants';
import {updateModuleDefinition} from '../../actions/editorActions';
import GenAiApi from '../../api/GenAiApi';

export const AiTileTopBar = () => {
  const dispatch = useDispatch();
  const isPreview = useIsPreview();
  const params = useParams();
  const {panelName} = params;

  const pageModel: any = useSelector(state => {
    const appModel: AppModelType = state?.appModel;
    const jsModel = appModel?.jsModel;
    const pageModel = _.find(jsModel, e => e.pageId == AI_TILE_PAGE_ID);
    return pageModel;
  });
  const saveModuleUUID: String = pageModel?.plugins?.tile1?.moduleUUID;
  const moduleName: String = pageModel?.plugins?.tile1?.moduleName;
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const rawCode: String = pageModel?.plugins?.['tile1::webTunnel']?.rawCode;

  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const navigate = useNavigate();
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );
  const toggleIsPreview = (_isPreview: boolean) => {
    dispatch(initApptileIsPreview(_isPreview));
    dispatch(initApptileIsEditable(!_isPreview));
    if (typeof previewClicked !== undefined && !previewClicked) {
      dispatch(changeOnboardingMetadata({[APP_PREVIEW_CLICKED]: true}));
    }
  };

  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isMultiLanguageDisabled = !currentPlanFeatures.includes(allAvailablePlans.PLUS);

  const screens: ScreenConfig[] = useSelector(selectScreensInNav);
  const activeNavigation = useSelector(s => (s as any).activeNavigation);
  const activeScreen = screens?.filter((e: ScreenConfig) => e.screen == activeNavigation?.activePageId);

  //For animating topbar
  const topBarHeight = useSharedValue(50);
  useEffect(() => {
    topBarHeight.value = withTiming(isPreview ? 0 : 50, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, topBarHeight]);
  const topBarTopAnimation = useAnimatedStyle(() => ({
    height: topBarHeight.value,
  }));

  return (
    <Animated.View style={[styles.wrapper, topBarTopAnimation]}>
      <View style={[styles.leftSectionWrapper]}>
        <View style={styles.sidebarHeader}>
          <Pressable
            style={{flex: 1, justifyContent: 'center'}}
            onPress={() => {
              navigate(`../tiles`);
              dispatch(navComponentDelete(['/', AI_TILE_PAGE_ID]));
              dispatch(deletePageConfig(AI_TILE_PAGE_ID));
              dispatch(initApptileIsEditable(true));
              Analytics.track('editor:editor_AiTile_CloseTileDesign');
            }}>
            <Icon iconType="MaterialCommunityIcons" name="close" size={24} color="#000000" style={{marginRight: 4}} />
          </Pressable>
        </View>
        <Text style={styles.pageText}>Tile AI - Studio</Text>
      </View>
      <View>
        {moduleUUID && (
          <Button
            size="SMALL"
            color="CTA"
            variant="FILLED-PILL"
            onPress={async () => {
              if (saveModuleUUID) {
                GenAiApi.setReactCode('module-' + moduleUUID, rawCode.replace(/\{\{/gim, '{ {'));
                dispatch(
                  pluginConfigUpdate('tile1::webTunnel', AI_TILE_PAGE_ID, {
                    rawCode: '',
                  }),
                );
                await new Promise(res => setTimeout(res, 1000));
                if (moduleName == 'AI Tile New') {
                  dispatch(
                    updateModuleDefinition('tile1', AI_TILE_PAGE_ID, saveModuleUUID, {
                      moduleName: moduleName + ' 01',
                    }),
                  );
                } else {
                  dispatch(updateModuleDefinition('tile1', AI_TILE_PAGE_ID, saveModuleUUID));
                }
                Analytics.track('editor:editor_AiTile_SaveTileDesign');
              } else {
                navigate(`../tiles`);
              }
            }}>
            Save Tile Design
          </Button>
        )}
      </View>
      <View
        style={{width: '100%', borderBottomWidth: 1, borderColor: '#E5E5E5', position: 'absolute', bottom: 0, left: 0}}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  separatorLine: {
    width: 1,
    height: 18,
    backgroundColor: '#E5E5E5',
    marginHorizontal: 12,
  },
  wrapper: {
    height: 50,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    overflow: 'hidden',
  },
  leftSectionWrapper: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },
  logo: {
    width: 40,
    height: 40,
  },
  pageText: {
    fontWeight: '500',
    color: '#000',
    fontSize: 20,
    lineHeight: 24,
    marginLeft: 8,
    fontFamily: theme.FONT_FAMILY,
  },
  sidebarHeader: {
    paddingRight: 10,
    overflow: 'hidden',
    borderRightWidth: 1,
    borderColor: '#E5E5E5',
    height: '100%',
  },
  appPopoverScreenText: {
    flexDirection: 'row',
    paddingVertical: 10,
    textAlign: 'left',
  },
  popoverPressableStyles: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appContainerHeaderText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    color: theme.SECONDARY_COLOR,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 16,
    maxWidth: 80,
    // paddingVertical: 8,
  },
  appContainerHeaderPreText: {
    whiteSpace: 'nowrap',
    color: theme.SECONDARY_COLOR,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 14,
    paddingVertical: 8,
  },
  appContainerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    // paddingLeft: 12,
    // paddingRight: 4,
    // height: 38,
    zIndex: 2,
    // width: 131,
  },
  flexRow: {
    paddingHorizontal: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
});
