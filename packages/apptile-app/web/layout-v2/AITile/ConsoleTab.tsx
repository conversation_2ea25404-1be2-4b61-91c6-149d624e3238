import React, {useState, useMemo, useRef, useEffect} from 'react';
import {Pressable, StyleSheet, View, Text, ScrollView} from 'react-native';
import Animated, {useSharedValue, useAnimatedStyle, withTiming, Easing} from 'react-native-reanimated';
import theme from '../../styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import Button from '../../components-v2/base/Button';

type Log = {
  method: string;
  args: any[];
};

interface ConsoleTabProps {
  consoleLogs: Log[];
  addToChat: (message: string) => void;
}

export const ConsoleTab = React.memo(({consoleLogs, addToChat}: ConsoleTabProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState({
    log: true,
    error: true,
    BABELERROR: true,
    warn: false,
  });

  const height = useSharedValue(0);

  useEffect(() => {
    height.value = withTiming(isExpanded ? 300 : 0, {
      duration: 300,
      easing: Easing.bezier(0.4, 0, 0.2, 1),
    });
  }, [isExpanded, height]);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      height: height.value,
    };
  });

  const filteredLogs = useMemo(() => {
    return consoleLogs?.filter(log => filters[log.method as keyof typeof filters]) || [];
  }, [consoleLogs, filters]);

  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (scrollViewRef.current && filteredLogs.length > 0) {
      scrollViewRef.current.scrollToEnd({animated: true});
    }
  }, [filteredLogs]);

  const toggleFilter = (type: keyof typeof filters) => {
    setFilters(prev => {
      const newValue = {...prev, [type]: !prev[type]};
      if (type == 'error') newValue.BABELERROR = newValue.error;
      return newValue;
    });
  };
  const [hoveredLogIndex, setHoveredLogIndex] = useState<number | null>(null);

  const getLogStyle = (method: string) => {
    switch (method) {
      case 'error':
        return styles.errorLog;
      case 'BABELERROR':
        return styles.errorLog;
      case 'warn':
        return styles.warnLog;
      default:
        return styles.normalLog;
    }
  };

  return (
    <View style={styles.container}>
      <Pressable onPress={() => setIsExpanded(!isExpanded)}>
        <View style={styles.trigger}>
          <View style={styles.filterContainer}>
            <Text style={styles.triggerText}>Console</Text>
            <MaterialCommunityIcons color={'#fff'} name={isExpanded ? 'chevron-down' : 'chevron-up'} size={22} />
          </View>
          <View style={styles.filterContainer}>
            <Pressable onPress={() => toggleFilter('log')}>
              <View style={[styles.filterButton, filters.log && styles.filterActive]}>
                <Text style={styles.filterText}>Log</Text>
              </View>
            </Pressable>
            <Pressable onPress={() => toggleFilter('error')}>
              <View style={[styles.filterButton, filters.error && styles.filterActive]}>
                <Text style={styles.filterText}>Error</Text>
              </View>
            </Pressable>
            <Pressable onPress={() => toggleFilter('warn')}>
              <View style={[styles.filterButton, filters.warn && styles.filterActive]}>
                <Text style={styles.filterText}>Warning</Text>
              </View>
            </Pressable>
          </View>
        </View>
      </Pressable>
      {isExpanded && (
        <View style={[styles.consoleContainer, {height: 300}]}>
          <ScrollView ref={scrollViewRef} style={styles.scrollContainer}>
            {/* Hover state managed in parent for web */}
            {filteredLogs.map((log, index) => (
              <div
                key={`log-${index}`}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  ...styles.logItem,
                  ...getLogStyle(log.method),
                  position: 'relative',
                }}
                onMouseEnter={() => setHoveredLogIndex(index)}
                onMouseLeave={() => setHoveredLogIndex(null)}>
                <span style={{...styles.logText, flexGrow: 1}}>
                  {log.args
                    ?.map(arg =>
                      arg?.stack ? arg?.message : typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg),
                    )
                    .join(' ')}
                </span>
                {hoveredLogIndex === index && (
                  <Button
                    onPress={() => {
                      addToChat(
                        log.args
                          ?.map(arg =>
                            arg?.stack
                              ? arg?.message
                              : typeof arg === 'object'
                              ? JSON.stringify(arg, null, 2)
                              : String(arg),
                          )
                          .join(' '),
                      );
                    }}
                    size="EXTRA-SMALL"
                    variant="TEXT"
                    color={log.method == 'log' ? 'PRIMARY' : 'ERROR'}>
                    Add To Chat
                  </Button>
                )}
              </div>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#242424',
    borderTopWidth: 1,
    borderTopColor: '#454545',
  },
  trigger: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: '#2d2d2d',
  },
  triggerText: {
    color: '#ffffff',
    fontSize: theme.FONT_SIZE,
    fontFamily: theme.FONT_FAMILY,
  },
  filterContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 4,
    backgroundColor: '#363636',
  },
  filterActive: {
    backgroundColor: '#6a6a6a',
  },
  filterText: {
    color: '#ffffff',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
  },
  consoleContainer: {
    overflow: 'hidden',
  },
  scrollContainer: {
    flex: 1,
    height: '100%',
  },
  contentContainer: {
    padding: 8,
    flexGrow: 1,
  },
  logItem: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#363636',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    background: 'none',
  },
  normalLog: {
    backgroundColor: 'transparent',
  },
  errorLog: {
    backgroundColor: 'rgba(255, 0, 0, 0.16)',
  },
  warnLog: {
    backgroundColor: 'rgba(255, 190, 0, 0.25)',
  },
  logText: {
    color: '#ffffff',
    fontSize: theme.FONT_SIZE,
    fontFamily: theme.FONT_FAMILY,
    flexGrow: 1,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
  },
});
