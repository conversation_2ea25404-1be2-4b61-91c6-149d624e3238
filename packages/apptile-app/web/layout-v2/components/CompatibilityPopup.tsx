import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { MaterialCommunityIcons } from 'apptile-core';

interface CompatibilityPopupProps {
    onClose: () => void;
}

const CompatibilityPopup = ({ onClose }: CompatibilityPopupProps) => {
    const handleLinkPress = () => {
        Linking.openURL('https://help.apptile.com/en/articles/11136778-platform-compatibility-guide');
    };

    return (
        <View style={styles.container}>
            <View style={styles.leftBorder} />
            <View style={styles.contentContainer}>
                <View style={styles.headerContainer}>
                    <View style={styles.iconContainer}>
                        <MaterialCommunityIcons name="alert" size={18} color="black" />
                    </View>
                    <Text style={styles.title}>Compatibility issue detected</Text>
                </View>
                <Text style={styles.description}>
                    Your changes may not be saved. Please update your browser or switch to a supported device.
                    Find list of supported browsers and devices{' '}
                    <Text style={styles.link} onPress={handleLinkPress}>
                        here
                    </Text>.
                </Text>
                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                >
                    <MaterialCommunityIcons name="close" size={16} color="#535353" />
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 346,
        position: 'fixed',
        top: 20,
        right: 20,
        backgroundColor: '#FDF1D5',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: -5, height: 5 },
        shadowOpacity: 0.1,
        shadowRadius: 40,
        elevation: 5,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        overflow: 'hidden',
        zIndex: 9999,
    },
    contentContainer: {
        padding: 16,
        paddingLeft: 20,
        paddingRight: 16,
    },
    leftBorder: {
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: 6,
        backgroundColor: '#F3B947',
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    iconContainer: {
        width: 24,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
    },
    title: {
        color: 'black',
        fontSize: 14,
        fontFamily: 'Work Sans',
        fontWeight: '600',
        lineHeight: 16,
    },
    description: {
        color: '#535353',
        fontSize: 12,
        fontFamily: 'Work Sans',
        fontWeight: '400',
        lineHeight: 16,
        width: 303,
    },
    link: {
        textDecorationLine: 'underline',
        color: '#535353',
    },
    closeButton: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 16,
        height: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default CompatibilityPopup;