import ApptileApp from '@/root/app/ApptileApp';
import {DispatchActions, getAppConstants, selectAppConfig} from 'apptile-core';
import React, {useEffect, useState} from 'react';
import {Dimensions, View, StyleSheet, ActivityIndicator} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {verifyAppForks} from '../../actions/editorActions';
import {selectCurrentApptileForkId} from '../../selectors/AppSelectors';
import {EditorRootState} from '../../store/EditorRootState';
import {apptileNavigationRef} from 'apptile-core/views/navigation/ApptileRootNavigationHelpers';

const PREVIEW_HEIGHT = 1028;

const AppClip: React.FC<any> = (props: any) => {
  const [viewHeight, setViewHeight] = useState(Dimensions.get('window').height);
  const dispatch = useDispatch();
  const appId = getAppConstants().APPTILE_APP_ID;
  const currentApptileForkId = useSelector(selectCurrentApptileForkId);
  const [loader, setLoader] = useState(true);
  useEffect(() => {
    dispatch(verifyAppForks(appId));
  }, [appId, dispatch]);
  const activePageKey = useSelector((state: EditorRootState) => state?.activeNavigation?.activePageKey);

  const model = useSelector((state: EditorRootState) => state?.appModel);
  const appConfig = useSelector(selectAppConfig);
  console.log('apptileNavigation', activePageKey);
  useEffect(() => {
    const originalNavigate = apptileNavigationRef.navigate;
    apptileNavigationRef.navigate = (routeName: string, params?: any) => {
      if (routeName === 'ShopifyWebCheckout') {
        const checkoutUrl = params?.checkoutUrl || params?.webUrl || '';
        if (window.parent) {
          window?.webkit.messageHandlers.checkoutHandler.postMessage({
            type: 'OpenCheckout',
            url: checkoutUrl,
          });
        }
        return;
      }
      if (routeName === 'ShopifyOtpLogin') {
        toast.show('Login not available in appclip', {
          type: 'normal',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 20},
        });
        return;
      }
      return originalNavigate.call(apptileNavigationRef, routeName, params);
    };
    return () => {
      apptileNavigationRef.navigate = originalNavigate;
    };
  }, []);

  useEffect(() => {
    if (activePageKey && props?.screenName && loader) {
      dispatch({
        type: DispatchActions.APPNAV_NAVIGATE,
        payload: {
          screenName: props.screenName,
          params: {...props.params},
        },
      });
      setTimeout(() => setLoader(false), 70);
    } else if (activePageKey && loader) {
      setTimeout(() => setLoader(false), 70);
    }
  }, [activePageKey]);

  useEffect(() => {
    async function handleMessage(event: {data: {type: any}}) {
      if (!event.data) return;
      try {
        const {type} = event.data;
        if (type === 'OrderSuccess') {
          const checkoutActions = await import('../../../app/plugins/datasource/ShopifyV_22_10/actions/checkoutAction');
          console.log('appConfig', appConfig);
          const pluginConfig = appConfig?.getPlugin('shopify');
          await checkoutActions.default.clearLocalCartState(dispatch, pluginConfig, model, ['shopify'], {
            navigateToSuccessScreen: true,
            skipAnalyticsPurchaseEvent: false,
          });
        } else if (type === 'OrderCancelled') {
          dispatch({
            type: DispatchActions.APPNAV_NAVIGATE,
            payload: {
              screenName: 'Cart',
              params: {},
            },
          });
        }
      } catch (e) {
        console.error('Message error:', e);
      }
    }
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [appConfig, dispatch, model]);

  return (
    <>
      {currentApptileForkId ? (
        <View style={[styles.previewContainer, {height: viewHeight, flexBasis: viewHeight, maxHeight: viewHeight}]}>
          <View style={[styles.appCanvas, {height: viewHeight - 1}]}>
            <ApptileApp />
          </View>
        </View>
      ) : (
        <></>
      )}
      {loader && (
        <View
          style={{
            flex: 1,
            position: 'fixed',
            height: '100vh',
            width: '100vw',
            zIndex: 999999999999,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#fff',
          }}>
          <ActivityIndicator size="large" />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    height: '100%',
    maxHeight: '100%',
    flex: 1,
    position: 'relative',
  },
  deviceBezel: {
    backgroundColor: 'transparent',
    // shadowColor: 'black',
    // shadowOffset: {width: 10, height: 10},
    // shadowRadius: 50,
    // shadowOpacity: 0.4,
    overflow: 'visible',
  },
  appContainer: {
    width: 950,
    height: PREVIEW_HEIGHT,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    // alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    position: 'absolute',
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
  },
});

export default AppClip;
