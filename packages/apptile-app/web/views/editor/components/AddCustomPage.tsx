import React, {useEffect, useState, useCallback} from 'react';
import {StyleSheet, View} from 'react-native';

import CodeInputControl from '@/root/web/components/controls/CodeInputControl';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useDispatch} from 'react-redux';
import {DispatchActions} from 'apptile-core';
import {addNewPageInNavigation} from '@/root/web/actions/editorActions';
import Button from '../../../components-v2/base/Button';

type AddCustomPageProps = {showAddPageDialog: boolean};
const AddCustomPage: React.FC<AddCustomPageProps> = ({showAddPageDialog}) => {
  const [newScreenTitle, setNewScreenTitle] = useState('');
  const [showNewScreenContainer, setShowNewScreenContainer] = useState(showAddPageDialog ?? false);
  useEffect(() => {
    setShowNewScreenContainer(showAddPageDialog);
  }, [showAddPageDialog]);
  const slideAnimationHeight = useSharedValue(0);
  const dispatch = useDispatch();
  useEffect(() => {
    slideAnimationHeight.value = withTiming(showNewScreenContainer ? 140 : 0, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [showNewScreenContainer, slideAnimationHeight]);
  const slideAnimation = useAnimatedStyle(() => ({
    height: slideAnimationHeight.value,
  }));
  const onAddPage = useCallback(() => {
    const screenTitle = newScreenTitle?.trim();
    if (screenTitle) {
      const screenId = 'Custom_' + screenTitle.replace(/[^a-z0-9]/gim, '_');
      dispatch({
        type: DispatchActions.ADD_NEW_PAGE,
        payload: {pageId: screenId},
      });
      dispatch({
        type: DispatchActions.ADD_NAVIGATION_PAGE,
        payload: {
          navSelector: ['/'],
          screenName: screenId,
          screenConfig: {title: screenTitle, screen: screenId, showTitleBar: true},
        },
      });
      setNewScreenTitle('');
      setShowNewScreenContainer(false);
      dispatch(addNewPageInNavigation(screenId));
    }
  }, [dispatch, newScreenTitle]);

  return (
    <View>
      <Animated.View
        style={[
          styles.newPageContainer,
          {
            borderWidth: showNewScreenContainer ? 1 : 0,
            paddingTop: showNewScreenContainer ? 12 : 0,
            paddingHorizontal: showNewScreenContainer ? 16 : 0,
            marginTop: showNewScreenContainer ? 16 : 0,
            marginBottom: showNewScreenContainer ? 12 : 0,
          },
          showNewScreenContainer && {flex: 1},
          slideAnimation,
        ]}>
        <CodeInputControl
          value={newScreenTitle}
          placeholder="New Screen Name"
          onChange={function (value: string): void {
            setNewScreenTitle(value);
            setNewScreenTitle(value.replace(/[^a-z0-9 ]/gim, ''));
          }}
          singleLine={true}
        />

        <View style={styles.dialogFooter}>
          <Button containerStyles={styles.buttonStyles} variant="FILLED-PILL" size="MEDIUM" onPress={onAddPage}>
            Add
          </Button>
          <Button
            containerStyles={styles.buttonStyles}
            variant="PILL"
            color="SECONDARY"
            size="MEDIUM"
            onPress={() => {
              setShowNewScreenContainer(false);
            }}>
            Cancel
          </Button>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  newPageContainer: {
    overflow: 'hidden',
    borderColor: '#bfbfbf',
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
  },
  buttonStyles: {
    width: 80,
  },
  dialogFooter: {
    flex: 1,
    flexBasis: 0,
    flexDirection: 'row',
    height: 54,
    minHeight: 54,
    maxHeight: 54,
    width: '100%',
    padding: 4,
    paddingBottom: 14,
    flexGrow: 1,
    flexShrink: 0,
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
});

export default AddCustomPage;
