import React, {useEffect, useState, useCallback} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useDispatch, useSelector} from 'react-redux';
import Button from '../../../components-v2/base/Button';
import theme from '../../../styles-v2/theme';
import {deletePageConfig, navComponentDelete} from 'apptile-core';
import {selectNavComponentSelector} from '../../../selectors/EditorSelectors';
import {EditorRootState} from '../../../store/EditorRootState';

type DeletePageProps = {showDeletePageDialog: boolean; screen: any; selector: [string]};
const DeletePageDialog: React.FC<DeletePageProps> = ({showDeletePageDialog, screen, selector}) => {
  //To trigger showDeleteDialog
  const [showDeletePageContainer, setShowDeletePageContainer] = useState(showDeletePageDialog ?? false);
  useEffect(() => {
    setShowDeletePageContainer(showDeletePageDialog);
  }, [showDeletePageDialog]);

  //animation code
  const slideAnimationHeight = useSharedValue(0);
  const dispatch = useDispatch();
  useEffect(() => {
    slideAnimationHeight.value = withTiming(showDeletePageContainer ? 140 : 0, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [showDeletePageContainer, slideAnimationHeight]);
  const slideAnimation = useAnimatedStyle(() => ({
    height: slideAnimationHeight.value,
  }));

  //Ondelete functionality
  const onDeletePage = useCallback(() => {
    dispatch(deletePageConfig(screen.name));
    dispatch(navComponentDelete(selector));
    setShowDeletePageContainer(false);
  }, [dispatch, screen.name, selector]);

  return (
    <View>
      <Animated.View
        style={[
          styles.deletePageContainer,
          {
            borderWidth: showDeletePageContainer ? 1 : 0,
            paddingTop: showDeletePageContainer ? 12 : 0,
            paddingHorizontal: showDeletePageContainer ? 16 : 0,
            marginTop: showDeletePageContainer ? 16 : 0,
            marginBottom: showDeletePageContainer ? 12 : 0,
          },
          showDeletePageContainer && {flex: 1},
          slideAnimation,
        ]}>
        <Text style={styles.deleteTitleText}>Are you sure you want to delete {screen.name} page?</Text>
        <View style={styles.dialogFooter}>
          <Button containerStyles={styles.buttonStyles} variant="FILLED-PILL" size="MEDIUM" onPress={onDeletePage}>
            Delete
          </Button>
          <Button
            containerStyles={styles.buttonStyles}
            variant="PILL"
            color="SECONDARY"
            size="MEDIUM"
            onPress={() => {
              setShowDeletePageContainer(false);
            }}>
            Cancel
          </Button>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  deletePageContainer: {
    overflow: 'hidden',
    borderColor: '#bfbfbf',
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
    height: 300,
    marginVertical: 10,
  },
  deleteTitleText: {
    fontWeight: '400',
    fontFamily: theme.FONT_FAMILY,
    color: '#A3A3A3',
    textAlign: 'center',
  },
  buttonStyles: {
    width: 80,
  },
  dialogFooter: {
    flex: 1,
    flexBasis: 0,
    flexDirection: 'row',
    height: 54,
    minHeight: 54,
    maxHeight: 54,
    width: '100%',
    padding: 4,
    paddingBottom: 14,
    flexGrow: 1,
    flexShrink: 0,
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
});

export default DeletePageDialog;
