import React, {useEffect} from 'react';
import {StyleSheet} from 'react-native';
import _ from 'lodash';
import Button from '../../components-v2/base/Button';
import {BuildManagerApi} from '../../api/BuildApi';
import {useParams} from 'react-router';
import {selectScreensInNav} from '../../selectors/EditorSelectors';
import {useDispatch, useSelector} from 'react-redux';
import {selectPagePlugins} from '../../selectors/PluginSelectors';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {
  EventHandlerConfig,
  LayoutRecord,
  PluginConfig,
  pluginConfigSetPathValue,
  pluginConfigUpdatePath,
} from 'apptile-core';
import {List} from 'immutable';
import Immutable from 'immutable';
import {addPlugin, DispatchActions} from '../../../../apptile-core/actions/DispatchActions';
import {toggleNotificationCenter, fetchPage} from '../../actions/editorActions';

const NotificationCenter: React.FC = props => {
  const dispatch = useDispatch();
  const pageId = 'NotificationCenter';
  let appConfig = useSelector((state: EditorRootState) => state.appConfig.current);
  const isNotificationCenterEnabled = appConfig?.pages && appConfig?.pages.get(pageId) ? true : false;
  const addForceUpdate = () => {
    if (isNotificationCenterEnabled) {
      dispatch(toggleNotificationCenter(false));
    } else {
      dispatch(toggleNotificationCenter(true));
    }
  };
  return (
    <Button color={'CTA'} onPress={addForceUpdate}>
      {isNotificationCenterEnabled ? 'Remove Notification Center' : 'Add Notification Center'}
    </Button>
  );
};

const styles = StyleSheet.create({});
export default NotificationCenter;
