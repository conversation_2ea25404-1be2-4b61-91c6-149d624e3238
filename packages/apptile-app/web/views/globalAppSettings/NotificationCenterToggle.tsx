import React from 'react';
import CheckboxControl from '../../components/controls/CheckboxControl';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {toggleNotificationCenter} from '../../actions/editorActions';

const NotificationCenterToggle: React.FC = () => {
  const dispatch = useDispatch();
  const pageId = 'NotificationCenter';
  // Use 'as any' to bypass TS error for 'pages', or update type if you know the correct one
  const appConfig = useSelector((state: EditorRootState) => state.appConfig.current) as any;
  const isNotificationCenterEnabled = appConfig?.pages && appConfig.pages.get(pageId) ? true : false;
  const handleToggle = () => {
    dispatch(toggleNotificationCenter(!isNotificationCenterEnabled));
  };
  return (
    <CheckboxControl
      value={isNotificationCenterEnabled}
      label={isNotificationCenterEnabled ? 'Remove Notification Center' : 'Add Notification Center'}
      onChange={handleToggle}
    />
  );
};

export default NotificationCenterToggle;
