import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import {useNavigate} from '../../routing.web';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import TextElement from '../../components-v2/base/TextElement';
import {MaterialCommunityIcons} from 'apptile-core';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import Button from '../../components-v2/base/Button';

export const AppEmbedNotEnabledModal: React.FC<{
  themeId: string;
  shop: string;
  closeModal: () => void;
}> = ({themeId, shop, closeModal}) => {
  const navigate = useNavigate();
  const [showPopover, setShowPopover] = useState(false);

  return (
    <View style={styles.container}>
      <View style={{justifyContent: 'center', alignItems: 'center', backgroundColor: '#1060E00D', flex: 1}}>
        <Image source={require('./assets/shopifyTopBar.png')} style={{width: 160, height: 53}} />
      </View>
      <View
        style={{
          borderRadius: 8,
          paddingVertical: 70,
          paddingHorizontal: 15,
          width: '70%',
          gap: 18,
          alignItems: 'flex-start',
        }}>
        <Pressable style={[{position: 'absolute', top: 20, right: 20}, commonStyles.heading]} onPress={closeModal}>
          X
        </Pressable>
        <Text style={[commonStyles.heading, {fontSize: 22, lineHeight: 28}]}>
          Before your Mobile Install Banner can be published...
        </Text>
        <Text style={[commonStyles.baseText, {fontSize: 16, lineHeight: 18, color: '#000'}]}>
          You must enable the Apptile Smart Banner embed on Shopify. Remember to save it once it is enabled.
        </Text>
        <Button
          color="CTA"
          onPress={() => {
            window.open(`https://admin.shopify.com/store/${shop}/themes/${themeId}/editor?context=apps`);
          }}>
          Enable Smart Banner <MaterialCommunityIcons name="open-in-new" />
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 0,
    flexDirection: 'row',
    width: 700,
    borderRadius: 8,
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  bottomContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  cardHeading: {
    marginTop: 18,
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#F7F7F7',
    paddingVertical: 24,
  },
  formArea: {
    gap: 22,
    paddingTop: 15,
  },
  formGroup: {
    gap: 22,
  },
  formField: {
    gap: 6,
  },
  formGroupWithBorder: {
    gap: 22,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
});
