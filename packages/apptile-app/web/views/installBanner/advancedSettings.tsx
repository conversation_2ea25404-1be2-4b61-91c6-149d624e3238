import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';
import {useNavigate} from '../../routing.web';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import CollapsiblePanel from '../../components/CollapsiblePanel';
import EditorSectionHeader from '../../components/controls/EditorSectionHeader';
import DropDownControl from '../../components/controls/DropDownControl';
import {BannerConfig, defaultTemplate} from '.';
import {MaterialCommunityIcons} from 'apptile-core';

export const AdvancedSettings: React.FC<{
  configLoading: boolean;
  bannerConfig: BannerConfig;
  updateConfig: (path: string[], value: any) => void;
  type: 'desktop' | 'mobile';
}> = ({configLoading, bannerConfig, updateConfig, type}) => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<{[key: string]: string} | null>(null);
  const [collapsibleOpen, setCollapsibleOpen] = useState<boolean>(false);
  const [displayType, setDisplayType] = useState<number>(60);

  useEffect(() => {
    if (bannerConfig) setConfig(bannerConfig);
  }, [bannerConfig]);
  return configLoading ? (
    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
  ) : (
    <View style={styles.formArea}>
      <CollapsiblePanel
        isOpen={collapsibleOpen}
        setOpen={(open: boolean) => {
          setCollapsibleOpen(open);
        }}
        backgroundStyle={{borderWidth: 0}}
        title={'Advanced Settings'}
        customHeader={
          <EditorSectionHeader
            label={'Advanced Settings'}
            name={''}
            textStyles={{fontSize: 15, fontWeight: '500'}}
            icon={collapsibleOpen ? 'chevron-up' : 'chevron-down'}
            iconSize={18}
            iconType={'Material Icon'}
          />
        }>
        <View style={{marginTop: 15, marginBottom: 22}}>
          <View style={{gap: 6, marginBottom: 22}}>
            <Text style={[commonStyles.heading, styles.headingText]}>Placement on website pages</Text>
            <Pressable
              onPress={() => {
                updateConfig(['displaySettings', 'showOnAllPages'], true);
              }}
              style={[
                commonStyles.input,
                {flexDirection: 'row', paddingHorizontal: 10, paddingVertical: 8, gap: 6, alignItems: 'center'},
              ]}>
              <View style={{borderWidth: 2, borderColor: '#000', borderRadius: 20, padding: 2}}>
                <View
                  style={[
                    {padding: 4, borderRadius: 50},
                    bannerConfig.displaySettings.showOnAllPages && {backgroundColor: '#000'},
                  ]}
                />
              </View>
              <Text
                style={[
                  commonStyles.inputText,
                  styles.baseText,
                  bannerConfig.displaySettings.showOnAllPages && {color: '#000'},
                ]}>
                All Pages
              </Text>
            </Pressable>
            <Pressable
              onPress={() => {
                updateConfig(['displaySettings', 'showOnAllPages'], false);
              }}
              style={[
                commonStyles.input,
                {flexDirection: 'row', paddingHorizontal: 10, paddingVertical: 8, gap: 6, alignItems: 'center'},
              ]}>
              <View style={{borderWidth: 2, borderColor: '#000', borderRadius: 20, padding: 2}}>
                <View
                  style={[
                    {padding: 4, borderRadius: 50},
                    !bannerConfig.displaySettings.showOnAllPages && {backgroundColor: '#000'},
                  ]}
                />
              </View>
              <Text
                style={[
                  commonStyles.inputText,
                  styles.baseText,
                  !bannerConfig.displaySettings.showOnAllPages && {color: '#000'},
                ]}>
                Specific Pages
              </Text>
            </Pressable>
          </View>
          {!bannerConfig?.displaySettings?.showOnAllPages && (
            <View style={{gap: 6, alignItems: 'flex-start', flex: 1}}>
              <Text style={[commonStyles.heading, styles.headingText]}>
                Choose where to display the banner by specifying website URLs
              </Text>
              {bannerConfig?.displaySettings?.specificPages?.map((e, i) => {
                const type =
                  e.type == 'urlStartsWith' && e.value == '/collections/'
                    ? 'collectionUrl'
                    : e.type == 'urlContains' && e.value == '/products/'
                    ? 'productUrl'
                    : e.type;
                return (
                  <View style={{flexDirection: 'row', width: '100%', gap: 12, marginBottom: 15}}>
                    <View style={{minWidth: 200}} key={`pagesType-${type}-${i}`}>
                      <DropDownControl
                        options={[
                          {
                            name: 'Exact URL match',
                            value: 'exactMatch',
                          },
                          {
                            name: 'URL starts with',
                            value: 'urlStartsWith',
                          },
                          {
                            name: 'URL ends with',
                            value: 'urlEndsWith',
                          },
                          {
                            name: 'URL contains',
                            value: 'urlContains',
                          },
                          {
                            name: 'Collection Pages',
                            value: 'collectionUrl',
                          },
                          {
                            name: 'Product Pages',
                            value: 'productUrl',
                          },
                        ]}
                        nameKey="name"
                        valueKey="value"
                        defaultValue="exactMatch"
                        onChange={function (value: string): void {
                          if (value == 'collectionUrl') {
                            updateConfig(['displaySettings', 'specificPages', i], {
                              type: 'urlStartsWith',
                              value: '/collections/',
                            });
                          } else if (value == 'productUrl') {
                            updateConfig(['displaySettings', 'specificPages', i], {
                              type: 'urlContains',
                              value: '/products/',
                            });
                          } else {
                            updateConfig(['displaySettings', 'specificPages', i, 'type'], value);
                          }
                        }}
                        value={type}
                      />
                    </View>
                    <View style={{minWidth: 240, flexGrow: 1}} key={`pages-${type}-${i}`}>
                      <CodeInputControlV2
                        singleLine
                        value={e.value}
                        // disabled={type == 'collectionUrl' || type == 'productUrl'}
                        placeholder={'/pages/contact'}
                        onChange={function (value: string): void {
                          if (value?.toLowerCase()?.startsWith('http')) {
                            value = value.slice(
                              value.indexOf('/', 8),
                              value.indexOf('?') > -1 ? value.indexOf('?') : value.length,
                            );
                          }
                          updateConfig(['displaySettings', 'specificPages', i, 'value'], value);
                        }}
                      />
                      <MaterialCommunityIcons
                        name={'trash-can-outline'}
                        style={{position: 'absolute', right: 8, top: 11}}
                        size={19}
                        color={'rgba(255,76,40,1)'}
                        onPress={() => {
                          bannerConfig?.displaySettings?.specificPages.splice(i, 1);
                          updateConfig(
                            ['displaySettings', 'specificPages'],
                            [...bannerConfig?.displaySettings?.specificPages],
                          );
                        }}
                      />
                    </View>
                  </View>
                );
              })}
              <Button
                color="PRIMARY"
                variant="PILL"
                onPress={() => {
                  updateConfig(
                    ['displaySettings', 'specificPages', bannerConfig?.displaySettings?.specificPages?.length],
                    {type: 'exactMatch', value: ''},
                  );
                }}>
                Add URL
              </Button>
            </View>
          )}
        </View>
        <View style={{gap: 6, marginBottom: 22}}>
          <Text style={[commonStyles.heading, styles.headingText]}>Trigger Delay</Text>
          <Text style={[commonStyles.baseText]}>
            Select how quickly the banner should be shown to users upon visiting your site
          </Text>
          <DropDownControl
            options={[
              {
                name: 'Immediately',
                value: '0',
              },
              {
                name: 'After 5 Seconds',
                value: '5',
              },
              {
                name: 'After 10 Seconds',
                value: '10',
              },
              {
                name: 'After 15 Seconds',
                value: '15',
              },
              {
                name: 'After 30 Seconds',
                value: '30',
              },
              {
                name: 'After 1 Minute',
                value: '60',
              },
            ]}
            nameKey="name"
            valueKey="value"
            defaultValue="0"
            onChange={function (value: string): void {
              updateConfig(['displaySettings', 'delay'], parseInt(value, 10));
            }}
            value={`${bannerConfig?.displaySettings?.delay}`}
          />
        </View>
        <View style={{flexDirection: 'row', gap: 12}}>
          <View style={{gap: 6}}>
            <Text style={[commonStyles.heading, styles.headingText]}>Display Frequency</Text>
            <Text style={[commonStyles.baseText]}>Choose how often the banner will display once dismissed</Text>
            <View style={{flexDirection: 'row', width: '100%', gap: 12, marginBottom: 15}}>
              <View style={{minWidth: 140, flexGrow: 1}}>
                <CodeInputControlV2
                  singleLine
                  value={
                    bannerConfig?.displaySettings?.displayFrequency >= 3600
                      ? bannerConfig?.displaySettings?.displayFrequency / 3600
                      : bannerConfig?.displaySettings?.displayFrequency / 60
                  }
                  onChange={function (value: string): void {
                    updateConfig(['displaySettings', 'displayFrequency'], displayType * parseInt(value, 10));
                  }}
                />
              </View>
              <View style={{minWidth: 200}}>
                <DropDownControl
                  options={[
                    {
                      name: 'Hours',
                      value: '3600',
                    },
                    {
                      name: 'Minutes',
                      value: '60',
                    },
                  ]}
                  nameKey="name"
                  valueKey="value"
                  defaultValue="hours"
                  onChange={function (value: string): void {
                    const base = bannerConfig?.displaySettings?.displayFrequency / displayType;
                    updateConfig(['displaySettings', 'displayFrequency'], base * parseInt(value, 10));
                    setDisplayType(parseInt(value, 10));
                  }}
                  value={`${displayType}`}
                />
              </View>
            </View>
          </View>
        </View>
        <View style={{marginBottom: 22}}>
          <Pressable
            onPress={() => {
              updateConfig(['displaySettings'], defaultTemplate.displaySettings);
            }}>
            <Text style={[commonStyles.baseText, {color: '#000', textDecoration: 'underline'}]}>
              Restore default settings
            </Text>
          </Pressable>
        </View>
      </CollapsiblePanel>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    flex: 1,
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  bottomContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  cardHeading: {
    marginTop: 18,
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#F7F7F7',
    paddingVertical: 24,
  },
  formArea: {
    flex: 1,
    gap: 22,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  playArea: {
    backgroundColor: '#F3F3F3',
    width: 280,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogHeading: {
    flexDirection: 'column',
    flexBasis: 'auto',
    paddingVertical: 0,
    paddingHorizontal: 6,
    paddingBottom: 8,
  },
  dialogTop: {
    height: 340,
    borderRadius: 8,
    position: 'relative',
    paddingHorizontal: 11,
    paddingTop: 10,
    backgroundColor: 'rgb(221, 221, 221)',
    color: 'rgb(34, 34, 34)',
  },
  desktopHeadling: {
    fontFamily: 'sans-serif',
    fontWeight: '200',
    fontSize: 11,
    textAlign: 'center',
  },
  desktopHeadline: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 21,
    marginVertical: 12,
    marginHorizontal: 0,
  },
  desktopHeadlineText: {
    fontFamily: 'sans-serif',
    fontWeight: '200',
    fontSize: 11,
    textAlign: 'center',
  },
  qrCode: {
    marginTop: 20,
    paddingTop: 15,
    justifyContent: 'center',
  },
  dialogBottom: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 60,
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
  },
  desktopButtonText: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 12,
    lineHeight: 18,
    textAlign: 'center',
    width: '100%',
  },
  crossIcon: {
    minWidth: 20,
    minHeight: 20,
    paddingVertical: 2,
    paddingHorizontal: 5,
    position: 'absolute',
    right: 10,
    top: 10,
  },
});
