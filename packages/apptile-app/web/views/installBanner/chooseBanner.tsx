import React from 'react';
import {StyleSheet, Image, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';
import Icon from '../../components-v2/base/Icon';
import {useNavigate} from '../../routing.web';
import Analytics from '@/root/web/lib/segment';

export const ChooseBanner: React.FC<{configLoading: boolean}> = ({configLoading}) => {
  const navigate = useNavigate();
  return configLoading ? (
    <View style={{flex: 1, height: '100%', gap: 6, justifyContent: 'center', alignItems: 'center'}}>
      <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      <Text style={[commonStyles.heading, {fontSize: 19, lineHeight: 20}]}>Intialising App Install Banner</Text>
    </View>
  ) : (
    <View style={styles.container}>
      <View style={{flexDirection: 'row'}}>
        <Text style={[commonStyles.baseText, styles.baseText]}>Integrations / </Text>
        <Text style={[commonStyles.heading, styles.headingText]}>App Install Banner</Text>
      </View>
      <View style={styles.banner}>
        <View style={{gap: 8}}>
          <Text style={[commonStyles.heading, {fontSize: 22, lineHeight: 20}]}>App Install Banner</Text>
          <Text style={[commonStyles.baseText, styles.baseText]}>
            Redirect your users to your mobile app by adding an app install banner on your website.
          </Text>
        </View>
        <Button color="ERROR" inversed border={false}>
          Disconnect
        </Button>
      </View>
      <View style={{backgroundColor: '#fff', borderRadius: 10}}>
        <View style={styles.bottomContainerHeader}>
          <Text style={[commonStyles.heading, {fontSize: 16, lineHeight: 18}]}>Choose a banner</Text>
        </View>
        <View style={styles.bottomContainer}>
          <Pressable
            style={styles.cardContainer}
            onPress={() => {
              Analytics.track(`editor:appInstallBanner_openMobileInstallBanner`);
              navigate('./mobile/');
            }}>
            <Icon name={'phone-portrait-outline'} iconType="Ionicons" size="3xl" color="SECONDARY" />
            <Text style={[commonStyles.heading, styles.cardHeading]}>Mobile Install Banner</Text>
            <Text style={[commonStyles.baseText]}>To redirect mobile users to your app</Text>
          </Pressable>
          <Pressable
            style={styles.cardContainer}
            onPress={() => {
              Analytics.track(`editor:appInstallBanner_opeDesktopInstallBanner`);
              navigate('./desktop/');
            }}>
            <Icon name={'iconfontdesktop'} iconType="AntDesign" size="3xl" color="SECONDARY" />
            <Text style={[commonStyles.heading, styles.cardHeading]}>Desktop Install Widget</Text>
            <Text style={[commonStyles.baseText]}>To redirect web users to your app</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    flex: 1,
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  bottomContainer: {
    paddingVertical: 33,
    paddingHorizontal: 24,
    gap: 24,
    justifyContent: 'center',
    flexDirection: 'row',
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  cardHeading: {
    marginTop: 18,
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#F7F7F7',
    paddingVertical: 24,
  },
});
