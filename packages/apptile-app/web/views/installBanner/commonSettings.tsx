import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, Pressable, ImageSourcePropType} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import {useNavigate} from '../../routing.web';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import TextElement from '../../components-v2/base/TextElement';
import {MaterialCommunityIcons} from 'apptile-core';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import {BannerConfig} from '.';
import Button from '../../components-v2/base/Button';
import Icon from '../../components-v2/base/Icon';
import Tooltip from '../../components-v2/base/Tooltip/Index';

export const CommonSettings: React.FC<{
  configLoading: boolean;
  bannerConfig: BannerConfig;
  updateConfig: (path: string[], value: any) => void;
  type: 'desktop' | 'mobile';
  embedEnabled: boolean;
  shop: string;
  themeId: string;
  checkThemeBlock: () => void;
}> = ({configLoading, bannerConfig, updateConfig, type, shop, themeId, embedEnabled, checkThemeBlock}) => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<{[key: string]: string} | null>(null);
  const [showPopover, setShowPopover] = useState(false);

  const onLogoUpdate = (value: string) => {
    updateConfig(['appSettings', 'logoUrl'], value);
  };

  useEffect(() => {
    if (bannerConfig) setConfig(bannerConfig);
  }, [bannerConfig]);

  return configLoading ? (
    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
  ) : (
    <View style={styles.formArea}>
      {!embedEnabled && (
        <View style={{backgroundColor: '#FCF1EE', borderRadius: 8, padding: 16, gap: 12, alignItems: 'flex-start'}}>
          <Text style={[commonStyles.heading, styles.headingText]}>
            App Install Banner extension is not enabled yet
          </Text>
          <Text style={[commonStyles.baseText, styles.baseText, {color: '#000'}]}>
            You must enable the App Install Banner extension on Shopify. Remember to save it once it is enabled.
          </Text>

          <View style={{flexDirection: 'row', gap: 8}}>
            <Button
              onPress={() => {
                window.open(
                  `https://admin.shopify.com/store/${shop}/themes/${themeId}/editor?context=apps&activateAppId=5333f159-b25e-4fd4-acf6-d46ebf21aef7/apptile-smart-banner`,
                );
              }}>
              Enable Smart Banner <MaterialCommunityIcons name="open-in-new" />
            </Button>

            <Button color="TILE" border={false} onPress={checkThemeBlock}>
              Refresh <MaterialCommunityIcons name="reload" size={14} />
            </Button>
          </View>
        </View>
      )}
      <View style={styles.formGroup}>
        <View style={styles.formField}>
          <View style={{flexDirection: 'row', zIndex: 1, gap: 4}}>
            <Text style={[commonStyles.heading, styles.headingText]}>Appstore Link</Text>
            <Tooltip
              visible={true}
              tooltip={
                <View
                  style={{
                    width: 200,
                    background: '#fff',
                    padding: 8,
                    borderWidth: 1,
                    borderColor: '#ccc',
                    borderRadius: 8,
                  }}>
                  <Text style={[commonStyles.baseText]}>
                    Find your mobile app in the Appstore, click on 'Share' and then 'Copy link' to paste here!
                  </Text>
                </View>
              }>
              <Icon iconType="Feather" name="info" size="xs" color="#666" />
            </Tooltip>
          </View>
          <CodeInputControlV2
            singleLine
            value={bannerConfig?.appSettings?.appstoreLink}
            onChange={(value: string) => {
              updateConfig(['appSettings', 'appstoreLink'], value);
            }}
          />
        </View>
        <View style={styles.formField}>
          <View style={{flexDirection: 'row', zIndex: 1, gap: 4}}>
            <Text style={[commonStyles.heading, styles.headingText]}>Playstore Link</Text>
            <Tooltip
              visible={true}
              tooltip={
                <View
                  style={{
                    width: 200,
                    background: '#fff',
                    padding: 8,
                    borderWidth: 1,
                    borderColor: '#ccc',
                    borderRadius: 8,
                  }}>
                  <Text style={[commonStyles.baseText]}>
                    Find your mobile app in the Play store, click on 'Share' and then 'Copy link' to paste here!
                  </Text>
                </View>
              }>
              <Icon iconType="Feather" name="info" size="xs" color="#666" />
            </Tooltip>
          </View>
          <CodeInputControlV2
            singleLine
            value={bannerConfig?.appSettings?.playstoreLink}
            onChange={(value: string) => {
              updateConfig(['appSettings', 'playstoreLink'], value);
            }}
          />
        </View>
        <View style={styles.formField}>
          <Text style={[commonStyles.heading, styles.headingText]}>App Icon</Text>

          {!bannerConfig?.appSettings?.logoUrl ? (
            <Pressable
              style={[styles.imageContainer, styles.upload]}
              onPress={() => setShowPopover(true)}
              nativeID="uploadLogoSection">
              <TextElement fontSize="sm" lineHeight="md" color="PRIMARY">
                + UPLOAD
              </TextElement>
            </Pressable>
          ) : (
            <Pressable style={styles.imageContainer} onPress={() => setShowPopover(true)}>
              <Image resizeMode="contain" source={{uri: bannerConfig?.appSettings?.logoUrl}} style={styles.image} />
              <View
                style={{
                  padding: 5,
                  borderRadius: 30,
                  overflow: 'hidden',
                  backgroundColor: '#fff',
                  position: 'absolute',
                  bottom: 5,
                  right: 5,
                }}>
                <MaterialCommunityIcons name="pencil-outline" size={15} />
              </View>
            </Pressable>
          )}

          <AssetChooseDialog
            askURL={false}
            currentAssetId={''}
            onSelectAsset={(assetId, url?: string) => {
              onLogoUpdate(url as string);
            }}
            onCloseDialog={val => {
              setShowPopover(val);
            }}
            showDialog={showPopover}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    flex: 1,
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  bottomContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  cardHeading: {
    marginTop: 18,
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#F7F7F7',
    paddingVertical: 24,
  },
  formArea: {
    gap: 22,
    paddingTop: 15,
  },
  formGroup: {
    gap: 22,
  },
  formField: {
    gap: 6,
  },
  formGroupWithBorder: {
    gap: 22,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
});
