import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';
import {useNavigate} from '../../routing.web';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import ColorInputControl from '../../components/controls/ColorInputControl';
import {AdvancedSettings} from './advancedSettings';
import {BannerConfig} from '.';
import {CommonSettings} from './commonSettings';
import theme from '../../styles-v2/theme';
import { MaterialCommunityIcons } from 'apptile-core';

interface DesktopDashboardProps {
  configLoading: boolean;
  bannerConfig: BannerConfig;
  updateConfig: (path: string[], value: any) => void;
  publishConfig: (type: 'desktop' | 'mobile', disable: boolean) => void;
  publishLoading: boolean;
  embedEnabled: boolean;
  shop: string;
  themeId: string;
  checkThemeBlock: () => void;
}

export const DesktopDashboard: React.FC<DesktopDashboardProps> = ({
  configLoading,
  bannerConfig,
  updateConfig,
  publishConfig,
  publishLoading,
  embedEnabled,
  shop,
  themeId,
  checkThemeBlock,
}) => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<BannerConfig | null>(null);

  useEffect(() => {
    if (bannerConfig) setConfig(bannerConfig);
  }, [bannerConfig]);

  return configLoading ? (
    <View style={{flex: 1, height: '100%', gap: 6, justifyContent: 'center', alignItems: 'center'}}>
      <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      <Text style={[commonStyles.heading, {fontSize: 19, lineHeight: 20}]}>Intialising App Install Banner</Text>
    </View>
  ) : (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable
          onPress={() => {
            if (!publishLoading) navigate('../');
          }}>
          <Text style={[commonStyles.baseText, styles.baseText, {color: '#000'}]}>
            <MaterialCommunityIcons name="chevron-left" size={16} /> BACK
          </Text>
        </Pressable>
      </View>
      <View style={styles.banner}>
        <View style={styles.bannerTextContainer}>
          <Text style={[commonStyles.heading, styles.bannerHeading]}>Desktop Install Widget</Text>
          <Text style={[commonStyles.baseText, styles.baseText]}>
            Add a banner to your website to direct desktop users to download your app.
          </Text>
        </View>
        {bannerConfig?.desktopBanner?.enabled ? (
          <View style={{flexDirection: 'row', gap: 6}}>
            <Button
              size={'SMALL'}
              containerStyles={styles.publishButton}
              inversed
              border={false}
              disabled={publishLoading}
              onPress={() => {
                setTimeout(() => {
                  publishConfig('desktop', true);
                }, 250);
              }}>
              Disable Banner
            </Button>
            <Button
              color="CTA"
              size={'SMALL'}
              containerStyles={styles.publishButton}
              border={false}
              disabled={publishLoading}
              onPress={() => publishConfig('desktop', false)}>
              Update
            </Button>
          </View>
        ) : (
          <Button
            color="CTA"
            size={'SMALL'}
            containerStyles={styles.publishButton}
            border={false}
            disabled={publishLoading}
            onPress={() => publishConfig('desktop', false)}>
            Publish
          </Button>
        )}
      </View>
      <View style={styles.bannerDetailsContainer}>
        <View style={styles.bottomContainerHeader}>
          <Text style={[commonStyles.heading, styles.bottomContainerHeaderText]}>Banner Details</Text>
        </View>
        {publishLoading ? (
          <View style={{justifyContent: 'center', alignItems: 'center', flex: 1}}>
            <Image
              source={require('@/root/web/assets/images/preloader-blue.svg')}
              style={{width: 50, height: 50, marginBottom: 12}}
            />
            <Text style={[commonStyles.baseText, {fontSize: 14, lineHeight: 16}]}>Saving Desktop Install Banner</Text>
          </View>
        ) : (
          <View style={styles.bottomContainer}>
            <View style={styles.formArea}>
              <CommonSettings
                bannerConfig={bannerConfig}
                configLoading={configLoading}
                type="desktop"
                updateConfig={updateConfig}
                embedEnabled={embedEnabled}
                shop={shop}
                themeId={themeId}
                checkThemeBlock={checkThemeBlock}
              />
              <View style={styles.formGroupWithBorder}>
                <View style={styles.formField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Headline</Text>
                  <CodeInputControlV2
                    singleLine
                    inputStyles={{paddingRight: 15}}
                    value={bannerConfig?.desktopBanner?.headline}
                    onChange={(value: string) => {
                      updateConfig(['desktopBanner', 'headline'], value);
                    }}
                  />
                  <Text
                    style={[
                      commonStyles.baseText,
                      {
                        position: 'absolute',
                        right: 8,
                        bottom: bannerConfig?.desktopBanner?.headline.length > 40 ? 33.5 : 12.5,
                      },
                    ]}>
                    {bannerConfig?.desktopBanner?.headline.length}/40
                  </Text>
                  {bannerConfig?.desktopBanner?.headline.length > 40 && (
                    <Text style={[commonStyles.baseText, {color: '#c00'}]}>
                      Headline can have maximum 40 chars only
                    </Text>
                  )}
                </View>
                <View style={styles.formField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Button Text</Text>
                  <CodeInputControlV2
                    singleLine
                    value={bannerConfig?.desktopBanner?.buttonText}
                    onChange={(value: string) => {
                      updateConfig(['desktopBanner', 'buttonText'], value);
                    }}
                  />
                  <Text
                    style={[
                      commonStyles.baseText,
                      {
                        position: 'absolute',
                        right: 8,
                        bottom: bannerConfig?.desktopBanner?.buttonText.length > 22 ? 33.5 : 12.5,
                      },
                    ]}>
                    {bannerConfig?.desktopBanner?.buttonText.length}/22
                  </Text>
                  {bannerConfig?.desktopBanner?.buttonText.length > 22 && (
                    <Text style={[commonStyles.baseText, {color: '#c00'}]}>
                      Button Text can have maximum 22 chars only
                    </Text>
                  )}
                </View>
              </View>
              <View style={styles.colorInputGroup}>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Background Color</Text>
                  <ColorInputControl
                    inTheme
                    value={bannerConfig?.desktopBanner?.cardBackgroundColor}
                    onChange={(value: string) => {
                      updateConfig(['desktopBanner', 'cardBackgroundColor'], value);
                    }}
                  />
                </View>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Text Color</Text>
                  <ColorInputControl 
                    inTheme
                    value={bannerConfig?.desktopBanner?.cardTextColor}
                    onChange={(value: string) => {
                      updateConfig(['desktopBanner', 'cardTextColor'], value);
                    }}
                  />
                </View>
              </View>
              <View style={[styles.formGroupWithBorder, styles.flexRow]}>
                <View style={[styles.formField, {flex: 1.5}]}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Placement on website</Text>
                  <View style={{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 10}}>
                    <Pressable
                      onPress={() => {
                        updateConfig(['desktopBanner', 'placement'], 'top left');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.desktopBanner.placement == 'top left' ? theme.CONTROL_ACTIVE_COLOR : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/TopLeft.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Top Left</Text>
                    </Pressable>
                    <Pressable
                      onPress={() => {
                        updateConfig(['desktopBanner', 'placement'], 'top right');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.desktopBanner.placement == 'top right' ? theme.CONTROL_ACTIVE_COLOR : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/TopRight.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Top Right</Text>
                    </Pressable>
                    <Pressable
                      onPress={() => {
                        updateConfig(['desktopBanner', 'placement'], 'bottom left');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.desktopBanner.placement == 'bottom left'
                            ? theme.CONTROL_ACTIVE_COLOR
                            : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/BottomLeft.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Bottom Left</Text>
                    </Pressable>
                    <Pressable
                      onPress={() => {
                        updateConfig(['desktopBanner', 'placement'], 'bottom right');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.desktopBanner.placement == 'bottom right'
                            ? theme.CONTROL_ACTIVE_COLOR
                            : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/BottomRight.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Bottom Right</Text>
                    </Pressable>
                  </View>
                </View>
              </View>
              <AdvancedSettings configLoading={configLoading} bannerConfig={bannerConfig} updateConfig={updateConfig} />
            </View>
            <View style={styles.playArea}>
              <View style={styles.dialogContainer}>
                <View style={[styles.dialogTop, {backgroundColor: config?.desktopBanner?.cardBackgroundColor}]}>
                  <View style={styles.crossIcon}>
                    <svg
                      width="5"
                      height="5"
                      viewBox="0 0 5 5"
                      style={styles.svgIcon}
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M4.7687 3.94704L1.0625 0.249542M4.7687 0.249542L1.0625 3.94704"
                        stroke="#222"
                        strokeWidth="0.369718"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </View>
                  <View style={styles.dialogHeading}>
                    <Text style={[styles.desktopHeadline, {color: config?.desktopBanner?.cardTextColor}]}>
                      {config?.desktopBanner?.headline}
                    </Text>
                    <Text style={[styles.desktopHeadlineText, {color: config?.desktopBanner?.cardTextColor}]}>
                      Scan the QR code below
                    </Text>
                  </View>
                  <View
                    id="apptile-qr-code"
                    style={[styles.qrCode, {borderTop: 1, borderTopColor: config?.desktopBanner?.cardTextColor}]}>
                    <svg width="150" height="150">
                      <defs>
                        <clipPath id="clip-path-dot-color">
                          <path d="M 1 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,3,35)" />
                          <path d="M 1 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,3,71)" />
                          <circle cx="3" cy="79" r="2" transform="rotate(0,3,79)" />
                          <path d="M 1 97v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,3,99)" />
                          <path d="M 1 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,3,107)" />
                          <path d="M 1 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,3,111)" />
                          <rect x="5" y="33" width="4" height="4" transform="rotate(0,7,35)" />
                          <rect x="5" y="37" width="4" height="4" transform="rotate(0,7,39)" />
                          <path d="M 5 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,7,43)" />
                          <path d="M 5 49v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,7,51)" />
                          <path d="M 5 57v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,7,59)" />
                          <rect x="5" y="61" width="4" height="4" transform="rotate(0,7,63)" />
                          <rect x="5" y="65" width="4" height="4" transform="rotate(0,7,67)" />
                          <path d="M 5 69v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,7,71)" />
                          <path d="M 5 81v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,7,83)" />
                          <rect x="5" y="85" width="4" height="4" transform="rotate(0,7,87)" />
                          <rect x="5" y="89" width="4" height="4" transform="rotate(0,7,91)" />
                          <rect x="5" y="93" width="4" height="4" transform="rotate(0,7,95)" />
                          <rect x="5" y="97" width="4" height="4" transform="rotate(0,7,99)" />
                          <path d="M 5 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,7,103)" />
                          <path d="M 5 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,7,111)" />
                          <rect x="9" y="33" width="4" height="4" transform="rotate(0,11,35)" />
                          <path d="M 9 45v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,11,47)" />
                          <rect x="9" y="49" width="4" height="4" transform="rotate(0,11,51)" />
                          <rect x="9" y="53" width="4" height="4" transform="rotate(0,11,55)" />
                          <rect x="9" y="57" width="4" height="4" transform="rotate(0,11,59)" />
                          <circle cx="11" cy="79" r="2" transform="rotate(0,11,79)" />
                          <rect x="9" y="89" width="4" height="4" transform="rotate(0,11,91)" />
                          <path d="M 9 97v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,11,99)" />
                          <path d="M 9 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,11,107)" />
                          <circle cx="11" cy="115" r="2" transform="rotate(0,11,115)" />
                          <rect x="13" y="33" width="4" height="4" transform="rotate(0,15,35)" />
                          <path d="M 13 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,15,39)" />
                          <path d="M 13 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,15,47)" />
                          <path d="M 13 57v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,15,59)" />
                          <circle cx="15" cy="67" r="2" transform="rotate(0,15,67)" />
                          <path d="M 13 81v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,15,83)" />
                          <rect x="13" y="89" width="4" height="4" transform="rotate(0,15,91)" />
                          <path d="M 13 93v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,15,95)" />
                          <path d="M 13 105v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,15,107)" />
                          <path d="M 13 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,15,111)" />
                          <path d="M 17 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,19,35)" />
                          <circle cx="19" cy="51" r="2" transform="rotate(0,19,51)" />
                          <circle cx="19" cy="71" r="2" transform="rotate(0,19,71)" />
                          <path d="M 17 77v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,19,79)" />
                          <rect x="17" y="81" width="4" height="4" transform="rotate(0,19,83)" />
                          <path d="M 17 89v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,19,91)" />
                          <rect x="17" y="93" width="4" height="4" transform="rotate(0,19,95)" />
                          <path d="M 17 97v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,19,99)" />
                          <rect x="17" y="109" width="4" height="4" transform="rotate(0,19,111)" />
                          <path d="M 17 113v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,19,115)" />
                          <path d="M 21 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,23,39)" />
                          <path d="M 21 41v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,23,43)" />
                          <path d="M 21 57v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,23,59)" />
                          <path d="M 21 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,23,63)" />
                          <path d="M 21 73v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,23,75)" />
                          <rect x="21" y="81" width="4" height="4" transform="rotate(0,23,83)" />
                          <path d="M 21 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,23,87)" />
                          <path d="M 21 93v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,23,95)" />
                          <rect x="21" y="97" width="4" height="4" transform="rotate(0,23,99)" />
                          <path d="M 21 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,23,111)" />
                          <rect x="21" y="113" width="4" height="4" transform="rotate(0,23,115)" />
                          <path d="M 25 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,27,35)" />
                          <path d="M 25 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,27,43)" />
                          <path d="M 25 49v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,27,51)" />
                          <path d="M 25 57v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,27,59)" />
                          <circle cx="27" cy="67" r="2" transform="rotate(0,27,67)" />
                          <rect x="25" y="73" width="4" height="4" transform="rotate(0,27,75)" />
                          <rect x="25" y="81" width="4" height="4" transform="rotate(0,27,83)" />
                          <circle cx="27" cy="91" r="2" transform="rotate(0,27,91)" />
                          <rect x="25" y="97" width="4" height="4" transform="rotate(0,27,99)" />
                          <path d="M 25 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,27,107)" />
                          <rect x="25" y="113" width="4" height="4" transform="rotate(0,27,115)" />
                          <path d="M 29 33v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,31,35)" />
                          <path d="M 29 37v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,31,39)" />
                          <path d="M 29 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,31,47)" />
                          <rect x="29" y="49" width="4" height="4" transform="rotate(0,31,51)" />
                          <path d="M 29 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,31,71)" />
                          <rect x="29" y="73" width="4" height="4" transform="rotate(0,31,75)" />
                          <rect x="29" y="77" width="4" height="4" transform="rotate(0,31,79)" />
                          <rect x="29" y="81" width="4" height="4" transform="rotate(0,31,83)" />
                          <path d="M 29 85v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,31,87)" />
                          <path d="M 29 93v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,31,95)" />
                          <rect x="29" y="97" width="4" height="4" transform="rotate(0,31,99)" />
                          <rect x="29" y="101" width="4" height="4" transform="rotate(0,31,103)" />
                          <path d="M 29 105v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,31,107)" />
                          <rect x="29" y="113" width="4" height="4" transform="rotate(0,31,115)" />
                          <path d="M 33 5v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,35,7)" />
                          <path d="M 33 9v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,35,11)" />
                          <circle cx="35" cy="19" r="2" transform="rotate(0,35,19)" />
                          <path d="M 33 25v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,35,27)" />
                          <path d="M 33 29v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,35,31)" />
                          <rect x="33" y="37" width="4" height="4" transform="rotate(0,35,39)" />
                          <path d="M 33 41v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,35,43)" />
                          <path d="M 33 49v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,35,51)" />
                          <path d="M 33 53v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,35,55)" />
                          <circle cx="35" cy="67" r="2" transform="rotate(0,35,67)" />
                          <path d="M 33 73v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,35,75)" />
                          <path d="M 33 77v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,35,79)" />
                          <path d="M 33 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,35,87)" />
                          <path d="M 33 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,35,103)" />
                          <path d="M 33 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,35,111)" />
                          <rect x="33" y="113" width="4" height="4" transform="rotate(0,35,115)" />
                          <path d="M 33 121v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,35,123)" />
                          <path d="M 33 129v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,35,131)" />
                          <rect x="33" y="133" width="4" height="4" transform="rotate(0,35,135)" />
                          <rect x="33" y="137" width="4" height="4" transform="rotate(0,35,139)" />
                          <rect x="33" y="141" width="4" height="4" transform="rotate(0,35,143)" />
                          <path d="M 33 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,35,147)" />
                          <circle cx="39" cy="3" r="2" transform="rotate(0,39,3)" />
                          <path d="M 37 29v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,39,31)" />
                          <rect x="37" y="33" width="4" height="4" transform="rotate(0,39,35)" />
                          <rect x="37" y="37" width="4" height="4" transform="rotate(0,39,39)" />
                          <path d="M 37 41v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,39,43)" />
                          <rect x="37" y="53" width="4" height="4" transform="rotate(0,39,55)" />
                          <rect x="37" y="57" width="4" height="4" transform="rotate(0,39,59)" />
                          <path d="M 37 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,39,63)" />
                          <circle cx="39" cy="83" r="2" transform="rotate(0,39,83)" />
                          <circle cx="39" cy="91" r="2" transform="rotate(0,39,91)" />
                          <circle cx="39" cy="99" r="2" transform="rotate(0,39,99)" />
                          <rect x="37" y="113" width="4" height="4" transform="rotate(0,39,115)" />
                          <rect x="37" y="117" width="4" height="4" transform="rotate(0,39,119)" />
                          <rect x="37" y="121" width="4" height="4" transform="rotate(0,39,123)" />
                          <path d="M 37 133v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,39,135)" />
                          <rect x="37" y="137" width="4" height="4" transform="rotate(0,39,139)" />
                          <rect x="37" y="141" width="4" height="4" transform="rotate(0,39,143)" />
                          <rect x="37" y="145" width="4" height="4" transform="rotate(0,39,147)" />
                          <path d="M 41 13v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,43,15)" />
                          <path d="M 41 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,43,19)" />
                          <circle cx="43" cy="27" r="2" transform="rotate(0,43,27)" />
                          <path d="M 41 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,43,39)" />
                          <path d="M 41 45v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,43,47)" />
                          <rect x="41" y="49" width="4" height="4" transform="rotate(0,43,51)" />
                          <rect x="41" y="53" width="4" height="4" transform="rotate(0,43,55)" />
                          <path d="M 41 65v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,43,67)" />
                          <path d="M 41 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,43,71)" />
                          <path d="M 41 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,43,87)" />
                          <path d="M 41 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,43,103)" />
                          <rect x="41" y="113" width="4" height="4" transform="rotate(0,43,115)" />
                          <path d="M 41 121v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,43,123)" />
                          <rect x="41" y="125" width="4" height="4" transform="rotate(0,43,127)" />
                          <path d="M 41 129v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,43,131)" />
                          <path d="M 41 137v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,43,139)" />
                          <rect x="41" y="141" width="4" height="4" transform="rotate(0,43,143)" />
                          <rect x="41" y="145" width="4" height="4" transform="rotate(0,43,147)" />
                          <circle cx="47" cy="7" r="2" transform="rotate(0,47,7)" />
                          <rect x="45" y="17" width="4" height="4" transform="rotate(0,47,19)" />
                          <path d="M 45 21v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,47,23)" />
                          <circle cx="47" cy="35" r="2" transform="rotate(0,47,35)" />
                          <rect x="45" y="45" width="4" height="4" transform="rotate(0,47,47)" />
                          <rect x="45" y="49" width="4" height="4" transform="rotate(0,47,51)" />
                          <rect x="45" y="53" width="4" height="4" transform="rotate(0,47,55)" />
                          <rect x="45" y="57" width="4" height="4" transform="rotate(0,47,59)" />
                          <path d="M 45 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,47,63)" />
                          <circle cx="47" cy="75" r="2" transform="rotate(0,47,75)" />
                          <path d="M 45 81v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,47,83)" />
                          <rect x="45" y="85" width="4" height="4" transform="rotate(0,47,87)" />
                          <circle cx="47" cy="95" r="2" transform="rotate(0,47,95)" />
                          <rect x="45" y="101" width="4" height="4" transform="rotate(0,47,103)" />
                          <path d="M 45 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,47,107)" />
                          <path d="M 45 113v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,47,115)" />
                          <path d="M 45 117v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,47,119)" />
                          <rect x="45" y="125" width="4" height="4" transform="rotate(0,47,127)" />
                          <path d="M 45 129v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,47,131)" />
                          <rect x="45" y="145" width="4" height="4" transform="rotate(0,47,147)" />
                          <circle cx="51" cy="3" r="2" transform="rotate(0,51,3)" />
                          <rect x="49" y="17" width="4" height="4" transform="rotate(0,51,19)" />
                          <rect x="49" y="21" width="4" height="4" transform="rotate(0,51,23)" />
                          <path d="M 49 25v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,51,27)" />
                          <circle cx="51" cy="39" r="2" transform="rotate(0,51,39)" />
                          <path d="M 49 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,51,47)" />
                          <rect x="49" y="53" width="4" height="4" transform="rotate(0,51,55)" />
                          <rect x="49" y="57" width="4" height="4" transform="rotate(0,51,59)" />
                          <path d="M 49 77v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,51,79)" />
                          <rect x="49" y="81" width="4" height="4" transform="rotate(0,51,83)" />
                          <path d="M 49 85v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,51,87)" />
                          <rect x="49" y="101" width="4" height="4" transform="rotate(0,51,103)" />
                          <circle cx="51" cy="111" r="2" transform="rotate(0,51,111)" />
                          <path d="M 49 117v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,51,119)" />
                          <rect x="49" y="121" width="4" height="4" transform="rotate(0,51,123)" />
                          <rect x="49" y="125" width="4" height="4" transform="rotate(0,51,127)" />
                          <rect x="49" y="145" width="4" height="4" transform="rotate(0,51,147)" />
                          <path d="M 53 13v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,55,15)" />
                          <rect x="53" y="17" width="4" height="4" transform="rotate(0,55,19)" />
                          <path d="M 53 21v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,55,23)" />
                          <path d="M 53 29v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,55,31)" />
                          <path d="M 53 33v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,55,35)" />
                          <circle cx="55" cy="43" r="2" transform="rotate(0,55,43)" />
                          <path d="M 53 49v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,55,51)" />
                          <rect x="53" y="53" width="4" height="4" transform="rotate(0,55,55)" />
                          <rect x="53" y="57" width="4" height="4" transform="rotate(0,55,59)" />
                          <path d="M 53 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,55,63)" />
                          <path d="M 53 97v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,55,99)" />
                          <path d="M 53 101v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,55,103)" />
                          <path d="M 53 113v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,55,115)" />
                          <path d="M 53 121v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,55,123)" />
                          <path d="M 53 125v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,55,127)" />
                          <path d="M 53 133v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,55,135)" />
                          <rect x="53" y="137" width="4" height="4" transform="rotate(0,55,139)" />
                          <rect x="53" y="141" width="4" height="4" transform="rotate(0,55,143)" />
                          <rect x="53" y="145" width="4" height="4" transform="rotate(0,55,147)" />
                          <path d="M 57 5v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,59,7)" />
                          <path d="M 57 13v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,59,15)" />
                          <path d="M 57 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,59,19)" />
                          <circle cx="59" cy="27" r="2" transform="rotate(0,59,27)" />
                          <path d="M 57 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,59,35)" />
                          <path d="M 57 113v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,59,115)" />
                          <path d="M 57 117v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,59,119)" />
                          <circle cx="59" cy="131" r="2" transform="rotate(0,59,131)" />
                          <path d="M 57 137v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,59,139)" />
                          <path d="M 57 145v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,59,147)" />
                          <rect x="61" y="5" width="4" height="4" transform="rotate(0,63,7)" />
                          <path d="M 61 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,63,11)" />
                          <path d="M 61 21v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,63,23)" />
                          <path d="M 61 29v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,63,31)" />
                          <path d="M 61 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,63,43)" />
                          <path d="M 61 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,63,47)" />
                          <path d="M 61 101v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,63,103)" />
                          <rect x="61" y="105" width="4" height="4" transform="rotate(0,63,107)" />
                          <path d="M 61 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,63,111)" />
                          <circle cx="63" cy="127" r="2" transform="rotate(0,63,127)" />
                          <path d="M 65 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,67,3)" />
                          <rect x="65" y="5" width="4" height="4" transform="rotate(0,67,7)" />
                          <rect x="65" y="9" width="4" height="4" transform="rotate(0,67,11)" />
                          <rect x="65" y="13" width="4" height="4" transform="rotate(0,67,15)" />
                          <rect x="65" y="17" width="4" height="4" transform="rotate(0,67,19)" />
                          <rect x="65" y="21" width="4" height="4" transform="rotate(0,67,23)" />
                          <rect x="65" y="25" width="4" height="4" transform="rotate(0,67,27)" />
                          <rect x="65" y="29" width="4" height="4" transform="rotate(0,67,31)" />
                          <rect x="65" y="33" width="4" height="4" transform="rotate(0,67,35)" />
                          <path d="M 65 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,67,39)" />
                          <rect x="65" y="101" width="4" height="4" transform="rotate(0,67,103)" />
                          <path d="M 65 121v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,67,123)" />
                          <circle cx="67" cy="135" r="2" transform="rotate(0,67,135)" />
                          <path d="M 65 141v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,67,143)" />
                          <path d="M 65 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,67,147)" />
                          <path d="M 69 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,71,3)" />
                          <path d="M 69 5v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,71,7)" />
                          <rect x="69" y="21" width="4" height="4" transform="rotate(0,71,23)" />
                          <path d="M 69 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,71,35)" />
                          <path d="M 69 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,71,43)" />
                          <path d="M 69 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,71,103)" />
                          <path d="M 69 113v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,71,115)" />
                          <rect x="69" y="117" width="4" height="4" transform="rotate(0,71,119)" />
                          <path d="M 69 121v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,71,123)" />
                          <path d="M 69 137v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,71,139)" />
                          <rect x="69" y="141" width="4" height="4" transform="rotate(0,71,143)" />
                          <rect x="69" y="145" width="4" height="4" transform="rotate(0,71,147)" />
                          <path d="M 73 9v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,75,11)" />
                          <rect x="73" y="13" width="4" height="4" transform="rotate(0,75,15)" />
                          <rect x="73" y="17" width="4" height="4" transform="rotate(0,75,19)" />
                          <rect x="73" y="21" width="4" height="4" transform="rotate(0,75,23)" />
                          <path d="M 73 25v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,75,27)" />
                          <path d="M 73 41v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,75,43)" />
                          <path d="M 73 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,75,47)" />
                          <path d="M 73 117v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,75,119)" />
                          <circle cx="75" cy="127" r="2" transform="rotate(0,75,127)" />
                          <path d="M 73 141v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,75,143)" />
                          <rect x="73" y="145" width="4" height="4" transform="rotate(0,75,147)" />
                          <path d="M 77 1v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,79,3)" />
                          <path d="M 77 5v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,79,7)" />
                          <path d="M 77 21v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,79,23)" />
                          <circle cx="79" cy="31" r="2" transform="rotate(0,79,31)" />
                          <circle cx="79" cy="39" r="2" transform="rotate(0,79,39)" />
                          <path d="M 77 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,79,103)" />
                          <path d="M 77 113v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,79,115)" />
                          <circle cx="79" cy="131" r="2" transform="rotate(0,79,131)" />
                          <rect x="77" y="145" width="4" height="4" transform="rotate(0,79,147)" />
                          <rect x="81" y="5" width="4" height="4" transform="rotate(0,83,7)" />
                          <path d="M 81 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,83,11)" />
                          <circle cx="83" cy="27" r="2" transform="rotate(0,83,27)" />
                          <path d="M 81 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,83,35)" />
                          <path d="M 81 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,83,43)" />
                          <path d="M 81 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,83,47)" />
                          <path d="M 81 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,83,103)" />
                          <path d="M 81 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,83,111)" />
                          <rect x="81" y="113" width="4" height="4" transform="rotate(0,83,115)" />
                          <rect x="81" y="117" width="4" height="4" transform="rotate(0,83,119)" />
                          <rect x="81" y="121" width="4" height="4" transform="rotate(0,83,123)" />
                          <path d="M 81 125v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,83,127)" />
                          <path d="M 81 141v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,83,143)" />
                          <rect x="81" y="145" width="4" height="4" transform="rotate(0,83,147)" />
                          <rect x="85" y="5" width="4" height="4" transform="rotate(0,87,7)" />
                          <rect x="85" y="9" width="4" height="4" transform="rotate(0,87,11)" />
                          <path d="M 85 13v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,87,15)" />
                          <circle cx="87" cy="23" r="2" transform="rotate(0,87,23)" />
                          <path d="M 85 29v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,87,31)" />
                          <path d="M 85 33v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,87,35)" />
                          <rect x="85" y="117" width="4" height="4" transform="rotate(0,87,119)" />
                          <rect x="85" y="121" width="4" height="4" transform="rotate(0,87,123)" />
                          <path d="M 85 133v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,87,135)" />
                          <rect x="85" y="137" width="4" height="4" transform="rotate(0,87,139)" />
                          <rect x="85" y="141" width="4" height="4" transform="rotate(0,87,143)" />
                          <rect x="85" y="145" width="4" height="4" transform="rotate(0,87,147)" />
                          <rect x="89" y="5" width="4" height="4" transform="rotate(0,91,7)" />
                          <path d="M 89 13v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,91,15)" />
                          <circle cx="91" cy="27" r="2" transform="rotate(0,91,27)" />
                          <path d="M 89 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,91,43)" />
                          <path d="M 89 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,91,103)" />
                          <path d="M 89 113v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,91,115)" />
                          <rect x="89" y="117" width="4" height="4" transform="rotate(0,91,119)" />
                          <rect x="89" y="121" width="4" height="4" transform="rotate(0,91,123)" />
                          <circle cx="91" cy="131" r="2" transform="rotate(0,91,131)" />
                          <path d="M 89 137v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,91,139)" />
                          <rect x="89" y="141" width="4" height="4" transform="rotate(0,91,143)" />
                          <rect x="89" y="145" width="4" height="4" transform="rotate(0,91,147)" />
                          <path d="M 93 1v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,95,3)" />
                          <rect x="93" y="5" width="4" height="4" transform="rotate(0,95,7)" />
                          <path d="M 93 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,95,11)" />
                          <circle cx="95" cy="23" r="2" transform="rotate(0,95,23)" />
                          <path d="M 93 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,95,35)" />
                          <rect x="93" y="37" width="4" height="4" transform="rotate(0,95,39)" />
                          <rect x="93" y="41" width="4" height="4" transform="rotate(0,95,43)" />
                          <path d="M 93 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,95,47)" />
                          <circle cx="95" cy="55" r="2" transform="rotate(0,95,55)" />
                          <path d="M 93 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,95,87)" />
                          <path d="M 93 93v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,95,95)" />
                          <rect x="93" y="97" width="4" height="4" transform="rotate(0,95,99)" />
                          <rect x="93" y="101" width="4" height="4" transform="rotate(0,95,103)" />
                          <path d="M 93 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,95,107)" />
                          <rect x="93" y="117" width="4" height="4" transform="rotate(0,95,119)" />
                          <rect x="93" y="121" width="4" height="4" transform="rotate(0,95,123)" />
                          <rect x="93" y="145" width="4" height="4" transform="rotate(0,95,147)" />
                          <rect x="97" y="5" width="4" height="4" transform="rotate(0,99,7)" />
                          <rect x="97" y="9" width="4" height="4" transform="rotate(0,99,11)" />
                          <rect x="97" y="13" width="4" height="4" transform="rotate(0,99,15)" />
                          <path d="M 97 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,99,19)" />
                          <circle cx="99" cy="27" r="2" transform="rotate(0,99,27)" />
                          <rect x="97" y="37" width="4" height="4" transform="rotate(0,99,39)" />
                          <path d="M 97 49v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,99,51)" />
                          <path d="M 97 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,99,63)" />
                          <rect x="97" y="65" width="4" height="4" transform="rotate(0,99,67)" />
                          <rect x="97" y="69" width="4" height="4" transform="rotate(0,99,71)" />
                          <path d="M 97 73v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,99,75)" />
                          <path d="M 97 81v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,99,83)" />
                          <rect x="97" y="85" width="4" height="4" transform="rotate(0,99,87)" />
                          <rect x="97" y="89" width="4" height="4" transform="rotate(0,99,91)" />
                          <rect x="97" y="93" width="4" height="4" transform="rotate(0,99,95)" />
                          <rect x="97" y="97" width="4" height="4" transform="rotate(0,99,99)" />
                          <rect x="97" y="101" width="4" height="4" transform="rotate(0,99,103)" />
                          <path d="M 97 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,99,111)" />
                          <rect x="97" y="113" width="4" height="4" transform="rotate(0,99,115)" />
                          <rect x="97" y="117" width="4" height="4" transform="rotate(0,99,119)" />
                          <rect x="97" y="121" width="4" height="4" transform="rotate(0,99,123)" />
                          <rect x="97" y="145" width="4" height="4" transform="rotate(0,99,147)" />
                          <path d="M 101 5v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,103,7)" />
                          <rect x="101" y="9" width="4" height="4" transform="rotate(0,103,11)" />
                          <rect x="101" y="13" width="4" height="4" transform="rotate(0,103,15)" />
                          <rect x="101" y="17" width="4" height="4" transform="rotate(0,103,19)" />
                          <path d="M 101 29v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,103,31)" />
                          <rect x="101" y="33" width="4" height="4" transform="rotate(0,103,35)" />
                          <path d="M 101 37v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,103,39)" />
                          <path d="M 101 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,103,47)" />
                          <rect x="101" y="49" width="4" height="4" transform="rotate(0,103,51)" />
                          <path d="M 101 53v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,103,55)" />
                          <path d="M 101 69v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,103,71)" />
                          <rect x="101" y="73" width="4" height="4" transform="rotate(0,103,75)" />
                          <rect x="101" y="81" width="4" height="4" transform="rotate(0,103,83)" />
                          <path d="M 101 89v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,103,91)" />
                          <path d="M 101 97v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,103,99)" />
                          <path d="M 101 101v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,103,103)" />
                          <rect x="101" y="121" width="4" height="4" transform="rotate(0,103,123)" />
                          <circle cx="103" cy="139" r="2" transform="rotate(0,103,139)" />
                          <rect x="101" y="145" width="4" height="4" transform="rotate(0,103,147)" />
                          <circle cx="107" cy="3" r="2" transform="rotate(0,107,3)" />
                          <rect x="105" y="9" width="4" height="4" transform="rotate(0,107,11)" />
                          <rect x="105" y="17" width="4" height="4" transform="rotate(0,107,19)" />
                          <rect x="105" y="21" width="4" height="4" transform="rotate(0,107,23)" />
                          <path d="M 105 25v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,107,27)" />
                          <path d="M 105 57v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,107,59)" />
                          <rect x="105" y="61" width="4" height="4" transform="rotate(0,107,63)" />
                          <path d="M 105 65v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,107,67)" />
                          <rect x="105" y="73" width="4" height="4" transform="rotate(0,107,75)" />
                          <rect x="105" y="77" width="4" height="4" transform="rotate(0,107,79)" />
                          <path d="M 105 81v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,107,83)" />
                          <path d="M 105 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,107,107)" />
                          <rect x="105" y="121" width="4" height="4" transform="rotate(0,107,123)" />
                          <path d="M 105 129v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,107,131)" />
                          <path d="M 105 133v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,107,135)" />
                          <path d="M 105 141v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,107,143)" />
                          <rect x="105" y="145" width="4" height="4" transform="rotate(0,107,147)" />
                          <path d="M 109 5v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,111,7)" />
                          <path d="M 109 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,111,11)" />
                          <rect x="109" y="17" width="4" height="4" transform="rotate(0,111,19)" />
                          <rect x="109" y="21" width="4" height="4" transform="rotate(0,111,23)" />
                          <path d="M 109 29v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,111,31)" />
                          <path d="M 109 37v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,111,39)" />
                          <path d="M 109 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,111,43)" />
                          <circle cx="111" cy="55" r="2" transform="rotate(0,111,55)" />
                          <path d="M 109 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,111,71)" />
                          <rect x="109" y="73" width="4" height="4" transform="rotate(0,111,75)" />
                          <rect x="109" y="77" width="4" height="4" transform="rotate(0,111,79)" />
                          <path d="M 109 85v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,111,87)" />
                          <rect x="109" y="89" width="4" height="4" transform="rotate(0,111,91)" />
                          <rect x="109" y="93" width="4" height="4" transform="rotate(0,111,95)" />
                          <rect x="109" y="97" width="4" height="4" transform="rotate(0,111,99)" />
                          <rect x="109" y="101" width="4" height="4" transform="rotate(0,111,103)" />
                          <rect x="109" y="105" width="4" height="4" transform="rotate(0,111,107)" />
                          <path d="M 109 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,111,111)" />
                          <rect x="109" y="121" width="4" height="4" transform="rotate(0,111,123)" />
                          <path d="M 109 125v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,111,127)" />
                          <path d="M 109 145v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,111,147)" />
                          <circle cx="115" cy="3" r="2" transform="rotate(0,115,3)" />
                          <path d="M 113 13v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,115,15)" />
                          <rect x="113" y="17" width="4" height="4" transform="rotate(0,115,19)" />
                          <rect x="113" y="21" width="4" height="4" transform="rotate(0,115,23)" />
                          <rect x="113" y="25" width="4" height="4" transform="rotate(0,115,27)" />
                          <rect x="113" y="29" width="4" height="4" transform="rotate(0,115,31)" />
                          <rect x="113" y="33" width="4" height="4" transform="rotate(0,115,35)" />
                          <rect x="113" y="37" width="4" height="4" transform="rotate(0,115,39)" />
                          <circle cx="115" cy="59" r="2" transform="rotate(0,115,59)" />
                          <rect x="113" y="77" width="4" height="4" transform="rotate(0,115,79)" />
                          <rect x="113" y="85" width="4" height="4" transform="rotate(0,115,87)" />
                          <path d="M 113 93v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,115,95)" />
                          <path d="M 113 101v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,115,103)" />
                          <path d="M 113 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,115,111)" />
                          <rect x="113" y="113" width="4" height="4" transform="rotate(0,115,115)" />
                          <rect x="113" y="117" width="4" height="4" transform="rotate(0,115,119)" />
                          <rect x="113" y="121" width="4" height="4" transform="rotate(0,115,123)" />
                          <rect x="113" y="125" width="4" height="4" transform="rotate(0,115,127)" />
                          <rect x="113" y="129" width="4" height="4" transform="rotate(0,115,131)" />
                          <path d="M 113 133v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,115,135)" />
                          <path d="M 113 141v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,115,143)" />
                          <rect x="117" y="33" width="4" height="4" transform="rotate(0,119,35)" />
                          <rect x="117" y="37" width="4" height="4" transform="rotate(0,119,39)" />
                          <path d="M 117 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,119,43)" />
                          <path d="M 117 53v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,119,55)" />
                          <path d="M 117 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,119,63)" />
                          <rect x="117" y="65" width="4" height="4" transform="rotate(0,119,67)" />
                          <path d="M 117 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,119,71)" />
                          <path d="M 117 77v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,119,79)" />
                          <rect x="117" y="81" width="4" height="4" transform="rotate(0,119,83)" />
                          <rect x="117" y="85" width="4" height="4" transform="rotate(0,119,87)" />
                          <path d="M 117 89v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,119,91)" />
                          <path d="M 117 97v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,119,99)" />
                          <circle cx="119" cy="107" r="2" transform="rotate(0,119,107)" />
                          <rect x="117" y="113" width="4" height="4" transform="rotate(0,119,115)" />
                          <rect x="117" y="129" width="4" height="4" transform="rotate(0,119,131)" />
                          <rect x="117" y="141" width="4" height="4" transform="rotate(0,119,143)" />
                          <path d="M 117 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,119,147)" />
                          <path d="M 121 33v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,123,35)" />
                          <path d="M 121 37v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,123,39)" />
                          <rect x="121" y="53" width="4" height="4" transform="rotate(0,123,55)" />
                          <rect x="121" y="65" width="4" height="4" transform="rotate(0,123,67)" />
                          <path d="M 121 81v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,123,83)" />
                          <rect x="121" y="89" width="4" height="4" transform="rotate(0,123,91)" />
                          <path d="M 121 97v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,123,99)" />
                          <path d="M 121 101v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,123,103)" />
                          <path d="M 121 109v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,123,111)" />
                          <rect x="121" y="113" width="4" height="4" transform="rotate(0,123,115)" />
                          <circle cx="123" cy="123" r="2" transform="rotate(0,123,123)" />
                          <rect x="121" y="129" width="4" height="4" transform="rotate(0,123,131)" />
                          <path d="M 121 137v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,123,139)" />
                          <rect x="121" y="141" width="4" height="4" transform="rotate(0,123,143)" />
                          <path d="M 121 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,123,147)" />
                          <path d="M 125 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,127,47)" />
                          <rect x="125" y="49" width="4" height="4" transform="rotate(0,127,51)" />
                          <rect x="125" y="53" width="4" height="4" transform="rotate(0,127,55)" />
                          <path d="M 125 57v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,127,59)" />
                          <rect x="125" y="65" width="4" height="4" transform="rotate(0,127,67)" />
                          <path d="M 125 69v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,127,71)" />
                          <path d="M 125 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,127,87)" />
                          <rect x="125" y="89" width="4" height="4" transform="rotate(0,127,91)" />
                          <path d="M 125 93v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,127,95)" />
                          <rect x="125" y="101" width="4" height="4" transform="rotate(0,127,103)" />
                          <rect x="125" y="113" width="4" height="4" transform="rotate(0,127,115)" />
                          <rect x="125" y="129" width="4" height="4" transform="rotate(0,127,131)" />
                          <rect x="125" y="141" width="4" height="4" transform="rotate(0,127,143)" />
                          <circle cx="131" cy="35" r="2" transform="rotate(0,131,35)" />
                          <rect x="129" y="53" width="4" height="4" transform="rotate(0,131,55)" />
                          <path d="M 129 65v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,131,67)" />
                          <rect x="129" y="69" width="4" height="4" transform="rotate(0,131,71)" />
                          <rect x="129" y="73" width="4" height="4" transform="rotate(0,131,75)" />
                          <rect x="129" y="77" width="4" height="4" transform="rotate(0,131,79)" />
                          <path d="M 129 81v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,131,83)" />
                          <rect x="129" y="89" width="4" height="4" transform="rotate(0,131,91)" />
                          <path d="M 129 97v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,131,99)" />
                          <rect x="129" y="101" width="4" height="4" transform="rotate(0,131,103)" />
                          <path d="M 129 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,131,107)" />
                          <path d="M 129 113v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,131,115)" />
                          <rect x="129" y="117" width="4" height="4" transform="rotate(0,131,119)" />
                          <rect x="129" y="121" width="4" height="4" transform="rotate(0,131,123)" />
                          <rect x="129" y="125" width="4" height="4" transform="rotate(0,131,127)" />
                          <rect x="129" y="129" width="4" height="4" transform="rotate(0,131,131)" />
                          <path d="M 129 133v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,131,135)" />
                          <path d="M 129 141v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,131,143)" />
                          <path d="M 133 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,135,39)" />
                          <path d="M 133 45v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,135,47)" />
                          <rect x="133" y="49" width="4" height="4" transform="rotate(0,135,51)" />
                          <rect x="133" y="53" width="4" height="4" transform="rotate(0,135,55)" />
                          <path d="M 133 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,135,63)" />
                          <path d="M 133 73v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,135,75)" />
                          <rect x="133" y="77" width="4" height="4" transform="rotate(0,135,79)" />
                          <rect x="133" y="81" width="4" height="4" transform="rotate(0,135,83)" />
                          <rect x="133" y="89" width="4" height="4" transform="rotate(0,135,91)" />
                          <path d="M 133 93v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,135,95)" />
                          <rect x="133" y="117" width="4" height="4" transform="rotate(0,135,119)" />
                          <rect x="133" y="129" width="4" height="4" transform="rotate(0,135,131)" />
                          <rect x="133" y="133" width="4" height="4" transform="rotate(0,135,135)" />
                          <path d="M 133 145v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,135,147)" />
                          <path d="M 137 33v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,139,35)" />
                          <rect x="137" y="37" width="4" height="4" transform="rotate(0,139,39)" />
                          <path d="M 137 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,139,43)" />
                          <rect x="137" y="53" width="4" height="4" transform="rotate(0,139,55)" />
                          <path d="M 137 61v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,139,63)" />
                          <path d="M 137 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,139,71)" />
                          <path d="M 137 77v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,139,79)" />
                          <rect x="137" y="81" width="4" height="4" transform="rotate(0,139,83)" />
                          <rect x="137" y="89" width="4" height="4" transform="rotate(0,139,91)" />
                          <path d="M 137 105v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,139,107)" />
                          <path d="M 137 113v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,139,115)" />
                          <path d="M 137 117v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,139,119)" />
                          <path d="M 137 125v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,139,127)" />
                          <rect x="137" y="129" width="4" height="4" transform="rotate(0,139,131)" />
                          <path d="M 137 133v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,139,135)" />
                          <path d="M 137 145v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,139,147)" />
                          <path d="M 141 33v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,143,35)" />
                          <rect x="141" y="53" width="4" height="4" transform="rotate(0,143,55)" />
                          <path d="M 141 57v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,143,59)" />
                          <path d="M 141 65v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,143,67)" />
                          <rect x="141" y="69" width="4" height="4" transform="rotate(0,143,71)" />
                          <path d="M 141 81v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,143,83)" />
                          <rect x="141" y="85" width="4" height="4" transform="rotate(0,143,87)" />
                          <rect x="141" y="89" width="4" height="4" transform="rotate(0,143,91)" />
                          <rect x="141" y="93" width="4" height="4" transform="rotate(0,143,95)" />
                          <rect x="141" y="97" width="4" height="4" transform="rotate(0,143,99)" />
                          <rect x="141" y="101" width="4" height="4" transform="rotate(0,143,103)" />
                          <rect x="141" y="105" width="4" height="4" transform="rotate(0,143,107)" />
                          <rect x="141" y="109" width="4" height="4" transform="rotate(0,143,111)" />
                          <rect x="141" y="113" width="4" height="4" transform="rotate(0,143,115)" />
                          <circle cx="143" cy="123" r="2" transform="rotate(0,143,123)" />
                          <path d="M 141 129v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,143,131)" />
                          <path d="M 141 141v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(180,143,143)" />
                          <path d="M 145 37v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(-90,147,39)" />
                          <path d="M 145 41v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,147,43)" />
                          <path d="M 145 53v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,147,55)" />
                          <path d="M 145 69v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,147,71)" />
                          <circle cx="147" cy="79" r="2" transform="rotate(0,147,79)" />
                          <path d="M 145 85v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(0,147,87)" />
                          <path d="M 145 93v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,147,95)" />
                          <rect x="145" y="97" width="4" height="4" transform="rotate(0,147,99)" />
                          <path d="M 145 101v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,147,103)" />
                          <path d="M 145 109v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,147,111)" />
                          <rect x="145" y="113" width="4" height="4" transform="rotate(0,147,115)" />
                          <path d="M 145 117v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,147,119)" />
                          <circle cx="147" cy="127" r="2" transform="rotate(0,147,127)" />
                          <path d="M 145 141v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,147,143)" />
                          <path d="M 145 145v 4h 2a 2 2, 0, 0, 0, 0 -4" transform="rotate(90,147,147)" />
                          <path d="M 1 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,3,3)" />
                          <rect x="1" y="5" width="4" height="4" transform="rotate(0,3,7)" />
                          <rect x="1" y="9" width="4" height="4" transform="rotate(0,3,11)" />
                          <rect x="1" y="13" width="4" height="4" transform="rotate(0,3,15)" />
                          <rect x="1" y="17" width="4" height="4" transform="rotate(0,3,19)" />
                          <rect x="1" y="21" width="4" height="4" transform="rotate(0,3,23)" />
                          <path d="M 1 25v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,3,27)" />
                          <rect x="5" y="1" width="4" height="4" transform="rotate(0,7,3)" />
                          <rect x="5" y="25" width="4" height="4" transform="rotate(0,7,27)" />
                          <rect x="9" y="1" width="4" height="4" transform="rotate(0,11,3)" />
                          <rect x="9" y="25" width="4" height="4" transform="rotate(0,11,27)" />
                          <rect x="13" y="1" width="4" height="4" transform="rotate(0,15,3)" />
                          <rect x="13" y="25" width="4" height="4" transform="rotate(0,15,27)" />
                          <rect x="17" y="1" width="4" height="4" transform="rotate(0,19,3)" />
                          <rect x="17" y="25" width="4" height="4" transform="rotate(0,19,27)" />
                          <rect x="21" y="1" width="4" height="4" transform="rotate(0,23,3)" />
                          <rect x="21" y="25" width="4" height="4" transform="rotate(0,23,27)" />
                          <path d="M 25 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,27,3)" />
                          <rect x="25" y="5" width="4" height="4" transform="rotate(0,27,7)" />
                          <rect x="25" y="9" width="4" height="4" transform="rotate(0,27,11)" />
                          <rect x="25" y="13" width="4" height="4" transform="rotate(0,27,15)" />
                          <rect x="25" y="17" width="4" height="4" transform="rotate(0,27,19)" />
                          <rect x="25" y="21" width="4" height="4" transform="rotate(0,27,23)" />
                          <path d="M 25 25v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,27,27)" />
                          <path d="M 9 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,11,11)" />
                          <rect x="9" y="13" width="4" height="4" transform="rotate(0,11,15)" />
                          <path d="M 9 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,11,19)" />
                          <rect x="13" y="9" width="4" height="4" transform="rotate(0,15,11)" />
                          <rect x="13" y="13" width="4" height="4" transform="rotate(0,15,15)" />
                          <rect x="13" y="17" width="4" height="4" transform="rotate(0,15,19)" />
                          <path d="M 17 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,19,11)" />
                          <rect x="17" y="13" width="4" height="4" transform="rotate(0,19,15)" />
                          <path d="M 17 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,19,19)" />
                          <path d="M 121 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,123,3)" />
                          <rect x="121" y="5" width="4" height="4" transform="rotate(0,123,7)" />
                          <rect x="121" y="9" width="4" height="4" transform="rotate(0,123,11)" />
                          <rect x="121" y="13" width="4" height="4" transform="rotate(0,123,15)" />
                          <rect x="121" y="17" width="4" height="4" transform="rotate(0,123,19)" />
                          <rect x="121" y="21" width="4" height="4" transform="rotate(0,123,23)" />
                          <path d="M 121 25v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,123,27)" />
                          <rect x="125" y="1" width="4" height="4" transform="rotate(0,127,3)" />
                          <rect x="125" y="25" width="4" height="4" transform="rotate(0,127,27)" />
                          <rect x="129" y="1" width="4" height="4" transform="rotate(0,131,3)" />
                          <rect x="129" y="25" width="4" height="4" transform="rotate(0,131,27)" />
                          <rect x="133" y="1" width="4" height="4" transform="rotate(0,135,3)" />
                          <rect x="133" y="25" width="4" height="4" transform="rotate(0,135,27)" />
                          <rect x="137" y="1" width="4" height="4" transform="rotate(0,139,3)" />
                          <rect x="137" y="25" width="4" height="4" transform="rotate(0,139,27)" />
                          <rect x="141" y="1" width="4" height="4" transform="rotate(0,143,3)" />
                          <rect x="141" y="25" width="4" height="4" transform="rotate(0,143,27)" />
                          <path d="M 145 1v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,147,3)" />
                          <rect x="145" y="5" width="4" height="4" transform="rotate(0,147,7)" />
                          <rect x="145" y="9" width="4" height="4" transform="rotate(0,147,11)" />
                          <rect x="145" y="13" width="4" height="4" transform="rotate(0,147,15)" />
                          <rect x="145" y="17" width="4" height="4" transform="rotate(0,147,19)" />
                          <rect x="145" y="21" width="4" height="4" transform="rotate(0,147,23)" />
                          <path d="M 145 25v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,147,27)" />
                          <path d="M 129 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,131,11)" />
                          <rect x="129" y="13" width="4" height="4" transform="rotate(0,131,15)" />
                          <path d="M 129 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,131,19)" />
                          <rect x="133" y="9" width="4" height="4" transform="rotate(0,135,11)" />
                          <rect x="133" y="13" width="4" height="4" transform="rotate(0,135,15)" />
                          <rect x="133" y="17" width="4" height="4" transform="rotate(0,135,19)" />
                          <path d="M 137 9v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,139,11)" />
                          <rect x="137" y="13" width="4" height="4" transform="rotate(0,139,15)" />
                          <path d="M 137 17v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,139,19)" />
                          <path d="M 1 121v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,3,123)" />
                          <rect x="1" y="125" width="4" height="4" transform="rotate(0,3,127)" />
                          <rect x="1" y="129" width="4" height="4" transform="rotate(0,3,131)" />
                          <rect x="1" y="133" width="4" height="4" transform="rotate(0,3,135)" />
                          <rect x="1" y="137" width="4" height="4" transform="rotate(0,3,139)" />
                          <rect x="1" y="141" width="4" height="4" transform="rotate(0,3,143)" />
                          <path d="M 1 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,3,147)" />
                          <rect x="5" y="121" width="4" height="4" transform="rotate(0,7,123)" />
                          <rect x="5" y="145" width="4" height="4" transform="rotate(0,7,147)" />
                          <rect x="9" y="121" width="4" height="4" transform="rotate(0,11,123)" />
                          <rect x="9" y="145" width="4" height="4" transform="rotate(0,11,147)" />
                          <rect x="13" y="121" width="4" height="4" transform="rotate(0,15,123)" />
                          <rect x="13" y="145" width="4" height="4" transform="rotate(0,15,147)" />
                          <rect x="17" y="121" width="4" height="4" transform="rotate(0,19,123)" />
                          <rect x="17" y="145" width="4" height="4" transform="rotate(0,19,147)" />
                          <rect x="21" y="121" width="4" height="4" transform="rotate(0,23,123)" />
                          <rect x="21" y="145" width="4" height="4" transform="rotate(0,23,147)" />
                          <path d="M 25 121v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,27,123)" />
                          <rect x="25" y="125" width="4" height="4" transform="rotate(0,27,127)" />
                          <rect x="25" y="129" width="4" height="4" transform="rotate(0,27,131)" />
                          <rect x="25" y="133" width="4" height="4" transform="rotate(0,27,135)" />
                          <rect x="25" y="137" width="4" height="4" transform="rotate(0,27,139)" />
                          <rect x="25" y="141" width="4" height="4" transform="rotate(0,27,143)" />
                          <path d="M 25 145v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,27,147)" />
                          <path d="M 9 129v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(-90,11,131)" />
                          <rect x="9" y="133" width="4" height="4" transform="rotate(0,11,135)" />
                          <path d="M 9 137v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(180,11,139)" />
                          <rect x="13" y="129" width="4" height="4" transform="rotate(0,15,131)" />
                          <rect x="13" y="133" width="4" height="4" transform="rotate(0,15,135)" />
                          <rect x="13" y="137" width="4" height="4" transform="rotate(0,15,139)" />
                          <path d="M 17 129v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(0,19,131)" />
                          <rect x="17" y="133" width="4" height="4" transform="rotate(0,19,135)" />
                          <path d="M 17 137v 4h 4v -2a 2 2, 0, 0, 0, -2 -2" transform="rotate(90,19,139)" />
                        </clipPath>
                      </defs>
                      <rect
                        x="0"
                        y="0"
                        height="150"
                        width="150"
                        clip-path="url('#clip-path-background-color')"
                        fill="#0000"
                      />
                      <rect
                        x="1"
                        y="1"
                        height="148"
                        width="148"
                        clip-path="url('#clip-path-dot-color')"
                        fill={config?.desktopBanner?.cardTextColor}
                      />
                      <image
                        href={config?.appSettings?.logoUrl}
                        x="57"
                        y="51"
                        width="36px"
                        height="48px"
                        style={{clipPath: 'inset(0% round 3px)'}}
                      />
                    </svg>
                  </View>
                </View>
                <View style={[styles.dialogBottom, {backgroundColor: config?.desktopBanner?.cardBackgroundColor}]}>
                  <Text style={[styles.desktopButtonText, {color: config?.desktopBanner?.cardTextColor}]}>
                    {config?.desktopBanner?.buttonText}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  bannerTextContainer: {
    gap: 8,
  },
  bannerHeading: {
    fontSize: 22,
    lineHeight: 20,
  },
  publishButton: {
    paddingHorizontal: 30,
  },
  bannerDetailsContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    flex: 1,
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  bottomContainerHeaderText: {
    fontSize: 16,
    lineHeight: 18,
  },
  bottomContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  formArea: {
    flex: 1,
    padding: 25,
    gap: 22,
    overflow: 'scroll',
  },
  formGroup: {
    gap: 22,
  },
  formField: {
    gap: 6,
  },
  formGroupWithBorder: {
    gap: 22,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  colorInputGroup: {
    flexDirection: 'row',
    gap: 12,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  colorInputField: {
    gap: 6,
    minWidth: 200,
  },
  playArea: {
    backgroundColor: '#F3F3F3',
    width: 280,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    padding: 4,
    flexDirection: 'column',
    gap: 20,
    width: 182,
    color: '#fff',
  },
  dialogHeading: {
    flexDirection: 'column',
    flexBasis: 'auto',
    paddingVertical: 0,
    paddingHorizontal: 6,
    paddingBottom: 8,
  },
  dialogTop: {
    height: 340,
    borderRadius: 8,
    position: 'relative',
    paddingHorizontal: 11,
    paddingTop: 10,
    backgroundColor: 'rgb(221, 221, 221)',
    color: 'rgb(34, 34, 34)',
  },
  desktopHeadline: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 21,
    marginVertical: 12,
    marginHorizontal: 0,
  },
  desktopHeadlineText: {
    fontFamily: 'sans-serif',
    fontWeight: '200',
    fontSize: 11,
    textAlign: 'center',
  },
  qrCode: {
    marginTop: 5,
    justifyContent: 'center',
  },
  dialogBottom: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 60,
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
  },
  desktopButtonText: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 12,
    lineHeight: 18,
    textAlign: 'center',
    width: '100%',
  },
  crossIcon: {
    minWidth: 20,
    minHeight: 20,
    paddingVertical: 2,
    paddingHorizontal: 5,
    position: 'absolute',
    right: 10,
    top: 10,
  },
  svgIcon: {
    width: '100%',
    height: '100%',
  },
  preloader: {
    width: 50,
    height: 50,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
});
