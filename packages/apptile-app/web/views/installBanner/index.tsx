import React, {useCallback, useEffect, useState} from 'react';
import {Route, Routes, useParams} from 'react-router';
import {ChooseBanner} from './chooseBanner';
import {MobileDashboard} from './mobile-dashboard';
import {DesktopDashboard} from './desktop-dashboard';
import {useDispatch, useSelector} from 'react-redux';
import {datasourceTypeModelSel} from 'apptile-core';
import axios from 'axios';
import _ from 'lodash';
import {makeToast} from '../../actions/toastActions';
import AppBannerApi from '../../api/AppBannerApi';
import {BuildManagerApi} from '../../api/BuildApi';
import ModalComponent from '../../components-v2/base/Modal';
import {PublishedModal} from './publishedModal';
import {View} from 'react-native';
import {useNavigate} from '../../routing.web';
import {AppEmbedNotEnabledModal} from './AppEmbedNotEnabledModal';
import Analytics from '@/root/web/lib/segment';

const makeShopifyModelSelector = (pluginType: string) => {
  const shopifyDSSel = state => datasourceTypeModelSel(state, pluginType);
  return shopifyDSSel;
};

export interface AppSettings {
  appstoreLink: string;
  playstoreLink: string;
  linkingPrefix: string;
  logoUrl: string;
}

export interface MobileBanner {
  enabled: boolean;
  ctaButtonBackgroundColor: string;
  ctaButtonTextColor: string;
  cardTextColor: string;
  cardBackgroundColor: string;
  appName: string;
  bannerText: string;
  buttonText: string;
  placement: string;
}

export interface DesktopBanner {
  enabled: boolean;
  cardTextColor: string;
  cardBackgroundColor: string;
  headline: string;
  buttonText: string;
  placement: string;
}

export interface DisplaySettings {
  showOnAllPages: boolean;
  specificPages: {value: string; type: string}[];
  delay: number;
  displayFrequency: number;
}

export interface BannerConfig {
  appSettings: AppSettings;
  mobileBanner: MobileBanner;
  desktopBanner: DesktopBanner;
  displaySettings: DisplaySettings;
}

export const defaultTemplate = {
  appSettings: {
    appstoreLink: '',
    playstoreLink: '',
    linkingPrefix: '',
    logoUrl: '',
  },
  mobileBanner: {
    enabled: false,
    ctaButtonBackgroundColor: '#000',
    ctaButtonTextColor: '#fff',
    cardTextColor: '#000',
    cardBackgroundColor: '#fff',
    appName: '',
    bannerText: 'Extra 10% off | Code : APP10',
    buttonText: 'Get',
    placement: 'top',
  },
  desktopBanner: {
    enabled: false,
    cardTextColor: '#000',
    cardBackgroundColor: '#fff',
    headline: 'SCAN TO GET THE APP & EXCLUSIVE DEALS',
    buttonText: 'GET YOUR APP TODAY',
    placement: 'bottom right',
  },
  displaySettings: {
    showOnAllPages: true,
    specificPages: [],
    delay: 0,
    displayFrequency: 3600,
  },
};

export const InstallBanner: React.FC = () => {
  const dispatch = useDispatch();
  const {id: appId} = useParams();
  const [loading, setLoading] = useState(true);
  const [publishLoading, setPublishLoading] = useState(false);
  const [embedEnabled, setEmbedEnabled] = useState(true);
  const [bannerConfig, setBannerConfig] = useState<BannerConfig>(null);
  const [ogbannerConfig, setOGBannerConfig] = useState<BannerConfig>(null);
  const [showPublishedModal, setShowPublishedModal] = useState(false);
  const [showNonEmbedModal, setShowNonEmbedModal] = useState(false);
  const [disabledPublish, setDisabledPublish] = useState(false);
  const [publishBannerType, setPublishBannerType] = useState('mobile');
  const [shop, setShop] = useState('');
  const [themeId, setThemeId] = useState('');

  const shopifyDatasourceType = 'shopifyV_22_10';

  const shopifyDsModelSel = makeShopifyModelSelector(shopifyDatasourceType);
  const shopifyDsModel = useSelector(shopifyDsModelSel);
  const shopifyShopUrl = shopifyDsModel?.get('storefrontApiUrl');

  const navigate = useNavigate();

  const updatePublishingStatus = _.debounce(async status => {
    setPublishLoading(status);
  }, 200);

  const checkThemeBlock = async () => {
    return AppBannerApi.checkShopifyAppBannerBlock(appId)
      .then(resp => {
        setEmbedEnabled(!!resp.data?.enabledInTheme);
        setThemeId(resp.data?.themeId?.slice(resp.data?.themeId?.lastIndexOf('/') + 1, resp.data?.themeId.length));
        return !!resp.data?.enabledInTheme;
      })
      .catch(() => {
        setEmbedEnabled(false);
        setThemeId('');
        return false;
      });
  };

  useEffect(() => {
    if (shopifyShopUrl && appId) {
      checkThemeBlock();
      const domain = shopifyShopUrl?.slice(0, shopifyShopUrl?.indexOf('/api/'))?.replace('https://', '');
      setShop(domain.slice(0, domain.indexOf('.myshopify')));
      axios
        .get(`https://cdn.apptile.io/app-banner/${domain}.json`)
        .then(resp => {
          setLoading(false);
          setBannerConfig(resp.data);
          setOGBannerConfig(_.cloneDeep(resp.data));
        })
        .catch(async () => {
          let builds = {};
          try {
            builds = (await BuildManagerApi.getBuilds(appId)).data;
          } catch (e) {
            alert(e.message);
          }
          let androidUrl = builds?.android?.url;
          let iosUrl = builds?.ios?.url;
          defaultTemplate.appSettings.appstoreLink = iosUrl;
          defaultTemplate.appSettings.playstoreLink = androidUrl;
          try {
            builds = (await BuildManagerApi.getAppSettings(appId)).data;
          } catch (e) {
            alert(e.message);
          }
          defaultTemplate.appSettings.linkingPrefix = builds.bundleUrlScheme;
          defaultTemplate.mobileBanner.appName = builds.displayName;
          if (defaultTemplate.mobileBanner.appName) {
            setLoading(false);
            setBannerConfig(defaultTemplate);
            setOGBannerConfig({});
          } else {
            dispatch(
              makeToast({
                content: "Can't initialise install banner before first app build",
                appearances: 'error',
              }),
            );
            navigate('../');
          }
        });
    }
  }, [appId, shopifyShopUrl]);

  const updateConfig = (path: string[], value: any) => {
    let newBannerConfig = _.cloneDeep(bannerConfig);
    newBannerConfig = _.set(newBannerConfig, path, value);
    Analytics.track(`editor:appInstallBanner_propertyUpdated`, {path, value});
    setBannerConfig(newBannerConfig);
  };

  const validateConfig = (config: BannerConfig, type: 'desktop' | 'mobile'): boolean => {
    const isEmpty = (value: any) => value === null || value === undefined || value === '';

    const checkFields = (obj: any): boolean => {
      for (const key in obj) {
        if (isEmpty(obj[key])) {
          return false;
        } else if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          if (!checkFields(obj[key])) return false;
        }
      }
      return true;
    };

    return (
      checkFields(config.appSettings) &&
      checkFields(config.displaySettings) &&
      checkFields(config[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'])
    );
  };

  const publishConfig = async (type: 'desktop' | 'mobile', disable: boolean) => {
    setDisabledPublish(disable);
    setPublishBannerType(type);
    updatePublishingStatus(true);
    const embedStatus = await checkThemeBlock();
    if (!embedStatus) {
      updatePublishingStatus(false);
      return setShowNonEmbedModal(true);
    }
    updatePublishingStatus(false);
    let newBannerConfig = _.cloneDeep(bannerConfig);
    newBannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'].enabled = !disable;
    if (
      !_.isEqual(ogbannerConfig.appSettings, newBannerConfig.appSettings) ||
      !_.isEqual(ogbannerConfig.displaySettings, newBannerConfig.displaySettings) ||
      !_.isEqual(
        ogbannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'],
        newBannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'],
      )
    ) {
      if (
        validateConfig(newBannerConfig, type) &&
        newBannerConfig.mobileBanner?.bannerText?.length <= 30 &&
        newBannerConfig.mobileBanner?.buttonText?.length <= 10 &&
        newBannerConfig.desktopBanner?.headline?.length <= 40 &&
        newBannerConfig.desktopBanner?.buttonText?.length <= 22
      ) {
        updatePublishingStatus(true);
        newBannerConfig = ogbannerConfig;
        newBannerConfig.appSettings = bannerConfig.appSettings;
        newBannerConfig.displaySettings = bannerConfig.displaySettings;
        newBannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'] =
          bannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'];
        if (!newBannerConfig[type != 'desktop' ? 'desktopBanner' : 'mobileBanner'])
          newBannerConfig[type != 'desktop' ? 'desktopBanner' : 'mobileBanner'] =
            bannerConfig[type != 'desktop' ? 'desktopBanner' : 'mobileBanner'];
        newBannerConfig[type == 'desktop' ? 'desktopBanner' : 'mobileBanner'].enabled = !disable;
        Analytics.track(`editor:appInstallBanner_${type}_${disable ? 'un' : ''}publishButtonClicked`);
        AppBannerApi.updateBanner(newBannerConfig, appId)
          .then(() => {
            updatePublishingStatus(false);
            dispatch(
              makeToast({
                content: 'Banner configuration published successfully',
                appearances: 'success',
              }),
            );
            setOGBannerConfig(_.cloneDeep(newBannerConfig));
            setShowPublishedModal(true);
          })
          .catch(error => {
            updatePublishingStatus(false);
            dispatch(
              makeToast({
                content: 'Failed to publish banner configuration',
                appearances: 'error',
              }),
            );
            console.error('Error publishing banner configuration:', error);
          });
      } else {
        if (newBannerConfig.mobileBanner?.bannerText?.length > 30) {
          dispatch(
            makeToast({
              content: 'Banner Text should be less than 30 characters',
              appearances: 'error',
            }),
          );
        }
        if (newBannerConfig.mobileBanner?.buttonText?.length > 10) {
          dispatch(
            makeToast({
              content: 'Button Text should be less than 10 characters',
              appearances: 'error',
            }),
          );
        }
        if (newBannerConfig.desktopBanner?.headline?.length > 40) {
          dispatch(
            makeToast({
              content: 'Headline should be less than 40 characters',
              appearances: 'error',
            }),
          );
        }
        if (newBannerConfig.desktopBanner?.buttonText?.length > 22) {
          dispatch(
            makeToast({
              content: 'Banner Text should be less than 22 characters',
              appearances: 'error',
            }),
          );
        }
        dispatch(
          makeToast({
            content: 'All fields are mandatory',
            appearances: 'error',
          }),
        );
      }
    } else {
      dispatch(
        makeToast({
          content: 'No changes detected',
          appearances: 'info',
        }),
      );
    }
  };

  return (
    <>
      <View style={{flex: 0}}>
        <ModalComponent
          onVisibleChange={setShowPublishedModal}
          visible={showPublishedModal}
          content={
            <PublishedModal
              disabledPublish={disabledPublish}
              publishBannerType={publishBannerType}
              closeModal={function (): void {
                setShowPublishedModal(false);
              }}
            />
          }
        />
      </View>
      <View style={{flex: 0}}>
        <ModalComponent
          onVisibleChange={setShowNonEmbedModal}
          visible={showNonEmbedModal}
          content={
            <AppEmbedNotEnabledModal
              shop={shop}
              themeId={themeId}
              closeModal={function (): void {
                setShowNonEmbedModal(false);
              }}
            />
          }
        />
      </View>
      <Routes>
        <Route path="/" element={<ChooseBanner configLoading={loading} />} />
        <Route
          path="/mobile/"
          element={
            <MobileDashboard
              configLoading={loading}
              bannerConfig={bannerConfig}
              updateConfig={updateConfig}
              publishLoading={publishLoading}
              publishConfig={publishConfig}
              embedEnabled={embedEnabled}
              shop={shop}
              themeId={themeId}
              checkThemeBlock={checkThemeBlock}
            />
          }
        />
        <Route
          path="/desktop/"
          element={
            <DesktopDashboard
              configLoading={loading}
              bannerConfig={bannerConfig}
              updateConfig={updateConfig}
              publishLoading={publishLoading}
              publishConfig={publishConfig}
              embedEnabled={embedEnabled}
              shop={shop}
              themeId={themeId}
              checkThemeBlock={checkThemeBlock}
            />
          }
        />
      </Routes>
    </>
  );
};
