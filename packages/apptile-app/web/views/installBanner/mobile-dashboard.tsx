import React, {useEffect, useState} from 'react';
import {StyleSheet, Image, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';
import {useNavigate} from '../../routing.web';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import ColorInputControl from '../../components/controls/ColorInputControl';
import {AdvancedSettings} from './advancedSettings';
import {BannerConfig} from '.';
import {CommonSettings} from './commonSettings';
import theme from '../../styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';

interface MobileDashboardProps {
  configLoading: boolean;
  bannerConfig: BannerConfig;
  updateConfig: (path: string[], value: any) => void;
  publishConfig: (type: 'desktop' | 'mobile', disable: boolean) => void;
  publishLoading: boolean;
  embedEnabled: boolean;
  shop: string;
  themeId: string;
  checkThemeBlock: () => void;
}

export const MobileDashboard: React.FC<MobileDashboardProps> = ({
  configLoading,
  bannerConfig,
  updateConfig,
  publishConfig,
  publishLoading,
  embedEnabled,
  shop,
  themeId,
  checkThemeBlock,
}) => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<BannerConfig | null>(null);

  useEffect(() => {
    if (bannerConfig) setConfig(bannerConfig);
  }, [bannerConfig]);

  return configLoading ? (
    <View style={{flex: 1, height: '100%', gap: 6, justifyContent: 'center', alignItems: 'center'}}>
      <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      <Text style={[commonStyles.heading, {fontSize: 19, lineHeight: 20}]}>Intialising App Install Banner</Text>
    </View>
  ) : (
    <View style={styles.container}>
      <View style={styles.flexRow}>
        <Pressable
          onPress={() => {
            if (!publishLoading) navigate('../');
          }}>
          <Text style={[commonStyles.baseText, styles.baseText, {color: '#000'}]}>
            <MaterialCommunityIcons name="chevron-left" size={16} /> BACK
          </Text>
        </Pressable>
      </View>
      <View style={styles.banner}>
        <View style={styles.bannerTextContainer}>
          <Text style={[commonStyles.heading, styles.bannerHeading]}>Mobile Install Banner</Text>
          <Text style={[commonStyles.baseText, styles.baseText]}>
            Add a banner to your website to direct mobile users to download your app.
          </Text>
        </View>

        {bannerConfig?.mobileBanner?.enabled ? (
          <View style={{flexDirection: 'row', gap: 6}}>
            <Button
              size={'SMALL'}
              containerStyles={styles.publishButton}
              inversed
              border={false}
              disabled={publishLoading}
              onPress={() => {
                setTimeout(() => {
                  publishConfig('mobile', true);
                }, 250);
              }}>
              Disable Banner
            </Button>
            <Button
              color="CTA"
              size={'SMALL'}
              containerStyles={styles.publishButton}
              border={false}
              disabled={publishLoading}
              onPress={() => publishConfig('mobile', false)}>
              Update
            </Button>
          </View>
        ) : (
          <Button
            color="CTA"
            size={'SMALL'}
            containerStyles={styles.publishButton}
            border={false}
            disabled={publishLoading}
            onPress={() => publishConfig('mobile', false)}>
            Publish
          </Button>
        )}
      </View>
      <View style={styles.bannerDetailsContainer}>
        <View style={styles.bottomContainerHeader}>
          <Text style={[commonStyles.heading, styles.bottomContainerHeaderText]}>Banner Details</Text>
        </View>
        {publishLoading ? (
          <View style={{justifyContent: 'center', alignItems: 'center', flex: 1}}>
            <Image
              source={require('@/root/web/assets/images/preloader-blue.svg')}
              style={{width: 50, height: 50, marginBottom: 12}}
            />
            <Text style={[commonStyles.baseText, {fontSize: 14, lineHeight: 16}]}>Saving Mobile Install Banner</Text>
          </View>
        ) : (
          <View style={styles.bottomContainer}>
            <View style={styles.formArea}>
              <CommonSettings
                type="mobile"
                configLoading={configLoading}
                bannerConfig={bannerConfig}
                updateConfig={updateConfig}
                embedEnabled={embedEnabled}
                shop={shop}
                themeId={themeId}
                checkThemeBlock={checkThemeBlock}
              />
              <View style={[styles.formGroupWithBorder, styles.flexRow]}>
                <View style={[styles.formField, {flex: 1.5}]}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Banner Text</Text>
                  <CodeInputControlV2
                    singleLine
                    value={bannerConfig?.mobileBanner.bannerText}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'bannerText'], value);
                    }}
                  />
                  <Text
                    style={[
                      commonStyles.baseText,
                      {
                        position: 'absolute',
                        right: 8,
                        bottom: bannerConfig?.mobileBanner?.bannerText.length > 30 ? 33.5 : 12.5,
                      },
                    ]}>
                    {bannerConfig?.mobileBanner?.bannerText.length}/30
                  </Text>
                  {bannerConfig?.mobileBanner?.bannerText.length > 30 && (
                    <Text style={[commonStyles.baseText, {color: '#c00'}]}>
                      Banner Text can have maximum 30 chars only
                    </Text>
                  )}
                </View>
                <View style={[styles.formField, {flex: 1}]}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Button Text</Text>
                  <CodeInputControlV2
                    singleLine
                    value={bannerConfig?.mobileBanner.buttonText}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'buttonText'], value);
                    }}
                  />
                  <Text
                    style={[
                      commonStyles.baseText,
                      {
                        position: 'absolute',
                        right: 8,
                        bottom: bannerConfig?.mobileBanner?.buttonText.length > 10 ? 33.5 : 12.5,
                      },
                    ]}>
                    {bannerConfig?.mobileBanner?.buttonText.length}/10
                  </Text>
                  {bannerConfig?.mobileBanner?.buttonText.length > 10 && (
                    <Text style={[commonStyles.baseText, {color: '#c00'}]}>
                      Button Text can have maximum 10 chars only
                    </Text>
                  )}
                </View>
              </View>
              <View style={styles.colorInputGroup}>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Background Color</Text>
                  <ColorInputControl inTheme
                    value={bannerConfig?.mobileBanner?.cardBackgroundColor}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'cardBackgroundColor'], value);
                    }}
                  />
                </View>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Text Color</Text>
                  <ColorInputControl inTheme
                    value={bannerConfig?.mobileBanner?.cardTextColor}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'cardTextColor'], value);
                    }}
                  />
                </View>
              </View>
              <View style={[styles.colorInputGroup, {borderTopWidth: 0, paddingTop: 0}]}>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Button Background Color</Text>
                  <ColorInputControl inTheme
                    value={bannerConfig?.mobileBanner?.ctaButtonBackgroundColor}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'ctaButtonBackgroundColor'], value);
                    }}
                  />
                </View>
                <View style={styles.colorInputField}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Button Text Color</Text>
                  <ColorInputControl inTheme
                    value={bannerConfig?.mobileBanner?.ctaButtonTextColor}
                    onChange={(value: string) => {
                      updateConfig(['mobileBanner', 'ctaButtonTextColor'], value);
                    }}
                  />
                </View>
              </View>
              <View style={[styles.formGroupWithBorder, styles.flexRow]}>
                <View style={[styles.formField, {flex: 1.5}]}>
                  <Text style={[commonStyles.heading, styles.headingText]}>Placement on mobile website</Text>
                  <View style={{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 10}}>
                    <Pressable
                      onPress={() => {
                        updateConfig(['mobileBanner', 'placement'], 'top');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.mobileBanner.placement == 'top' ? theme.CONTROL_ACTIVE_COLOR : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/Top.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Top</Text>
                    </Pressable>
                    <Pressable
                      onPress={() => {
                        updateConfig(['mobileBanner', 'placement'], 'bottom');
                      }}
                      style={{
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          bannerConfig.mobileBanner.placement == 'bottom' ? theme.CONTROL_ACTIVE_COLOR : '#e5e5e5',
                        alignItems: 'center',
                        padding: 10,
                        gap: 5,
                      }}>
                      <Image
                        source={require('./assets/Bottom.svg')}
                        resizeMode="contain"
                        style={{height: 50, width: 50, marginTop: 5}}
                      />
                      <Text style={commonStyles.baseText}>Bottom</Text>
                    </Pressable>
                  </View>
                </View>
              </View>
              <AdvancedSettings
                type="mobile"
                configLoading={configLoading}
                bannerConfig={bannerConfig}
                updateConfig={updateConfig}
              />
            </View>
            <View style={styles.playArea}>
              <View style={{height: 387, width: 190}}>
                <Image
                  source={require('./assets/mobile.svg')}
                  style={{height: '100%', width: '100%', position: 'absolute'}}
                  resizeMode="cover"
                />
                <View
                  style={[
                    {
                      zIndex: 1,
                      top: bannerConfig?.mobileBanner?.placement == 'top' ? 40 : 315,
                      width: '108%',
                      left: '-4%',
                    },
                  ]}>
                  <View
                    style={{
                      backgroundColor: bannerConfig?.mobileBanner?.cardBackgroundColor,
                      flexDirection: 'row',
                      paddingVertical: 5,
                      paddingHorizontal: 6,
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      overflow: 'hidden',
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
                      <Image
                        source={{uri: bannerConfig?.appSettings?.logoUrl}}
                        style={{width: 25, height: 25, borderRadius: 2}}
                        resizeMode="contain"
                      />
                      <View>
                        <Text
                          style={[
                            commonStyles.baseText,
                            {fontSize: 7, lineHeight: 10, color: bannerConfig.mobileBanner.cardTextColor},
                          ]}>
                          {bannerConfig?.mobileBanner?.appName}
                        </Text>
                        <Text
                          style={[
                            commonStyles.baseText,
                            {fontSize: 7, lineHeight: 10, color: bannerConfig.mobileBanner.cardTextColor},
                          ]}>
                          {bannerConfig?.mobileBanner?.bannerText}
                        </Text>
                      </View>
                    </View>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
                      <Text
                        style={{
                          backgroundColor: bannerConfig?.mobileBanner?.ctaButtonBackgroundColor,
                          color: bannerConfig?.mobileBanner?.ctaButtonTextColor,
                          paddingVertical: 2,
                          paddingHorizontal: 4,
                          borderRadius: 3,
                          fontSize: 7,
                        }}>
                        {bannerConfig?.mobileBanner?.buttonText}
                      </Text>
                      <Text
                        style={
                          (commonStyles.baseText,
                          {fontSize: 7, lineHeight: 9, color: bannerConfig.mobileBanner.cardTextColor})
                        }>
                        X
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 32,
    flex: 1,
  },
  flexRow: {
    flexDirection: 'row',
  },
  banner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 22,
  },
  bannerTextContainer: {
    gap: 8,
  },
  bannerHeading: {
    fontSize: 22,
    lineHeight: 20,
  },
  publishButton: {
    paddingHorizontal: 30,
  },
  bannerDetailsContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    flex: 1,
  },
  bottomContainerHeader: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  bottomContainerHeaderText: {
    fontSize: 16,
    lineHeight: 18,
  },
  bottomContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  formArea: {
    flex: 1,
    padding: 25,
    gap: 22,
    overflow: 'scroll',
  },
  formGroup: {
    gap: 22,
  },
  formField: {
    gap: 6,
  },
  formGroupWithBorder: {
    gap: 22,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  colorInputGroup: {
    flexDirection: 'row',
    gap: 12,
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
    paddingTop: 22,
  },
  colorInputField: {
    gap: 6,
    minWidth: 200,
  },
  playArea: {
    backgroundColor: '#F3F3F3',
    width: 280,
    padding: 20,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    padding: 4,
    flexDirection: 'column',
    gap: 20,
    width: 182,
    color: '#fff',
  },
  dialogHeading: {
    flexDirection: 'column',
    flexBasis: 'auto',
    paddingVertical: 0,
    paddingHorizontal: 6,
    paddingBottom: 8,
  },
  dialogTop: {
    height: 340,
    borderRadius: 8,
    position: 'relative',
    paddingHorizontal: 11,
    paddingTop: 10,
    backgroundColor: 'rgb(221, 221, 221)',
    color: 'rgb(34, 34, 34)',
  },
  desktopHeadline: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 21,
    marginVertical: 12,
    marginHorizontal: 0,
  },
  desktopHeadlineText: {
    fontFamily: 'sans-serif',
    fontWeight: '200',
    fontSize: 11,
    textAlign: 'center',
  },
  qrCode: {
    marginTop: 20,
    paddingTop: 15,
    justifyContent: 'center',
  },
  dialogBottom: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 60,
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
  },
  desktopButtonText: {
    fontFamily: 'sans-serif',
    fontWeight: '600',
    fontSize: 12,
    lineHeight: 18,
    textAlign: 'center',
    width: '100%',
  },
  crossIcon: {
    minWidth: 20,
    minHeight: 20,
    paddingVertical: 2,
    paddingHorizontal: 5,
    position: 'absolute',
    right: 10,
    top: 10,
  },
  svgIcon: {
    width: '100%',
    height: '100%',
  },
  preloader: {
    width: 50,
    height: 50,
  },
  baseText: {
    fontSize: 14,
    lineHeight: 20,
  },
  headingText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
});
