import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Image, StyleSheet, View } from 'react-native';
import Button from '@/root/web/components-v2/base/Button';
import theme from '@/root/web/styles-v2/theme';
import LogRocket from 'logrocket';
import { useDispatch, useSelector } from 'react-redux';
import { LOGIN_MESSAGES } from '../../../../app/common/utils/apiErrorMessages/specificFeature';
import { loginToLively } from '../../../actions/liveSellingActions';
import TextInput from '../../../components-v2/base/TextInput';
import CodeInputControlV2 from '../../../components/controls-v2/CodeInputControl';
import commonStyles from '../../../styles-v2/commonStyles';
import TextElement from '../../../components-v2/base/TextElement';
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes';
import { EditorRootState } from '@/root/web/store/EditorRootState';

const Login = () => {
  const dispatch = useDispatch();
  const urlParams = new URLSearchParams(window?.location?.search);
  const liveLoginEmail = urlParams.get('liveLoginEmail');
  const appId = urlParams.get('app-id');

  const [loading, setLoading] = useState(false);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const loginStatus = useSelector((state: EditorRootState) => state.liveSelling.auth.status);
  const loginError = useSelector((state: EditorRootState) => state.liveSelling.auth.loginError);

  const customerLogin = () => {
    setError('');
    if (!email.trim() && !password.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_FIELDS);
    } else if (!email.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_EMAIL);
    } else if (!password.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_PASSWORD);
    } else {
      dispatch(loginToLively({email, password, appId, buildRedirection: true, redirectAfterLogin: LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE()}));
    }
  };

  useEffect(() => {
    if(loginError === LOGIN_MESSAGES.ERROR.LOGIN_FAILED){
      setError(LOGIN_MESSAGES.ERROR.LOGIN_FAILED)
    }
  },[loginError]) 

  useEffect(() => {
    if (loginStatus === 'LOADING') {
      setLoading(true);
    } else if (loginStatus === 'SUCCESS' || loginStatus === 'FAILED') {
      setLoading(false);
    }
  }, [loginStatus]);

  useEffect(() => {
    if (liveLoginEmail) {
      setEmail(liveLoginEmail ?? '');
      LogRocket.identify(liveLoginEmail, {
        email: liveLoginEmail,
        buildnumber: (window as any).BN_FOR_LOGROCKET,
      });
    }
  }, [liveLoginEmail]);

  return (
    <>
      <View style={styles.wrapper}>
        <View style={styles.ImageContainer}>
          <Image
            style={styles.image}
            source={require('@/root/web/assets/images/liveSellingDashboardLoginImage.png')}
          />
        </View>
        <View style={[styles.contentContainer, {opacity: loading ? 0.5 : 1}]}>
          <View style={styles.LoginConatiner}>
            <Image style={styles.logo} source={require('@/root/web/assets/icons/apptile-live.svg')} />
            <TextElement
              style={{
                fontSize: 18,
                textAlign: 'center',
                marginBottom: 30,
                marginTop: 10,
                color: '#000',
                fontWeight: 500,
              }}>
              Login To Broadcaster App
            </TextElement>
            <View style={styles.InputConatiner}>
              <TextElement style={styles.InputLabel}>E-mail</TextElement>
              <CodeInputControlV2
                placeholder="Email"
                singleLine={true}
                value={email}
                onChange={!loading && setEmail}
                inputStyles={{padding: 10}}
                containerStyles={{marginTop: 9}}
              />
            </View>
            <View style={[styles.InputConatiner, {marginTop: 10}]}>
              <TextElement style={styles.InputLabel}>Password</TextElement>
              {/* <CodeInputControlV2
                placeholder="Password"
                singleLine={true}
                value={password}
                onChange={!loading && setPassword}
                inputStyles={{padding: 10}}
              /> */}
              <View style={[commonStyles.inputContainer, {width: '100%', marginTop: 10}]}>
                <TextInput
                  placeholder="Password"
                  secureTextEntry={true}
                  style={[
                    commonStyles.input,
                    {padding: 8, width: '100%', outline: 'none', fontSize: 12, color: theme.CONTROL_INPUT_COLOR},
                  ]}
                  onChange={(e: any) => !loading  && setPassword(e.target.value)}
                />
              </View>
            </View>
            {error && (
              <TextElement style={[commonStyles.errorText, {color: 'red', fontSize: 12}]}>{error}</TextElement>
            )}
            <View style={{justifyContent: 'center', alignItems: 'center', marginTop: 30}}>
              <Button
                disabled={loading}
                color={'CTA'}
                innerContainerStyles={styles.loginBtn}
                onPress={() => customerLogin()}>
                Login
              </Button>
            </View>
          </View>
          {loading && (
            <View
              style={[
                styles.rowLayout,
                {
                  position: 'absolute',
                  backgroundColor: 'transparent',
                  width: '100%',
                  minHeight: '100px',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <ActivityIndicator size={20} />
            </View>
          )}
        </View>
      </View>
    </>
  );
};

export default Login;

const styles = StyleSheet.create({
  wrapper: {
    width: '-webkit-fill-available;',
    height: '-webkit-fill-available;',
    backgroundColor: theme.PRIMARY_BACKGROUND,
    justifyContent: 'center',
    flexDirection: 'row',
  },
  LoginConatiner: {
    backgroundColor: '#fff',
    paddingHorizontal: 60,
    borderTopRightRadius: 15,
    borderBottomRightRadius: 15,
    justifyContent: 'center',
    height: 533,
  },
  contentContainer: {
    justifyContent: 'center',
    width: 498,
    height: '100%',
  },
  ImageContainer: {
    // borderTopLeftRadius: 20,
    // borderBottomLeftRadius: 20,
    justifyContent: 'center',
  },
  image: {
    width: 446,
    height: 533,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  logo: {
    width: 223,
    height: 41,
    marginVertical: 10,
    alignSelf: 'center',
  },
  InputLabel: {
    fontSize: 12,
    fontWeight: '400',
    color: 'black',
  },
  InputConatiner: {
    marginVertical: 10,
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  loginBtn: {
    paddingLeft: 40,
    paddingRight: 40,
  },
});
