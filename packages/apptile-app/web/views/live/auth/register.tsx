import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Image, Text} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {DefaultRootState, useDispatch, useSelector} from 'react-redux';
import commonStyles from '../../../styles-v2/commonStyles';
import CodeInputControlV2 from '../../../components/controls-v2/CodeInputControl';
import TextInput from '../../../components-v2/base/TextInput';
import Button from '../../../components-v2/base/Button';
import {LIVELY_INTEGRATION_CODE, registerToLively} from '../../../actions/liveSellingActions';
import {useParams} from 'react-router';
import {fetchPage} from '../../../actions/editorActions';
import {checkApptileEmailSelector} from '../../../selectors/FeatureGatingSelector';
import {useIntercom} from 'react-use-intercom';

const Register = () => {
  const dispatch = useDispatch();
  const {id: appId} = useParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const registrationStatus = useSelector(state => state.liveSelling.auth.registrationStatus);
  const registrationState = useSelector(state => state.liveSelling.auth.registrationState);
  const registerError = useSelector(state => state.liveSelling.auth.registerError);
  const isApptileUser = useSelector(checkApptileEmailSelector);
  const livelyIntegration = useSelector((state: DefaultRootState) => {
    const allIntegrations = state.integration.appIntegrationsById;
    return allIntegrations[
      Object.keys(allIntegrations).find(e => allIntegrations[e].integrationCode == LIVELY_INTEGRATION_CODE)
    ];
  });

  const customerRegister = () => {
    setError('');
    if (confirmPassword.trim() != password.trim()) {
      setError('Confirm password and password should be same');
    } else if (email.trim() == '' || password.trim() == '') {
      setError('Please fill all fields');
    } else {
      dispatch(registerToLively({email: email.trim(), password: password.trim(), appId}));
    }
  };

  dispatch(fetchPage([LIVELY_INTEGRATION_CODE], true, true, false));

  useEffect(() => {
    setError(registerError);
  }, [registerError]);
  const {showNewMessage} = useIntercom();

  return (
    <>
      <View style={[styles.container]}>
        <View style={styles.wrapper}>
          <View style={styles.contentContainer}>
            {registrationStatus == 'REGISTERING' && (
              <View style={styles.contentWrapper}>
                <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 100, height: 100}} />
                <Text>{registrationState}</Text>
              </View>
            )}
            {registrationStatus != 'REGISTERING' && (
              <View style={styles.contentWrapper}>
                <View>
                  <Text
                    style={[
                      commonStyles.baseText,
                      {fontSize: 17, textAlign: 'center', marginVertical: 10, color: '#000'},
                    ]}>
                    {isApptileUser ? 'Register To Broadcaster App' : 'Enable live selling'}
                  </Text>
                </View>
                {isApptileUser ? (
                  <>
                    <View>
                      <CodeInputControlV2
                        placeholder="Email"
                        singleLine={true}
                        label={'Email'}
                        value={email}
                        onChange={setEmail}
                      />
                    </View>
                    <View>
                      <View style={[commonStyles.controlContainer, {flexDirection: 'row'}]}>
                        <View style={commonStyles.labelContainer}>
                          <Text style={commonStyles.labelText}>Password</Text>
                        </View>
                        <View style={commonStyles.inputContainer}>
                          <TextInput
                            placeholder="Password"
                            secureTextEntry={true}
                            style={[commonStyles.input, {padding: 5, width: '100%', outline: 'none'}]}
                            onChange={(e: any) => setPassword(e.target.value)}
                          />
                        </View>
                      </View>
                    </View>
                    <View>
                      <View style={[commonStyles.controlContainer, {flexDirection: 'row'}]}>
                        <View style={commonStyles.labelContainer}>
                          <Text style={commonStyles.labelText}>Confirm Password</Text>
                        </View>
                        <View style={commonStyles.inputContainer}>
                          <TextInput
                            placeholder="Password"
                            secureTextEntry={true}
                            style={[commonStyles.input, {padding: 5, width: '100%', outline: 'none'}]}
                            onChange={(e: any) => setConfirmPassword(e.target.value)}
                          />
                        </View>
                      </View>
                    </View>
                    {error && (
                      <View style={{backgroundColor: theme.ERROR_BACKGROUND, borderRadius: 8}}>
                        <Text style={[commonStyles.errorText, {color: theme.ERROR_COLOR}]}>{error}</Text>
                      </View>
                    )}
                    <View style={{justifyContent: 'center', alignItems: 'center'}}>
                      <Button color={'CTA'} containerStyles={{paddingHorizontal: 30}} onPress={customerRegister}>
                        Register
                      </Button>
                    </View>
                  </>
                ) : (
                  <View style={{justifyContent: 'center', alignItems: 'center'}}>
                    <Button
                      color={'CTA'}
                      containerStyles={{paddingHorizontal: 30}}
                      onPress={() => showNewMessage(`Please activate live selling for my store`)}>
                      Talk to us
                    </Button>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </>
  );
};

export default Register;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
  },
  wrapper: {
    width: '100%',
    height: '100%',
  },
  contentContainer: {
    width: '100%',
    height: '100%',
    marginBottom: 50,
    paddingVertical: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    color: theme.TEXT_COLOR,
    lineHeight: 16,
    fontWeight: '500',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  streamsList: {
    marginVertical: 20,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  emptyStream: {
    height: 175,
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  contentWrapper: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    width: 500,
    gap: 20,
  },
});
