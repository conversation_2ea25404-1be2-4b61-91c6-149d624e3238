import apolloQueryRunner from '@/root/app/plugins/datasource/ApolloWrapper/model';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {GET_SHOP} from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/shopDetails';
import {
  TransformGetCollectionProductsQuery,
  TransformGetProductsPaginatedQuery,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import _ from 'lodash';
import moment from 'moment';
import React, {useEffect, useRef, useState} from 'react';
import {Animated, Image, Pressable, ScrollView, StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {v4 as uuidv4} from 'uuid';
import {COMMON_ERROR_MESSAGE} from '../../../../app/common/utils/apiErrorMessages/generalMessages';
import {CREATE_STREAM} from '../../../../app/common/utils/apiErrorMessages/specificFeature';
import {fetchAppIntegrations} from '../../../actions/editorActions';
import {
  createStream,
  displayStreamDeleteConfirm,
  fetchFacebookPages,
  fetchInstagramPages,
  fetchStreamSettings,
  NotificationInfo,
} from '../../../actions/liveSellingActions';
import {makeToast, removeToast} from '../../../actions/toastActions';
import {ICreateStoreCreditLiveJoinEventRuleData} from '../../../api/ApiTypes';
import {LivelyApi} from '../../../api/LivelyApi';
import StoreCreditApi from '../../../api/StoreCreditApis';
import Button from '../../../components-v2/base/Button';
import TextElement from '../../../components-v2/base/TextElement';
import TextInput from '../../../components-v2/base/TextInput';
import CodeInputControlV2 from '../../../components/controls-v2/CodeInputControl';
import CheckboxControl from '../../../components/controls/CheckboxControl';
import DateAndTimeControl from '../../../components/controls/DateAndTimeControl';
import {ShopifyProductCollectionPicker} from '../../../integrations/shopify/components/ShopifyProductCollectionPicker';
import {useNavigate, useParams, useSearchParams} from '../../../routing.web';
import {EditorRootState} from '../../../store/EditorRootState';
import theme from '../../../styles-v2/theme';
import {DeleteStreamConfirmationModal} from '../modals/deleteStreamConfirmationModal';
import AuctionMeta, {AuctionMetaState} from '../shared/AuctionMeta';
import Back from '../shared/Back';
import {handleApiError, handleApiErrorWithoutNetwork} from '../shared/CommonError';
import InfoTooltip from '../shared/InfoTooltip';
import NetworkErrorPage from '../shared/NetworkErrorPage';
import {EXTENSION_TIME_OPTIONS, TRIGGER_TIME_OPTIONS} from '../utils/dropDownValues';
import {LIVE_DASHBOARD_ROUTES} from '../utils/liveDashboardRoutes';
import {shopifyProductTransformer} from '../utils/transformers';
import {useMountTransitionAnimation} from '../utils/useMountTransitionAnimation';
import {LivelyImageUploader} from './livelyImageUploader';
import ProductTable from './productsTable';
import {FACEBOOK_AUTH_APP_ID, FACEBOOK_AUTH_CONFIG_ID} from '../../../../.env.json';
import {Icon, selectAppConfig} from 'apptile-core';
import {AuctionApi} from '@/root/web/api/AuctionApi';
import commonStyles from '@/root/web/styles-v2/commonStyles';

export const CreateAuction = () => {
  /* Hooks */
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const appConfig = useSelector(selectAppConfig);
  const queryParams = new URLSearchParams(location.search);

  /* Selectors */
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const streamCreated = useSelector((state: EditorRootState) => state.liveSelling.streamCreated);
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const appIntegrationsById = useSelector((state: EditorRootState) => state.integration.appIntegrationsById);
  const streamCreationInProgress = useSelector((state: EditorRootState) => state.liveSelling.streamCreationInProgress);
  const settingsFromState = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data);
  let defaultStartingBidPercentage = useSelector(
    (state: EditorRootState) => state.liveSelling.streamSettings.data?.defaultStartingBidPercentage,
  );
  defaultStartingBidPercentage = defaultStartingBidPercentage ? Math.abs(100 - defaultStartingBidPercentage) : 50;
  const facebookLoading = useSelector((state: EditorRootState) => state.liveSelling.facebookChannelLoading);
  const facebookFound = useSelector((state: EditorRootState) => state.liveSelling.facebookChannelFound);
  const facebookChannels = useSelector((state: EditorRootState) => state.liveSelling.facebookChannelAuths);
  const isFacebookConnectionError = useSelector(
    (state: EditorRootState) => state.liveSelling.isFacebookConnectionNetworkError,
  );
  const streamToFacebookAuthId = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data?.streamToFacebookAuthId);
  const streamToInstagramAuthId = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data?.streamToInstagramAuthId);
  const instagramLoading = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelLoading);
  const instagramFound = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelFound);
  const instagramChannels = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelAuths);
  const isInstagramConnectionError = useSelector((state: EditorRootState) => state.liveSelling.isInstagramConnectionNetworkError);

  /* States */
  const [topic, setTopic] = useState('');
  const [description, setDescription] = useState('');
  const [newProduct, setNewProduct] = useState('');
  const [newCollection, setNewCollection] = useState('');
  const [assetSourceValue, setAssetSourceValue] = useState(null);
  const [streamProducts, setStreamProducts] = useState<any[]>([]);
  const [scheduleDate, setScheduleDate] = useState(new Date());
  const [queryRunner, setQueryRunner] = useState(null);
  const [hasScheduled, setHasScheduled] = useState(false);
  const [pastStreamInfo, setPastStreamInfo] = useState<any>({});
  const [hasNotification, setHasNotification] = useState(false);
  const [hasStreamToPlatforms, setHasStreamToPlatforms] = useState<boolean>(true);
  const [hasStoreCredit, setHasStoreCredit] = useState(false);
  const [notificationInfo, setNotificationInfo] = useState<Partial<NotificationInfo>>({});
  const [storeCreditInfo, setStoreCreditInfo] = useState<ICreateStoreCreditLiveJoinEventRuleData>({});
  const [currencyCode, setCurrencyCode] = useState(null);
  const [streamToPlatforms, setStreamToPlatforms] = useState({facebook: [], instagram: []});
  const [notificationValueChanged, setNotificationValueChanged] = useState(false);
  const [hasStoreCreditValueChanged, setHasStoreCreditValueChanged] = useState(false);
  const [isBarCodeResultLoading, setIsBarCodeResultLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [settings, setSettings] = useState(settingsFromState);
  const [auctionMeta, setAuctionMeta] = useState<AuctionMetaState>({
    biddingDuration: settings?.defaultBiddingDuration ? settings?.defaultBiddingDuration / 1000 : undefined,
    minimumBidIncrement: settings?.defaultMinimumBidIncrement,
    extendedBidding: settings?.defaultExtendedBidding!,
    triggerOption: TRIGGER_TIME_OPTIONS[0].value,
    extensionTime: EXTENSION_TIME_OPTIONS[0].value,
    biddingCap: settings?.defaultBiddingCap,
  });
  const [appendLotNumber, setAppendLotNumber] = useState<boolean>(false);
  const highestLotNumber =
    streamProducts.length > 0
      ? Math.max(
          ...streamProducts
            .filter(product => product.lotNumber !== undefined)
            .map(product => Number(product.lotNumber)),
        )
      : 200;

  /* Refs */
  const scannedBarcode = useRef<string>('');
  const scrollViewRef = useRef<ScrollView>(null);
  const didMountRef = useRef(false);

  /* Variables */
  const AuctionAPI = new AuctionApi(apptileAppId, authToken);
  const instaAuthCode = queryParams.get('code');
  const instaAuthToken = queryParams.get('state') ? queryParams.get('token') : '';

  /* Callbacks */
  const onEnableNotification = () => {
    setNotificationInfo({
      title: topic,
      description: description,
      thumbnailUrl: assetSourceValue as unknown as string,
    });
  };
  const onCreateClose = () => {
    navigate(-1);
  };
  const onProductDel = (obj: any) => {
    setStreamProducts(streamProducts.filter(e => e.id != obj.id));
  };
  const onProductBulkDel = (products: any) => {
    setStreamProducts(streamProducts.filter(e => !products.includes(e.id)));
  };
  const onCollectionAdd = async (obj: any) => {
    try {
      const queryResponse = await queryRunner?.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: obj.handle,
        sortKey: 'BEST_SELLING',
        reverse: false,
        first: 250,
        productMetafields: [],
        variantMetafields: [],
      });
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetCollectionProductsQuery},
        {},
        null,
      );

      console.log(transformedData);

      const newProducts = transformedData.map((obj: any, index: any) => ({
        id: obj.id,
        url: obj.onlineStoreUrl,
        image: obj.featuredImage,
        title: obj.title,
        price: obj.maxSalePrice,
        currencyCode: 'USD',
        totalInventory: obj.totalInventory,
        collections: obj.collections.map(e => e.title).join(', '),
        startingBid: Math.ceil(obj.maxSalePrice * (defaultStartingBidPercentage / 100)).toFixed(2),
      }));
      //Filter product with null product_url
      const filteredProducts = newProducts.filter((e: any) => e.url);
      const newCollectionProducts = filteredProducts.filter((e: any) => !streamProducts.find((f: any) => f.id == e.id));
      const oosFilteredProducts = newCollectionProducts.filter((e: any) => e.totalInventory > 0);
      const nonAuctionedProducts = oosFilteredProducts
        .filter((e: any) => !e.title.includes('LOT #'))
        .map((oop: any, index: number) => ({...oop, lotNumber: highestLotNumber + index + 1}));
      //Add only new products
      if (nonAuctionedProducts.length > 0) {
        setStreamProducts([...streamProducts, ...nonAuctionedProducts]);
        setNewCollection('');
        if (oosFilteredProducts.length !== newCollectionProducts.length) {
          dispatch(
            makeToast({
              content: CREATE_STREAM.SUCCESS.COLLECTION_ADDED_AFTER_FILTERING_OOS,
              appearances: 'success',
              duration: 2000,
            }),
          );
        }
        if (nonAuctionedProducts.length !== oosFilteredProducts.length) {
          dispatch(
            makeToast({
              content: CREATE_STREAM.SUCCESS.COLLECTION_ADDED_AFTER_FILTERING_AUCTIONED,
              appearances: 'success',
              duration: 2000,
            }),
          );
        }
        if (
          newCollectionProducts.length == oosFilteredProducts.length &&
          oosFilteredProducts.length == nonAuctionedProducts.length
        ) {
          dispatch(
            makeToast({
              content: CREATE_STREAM.SUCCESS.COLLECTION_ADDED,
              appearances: 'success',
              duration: 2000,
            }),
          );
        }
      } else {
        setNewCollection('');
        dispatch(
          makeToast({
            content: CREATE_STREAM.ERROR.COLLECTION_EXIST,
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'onCollectionAdd');
    }
  };
  const onProductAdd = async (obj: any) => {
    if (!obj) {
      return;
    }

    if (!obj.onlineStoreUrl) {
      handleApiErrorWithoutNetwork(
        '',
        CREATE_STREAM.ERROR.SHOPIFY_PRODUCT_404,
        dispatch,
        'onProductAdd - obj.onlineStoreUrl',
      );
      return;
    }
    setNewProduct(obj);
  };
  const addNewProduct = async (obj: any) => {
    if (newProduct) {
      if (!streamProducts.find(e => e.id == obj.id)) {
        let productMetadata = {};
        try {
          const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
            productId: obj.id,
          });
          productMetadata = shopifyProductTransformer(queryResponse?.data?.product) ?? {};
        } catch (error) {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'addNewProduct');
        }

        if (productMetadata?.totalInventory <= 0) {
          setNewProduct('');
          return dispatch(
            makeToast({
              content: CREATE_STREAM.ERROR.OUT_OF_STOCK,
              appearances: 'warning',
              duration: 2000,
            }),
          );
        }

        if (obj.title.includes('LOT #')) {
          setNewProduct('');
          return dispatch(
            makeToast({
              content: CREATE_STREAM.ERROR.ALREADY_AUCTIONED,
              appearances: 'error',
              duration: 2000,
            }),
          );
        }

        const startingBid = Math.ceil(productMetadata?.price * (defaultStartingBidPercentage / 100)).toFixed(2);

        //Add only new products
        setStreamProducts([
          ...streamProducts,
          {
            id: obj.id,
            url: obj.onlineStoreUrl,
            image: obj.featuredImage,
            title: obj.title,
            ...productMetadata,
            startingBid,
            lotNumber: highestLotNumber + 1,
          },
        ]);
        dispatch(
          makeToast({
            content: CREATE_STREAM.SUCCESS.PRODUCT_ADDED,
            appearances: 'success',
            duration: 2000,
          }),
        );
      } else {
        handleApiErrorWithoutNetwork('', CREATE_STREAM.ERROR.PRODUCT_EXIST, dispatch, 'addNewProduct - product exist');
      }
      setNewProduct('');
    }
  };
  const createNewStream = () => {
    const platforms = [];
    if(hasStreamToPlatforms) {
      if (streamToPlatforms.facebook.length > 0) platforms.push('facebook');
      if (streamToPlatforms.instagram.length > 0) platforms.push('instagram');
    }
    dispatch(
      createStream({
        topic,
        description,
        thumbnailUrl: assetSourceValue as unknown as string,
        platforms,
        streamProducts: streamProducts.map(e => ({
          product_url: e.url,
          store_product_id: e.id?.split('/')?.pop(),
          product_name: e.title,
          product_thumbnail: e.image,
          product_price: e.maxSalePrice,
          base_bid_price: Number(e.startingBid),
          bid_incrementer: Number(auctionMeta.minimumBidIncrement),
          meta: {
            lot_number: e.lotNumber,
          }
        })).reverse(),
        fbAuthId: streamToPlatforms.facebook[0],
        instaAuthId: streamToPlatforms.instagram[0],
        scheduleDate: hasScheduled ? scheduleDate : new Date(),
        hasNotification,
        notificationInfo,
        hasStoreCredit,
        storeCreditInfo,
        allowAuction: true,
        biddingDuration: auctionMeta.biddingDuration! * 1000,
        allowExtendedBidding: auctionMeta.extendedBidding,
        extendedBiddingDuration: auctionMeta.extendedBidding ? auctionMeta.extensionTime : undefined,
        triggerExtendedBiddingDuration: auctionMeta.extendedBidding ? auctionMeta.triggerOption : undefined,
        meta: {
          appendLotNumber,
          biddingCap: auctionMeta.biddingCap
        }
      }),
    );
    if (streamId) {
      LivelyApi.apptileArchiveStream(authToken, streamId);
    }
    const productIdLotNumberMap = streamProducts.reduce((acc, curr) => {
      // For Edit stream case (If lot number already present, skip that product)
      if (!curr.title.includes('LOT #')) {
        acc[curr.id] = `LOT #${curr?.lotNumber} - ${curr.title}`;
      }
      return acc;
    }, {});
    appendLotNumber && AuctionAPI.appendLotNumberToProductPages(productIdLotNumberMap);
  };
  const deleteStream = () => {
    dispatch(displayStreamDeleteConfirm(streamId, '', onCreateClose));
  };
  const getProductMetaData = async (id: any) => {
    if (queryRunner) {
      let productMetadata = {};
      try {
        const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
          productId: id,
        });
        productMetadata = shopifyProductTransformer(queryResponse?.data?.product) ?? {};
        return productMetadata;
      } catch (error) {
        handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getProductMetaData');
      }
    }
  };
  const addNewScannedProduct = _.debounce(async (barcode, queryRunner) => {
    setIsBarCodeResultLoading(true);
    const scanningId = uuidv4();
    // dispatch(
    //   makeToast({
    //     content: 'Scanning product',
    //     appearances: 'info',
    //     id: scanningId,
    //     cancellable: false,
    //   }),
    // );
    const queryResponse = await queryRunner.runQuery('query', CollectionGqls.SEARCH_PRODUCTS, {
      first: 50,
      query: barcode,
      countryCode: 'US',
      productMetafields: [],
      variantMetafields: [],
    });
    scannedBarcode.current = '';
    const {transformedData} = processShopifyGraphqlQueryResponse(
      queryResponse,
      {transformer: TransformGetProductsPaginatedQuery},
      {},
      null,
    );
    if (transformedData.length != 0) {
      const productScanned = transformedData.find(
        (e: any) => e.handle == barcode || !!e.variants.find((f: any) => f.barcode == barcode),
      );
      if (productScanned) {
        let foundProduct = false;
        setStreamProducts(streamProducts => {
          if (streamProducts.find(e => e.id == productScanned.id)) foundProduct = true;
          return streamProducts;
        });
        if (!foundProduct) {
          if (productScanned?.totalInventory <= 0) {
            dispatch(
              makeToast({
                content: CREATE_STREAM.ERROR.OUT_OF_STOCK,
                appearances: 'warning',
                duration: 2000,
              }),
            );
          } else if (productScanned?.title?.includes('LOT #')) {
            dispatch(
              makeToast({
                content: CREATE_STREAM.ERROR.ALREADY_AUCTIONED,
                appearances: 'error',
                duration: 2000,
              }),
            );
          } else {
            dispatch(removeToast([scanningId]));
            dispatch(
              makeToast({
                content: `${productScanned.title} added to the list`,
                appearances: 'success',
              }),
            );
            const productMetadata = {
              ...productScanned,
              id: productScanned.id,
              url: productScanned.onlineStoreUrl,
              price: productScanned.maxSalePrice,
              image: productScanned.featuredImage,
              title: productScanned.title,
              collections: productScanned.collections.map(e => e.title).join(', '),
              startingBid: Math.ceil(
                productScanned.maxSalePrice * (defaultStartingBidPercentage / 100),
              ).toFixed(2),
              lotNumber: highestLotNumber + 1,
            };
            //Add only new products
            setStreamProducts(streamProducts => [...streamProducts, productMetadata]);
          }
        } else {
          dispatch(removeToast([scanningId]));
          dispatch(
            makeToast({
              content: 'Product already added to the list',
              appearances: 'error',
              duration: 2000,
            }),
          );
        }
      } else {
        dispatch(removeToast([scanningId]));
        dispatch(
          makeToast({
            content: 'Invalid barcode, please try again',
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } else {
      dispatch(removeToast([scanningId]));
      dispatch(
        makeToast({
          content: 'Invalid barcode, please try again',
          appearances: 'error',
          duration: 2000,
        }),
      );
    }
    setIsBarCodeResultLoading(false);
  }, 300);
  const handleLoginFacebook = async () => {
    window.FB.login(
      (response: any) => {
        console.log(response);
        if (response.status === 'connected') {
          LivelyApi.updateFacebookStatus(
            authToken,
            response?.authResponse?.accessToken,
            response?.authResponse?.userID,
          ).then(() => {
            dispatch(fetchFacebookPages());
          });
          dispatch(fetchFacebookPages());
        }
      },
      {
        config_id: FACEBOOK_AUTH_CONFIG_ID,
      },
    );
  };
  const handleLoginInstagram = async () => {
    window.open(
      `https://www.instagram.com/oauth/authorize?client_id=977596304173492&redirect_uri=${window.location.href}&scope=business_basic%2Cbusiness_manage_messages%2Cbusiness_manage_comments%2Cbusiness_content_publish&response_type=code&logger_id=407cd1da-4a6b-452e-be97-897cd9cd9d9b`,
      '_self',
    );
  };
  const base64EncodedUnicodeInstagram = (obj: any) => {
    const jsonString = JSON.stringify(obj);
    const textEncoder = new TextEncoder();
    const uint8Array = textEncoder.encode(jsonString);
    const base64EncodedUnicode = btoa(String.fromCharCode(...uint8Array));
    const finalBase64EncodeUrl = base64EncodedUnicode.replace(/\+/g, '-').replace(/\//g, '_').replace(/[=]+$/, '');
    return finalBase64EncodeUrl;
  };
  const checkInstaRtmpToken = async (account_handle: string) => {
    try {
      const response = await LivelyApi.checkInstagramSession(authToken, account_handle);
      if (response?.data?.token && response?.data?.access_token) {
        alert('Authentication Successfull.');
      } else {
        alert('Token is invalid, Please Login Again.');
        const instaStateParamObject = {
          user_name: account_handle,
          redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
        };
        const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
        const params = new URLSearchParams();
        params.set('state', encodedStateParam);
        setTimeout(() => {
          window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}`, '_self');
        }, 100);
      }
    } catch (error) {
      const instaStateParamObject = {
        user_name: account_handle,
        redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
      };
      const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
      const params = new URLSearchParams();
      params.set('state', encodedStateParam);
      setTimeout(() => {
        window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}`, '_self');
      }, 100);
    }
  };

  /* Variables */
  const {streamId} = params as any;
  const goLiveDisabledDueToNotification = hasNotification
    ? !notificationInfo.title || !notificationInfo.description
    : false;
  const goLiveDisabledDueToStoreCredit = hasStoreCredit ? !storeCreditInfo.creditAmount : false;
  const goLiveDisabled =
    !topic ||
    !assetSourceValue ||
    streamProducts.length === 0 ||
    goLiveDisabledDueToNotification ||
    goLiveDisabledDueToStoreCredit ||
    !auctionMeta.biddingDuration ||
    !auctionMeta.minimumBidIncrement ||
    !auctionMeta.biddingCap;
  let reScheduleDisabled;
  streamId &&
    (reScheduleDisabled =
      pastStreamInfo?.streaming_name == topic &&
      pastStreamInfo?.streaming_description == description &&
      pastStreamInfo?.streaming_thumbnail == assetSourceValue &&
      new Date(pastStreamInfo?.start_time).getTime() == scheduleDate.getTime() &&
      pastStreamInfo?.product_info.length == streamProducts.length &&
      !notificationValueChanged &&
      !hasStoreCreditValueChanged &&
      pastStreamInfo?.auction_duration == auctionMeta.biddingDuration! * 1000 &&
      pastStreamInfo?.product_info[0].bid_incrementer == auctionMeta.minimumBidIncrement &&
      pastStreamInfo?.meta?.biddingCap == auctionMeta.biddingCap &&
      pastStreamInfo?.allow_extended_bidding == auctionMeta.extendedBidding &&
      pastStreamInfo?.ext_bidding_duration == auctionMeta.extensionTime &&
      pastStreamInfo?.trigger_ext_bidding_duration == auctionMeta.triggerOption &&
      pastStreamInfo.product_info.every(p =>
        streamProducts.find(sp => sp.id.split('/').pop() == p.store_product_id && p.base_bid_price == sp.startingBid),
      ) &&
      pastStreamInfo?.meta?.appendLotNumber == appendLotNumber &&
      pastStreamInfo?.product_info?.every(p => 
        streamProducts.find(sp => sp.id.split('/').pop() == p.store_product_id && p.meta?.lot_number == sp.lotNumber)
      )
    );
  const compareDate = moment(scheduleDate).isBefore(moment()) || moment(scheduleDate).isSame(moment());

  const appIntegrations = Object.values(appIntegrationsById || []);
  const storeCreditIntegration = appIntegrations.find(e => e.integrationCode === 'storeCredit');

  /* Effects */
  useEffect(() => {
    if (_.isEmpty(settingsFromState) && authToken) {
      dispatch(fetchStreamSettings());
    }
    if (!streamId) {
      setAuctionMeta({
        ...auctionMeta,
        biddingDuration: settingsFromState?.defaultBiddingDuration
          ? settingsFromState.defaultBiddingDuration / 1000
          : undefined,
        minimumBidIncrement: settingsFromState?.defaultMinimumBidIncrement,
        extendedBidding: settingsFromState?.defaultExtendedBidding!,
      });
      setStreamToPlatforms(prev => {
        const updated = {...prev};
        if (streamToFacebookAuthId) {
          updated.facebook = [streamToFacebookAuthId];
        }
        if (streamToInstagramAuthId) {
          updated.instagram = [streamToInstagramAuthId];
        }
        return updated;
      });
    }
    setSettings(settingsFromState);
  }, [settingsFromState, authToken, dispatch, streamId]);
  useEffect(() => {
    if (apptileAppId) dispatch(fetchAppIntegrations(apptileAppId as string));
    if (instaAuthCode && authToken) {
      LivelyApi.updateInstagramStatus(authToken, instaAuthCode)
        .then(response => {
          const account_handle = response?.data?.data?.account_handle;
          if (account_handle) checkInstaRtmpToken(account_handle);
          else window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
    if (instaAuthToken && authToken && queryParams.get('user-name')) {
      LivelyApi.setInstagramToken(authToken, queryParams.get('user-name'), instaAuthToken)
        .then(response => {
          // console.log(response);
          dispatch(fetchInstagramPages());
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
  }, [apptileAppId, instaAuthCode, instaAuthToken, authToken, dispatch]);
  useEffect(() => {
    if (streamCreated) {
      navigate(LIVE_DASHBOARD_ROUTES.OVERVIEW_AUCTION());
    }
  }, [streamCreated, navigate]);
  useEffect(() => {
    if (didMountRef.current) {
      if (scrollViewRef.current && (hasNotification || hasStoreCredit || auctionMeta.extendedBidding || hasScheduled)) {
        scrollViewRef.current.scrollToEnd({animated: true});
      }
    } else {
      didMountRef.current = true;
    }
  }, [hasNotification, hasStoreCredit, auctionMeta.extendedBidding, hasScheduled]);
  useEffect(() => {
    if (!hasScheduled) {
      setScheduleDate(new Date());
    }
  }, [hasScheduled]);
  useEffect(() => {
    if (!facebookLoading && !facebookFound) {
      (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
          return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js';
        fjs.parentNode.insertBefore(js, fjs);
      })(document, 'script', 'facebook-jssdk');

      window.fbAsyncInit = function () {
        window.FB.init({
          appId: FACEBOOK_AUTH_APP_ID,
          cookie: true,
          xfbml: true,
          version: 'v19.0',
        });
        window.FB.AppEvents.logPageView();
      };
    }
  }, [appConfig, facebookFound, facebookLoading]);

  // TODO(LIVE): Handle Lot Numbers while editing
  useEffect(() => {
    // CAUTION(Don't Remove): For Edit Stream scenario
    if (streamId && authToken) {
      const getStreamInfo = () => {
        LivelyApi.getLiveStreamInfoByID(authToken, streamId)
          .then(async (streamResponse: any) => {
            const streamInfo = streamResponse?.data?.data;
            setPastStreamInfo(streamInfo);
            setTopic(streamInfo.streaming_name);
            setDescription(streamInfo.streaming_description);
            setAssetSourceValue(streamInfo.streaming_thumbnail);
            setScheduleDate(new Date(streamInfo.start_time));
            if (facebookChannels?.length == 1)
              setStreamToPlatforms(e => ({...e, facebook: [facebookChannels?.[0]?.fb_auth_id]}));
            else setStreamToPlatforms(e => ({facebook: [], instagram: []}));
            setHasScheduled(true);
            const streamProducts = await Promise.all(
              streamInfo.product_info.map(async (obj: any, index: number) => {
                const id = `gid://shopify/Product/${obj.store_product_id}`;
                const productMetadata = await getProductMetaData(id);
                return {
                  id,
                  url: obj.product_url,
                  image: obj.product_thumbnail,
                  title: obj.product_name,
                  price: (obj.product_price / 100).toFixed(2),
                  currencyCode: 'USD',
                  startingBid: obj.base_bid_price,
                  bidIncrementer: obj.bid_incrementer,
                  lotNumber: obj.meta?.lot_number,
                  ...productMetadata,
                };
              }),
            );
            setStreamProducts(streamProducts);
            setAuctionMeta({
              biddingDuration: streamInfo?.auction_duration / 1000,
              minimumBidIncrement: streamInfo?.product_info?.[0]?.bid_incrementer,
              extendedBidding: streamInfo?.allow_extended_bidding,
              triggerOption: streamInfo?.trigger_ext_bidding_duration,
              extensionTime: streamInfo?.ext_bidding_duration,
            });
            setAppendLotNumber(streamInfo?.meta?.appendLotNumber);
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') {
              setIsNetworkError(true);
            }
            handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'getStreamInfo');
          });
      };
      const apptileGetLiveMetadata = () => {
        LivelyApi.apptileGetLiveMetadata(authToken, streamId)
          .then((response: any) => {
            const responseData = response?.data?.data?.notificationResponse;

            if (responseData) {
              setNotificationInfo({
                title: responseData?.headings?.en,
                description: responseData?.contents?.en,
                thumbnailUrl: responseData?.big_picture,
              });
              setHasNotification(true);
            }
          })
          .catch(error => {
            console.log('ERROR IN FETCHING LIVE METADATA', error);
          });
      };
      const getStoreCreditInfo = () => {
        StoreCreditApi.getLiveJoinStoreCreditData(authToken, streamId)
          .then((response: any) => {
            const responseData = response?.data;
            setHasStoreCredit(true);
            setStoreCreditInfo({
              creditAmount: responseData?.creditAmount,
              creditExpiryDays: responseData?.creditExpiryDays,
              currencyCode: responseData?.currencyCode,
            });
          })
          .catch((error: any) => {
            console.error('Store credit error: ', error);
          });
      };

      getStreamInfo();
      apptileGetLiveMetadata();
      apptileAppId && getStoreCreditInfo();
    }
    document.onkeydown = function (e) {
      const textInput = e.key || String.fromCharCode(e.keyCode);
      const targetName = e.target?.localName;
      if (textInput && textInput.length === 1 && targetName !== 'input' && targetName !== 'textarea') {
        scannedBarcode.current = scannedBarcode.current + textInput;
        console.log('queryRunner1', queryRunner);
        addNewScannedProduct(scannedBarcode.current, queryRunner);
      }
    };
    return () => {
      const cleanup = async () => {
        document.onkeydown = null;
      };
      cleanup();
    };
  }, [streamId, authToken, apptileAppId, queryRunner]);
  useEffect(() => {
    if (authToken) {
      dispatch(fetchFacebookPages());
      settings?.instagram && dispatch(fetchInstagramPages());
      LivelyApi.getShopifyCreds(authToken).then(response => {
        const newQueryRunner = apolloQueryRunner();
        newQueryRunner
          .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
            return {
              headers: {
                ...headers,
                'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
              },
            };
          })
          .then(() => {
            setQueryRunner(newQueryRunner);
          })
          .then(async () => {
            await newQueryRunner.runQuery('query', GET_SHOP, {}, {}).then(res => {
              const fetchedCurrencyCode = res?.data?.shop?.paymentSettings?.currencyCode;
              setStoreCreditInfo(prev => ({...prev, currencyCode: fetchedCurrencyCode}));
              setCurrencyCode(() => fetchedCurrencyCode);
            });
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') {
              setIsNetworkError(true);
            }
            handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getShopifyCreds', false);
          });
      });
    }
  }, [authToken]);

  /* Animations */
  const {animatedStyle} = useMountTransitionAnimation({direction: 'y'});

  return isNetworkError ? (
    <NetworkErrorPage />
  ) : (
    <Animated.View style={[styles.wrapper, animatedStyle]}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
        <View style={{flexDirection: 'row', gap: 20, alignItems: 'center'}}>
          <Back />
          <DeleteStreamConfirmationModal />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 14,
              borderWidth: 1,
              borderColor: theme.PRIMARY,
              borderRadius: 12,
              paddingHorizontal: 14,
              paddingVertical: 4,
              backgroundColor: 'rgb(217,234,255)',
            }}>
            <Icon name="gavel" iconType="MaterialIcons" size={14} color={theme.PRIMARY} />
            <TextElement fontSize="sm" fontWeight="500" color="PRIMARY">
              {streamId ? 'Edit' : 'Create'} Auction
            </TextElement>
          </View>
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', gap: 10}}>
          {streamId ? (
            <Button
              containerStyles={{paddingHorizontal: 14, paddingVertical: 6}}
              color={'CTA'}
              loading={streamCreationInProgress}
              onPress={createNewStream}
              disabled={goLiveDisabled || reScheduleDisabled}>
              Save
            </Button>
          ) : (
            <Button
              containerStyles={{paddingHorizontal: 14, paddingVertical: 6}}
              color={'CTA'}
              onPress={createNewStream}
              disabled={goLiveDisabled}
              loading={streamCreationInProgress}>
              {compareDate ? 'Go Live' : 'Schedule'}
            </Button>
          )}
          {streamId && (
            <Button
              containerStyles={{
                borderColor: '#D92626',
                backgroundColor: 'transparent',
                paddingHorizontal: 14,
                paddingVertical: 6,
              }}
              textStyles={{color: '#D92626'}}
              onPress={deleteStream}
              disabled={goLiveDisabled}>
              Delete
            </Button>
          )}
        </View>
      </View>

      <View style={[styles.mainWrapper]}>
        <ScrollView ref={scrollViewRef} contentContainerStyle={[styles.section, {flex: 1}]}>
          <View style={[styles.backgroundColorWhite]}>
            <View style={styles.sectionHeaderWrapper}>
              <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                Add auction details
              </TextElement>
            </View>
            <View style={[styles.sectionBody]}>
              <View style={styles.paddingTop12}>
                <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
                  Topic*
                </TextElement>
                <CodeInputControlV2 placeholder="Topic" value={topic} onChange={setTopic} />
              </View>
              <View style={styles.paddingTop12}>
                <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
                  Description
                </TextElement>
                <CodeInputControlV2 placeholder="Description" value={description} onChange={setDescription} />
              </View>
              <View style={[styles.paddingTop12]}>
                <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 6}}>
                  Thumbnail*
                </TextElement>
                <View style={{width: 100}}>
                  <LivelyImageUploader assetSourceValue={assetSourceValue} setAssetSourceValue={setAssetSourceValue} />
                </View>
              </View>
              <AuctionMeta auctionMeta={auctionMeta} setAuctionMeta={setAuctionMeta} currencyCode={currencyCode} />
              <View>
                <View
                  style={[
                    styles.rowLayout,
                    styles.paddingTop12,
                    {flex: 1, justifyContent: 'space-between', alignItems: 'center'},
                  ]}>
                  <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">
                    Schedule stream
                  </TextElement>
                  <View>
                    <CheckboxControl label="" value={hasScheduled} onChange={() => setHasScheduled(!hasScheduled)} />
                  </View>
                </View>
                {hasScheduled && (
                  <View style={{marginTop: 12}}>
                    <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">
                      Pick a time
                    </TextElement>
                    <DateAndTimeControl
                      value={scheduleDate}
                      onChange={(value: string): void => setScheduleDate(new Date(value))}
                    />
                  </View>
                )}
              </View>
            </View>
          </View>

          <View style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
            <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
              <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                Stream to other platforms
              </TextElement>
              <View>
                <CheckboxControl
                  label=""
                  value={hasStreamToPlatforms}
                  onChange={value => setHasStreamToPlatforms(value)}
                />
              </View>
            </View>

            {hasStreamToPlatforms && (
              <View style={[styles.paddingTop12]}>
                <View>
                  <Text style={[commonStyles.baseText, styles.labelText]}>Stream to facebook</Text>
                </View>
                <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 10}}>
                  {facebookLoading && (
                    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
                  )}
                  {!facebookLoading && !facebookFound && (
                    <View>
                      <Text>No facebook pages are linked. </Text>
                      <Pressable onPress={handleLoginFacebook} style={styles.facebookButton}>
                        <Text style={{color: '#fff'}}>Connect with facebook </Text>
                        <Icon color={'#fff'} name="facebook" size={20} iconType={'MaterialCommunityIcons'} />
                      </Pressable>
                    </View>
                  )}
                  {!facebookLoading &&
                    facebookFound &&
                    facebookChannels.map((e: any) => (
                      <Pressable
                        key={e.fb_auth_id}
                        onPress={() =>
                          streamToPlatforms.facebook?.includes(e.fb_auth_id)
                            ? setStreamToPlatforms(platforms => ({...platforms, facebook: []}))
                            : setStreamToPlatforms(platforms => ({...platforms, facebook: [e.fb_auth_id]}))
                        }
                        style={{
                          width: 80,
                          overflow: 'hidden',
                          justifyContent: 'center',
                          alignItems: 'center',
                          borderRadius: 8,
                          backgroundColor: streamToPlatforms.facebook?.includes(e.fb_auth_id)
                            ? '#D2E3FC'
                            : theme.CONTROL_BORDER,
                          padding: 4,
                        }}>
                        <View>
                          <Image
                            source={{uri: e.fb_page.profile_picture}}
                            style={{width: 60, height: 60, borderRadius: 80}}
                            resizeMode="cover"
                          />
                          <Image
                            source={require('@/root/web/assets/images/logos_facebook.png')}
                            style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                            resizeMode="contain"
                          />
                        </View>
                        <Text
                          style={{
                            lineBreak: 'anywhere',
                            textAlign: 'center',
                            fontSize: 12,
                            marginTop: 10,
                          }}>
                          {e.fb_page.name}
                        </Text>
                      </Pressable>
                    ))}
                </View>
                {settings?.instagram && (
                  <View style={{marginTop: 20}}>
                    <View>
                      <Text style={[commonStyles.baseText, styles.labelText]}>Stream to instagram</Text>
                    </View>
                    <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 10}}>
                      {instagramLoading && (
                        <Image
                          source={require('@/root/web/assets/images/preloader.svg')}
                          style={{width: 50, height: 50}}
                        />
                      )}
                      {!instagramLoading && (!instagramFound || instagramChannels.length < 1) && (
                        <View>
                          <Text>No instagram pages are linked. </Text>
                          <Pressable onPress={handleLoginInstagram} style={styles.instagramButton}>
                            <Text style={{color: '#fff'}}>Connect with instagram </Text>
                            <Icon color={'#fff'} name="instagram" size={20} iconType={'MaterialCommunityIcons'} />
                          </Pressable>
                        </View>
                      )}
                      {!instagramLoading &&
                        instagramFound &&
                        instagramChannels.map((e: any) => (
                          <Pressable
                            onPress={() =>
                              streamToPlatforms.instagram?.includes(e._id)
                                ? setStreamToPlatforms(platforms => ({...platforms, instagram: []}))
                                : setStreamToPlatforms(platforms => ({...platforms, instagram: [e._id]}))
                            }
                            style={{
                              width: 80,
                              overflow: 'hidden',
                              alignItems: 'center',
                              borderRadius: 8,
                              backgroundColor: streamToPlatforms.instagram?.includes(e._id)
                                ? '#D2E3FC'
                                : theme.CONTROL_BORDER,
                              padding: 4,
                            }}>
                            <View>
                              {e.profile_url ? (
                                <Image
                                  source={{uri: e.profile_url}}
                                  style={{width: 60, height: 60, borderRadius: 80}}
                                  resizeMode="cover"
                                />
                              ) : (
                                <View
                                  style={{
                                    width: 60,
                                    height: 60,
                                    borderRadius: 80,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: '#fff',
                                  }}>
                                  <Text style={[commonStyles.baseText, {fontSize: 24}]}>
                                    {(e?.account_handle?.[0] || '').toUpperCase()}
                                  </Text>
                                </View>
                              )}
                              <Image
                                source={require('@/root/web/assets/images/logos_instagram.png')}
                                style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                                resizeMode="contain"
                              />
                            </View>
                            <Text
                              style={{
                                lineBreak: 'anywhere',
                                textAlign: 'center',
                                fontSize: 12,
                                marginTop: 10,
                              }}>
                              {e.account_handle}
                            </Text>
                          </Pressable>
                        ))}
                    </View>
                  </View>
                )}
              </View>
            )}

          </View>

          <View style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
            <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
              <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                Notify users on going live
              </TextElement>
              <View>
                <CheckboxControl
                  label=""
                  value={hasNotification}
                  onChange={value => {
                    setHasNotification(value);
                    if (value) {
                      onEnableNotification();
                    }
                  }}
                />
              </View>
            </View>

            {hasNotification && (
              <View>
                {hasScheduled && <TextElement>(*It will be scheduled for the time mentioned above)</TextElement>}
                <View style={styles.notificationFieldWrapper}>
                  <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
                    Topic*
                  </TextElement>
                  <CodeInputControlV2
                    placeholder="Notification Topic"
                    value={notificationInfo.title!}
                    onChange={value => {
                      setNotificationInfo({...notificationInfo, title: value});
                      setNotificationValueChanged(true);
                    }}
                  />
                </View>
                <View style={styles.notificationFieldWrapper}>
                  <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
                    Description*
                  </TextElement>
                  <CodeInputControlV2
                    placeholder="Notification Description"
                    value={notificationInfo.description!}
                    onChange={value => {
                      setNotificationInfo({...notificationInfo, description: value});
                      setNotificationValueChanged(true);
                    }}
                  />
                </View>
                <View style={styles.notificationFieldWrapper}>
                  <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 6}}>
                    Thumbnail
                  </TextElement>
                  <View style={{width: 100}}>
                    <LivelyImageUploader
                      assetSourceValue={
                        notificationInfo.thumbnailUrl
                          ? {
                              uri: notificationInfo.thumbnailUrl,
                            }
                          : null
                      }
                      setAssetSourceValue={value => {
                        setNotificationInfo({...notificationInfo, thumbnailUrl: value as unknown as string});
                        setNotificationValueChanged(true);
                      }}
                    />
                  </View>
                </View>
              </View>
            )}
          </View>

          {storeCreditIntegration && (
            <View style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
              <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
                <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                  Give store credit for joining live
                </TextElement>
                <View>
                  <CheckboxControl
                    label=""
                    value={hasStoreCredit}
                    onChange={value => {
                      setHasStoreCredit(value);
                    }}
                  />
                </View>
              </View>

              {hasStoreCredit && (
                <View>
                  <View style={styles.notificationFieldWrapper}>
                    <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">
                      Store Credit Amount*
                    </TextElement>
                    <View style={styles.currencyContainer}>
                      <TextInput
                        value={storeCreditInfo.creditAmount}
                        keyboardType="numeric"
                        onChangeText={value => {
                          setStoreCreditInfo({...storeCreditInfo, creditAmount: Number(value)});
                          setHasStoreCreditValueChanged(true);
                        }}
                      />
                      <TextElement>{currencyCode}</TextElement>
                    </View>
                  </View>
                  <View style={styles.notificationFieldWrapper}>
                    <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">
                      Store Credit Expiry (In days)
                    </TextElement>
                    <TextInput
                      keyboardType="numeric"
                      value={storeCreditInfo.creditExpiryDays}
                      onChangeText={value => {
                        setStoreCreditInfo({...storeCreditInfo, creditExpiryDays: Number(value)});
                        setHasStoreCreditValueChanged(true);
                      }}
                    />
                  </View>
                </View>
              )}
            </View>
          )}

          <View style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
            <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
              <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                Append lot number to product pages
              </TextElement>
              <View>
                <CheckboxControl label="" value={appendLotNumber} onChange={value => setAppendLotNumber(value)} />
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={[styles.section, {flex: 3, overflow: 'hidden'}, styles.backgroundColorWhite]}>
          <View style={styles.sectionHeaderWrapper}>
            <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
              Add items
            </TextElement>
            <InfoTooltip tooltip="Search or scan items to add products" position="right" />
          </View>
          {isBarCodeResultLoading && (
            <View
              style={{
                flex: 1,
                width: '100%',
                height: '100%',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                position: 'absolute',
                zIndex: 2,
                gap: 16,
                marginTop: 37,
              }}>
              <Image style={{width: 30, height: 30}} source={require('@/root/web/assets/images/preloader.svg')} />
              <TextElement color="EDITOR_LIGHT_BLACK">Scanning Items</TextElement>
            </View>
          )}
          <View style={[styles.sectionBody]}>
            <View style={[styles.rowLayout]}>
              <ShopifyProductCollectionPicker
                onCollectionChange={setNewCollection}
                collectionHandle={newCollection?.handle}
                productHandle={newProduct?.handle}
                onProductChange={onProductAdd}
                queryRunner={queryRunner}
              />
              <View>
                <Button
                  containerStyles={{flexGrow: 0, flexShrink: 0}}
                  onPress={() => {
                    newProduct ? addNewProduct(newProduct) : onCollectionAdd(newCollection);
                  }}>
                  Add
                </Button>
              </View>
            </View>
            <View style={{flex: 1, marginTop: 26}}>
              {streamProducts.length > 0 ? (
                <ProductTable
                  appendLotNumber={appendLotNumber}
                  items={streamProducts}
                  setItems={setStreamProducts}
                  onProductDel={onProductDel}
                  onProductBulkDel={onProductBulkDel}
                />
              ) : (
                <View
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                  }}>
                  <Image
                    style={styles.emptyImage}
                    source={require('@/root/web/assets/images/snapshot-no-result-barcode.png')}
                  />
                  <TextElement fontSize="xs" fontWeight="400" color="SECONDARY">
                    <TextElement fontWeight="800">Search</TextElement> or{' '}
                    <TextElement fontWeight="800">Scan</TextElement> to add products to your livestream
                  </TextElement>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  emptyImage: {width: 150, height: 150},
  createStreamHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },
  currencyContainer: {
    justifyContent: 'center',
  },
  currency: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  labelText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000',
    marginBottom: 10,
  },
  notificationFieldWrapper: {
    flexDirection: 'column',
    marginTop: 12,
  },
  sectionHeaderWrapper: {
    borderBottomColor: '#ccc',
    borderBottomWidth: 0.5,
    paddingHorizontal: 25,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sectionBody: {
    paddingHorizontal: 25,
    paddingVertical: 10,
    flex: 1,
    overflow: 'scroll',
  },
  paddingTop12: {
    paddingTop: 12,
  },
  section: {
    height: '100%',
  },
  backgroundColorWhite: {
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  rowLayout: {
    flexDirection: 'row',
    gap: 20,
  },
  alignCenter: {
    alignItems: 'center',
  },
  wrapper: {
    padding: 30,
    width: '100%',
    gap: 10,
  },
  mainWrapper: {
    display: 'grid',
    gridTemplateColumns: '0.4fr 0.6fr',
    gap: 10,
    flex: 1,
    marginTop: 10,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
  facebookButton: {
    padding: 5,
    marginTop: 10,
    backgroundColor: '#0766FF',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  dropDownTrigger: {
    backgroundColor: theme.INPUT_BACKGROUND,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    borderRadius: 8,
    width: 200,
    // marginTop: 10
  },
  instagramButton: {
    padding: 5,
    marginTop: 10,
    backgroundColor: '#ff2255',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
});
