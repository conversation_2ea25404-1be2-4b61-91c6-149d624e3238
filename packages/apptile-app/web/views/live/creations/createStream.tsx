import React, {useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, View, Image, Text, ScrollView, Animated} from 'react-native';
import CodeInputControlV2 from '../../../components/controls-v2/CodeInputControl';
import {Icon, LocalStorage, selectAppConfig} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import commonStyles from '../../../styles-v2/commonStyles';
import {
  createLiveMarketingPage,
  createStream,
  displayStreamDeleteConfirm,
  fetchFacebookPages,
  fetchInstagramPages,
  fetchStreamSettings,
  NotificationInfo,
} from '../../../actions/liveSellingActions';
import theme from '../../../styles-v2/theme';
import Button from '../../../components-v2/base/Button';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {LivelyApi} from '../../../api/LivelyApi';
import {FACEBOOK_AUTH_APP_ID, FACEBOOK_AUTH_CONFIG_ID} from '../../../../.env.json';
import DateAndTimeControl from '../../../components/controls/DateAndTimeControl';
import {makeToast, removeToast} from '../../../actions/toastActions';
import {shopifyProductTransformer} from '../utils/transformers';
import Tooltip from '../../../components-v2/base/SimpleTooltip';
import moment from 'moment';
import {
  TransformGetCollectionProductsQuery,
  TransformGetProductsPaginatedQuery,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import CheckboxControl from '../../../components/controls/CheckboxControl';
import _ from 'lodash';
import apolloQueryRunner from '@/root/app/plugins/datasource/ApolloWrapper/model';
import {useNavigate, useParams} from '../../../routing.web';
import {useSearchParams} from 'react-router-dom';
import {ShopifyProductCollectionPicker} from '../../../integrations/shopify/components/ShopifyProductCollectionPicker';
import ProductTable from './productsTable';
import TextInput from '../../../components-v2/base/TextInput';
import {LivelyImageUploader} from './livelyImageUploader';
import LiveStreamHeader from '../dashboardV2/LiveStreamHeader';
import {v4 as uuidv4} from 'uuid';
import StoreCreditApi from '../../../api/StoreCreditApis';
import {GET_SHOP} from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/shopDetails';
import {ICreateStoreCreditLiveJoinEventRuleData} from '../../../api/ApiTypes';
import {fetchAppIntegrations} from '../../../actions/editorActions';
import {EditorRootState} from '../../../store/EditorRootState';
import {handleApiError, handleApiErrorWithoutNetwork} from '../shared/CommonError';
import {CREATE_STREAM} from '../../../../app/common/utils/apiErrorMessages/specificFeature';
import {COMMON_ERROR_MESSAGE} from '../../../../app/common/utils/apiErrorMessages/generalMessages';
import NetworkErrorPage from '../shared/NetworkErrorPage';
import {DeleteStreamConfirmationModal} from '../modals/deleteStreamConfirmationModal';
import { useMountTransitionAnimation } from '../utils/useMountTransitionAnimation';
import TextElement from '@/root/web/components-v2/base/TextElement';
import Back from '../shared/Back';
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes';
import AiNotificationCreator from '../../notificationAdmin/aiNotificationCreator';

export const CreateStream = () => {
  const dispatch = useDispatch();
  const params = useParams();
  const {streamId} = params;
  const facebookLoading = useSelector(state => state.liveSelling.facebookChannelLoading);
  const facebookFound = useSelector(state => state.liveSelling.facebookChannelFound);
  const facebookChannels = useSelector(state => state.liveSelling.facebookChannelAuths);
  const isFacebookConnectionError = useSelector(state => state.liveSelling.isFacebookConnectionNetworkError);
  const instagramLoading = useSelector(state => state.liveSelling.instagramChannelLoading);
  const instagramFound = useSelector(state => state.liveSelling.instagramChannelFound);
  const instagramChannels = useSelector(state => state.liveSelling.instagramChannelAuths);
  const isInstagramConnectionError = useSelector(state => state.liveSelling.isInstagramConnectionNetworkError);
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const streamCreated = useSelector(state => state.liveSelling.streamCreated);
  const companyId = useSelector(state => state.liveSelling?.auth?.livelyUser?.company_id);
  const streamCreationInProgress = useSelector(state => state.liveSelling.streamCreationInProgress);
  const streamToFacebookAuthId = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data?.streamToFacebookAuthId);
  const streamToInstagramAuthId = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data?.streamToInstagramAuthId);

  const navigate = useNavigate();
  const [topic, setTopic] = useState('');
  const [description, setDescription] = useState('');
  const [newProduct, setNewProduct] = useState('');
  const [newCollection, setNewCollection] = useState('');
  const [assetSourceValue, setAssetSourceValue] = useState(null);
  const [streamToPlatforms, setStreamToPlatforms] = useState({facebook: [], instagram: []});
  const [streamProducts, setStreamProducts] = useState<any[]>([]);
  const [scheduleDate, setScheduleDate] = useState(new Date());
  const appConfig = useSelector(selectAppConfig);
  const [queryRunner, setQueryRunner] = useState(null);
  const [hasScheduled, setHasScheduled] = useState(false);
  const [pastStreamInfo, setPastStreamInfo] = useState<any>({});
  const [hasNotification, setHasNotification] = useState(false);
  const [hasStoreCredit, setHasStoreCredit] = useState(false);
  const [notificationInfo, setNotificationInfo] = useState<NotificationInfo | {}>({});
  const [storeCreditInfo, setStoreCreditInfo] = useState<ICreateStoreCreditLiveJoinEventRuleData>({});
  const [currencyCode, setCurrencyCode] = useState(null);
  const [notificationValueChanged, setNotificationValueChanged] = useState(false);
  const [hasStoreCreditValueChanged, setHasStoreCreditValueChanged] = useState(false);
  const [isBarCodeResultLoading, setIsBarCodeResultLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [isInstagramEnabled, setIsInstagramEnabled] = useState(false);
  const queryParams = new URLSearchParams(location.search);
  const instaAuthCode = queryParams.get('code');
  const instaAuthToken = queryParams.get('state') ? queryParams.get('token') : '';
  const [record, setRecord] = useState({
    title: '',
    body: '',
  });
  useEffect(() => {
    setNotificationInfo({...notificationInfo, title: record.title, description: record.body});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [record]);
  const handleAiNotificationConfirm = () => {
    // setChangedAt(Date.now());
  };
  const handleChange = (key: keyof typeof record, value: any) => {
    setRecord(prev => ({
      ...prev,
      [key]: value,
    }));
  };
  const onEnableNotification = () => {
    setNotificationInfo({
      title: topic,
      description: description,
      thumbnailUrl: assetSourceValue as unknown as string,
    });
  };

  const onProductDel = (obj: any) => {
    setStreamProducts(streamProducts.filter(e => e.id != obj.id));
  };
  const onProductBulkDel = products => {
    setStreamProducts(streamProducts.filter(e => !products.includes(e.id)));
  };

  //Get app id from from query param
  const [searchParams, setSearchParams] = useSearchParams();
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);

  const onCollectionAdd = async (obj: any) => {
    try {
      const queryResponse = await queryRunner.runQuery('query', CollectionGqls.GET_COLLECTION_HANDLE_PRODUCTS, {
        collectionHandle: obj.handle,
        sortKey: 'BEST_SELLING',
        reverse: false,
        first: 250,
        productMetafields: [],
        variantMetafields: [],
      });
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetCollectionProductsQuery},
        {},
        null,
      );

      const newProducts = transformedData.map((obj: any) => ({
        id: obj.id,
        url: obj.onlineStoreUrl,
        image: obj.featuredImage,
        title: obj.title,
        price: obj.minPrice,
        currencyCode: 'USD',
        totalInventory: obj.totalInventory,
        collections: obj.collections.map(e => e.title).join(', '),
      }));
      //Filter product with null product_url
      const filteredProducts = newProducts.filter(e => e.url);
      const newCollectionProducts = filteredProducts.filter(e => !streamProducts.find(f => f.id == e.id));
      //Add only new products
      if (newCollectionProducts.length > 0) {
        setStreamProducts([...streamProducts, ...newCollectionProducts]);
        setNewCollection('');
        dispatch(
          makeToast({
            content: CREATE_STREAM.SUCCESS.COLLECTION_ADDED,
            appearances: 'success',
            duration: 2000,
          }),
        );
      } else {
        setNewCollection('');
        dispatch(
          makeToast({
            content: CREATE_STREAM.ERROR.COLLECTION_EXIST,
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'onCollectionAdd');
    }
  };

  const onProductAdd = async (obj: any) => {
    if (!obj) {
      return;
    }
    if (!obj.onlineStoreUrl) {
      handleApiErrorWithoutNetwork(
        '',
        CREATE_STREAM.ERROR.SHOPIFY_PRODUCT_404,
        dispatch,
        'onProductAdd - obj.onlineStoreUrl',
      );
      return;
    }
    setNewProduct(obj);
  };

  const addNewProduct = async (obj: any) => {
    if (newProduct) {
      if (!streamProducts.find(e => e.id == obj.id)) {
        let productMetadata = {};
        try {
          const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
            productId: obj.id,
          });
          productMetadata = shopifyProductTransformer(queryResponse?.data?.product) ?? {};
        } catch (error) {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'addNewProduct');
        }
        //Add only new products
        setStreamProducts([
          ...streamProducts,
          {
            id: obj.id,
            url: obj.onlineStoreUrl,
            image: obj.featuredImage,
            title: obj.title,
            ...productMetadata,
          },
        ]);
        dispatch(
          makeToast({
            content: CREATE_STREAM.SUCCESS.PRODUCT_ADDED,
            appearances: 'success',
            duration: 2000,
          }),
        );
      } else {
        handleApiErrorWithoutNetwork('', CREATE_STREAM.ERROR.PRODUCT_EXIST, dispatch, 'addNewProduct - product exist');
      }
      setNewProduct('');
    }
  };

  useEffect(() => {
    if (streamCreated) {
      if (companyId) {
        dispatch(createLiveMarketingPage({companyId}));
      }
      navigate(LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE());
    }
  }, [streamCreated, navigate, dispatch, companyId]);

  useEffect(() => {
    if (authToken) {
      LivelyApi.getShopifyCreds(authToken).then(response => {
        const newQueryRunner = apolloQueryRunner();
        newQueryRunner
          .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
            return {
              headers: {
                ...headers,
                'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
              },
            };
          })
          .then(async () => {
            await newQueryRunner.runQuery('query', GET_SHOP, {}, {}).then(res => {
              const fetchedCurrencyCode = res?.data?.shop?.paymentSettings?.currencyCode;
              setStoreCreditInfo(prev => ({...prev, currencyCode: fetchedCurrencyCode}));
              setCurrencyCode(() => fetchedCurrencyCode);
            });
          });
      });
    }
  }, [authToken]);

  const createNewStream = () => {
    const platforms = [];
    if (streamToPlatforms.facebook.length > 0) platforms.push('facebook');
    if (streamToPlatforms.instagram.length > 0) platforms.push('instagram');
    dispatch(
      createStream({
        topic,
        description,
        thumbnailUrl: assetSourceValue as unknown as string,
        platforms,
        streamProducts: streamProducts.map(e => ({
          product_url: e.url,
          store_product_id: e.id?.split('/')?.pop(),
          product_name: e.title,
          product_thumbnail: e.image,
        })).reverse(),
        fbAuthId: streamToPlatforms.facebook[0],
        scheduleDate: hasScheduled ? scheduleDate : new Date(),
        hasNotification,
        notificationInfo,
        hasStoreCredit,
        storeCreditInfo,
        instaAuthId: streamToPlatforms.instagram[0],
      }),
    );
    if (streamId) {
      LivelyApi.apptileArchiveStream(authToken, streamId);
    }
  };

  const deleteStream = () => {
    dispatch(displayStreamDeleteConfirm(streamId, '', () => navigate(LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE())));
  };

  const handleLoginFacebook = async () => {
    window.FB.login(
      (response: any) => {
        console.log(response);
        if (response.status === 'connected') {
          LivelyApi.updateFacebookStatus(
            authToken,
            response?.authResponse?.accessToken,
            response?.authResponse?.userID,
          ).then(() => {
            dispatch(fetchFacebookPages());
          });
          dispatch(fetchFacebookPages());
        }
      },
      {
        config_id: FACEBOOK_AUTH_CONFIG_ID,
      },
    );
  };

  const checkIfNewFeaturesEnabled = async () => {
    const response = await LivelyApi.getCompanyInfo(authToken);
    if (response?.data?.data?.meta?.instagram) {
      setIsInstagramEnabled(true);
      dispatch(fetchInstagramPages());
    }
  };

  const handleLoginInstagram = async () => {
    window.open(
      `https://www.instagram.com/oauth/authorize?client_id=977596304173492&redirect_uri=${window.location.href}&scope=business_basic%2Cbusiness_manage_messages%2Cbusiness_manage_comments%2Cbusiness_content_publish&response_type=code&logger_id=407cd1da-4a6b-452e-be97-897cd9cd9d9b`,
      '_self',
    );
  };

  const base64EncodedUnicodeInstagram = (obj: any) => {
    const jsonString = JSON.stringify(obj);
    const textEncoder = new TextEncoder();
    const uint8Array = textEncoder.encode(jsonString);
    const base64EncodedUnicode = btoa(String.fromCharCode(...uint8Array));
    const finalBase64EncodeUrl = base64EncodedUnicode.replace(/\+/g, '-').replace(/\//g, '_').replace(/[=]+$/, '');
    return finalBase64EncodeUrl;
  };

  const checkInstaRtmpToken = async (account_handle: string) => {
    try {
      const response = await LivelyApi.checkInstagramSession(authToken, account_handle);
      if (response?.data?.token && response?.data?.access_token) {
        alert('Authentication Successfull.');
      } else {
        alert('Token is invalid, Please Login Again.');
        const instaStateParamObject = {
          user_name: account_handle,
          redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
        };
        const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
        const params = new URLSearchParams();
        params.set('state', encodedStateParam);
        setTimeout(() => {
          window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}`, '_self');
        }, 100);
      }
    } catch (error) {
      const instaStateParamObject = {
        user_name: account_handle,
        redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
      };
      const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
      const params = new URLSearchParams();
      params.set('state', encodedStateParam);
      setTimeout(() => {
        window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}`, '_self');
      }, 100);
    }
  };

  const getProductMetaData = async (id: any) => {
    if (queryRunner) {
      let productMetadata = {};
      try {
        const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
          productId: id,
        });
        productMetadata = shopifyProductTransformer(queryResponse?.data?.product) ?? {};
        return productMetadata;
      } catch (error) {
        handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getProductMetaData');
      }
    }
  };

  const addNewScannedProduct = _.debounce(async (barcode, queryRunner) => {
    setIsBarCodeResultLoading(true);
    const scanningId = uuidv4();
    // dispatch(
    //   makeToast({
    //     content: 'Scanning product',
    //     appearances: 'info',
    //     id: scanningId,
    //     cancellable: false,
    //   }),
    // );
    const queryResponse = await queryRunner.runQuery('query', CollectionGqls.SEARCH_PRODUCTS, {
      first: 50,
      query: barcode,
      countryCode: 'US',
      productMetafields: [],
      variantMetafields: [],
    });
    scannedBarcode.current = '';
    const {transformedData} = processShopifyGraphqlQueryResponse(
      queryResponse,
      {transformer: TransformGetProductsPaginatedQuery},
      {},
      null,
    );
    if (transformedData.length != 0) {
      const productScanned = transformedData.find(
        (e: any) => e.handle == barcode || !!e.variants.find((f: any) => f.barcode == barcode),
      );
      if (productScanned) {
        let foundProduct = false;
        setStreamProducts(streamProducts => {
          if (streamProducts.find(e => e.id == productScanned.id)) foundProduct = true;
          return streamProducts;
        });
        if (!foundProduct) {
          dispatch(removeToast([scanningId]));
          dispatch(
            makeToast({
              content: `${productScanned.title} added to the list`,
              appearances: 'success',
            }),
          );
          const productMetadata = {
            ...productScanned,
            id: productScanned.id,
            url: productScanned.onlineStoreUrl,
            price: productScanned.minPrice,
            image: productScanned.featuredImage,
            title: productScanned.title,
            collections: productScanned.collections.map(e => e.title).join(', '),
          };
          //Add only new products
          setStreamProducts(streamProducts => [...streamProducts, productMetadata]);
        } else {
          dispatch(removeToast([scanningId]));
          dispatch(
            makeToast({
              content: 'Product already added to the list',
              appearances: 'error',
              duration: 2000,
            }),
          );
        }
      } else {
        dispatch(removeToast([scanningId]));
        dispatch(
          makeToast({
            content: 'Invalid barcode, please try again',
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } else {
      dispatch(removeToast([scanningId]));
      dispatch(
        makeToast({
          content: 'Invalid barcode, please try again',
          appearances: 'error',
          duration: 2000,
        }),
      );
    }
    setIsBarCodeResultLoading(false);
  }, 300);

  const scannedBarcode = useRef('');

  useEffect(() => {
    if (authToken) {
      dispatch(fetchFacebookPages());
      checkIfNewFeaturesEnabled();

      LivelyApi.getShopifyCreds(authToken).then(response => {
        const newQueryRunner = apolloQueryRunner();
        newQueryRunner
          .initClient(`https://${response?.data?.data?.store_name}/api/2024-10/graphql.json`, (_, {headers}) => {
            return {
              headers: {
                ...headers,
                'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
              },
            };
          })
          .then(async () => {
            setQueryRunner(newQueryRunner);
            await newQueryRunner.runQuery('query', GET_SHOP, {}, {}).then(res => {
              const fetchedCurrencyCode = res?.data?.shop?.paymentSettings?.currencyCode;
              setStoreCreditInfo(prev => ({...prev, currencyCode: fetchedCurrencyCode}));
              setCurrencyCode(() => fetchedCurrencyCode);
            });
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') {
              setIsNetworkError(true);
            }
            handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getShopifyCreds', false);
          });
      });
    }
  }, [authToken, dispatch]);

  useEffect(() => {
    // CAUTION: For Edit Stream scenario
    if (streamId) {
      const getStreamInfo = () => {
        LivelyApi.getLiveStreamInfoByID(authToken, streamId)
          .then(async (streamResponse: any) => {
            const streamInfo = streamResponse?.data?.data;
            setPastStreamInfo(streamInfo);
            setTopic(streamInfo.streaming_name);
            setDescription(streamInfo.streaming_description);
            setAssetSourceValue(streamInfo.streaming_thumbnail);
            setScheduleDate(new Date(streamInfo.start_time));
            if (facebookChannels?.length == 1)
              setStreamToPlatforms(e => ({...e, facebook: [facebookChannels?.[0]?.fb_auth_id]}));
            else setStreamToPlatforms(e => ({facebook: [], instagram: []}));
            setHasScheduled(true);
            const streamProducts = await Promise.all(
              streamInfo.product_info.map(async (obj: any) => {
                const id = `gid://shopify/Product/${obj.store_product_id}`;
                const productMetadata = await getProductMetaData(id);
                return {
                  id,
                  url: obj.product_url,
                  image: obj.product_thumbnail,
                  title: obj.product_name,
                  price: (obj.product_price / 100).toFixed(1),
                  currencyCode: 'USD',
                  ...productMetadata,
                };
              }),
            );
            setStreamProducts(streamProducts);
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') {
              setIsNetworkError(true);
            }
            handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'getStreamInfo');
          });
      };
      getStreamInfo();
    }
    document.onkeypress = function (e) {
      const textInput = e.key || String.fromCharCode(e.keyCode);
      const targetName = e.target.localName;
      if (textInput && textInput.length === 1 && targetName !== 'input' && targetName !== 'textarea') {
        scannedBarcode.current = scannedBarcode.current + textInput;
        addNewScannedProduct(scannedBarcode.current, queryRunner);
      }
    };
    return async () => {
      document.onkeypress = null;
    };
  }, [queryRunner]);

  useEffect(() => {
    if (!facebookLoading && !facebookFound) {
      (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
          return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js';
        fjs.parentNode.insertBefore(js, fjs);
      })(document, 'script', 'facebook-jssdk');

      window.fbAsyncInit = function () {
        window.FB.init({
          appId: FACEBOOK_AUTH_APP_ID,
          cookie: true,
          xfbml: true,
          version: 'v19.0',
        });
        window.FB.AppEvents.logPageView();
      };
    }
  }, [appConfig, facebookFound, facebookLoading]);

  const goLiveDisabledDueToNotification = hasNotification
    ? !notificationInfo.title || !notificationInfo.description
    : false;
  const goLiveDisabledDueToStoreCredit = hasStoreCredit ? !storeCreditInfo.creditAmount : false;
  const goLiveDisabled =
    !topic ||
    !assetSourceValue ||
    // streamProducts.length === 0 ||
    goLiveDisabledDueToNotification ||
    goLiveDisabledDueToStoreCredit;
  let reScheduleDisabled;
  streamId &&
    (reScheduleDisabled =
      pastStreamInfo?.streaming_name == topic &&
      pastStreamInfo?.streaming_description == description &&
      pastStreamInfo?.streaming_thumbnail == assetSourceValue &&
      new Date(pastStreamInfo?.start_time).getTime() == scheduleDate.getTime() &&
      pastStreamInfo?.product_info.length == streamProducts.length &&
      !notificationValueChanged &&
      !hasStoreCreditValueChanged);
  const compareDate = moment(scheduleDate).isBefore(moment()) || moment(scheduleDate).isSame(moment());

  const scrollViewRef = React.useRef(null);

  useEffect(() => {
    if (scrollViewRef.current && hasNotification && hasStoreCredit) {
      scrollViewRef.current.scrollToEnd({animated: true});
    }
  }, [hasNotification, hasStoreCredit]);

  //Get stream metadata if streamId is present
  useEffect(() => {
    if (streamId && authToken) {
      LivelyApi.apptileGetLiveMetadata(authToken, streamId).then((response: any) => {
        const responseData = response?.data?.data?.notificationResponse;

        if (responseData) {
          setNotificationInfo({
            title: responseData?.headings?.en,
            description: responseData?.contents?.en,
            thumbnailUrl: responseData?.big_picture,
          });
          setHasNotification(true);
        }
      });
    }
  }, [authToken, streamId]);

  useEffect(() => {
    if (!hasScheduled) {
      setScheduleDate(new Date());
    }
  }, [hasScheduled]);

  //Get storeCredit info
  useEffect(() => {
    if (streamId && apptileAppId) {
      StoreCreditApi.getLiveJoinStoreCreditData(apptileAppId, streamId)
        .then((response: any) => {
          const responseData = response?.data;
          setHasStoreCredit(true);
          setStoreCreditInfo({
            creditAmount: responseData?.creditAmount,
            creditExpiryDays: responseData?.creditExpiryDays,
            currencyCode: responseData?.currencyCode,
          });
        })
        .catch((error: any) => {
          console.error('Store credit error: ', error);
        });
    }
  }, [apptileAppId, streamId]);

  useEffect(() => {
    if (apptileAppId) dispatch(fetchAppIntegrations(apptileAppId as string));
    if (instaAuthCode && authToken) {
      LivelyApi.updateInstagramStatus(authToken, instaAuthCode)
        .then(response => {
          const account_handle = response?.data?.data?.account_handle;
          if (account_handle) checkInstaRtmpToken(account_handle);
          else window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
    if (instaAuthToken && authToken && queryParams.get('user-name')) {
      LivelyApi.setInstagramToken(authToken, queryParams.get('user-name'), instaAuthToken)
        .then(response => {
          // console.log(response);
          dispatch(fetchInstagramPages());
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
  }, [apptileAppId, instaAuthCode, instaAuthToken, authToken, dispatch]);

  //Checking if the user has store credit
  const appIntegrationsById = useSelector((state: EditorRootState) => state.integration.appIntegrationsById);
  const appIntegrations = Object.values(appIntegrationsById);
  const storeCreditIntegration = appIntegrations.find(e => e.integrationCode === 'storeCredit');

  /* Animations */
  const contentOpacity = React.useRef(new Animated.Value(0)).current;
  const contentTranslateY = React.useRef(new Animated.Value(100)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(contentOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(contentTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const { animatedStyle } = useMountTransitionAnimation({direction: 'y'})
  
  useEffect(() => {
    setStreamToPlatforms(prev => {
      const updated = {...prev};
      if (streamToFacebookAuthId) {
        updated.facebook = [streamToFacebookAuthId];
      }
      if (streamToInstagramAuthId) {
        updated.instagram = [streamToInstagramAuthId];
      }
      return updated;
    });
  }, [streamToFacebookAuthId, streamToInstagramAuthId]);

  useEffect(() => {
    if(authToken) {
      dispatch(fetchStreamSettings());
    }
  }, [dispatch, authToken])
  
  return (
    <>
      {isFacebookConnectionError || isNetworkError ? (
        <NetworkErrorPage />
      ) : instaAuthCode ? (
        <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      ) : (
        <Animated.View style={[styles.wrapper, animatedStyle]}> 
          <View style={styles.createStreamHeader}>
            <View style={{flexDirection: 'row', gap: 20, alignItems: 'center'}}>
              <Back />
              <DeleteStreamConfirmationModal />
              <View style={{flexDirection: 'row', alignItems: 'center', gap: 14, borderWidth: 1, borderColor: theme.PRIMARY, borderRadius: 12, paddingHorizontal: 14, paddingVertical: 4, backgroundColor: 'rgb(217,234,255)'}}>
                <Icon iconType="MaterialCommunityIcons" name="video" size={16} color={theme.PRIMARY} />
                <TextElement fontSize="sm" fontWeight="500" color="PRIMARY">{streamId ? 'Edit' : 'Create'} Stream</TextElement>
              </View>
            </View>
            <View style={{flexDirection: 'row', justifyContent: 'space-between', gap: '10px'}}>
              <Tooltip
                tooltip={
                  goLiveDisabled
                    ? 'Please fill all required fields'
                    : compareDate
                    ? 'Go Live'
                    : streamId
                    ? 'Save'
                    : 'Schedule'
                }
                position="left"
                toolTipMenuStyles={{height: 30, right: 40, paddingVertical: 4}}>
                {streamId ? (
                  <Button
                    color={'CTA'}
                    loading={streamCreationInProgress}
                    onPress={createNewStream}
                    disabled={goLiveDisabled || reScheduleDisabled}>
                    Save
                  </Button>
                ) : (
                  <Button
                    color={'CTA'}
                    onPress={createNewStream}
                    disabled={goLiveDisabled}
                    loading={streamCreationInProgress}>
                    {compareDate ? 'Go Live' : 'Schedule'}
                  </Button>
                )}
              </Tooltip>
              {streamId && (
                <Button
                  containerStyles={{borderColor: '#D92626', backgroundColor: 'transparent'}}
                  textStyles={{color: '#D92626'}}
                  onPress={deleteStream}
                  disabled={goLiveDisabled}>
                  Delete
                </Button>
              )}
            </View>
          </View>
          <View
            style={[
              styles.rowLayout,
              {
                gap: 10,
                flex: 1,
              },
            ]}>
            {/* Scroll to the bottom if hasNotification is true */}
            <ScrollView ref={scrollViewRef} contentContainerStyle={[styles.section, {flex: 1}]}>
              <View style={[styles.backgroundColorWhite]}>
                <View style={styles.sectionHeaderWrapper}>
                  <Text style={[commonStyles.baseText, styles.sectionHeaderText]}>Add stream details</Text>
                </View>
                <View style={[styles.sectionBody]}>
                  <View style={styles.paddingTop12}>
                    <Text style={[commonStyles.baseText, styles.labelText]}>Topic*</Text>
                    <CodeInputControlV2 placeholder="Topic" value={topic} onChange={setTopic} />
                  </View>
                  <View style={styles.paddingTop12}>
                    <Text style={[commonStyles.baseText, styles.labelText]}>Description</Text>
                    <CodeInputControlV2 placeholder="Description" value={description} onChange={setDescription} />
                  </View>
                  <View style={[styles.paddingTop12]}>
                    <Text style={[commonStyles.baseText, styles.labelText]}>Thumbnail*</Text>
                    <View style={{width: 100}}>
                      <LivelyImageUploader
                        assetSourceValue={assetSourceValue}
                        setAssetSourceValue={setAssetSourceValue}
                      />
                    </View>
                  </View>
                  <View style={[{gap: 10}, styles.paddingTop12]}>
                    <View>
                      <Text style={[commonStyles.baseText, styles.labelText]}>Stream to facebook</Text>
                    </View>
                    <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 10}}>
                      {facebookLoading && (
                        <Image
                          source={require('@/root/web/assets/images/preloader.svg')}
                          style={{width: 50, height: 50}}
                        />
                      )}
                      {!facebookLoading && !facebookFound && (
                        <View>
                          <Text>No facebook pages are linked. </Text>
                          <Pressable onPress={handleLoginFacebook} style={styles.facebookButton}>
                            <Text style={{color: '#fff'}}>Connect with facebook </Text>
                            <Icon color={'#fff'} name="facebook" size={20} iconType={'MaterialCommunityIcons'} />
                          </Pressable>
                        </View>
                      )}
                      {!facebookLoading &&
                        facebookFound &&
                        facebookChannels.map((e: any) => (
                          <Pressable
                            onPress={() =>
                              streamToPlatforms.facebook?.includes(e.fb_auth_id)
                                ? setStreamToPlatforms(platforms => ({...platforms, facebook: []}))
                                : setStreamToPlatforms(platforms => ({...platforms, facebook: [e.fb_auth_id]}))
                            }
                            style={{
                              width: 80,
                              overflow: 'hidden',
                              justifyContent: 'center',
                              alignItems: 'center',
                              borderRadius: 8,
                              backgroundColor: streamToPlatforms.facebook?.includes(e.fb_auth_id)
                                ? '#D2E3FC'
                                : theme.CONTROL_BORDER,
                              padding: 4,
                            }}>
                            <View>
                              <Image
                                source={{uri: e.fb_page.profile_picture}}
                                style={{width: 60, height: 60, borderRadius: 80}}
                                resizeMode="cover"
                              />
                              <Image
                                source={require('@/root/web/assets/images/logos_facebook.png')}
                                style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                                resizeMode="contain"
                              />
                            </View>
                            <Text
                              style={{
                                lineBreak: 'anywhere',
                                textAlign: 'center',
                                fontSize: 12,
                                marginTop: 10,
                              }}>
                              {e.fb_page.name}
                            </Text>
                          </Pressable>
                        ))}
                    </View>
                    {isInstagramEnabled && (
                      <>
                        <View>
                          <Text style={[commonStyles.baseText, styles.labelText]}>Stream to instagram</Text>
                        </View>
                        <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 10}}>
                          {instagramLoading && (
                            <Image
                              source={require('@/root/web/assets/images/preloader.svg')}
                              style={{width: 50, height: 50}}
                            />
                          )}
                          {!instagramLoading && (!instagramFound || instagramChannels.length < 1) && (
                            <View>
                              <Text>No instagram pages are linked. </Text>
                              <Pressable onPress={handleLoginInstagram} style={styles.instagramButton}>
                                <Text style={{color: '#fff'}}>Connect with instagram </Text>
                                <Icon color={'#fff'} name="instagram" size={20} iconType={'MaterialCommunityIcons'} />
                              </Pressable>
                            </View>
                          )}
                          {!instagramLoading &&
                            instagramFound &&
                            instagramChannels.map((e: any) => (
                              <Pressable
                                onPress={() =>
                                  streamToPlatforms.instagram?.includes(e._id)
                                    ? setStreamToPlatforms(platforms => ({...platforms, instagram: []}))
                                    : setStreamToPlatforms(platforms => ({...platforms, instagram: [e._id]}))
                                }
                                style={{
                                  width: 80,
                                  overflow: 'hidden',
                                  alignItems: 'center',
                                  borderRadius: 8,
                                  backgroundColor: streamToPlatforms.instagram?.includes(e._id)
                                    ? '#D2E3FC'
                                    : theme.CONTROL_BORDER,
                                  padding: 4,
                                }}>
                                <View>
                                  {e.profile_url ? (
                                    <Image
                                      source={{uri: e.profile_url}}
                                      style={{width: 60, height: 60, borderRadius: 80}}
                                      resizeMode="cover"
                                    />
                                  ) : (
                                    <View
                                      style={{
                                        width: 60,
                                        height: 60,
                                        borderRadius: 80,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: '#fff',
                                      }}>
                                      <Text style={[commonStyles.baseText, {fontSize: 24}]}>
                                        {(e?.account_handle?.[0] || '').toUpperCase()}
                                      </Text>
                                    </View>
                                  )}
                                  <Image
                                    source={require('@/root/web/assets/images/logos_instagram.png')}
                                    style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                                    resizeMode="contain"
                                  />
                                </View>
                                <Text
                                  style={{
                                    lineBreak: 'anywhere',
                                    textAlign: 'center',
                                    fontSize: 12,
                                    marginTop: 10,
                                  }}>
                                  {e.account_handle}
                                </Text>
                              </Pressable>
                            ))}
                        </View>
                      </>
                    )}
                    <View style={{marginTop: 12}}>
                      <View
                        style={[
                          styles.rowLayout,
                          styles.paddingTop12,
                          {flex: 1, justifyContent: 'space-between', alignItems: 'center'},
                        ]}>
                        <Text style={[commonStyles.baseText, styles.labelText, {flex: 1}]}>Schedule stream</Text>
                        <View>
                          <CheckboxControl
                            label=""
                            value={hasScheduled}
                            onChange={() => setHasScheduled(!hasScheduled)}
                          />
                        </View>
                      </View>
                      {hasScheduled && (
                        <View style={{marginTop: 12}}>
                          <Text style={[commonStyles.baseText, styles.labelText]}>Pick a time</Text>
                          <DateAndTimeControl
                            value={scheduleDate}
                            onChange={(value: string): void => setScheduleDate(new Date(value))}
                          />
                        </View>
                      )}
                    </View>
                  </View>
                </View>
              </View>
              <View style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
                <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
                  <Text style={[commonStyles.baseText, styles.sectionHeaderText]}>Notify users on going live</Text>
                  <View>
                    <CheckboxControl
                      label=""
                      value={hasNotification}
                      onChange={value => {
                        setHasNotification(value);
                        if (value) {
                          onEnableNotification();
                        }
                      }}
                    />
                  </View>
                </View>

                {hasNotification && (
                  <View>
                    {hasScheduled && (
                      <Text style={[commonStyles.baseText, {marginTop: 5}]}>
                        (*It will be scheduled for the time mentioned above)
                      </Text>
                    )}
                    <View style={styles.notificationFieldWrapper}>
                      <Text style={[commonStyles.baseText, styles.labelText]}>Topic*</Text>
                      <TextInput
                        value={notificationInfo.title}
                        onChangeText={value => {
                          setNotificationInfo({...notificationInfo, title: value});
                          setNotificationValueChanged(true);
                        }}
                      />
                    </View>
                    <View style={styles.notificationFieldWrapper}>
                      <Text style={[commonStyles.baseText, styles.labelText]}>Description*</Text>
                      <TextInput
                        value={notificationInfo.description}
                        onChangeText={value => {
                          setNotificationInfo({...notificationInfo, description: value});
                          setNotificationValueChanged(true);
                        }}
                      />
                    </View>
                    <View style={{display: 'flex', alignSelf: 'flex-start'}}>
                      <AiNotificationCreator
                        onConfirm={handleAiNotificationConfirm}
                        record={record}
                        handleChange={handleChange}
                      />
                    </View>
                    <View style={{
                        ...styles.notificationFieldWrapper,
                        borderTopWidth: 1,
                        borderTopColor: '#E5E5E5',
                        paddingTop: 10,
                      }}>
                      <Text style={[commonStyles.baseText, styles.labelText]}>Thumbnail</Text>
                      <View style={{width: 100}}>
                        <LivelyImageUploader
                          assetSourceValue={
                            notificationInfo.thumbnailUrl
                              ? {
                                  uri: notificationInfo.thumbnailUrl,
                                }
                              : null
                          }
                          setAssetSourceValue={value => {
                            setNotificationInfo({...notificationInfo, thumbnailUrl: value as unknown as string});
                            setNotificationValueChanged(true);
                          }}
                        />
                      </View>
                    </View>
                  </View>
                )}
              </View>
              {/* //Get data from store app integration. And check if the consumer has store credit */}
              {storeCreditIntegration && (
                <View
                  style={[styles.backgroundColorWhite, {paddingHorizontal: 19, marginTop: 20, paddingVertical: 15}]}>
                  <View style={[styles.rowLayout, {flex: 1, alignItems: 'center', justifyContent: 'space-between'}]}>
                    <Text style={[commonStyles.baseText, styles.sectionHeaderText]}>
                      Give store credit for joining live
                    </Text>
                    <View>
                      <CheckboxControl
                        label=""
                        value={hasStoreCredit}
                        onChange={value => {
                          setHasStoreCredit(value);
                        }}
                      />
                    </View>
                  </View>

                  {hasStoreCredit && (
                    <View>
                      <View style={styles.notificationFieldWrapper}>
                        <Text style={[commonStyles.baseText, styles.labelText]}>Store Credit Amount*</Text>
                        <View style={styles.currencyContainer}>
                          <TextInput
                            value={storeCreditInfo.creditAmount}
                            keyboardType="numeric"
                            onChangeText={value => {
                              setStoreCreditInfo({...storeCreditInfo, creditAmount: Number(value)});
                              setHasStoreCreditValueChanged(true);
                            }}
                          />
                          <Text style={[commonStyles.baseText, styles.currency]}>{currencyCode}</Text>
                        </View>
                      </View>
                      <View style={styles.notificationFieldWrapper}>
                        <Text style={[commonStyles.baseText, styles.labelText]}>Store Credit Expiry (In days)</Text>
                        <TextInput
                          keyboardType="numeric"
                          value={storeCreditInfo.creditExpiryDays}
                          onChangeText={value => {
                            setStoreCreditInfo({...storeCreditInfo, creditExpiryDays: Number(value)});
                            setHasStoreCreditValueChanged(true);
                          }}
                        />
                      </View>
                    </View>
                  )}
                </View>
              )}
            </ScrollView>
            <View
              style={[
                styles.section,
                {
                  flex: 3,
                  overflow: 'hidden',
                },
                styles.backgroundColorWhite,
              ]}>
              <View style={styles.sectionHeaderWrapper}>
                <Text style={[commonStyles.baseText, styles.sectionHeaderText]}>Add items</Text>
                <Tooltip
                  tooltip="Search or scan items to add products"
                  position="right"
                  containerStyles={{zIndex: 1000}}
                  toolTipMenuStyles={{
                    height: 40,
                    width: 150,
                    left: 72,
                    zIndex: 1000,
                    paddingVertical: 4,
                    paddingHorizontal: 6,
                    backgroundColor: 'white',
                    borderColor: 'black',
                    borderWidth: 0.4,
                    borderStyle: 'solid',
                  }}
                  tooltipStyles={{
                    height: 40,
                    width: 150,
                    flexWrap: 'wrap',
                    color: 'black',
                    paddingVertical: 4,
                    paddingHorizontal: 6,
                  }}>
                  <Icon iconType="MaterialCommunityIcons" name="information-outline" size={16} />
                </Tooltip>
              </View>
              {isBarCodeResultLoading && (
                <View
                  style={{
                    flex: 1,
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    position: 'absolute',
                    zIndex: 2,
                    gap: 16,
                    marginTop: 37,
                  }}>
                  <Image style={{width: 30, height: 30}} source={require('@/root/web/assets/images/preloader.svg')} />
                  <Text style={{color: '#4F4F4F', fontFamily: theme.FONT_FAMILY}}>Scanning Items</Text>
                </View>
              )}
              <View style={[styles.sectionBody]}>
                <View style={[styles.rowLayout]}>
                  <ShopifyProductCollectionPicker
                    onCollectionChange={setNewCollection}
                    collectionHandle={newCollection?.handle}
                    productHandle={newProduct?.handle}
                    onProductChange={onProductAdd}
                    queryRunner={queryRunner}
                  />
                  <View>
                    <Button
                      containerStyles={{flexGrow: 0, flexShrink: 0}}
                      onPress={() => {
                        newProduct ? addNewProduct(newProduct) : onCollectionAdd(newCollection);
                      }}>
                      Add
                    </Button>
                  </View>
                </View>

                <View style={{flex: 1, marginTop: 26}}>
                  {streamProducts.length > 0 ? (
                    <ProductTable
                      appendLotNumber={false}
                      items={streamProducts}
                      onProductDel={onProductDel}
                      onProductBulkDel={onProductBulkDel}
                      setItems={setStreamProducts}
                    />
                  ) : (
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                      }}>
                      <Image
                        style={styles.emptyImage}
                        source={require('@/root/web/assets/images/snapshot-no-result-barcode.png')}
                      />
                      <Text style={[commonStyles.baseText, {fontSize: 13, fontWeight: '400', color: '#000'}]}>
                        <Text style={{fontWeight: '800'}}>Search</Text> or <Text style={{fontWeight: '800'}}>Scan</Text>{' '}
                        to add products to your livestream
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  emptyImage: {width: 150, height: 150},
  createStreamHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },
  currencyContainer: {
    justifyContent: 'center',
  },
  currency: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  labelText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000',
    marginBottom: 10,
  },
  notificationFieldWrapper: {
    flexDirection: 'column',
    marginTop: 12,
  },
  sectionHeaderWrapper: {
    borderBottomColor: '#ccc',
    borderBottomWidth: 0.5,
    paddingHorizontal: 25,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sectionBody: {
    paddingHorizontal: 25,
    paddingVertical: 10,
    flex: 1,
    overflow: 'scroll',
  },
  paddingTop12: {
    paddingTop: 12,
  },
  section: {
    height: '100%',
  },
  backgroundColorWhite: {
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  rowLayout: {
    flexDirection: 'row',
    gap: 20,
  },
  alignCenter: {
    alignItems: 'center',
  },
  wrapper: {
    paddingVertical: 30,
    paddingHorizontal: 30,
    gap: 20,
    width: '100%',
    flex: 1,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
  facebookButton: {
    padding: 5,
    marginTop: 10,
    backgroundColor: '#0766FF',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  instagramButton: {
    padding: 5,
    marginTop: 10,
    backgroundColor: '#ff2255',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
  },
});
