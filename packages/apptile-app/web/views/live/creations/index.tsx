import { useSearchParams } from '@/root/web/routing.web'
import React from 'react'
import { STREAM_TYPE } from '../dashboardV2';
import { CreateStream } from './createStream';
import { CreateAuction } from './createAuction';

const Creations = () => {
  const [searchParams] = useSearchParams();
  const streamType = searchParams.get('stream-type');

  return streamType === STREAM_TYPE.LIVE ? <CreateStream /> : <CreateAuction />
}

export default Creations