import React, {useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '../../../api/LivelyApi';
import {Image, ImageSourcePropType, Pressable, View} from 'react-native';
import Dropzone from 'react-dropzone';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {MaterialCommunityIcons} from 'apptile-core';
import {StyleSheet} from 'react-native';
import {handleApiError, handleApiErrorWithoutNetwork} from '../shared/CommonError';
import {makeToast} from '@/root/web/actions/toastActions';
import { CREATE_STREAM } from '../../../../app/common/utils/apiErrorMessages/specificFeature';
import { COMMON_ERROR_MESSAGE } from '../../../../app/common/utils/apiErrorMessages/generalMessages';

export const LivelyImageUploader = ({assetSourceValue, setAssetSourceValue}) => {
  const [uploading, setUploading] = useState(false);
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const dispatch = useDispatch();

  const uploadImage = async (url, file) => {
    try {
      const data = await fetch(url, {
        method: 'PUT',
        body: file,
      });
      return data;
    } catch (error) {
      setUploading(false);
      console.log(error);
    }
  };

  const onDrop = (accepted: File[], rejected: FileRejection[]) => {
    if (Object.keys(rejected).length !== 0) {
      alert('Please submit valid file type');
    } else {
      setUploading(true);
      const file = accepted[0];
      LivelyApi.getUploadUrl(authToken, file?.name?.split('.')?.pop())
        .then(response => {
          const uploadURL = response?.data?.data?.uploadURL;
          if (file && uploadURL) {
            try {
              uploadImage(uploadURL, file).then(resp => {
                setUploading(false);
                setAssetSourceValue(uploadURL?.split('?')[0]);
                dispatch(
                  makeToast({
                    content: CREATE_STREAM.SUCCESS.IMAGE_UPLOAD,
                    appearances: 'success',
                    duration: 2000,
                  }),
                );
              });
            } catch (e) {
              setUploading(false);
              console.error(e);
              handleApiErrorWithoutNetwork("", CREATE_STREAM.ERROR.IMAGE_UPLOAD,dispatch,"onDrop - uploadImage" )
            }
          } else {
            setUploading(false);
            alert('Please select a file to upload');
          }
        })
        .catch(error => {
          setUploading(false);
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch), "onDrop -getUploadUrl";
        });
    }
  };

  return (
    <View style={{flex: 1}}>
      {!assetSourceValue ? (
        <>
          <Dropzone
            onDrop={onDrop}
            onDropAccepted={() => {
              // disableButtons(true);
              // startUpload();
              // changeHeader('Uploading...');
              // setUploadClicked(true);
            }}
            disabled={uploading}
            multiple={false}
            accept={{
              'image/jpeg': ['.jpeg', '.jpg', '.png', '.gif'],
            }}>
            {({getRootProps, getInputProps, open}) => (
              <View>
                <div {...getRootProps({className: 'dropzone'})} style={{display: 'flex', width: '100%'}}>
                  <input {...getInputProps()} className="fileInput" />
                  <Pressable
                    style={[styles.imageContainer, styles.upload]}
                    onPress={uploading ? () => {} : open}
                    disabled={uploading}
                    nativeID="uploadLogoSection">
                    <TextElement fontSize="sm" lineHeight="md">
                      {uploading ? 'UPLOADING' : '+ UPLOAD'}
                    </TextElement>
                  </Pressable>
                </div>
              </View>
            )}
          </Dropzone>
        </>
      ) : (
        <View style={styles.imageContainer}>
          <Pressable
            onPress={() => {
              setAssetSourceValue(null);
              dispatch(
                makeToast({
                  content: CREATE_STREAM.SUCCESS.IMAGE_REMOVED,
                  appearances: 'success',
                  duration: 3000,
                }),
              );
            }}
            style={styles.removeAsset}>
            <MaterialCommunityIcons name="close" size={12} color="#FFF" />
          </Pressable>
          <Image resizeMode="contain" source={assetSourceValue as ImageSourcePropType} style={styles.image} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
  },
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
});
