import Button from '@/root/web/components-v2/base/Button';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import { useSearchParams } from '@/root/web/routing.web';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import { Icon } from 'apptile-core';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import InfoTooltip from '../shared/InfoTooltip';

export const ProductTable: React.FC<{
  appendLotNumber?: boolean;
  items: {
    id: string;
    title: string;
    price: number;
    currencyCode: string;
    totalInventory: number;
    collections: string;
    image: string;
    startingBid?: number;
    lotNumber?: number;
  }[];
  setItems?: (items: any[]) => void;
  onProductDel: (product: any) => void;
  onProductBulkDel: (product: any) => void;
}> = ({appendLotNumber, items, setItems, onProductDel, onProductBulkDel}) => {

  /* Hooks */
  const [searchParams] = useSearchParams();
  
  /* States */
  const [checkedProducts, setCheckedProducts] = useState<string[]>([]);
  const [productStockFilter, setProductStockFilter] = useState([0, 1]);
  const [filteredProducts, setFilteredProducts] = useState(items);
  const [potentialRevenue, setPotentialRevenue] = useState<number>(0);

  const startingBidInputRef = useRef<any>(null);
  const [startingBidFocusId, setStartingBidFocusId] = useState<string>('');

  const lotNumberInputRef = useRef<any>(null);
  const [lotNumberFocusId, setLotNumberFocusId] = useState<string>('')

  const [showCollectionPopover, setShowCollectionPopover] = useState(false);

  /* Globals */
  let defaultStartingBidPercentage = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data?.defaultStartingBidPercentage);
  defaultStartingBidPercentage = defaultStartingBidPercentage ? Math.abs(100 - defaultStartingBidPercentage) : 50;
  const currencyCode = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser.currency.currency_code);

  /* Variables */
  const isAuction = searchParams.get('stream-type') === 'auction';

  useEffect(() => { setFilteredProducts(items) }, [items]);

  const resetFilters = () => {
    setProductStockFilter([0, 1]);
    setCheckedProducts([]);
  }
  const checkedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-outline" size={20} color="#1060E0" />
  );
  const uncheckedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-blank-outline" size={20} color="#D8D8D8" />
  );
  const onStockFilterApply = () => {
    setShowCollectionPopover(false);
    if (productStockFilter.includes(1) && productStockFilter.includes(0)) {
      setFilteredProducts(items);
    } else if (productStockFilter.includes(1)) {
      setFilteredProducts(items.filter(product => product.totalInventory > 0));
    } else if (productStockFilter.includes(0)) {
      setFilteredProducts(items.filter(product => product.totalInventory <= 0));
    }
  };

  const renderItem = ({item}: {item: any}) => {
    const isStartingBidChanged =
      Number(item?.startingBid) !== Math.ceil((filteredProducts.find(p => p.id === item?.id)?.price || 0) * (defaultStartingBidPercentage / 100));
    return (
      <View style={[styles.row, {gridTemplateColumns: isAuction ? '.5fr 1.5fr 1fr 1fr 1.5fr 1fr .3fr' : '.5fr 1.5fr 1fr 1fr 1.5fr .3fr'}]}>
        <View style={styles.cell}>
          {checkedProducts.includes(item.id) ? (
            <Pressable
              style={styles.checkBox}
              onPress={() => {
                setCheckedProducts(checkedProducts.filter(product => product !== item.id));
              }}>
              {checkedIcon()}
            </Pressable>
          ) : (
            <Pressable
              style={styles.checkBox}
              onPress={() => {
                setCheckedProducts([...checkedProducts, item.id]);
              }}>
              {uncheckedIcon()}
            </Pressable>
          )}
        </View>
        <View style={[{flexDirection: 'row', gap: 8, paddingRight: 8}]}>
          <Image source={{uri: item.image}} style={styles.image} />
          <Text style={[styles.cell, {textAlign: 'left'}]} numberOfLines={3}>
            {item.title}
          </Text>
        </View>
        <Text style={styles.cell}>
          {item.price} {item.currencyCode}
        </Text>
        {isAuction && (
          <>
          <Pressable onPress={() => {
            setStartingBidFocusId(item.id);
            startingBidInputRef.current?.focus();
          }}>
            {startingBidFocusId === item.id ? (
              <input
                type='number'
                ref={startingBidInputRef}
                defaultValue={+item.startingBid}
                onBlur={(e) => {
                  const value = Number(e.target.value);
                  if(!isNaN(value) && value! > 0) setItems(items.map(product => product.id === item.id ? {...product, startingBid: Number(value).toFixed(2)} : product));
                  setStartingBidFocusId('');
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    const value = Number((e.target as HTMLInputElement).value);
                    if (!isNaN(value)) {
                      setItems(items.map(p => 
                        p.id === item.id ? {...p, lotNumber: value} : p
                      ));
                    }
                    (e.target as HTMLInputElement).blur();
                    setLotNumberFocusId('');
                  }
                }}
                autoFocus
                style={{
                  textAlign: 'center',
                  fontFamily: theme.FONT_FAMILY, 
                  fontSize: 12, 
                  color: '#595959',
                  borderWidth: 1,
                  borderColor: theme.PRIMARY,
                  borderRadius: 4,
                  backgroundColor: 'white',
                }}
              />
            ) : (
              <View style={{flexDirection: 'row', gap: 8, alignItems: 'center', justifyContent: 'center'}}>
                 <Text 
                    style={[
                      styles.cell,
                      isStartingBidChanged && {color: theme.PRIMARY}
                    ]}>
                    {item.startingBid} {item.currencyCode}
                  </Text>
                <Icon name='edit-3' iconType='Feather' size={12} />
              </View>
            )}
          </Pressable>
          {appendLotNumber && (   
            <Pressable onPress={() => {
                setLotNumberFocusId(item.id);
                lotNumberInputRef.current?.focus();
              }}
            >
              {lotNumberFocusId === item.id ? (
                <input
                  type='number'
                  ref={lotNumberInputRef}
                  defaultValue={item?.lotNumber}
                  onBlur={(e) => {
                    const value = Number(e.target.value);
                    if(!isNaN(value)) {
                      if(!_.find(items, {lotNumber: value}) && value! > 0) {
                        setItems(items.map(product => product.id === item.id ? {...product, lotNumber: Number(value)} : product))
                      }
                    }
                    setLotNumberFocusId('');
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      const value = Number((e.target as HTMLInputElement).value);
                      if (!isNaN(value)) {
                        setItems(items.map(p => 
                          p.id === item.id ? {...p, lotNumber: value} : p
                        ));
                      }
                      (e.target as HTMLInputElement).blur();
                      setLotNumberFocusId('');
                    }
                  }}
                  autoFocus
                  style={{
                    textAlign: 'center',
                    fontFamily: theme.FONT_FAMILY, 
                    fontSize: 12, 
                    color: '#595959',
                    borderWidth: 1,
                    borderColor: theme.PRIMARY,
                    borderRadius: 4,
                    backgroundColor: 'white',
                  }}
                />
              ) : (
                <View style={{flexDirection: 'row', gap: 8, alignItems: 'center', justifyContent: 'center'}}>
                  <Text style={styles.cell}>LOT #{item.lotNumber}</Text>
                  <Icon name='edit-3' iconType='Feather' size={12} />
                </View>
              )}
            </Pressable>
          )}
          </>
        )}
        {!appendLotNumber && <Text numberOfLines={2} style={styles.cell}>{item.collections ?? 'Some collection'}</Text>}
        <Text style={styles.cell}>{item.totalInventory} Products</Text>
        <View style={styles.cell}>
          <Pressable onPress={() => onProductDel(item)}>
            <Icon iconType="MaterialCommunityIcons" name="delete-outline" size={20} color="#595959" />
          </Pressable>
        </View>
      </View>
    )
  }

  useEffect(() => {
    setPotentialRevenue(
      filteredProducts?.reduce(
        (acc, product) => 
          acc + (Number(product.price) + (Number(product.price) * (isAuction ? 0.2 : 0))) * Number(product.totalInventory), 
        0
      )
    );
  }, [filteredProducts])

  return (
    <View style={styles.container}>
      {checkedProducts.length === 0 && (
        <View style={[styles.headerRow, {gridTemplateColumns: isAuction ? '.5fr 1.5fr 1fr 1fr 1.5fr 1fr .3fr' : '.5fr 1.5fr 1fr 1fr 1.5fr .3fr'}]}>
          <Text style={[styles.headerCellText]}></Text>
          <Text style={[styles.headerCellText]}>Product</Text>
          <Text style={[styles.headerCellText]}>Price</Text>
          {isAuction && (
            <>
              <Text style={[styles.headerCellText]}>Starting Bid</Text>
              {appendLotNumber && <Text style={[styles.headerCellText]}>Lot Number</Text>}
            </>
          )}
          {(!appendLotNumber) && <Text style={[styles.headerCellText]}>Collections</Text>}
          <View style={[styles.headerCellSortWrapper]}>
            <Text style={[styles.headerCellText, {paddingLeft: isAuction ? 20 : 50}]}>Inventory</Text>
            <PopoverComponent
              trigger={
                <Pressable onPress={() => setShowCollectionPopover(!showCollectionPopover)}>
                  <Icon
                    iconType="MaterialCommunityIcons"
                    name="filter-variant"
                    size={20}
                    color={productStockFilter?.length === 2 || productStockFilter?.length === 0 ? '#595959' : '#1060E0'}
                  />
                </Pressable>
              }
              visible={showCollectionPopover}
              onVisibleChange={() => {}}>
              <View style={styles.stockRadiogroupWrapper}>
                <View style={styles.stockFilterWrapper}>
                  <Pressable
                    onPress={() => {
                      productStockFilter.includes(1)
                        ? setProductStockFilter(productStockFilter.filter(stock => stock !== 1))
                        : setProductStockFilter([...productStockFilter, 1]);
                    }}>
                    <Icon
                      iconType="MaterialCommunityIcons"
                      name={productStockFilter.includes(1) ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      size={20}
                      color={productStockFilter.includes(1) ? '#1060E0' : '#D8D8D8'}
                    />
                  </Pressable>
                  <Text>In stock</Text>
                </View>
                <View style={styles.stockFilterWrapper}>
                  <Pressable
                    onPress={() => {
                      productStockFilter.includes(0)
                        ? setProductStockFilter(productStockFilter.filter(stock => stock !== 0))
                        : setProductStockFilter([...productStockFilter, 0]);
                    }}>
                    <Icon
                      iconType="MaterialCommunityIcons"
                      name={productStockFilter.includes(0) ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      size={20}
                      color={productStockFilter.includes(0) ? '#1060E0' : '#D8D8D8'}
                    />
                  </Pressable>
                  <Text>Out of stock</Text>
                </View>
                <Button color="PRIMARY" variant="PILL" size="SMALL" onPress={onStockFilterApply}>
                  Apply
                </Button>
              </View>
            </PopoverComponent>
          </View>
        </View>
      )}
      {checkedProducts.length > 0 && (
        <View style={styles.checkedHeaderRow}>
          <View style={{width: '100%', paddingHorizontal: 16}}>
            {filteredProducts.length === checkedProducts.length ? (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts([]);
                  }}
                  style={{flex: 1}}>
                  {checkedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={() => {
                    onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete all
                </Button>
              </View>
            ) : (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts(filteredProducts.map(product => product.id));
                  }}
                  style={{flex: 1}}>
                  {uncheckedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={() => {
                    onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete selected
                </Button>
              </View>
            )}
          </View>
        </View>
      )}
      <FlatList data={filteredProducts} renderItem={renderItem} keyExtractor={item => item.id} />
      {filteredProducts?.length > 0 && (
        <View style={styles.footerRow}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 4, alignSelf: 'flex-end'}}>
            <InfoTooltip tooltip={
              isAuction ? 'Considering every product sells atleast 20% more than the selling price.' : 'Potential Revenue this stream can generate.'
            } position="top" />
            <Text style={[styles.headerCellText]}>Potential Revenue:</Text>
            <Text style={[styles.headerCellText, {color: theme.PRIMARY}]}>
              {new Intl.NumberFormat(undefined, {
                style: 'currency',
                currency: currencyCode,
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              }).format(potentialRevenue || 0)}
            </Text>
          </View> 
        </View>
      )}
    </View>
  );
};

const CollectionFilter = () => {
  const [selectedCollections, setSelectedCollections] = useState<string[]>([]);
  return <View></View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E3E3E3',
    overflow: 'auto',
  },
  stockFilterWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stockRadiogroupWrapper: {
    padding: 16,
    width: 200,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerRow: {
    height: 48,
    display: 'grid',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E3E3E3',
    paddingVertical: 16,
    backgroundColor: '#F7F7F7',
    marginBottom: 8,
  },
  footerRow: {
    height: 48,
    display: 'flex',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E3E3E3',
    paddingVertical: 16,
    paddingHorizontal: 12,
    backgroundColor: '#F7F7F7',
    marginTop: 8,
  },
  checkedHeaderRow: {
    height: 48,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E3E3E3',
    paddingVertical: 16,
    backgroundColor: '#F7F7F7',
    marginBottom: 8,
  },
  checkBox: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  headerCellSortWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  deleteHeaderWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerCellText: {
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: theme.FONT_FAMILY,
    color: '#616161',
  },
  row: {
    display: 'grid',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    paddingHorizontal: 16,
  },
  cell: {
    textAlign: 'center',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
    color: '#595959',
  },
  image: {
    width: 30,
    height: 40,
    borderRadius: 2
  },
  headerDeleteWrapper: {flexDirection: 'row', alignItems: 'center', gap: 24},
});

export default ProductTable;
