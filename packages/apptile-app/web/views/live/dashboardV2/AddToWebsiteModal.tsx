import { makeToast } from '@/root/web/actions/toastActions';
import { Lively<PERSON><PERSON> } from '@/root/web/api/LivelyApi';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import Icon from '../../../components-v2/base/Icon';
import theme from '../../../styles-v2/theme';

const videoOptions = {
  controls: true,
  responsive: true,
  fluid: true,
  sources: [
    {
      src: 'http://res.cloudinary.com/dngs3t1b0/video/upload/v1750804522/0b7c5657-d798-433f-90a4-38e5821b1973/Screen_Recording_2025-06-25_at_4.04.05_AM_b9uuz6.mov',
      type: 'video/mp4',
    },
  ],
}

const VideoPlayer = React.memo(({options}: {options: videojs.PlayerOptions}) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);

  useEffect(() => { 
    if (videoRef.current && !playerRef.current) {
      const player = videojs(videoRef.current, options, () => {
        console.log('player is ready');
      });
      playerRef.current = player;
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [options]);

  return (
    <div data-vjs-player>
      <video ref={videoRef} className="video-js vjs-big-play-centered" />
    </div>
  );
});

const AddStreamToWebsite = ({streamType}: {streamType: string}) => {
  const brandId = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser.company_id);
  const [streamId, setStreamId] = useState(null);
  const dispatch = useDispatch();
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const handleCopy = (data: string) => {
    navigator.clipboard.writeText(data).then(() =>
      dispatch(
        makeToast({
          content: 'Copied',
          appearances: 'info',
        }),
      ),
    );
  };
  
  useEffect(() => {
    const fetchData = async () => {
      const widgetsResponse = await LivelyApi.getWidgets(authToken);
      const pastStreamsWidgetId = widgetsResponse?.data?.data?.[0]?.widget_uuid;
      setStreamId(pastStreamsWidgetId);
    };
    fetchData();
  }, []);

  return (
    <View style={[styles.actionCard, styles.dropShadow, {maxWidth: '100%'}]}>
      <Text style={styles.headerText}>Watch this video on how to add stream to your website</Text>
      <View style={[styles.wrapper]}>
        <View style={{width: '100%'}}>
          {!streamId ? (
            <ActivityIndicator />
          ) : (
            <VideoPlayer
              options={videoOptions}
            />
          )}
        </View>
        <View style={{width: '100%'}}>
          {streamId && (
            <View style={styles.codeContainer}>
              <Text style={[commonStyles.labelText, {marginBottom: 5}]}>Streams Key</Text>
              <Text style={styles.codeText}>
                {brandId}-{streamId}
              </Text>
              <Pressable
                style={{position: 'absolute', right: 8, top: 15}}
                onPress={() => handleCopy(`${brandId}-${streamId}`)}>
                <Icon name={'clipboard-outline'} color="PRIMARY" />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};


export default AddStreamToWebsite;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 30,
    flex: 1
  },
  snippetContainer: {
    backgroundColor: '#f6f8fa',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 16,
    fontFamily: 'Menlo, monospace',
    minHeight: 80,
    width: '100%',
    overflow: 'auto',
  },
  snippetText: {
    fontFamily: 'Menlo, monospace',
    fontSize: 13,
    color: '#24292f',
  },
  stepsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  stepsWrapper: {
    flexDirection: 'column',
    gap: 8,
  },
  dropShadow: {
    shadowColor: theme.SECONDARY,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 5,
  },
  actionCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 26,
    maxWidth: 350,
    gap: 20,
  },
  headerText: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    fontFamily: theme.FONT_FAMILY,
    color: theme.FONT_COLOR,
  },
  codeContainer: {
    position: 'relative',
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 5,
  },
  codeText: {
    fontFamily: 'Courier New',
    fontSize: 14,
    color: '#333',
  },
});
const webStyles = `
.vjs-big-play-button {
  border-radius: 50% !important;
  width: 64px !important;
  height: 64px !important;
  margin-top: -32px !important;
  margin-left: -32px !important;
  background: white !important;
  border: none !important;
  opacity: 0.95 !important;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}

.vjs-big-play-button:hover {
  opacity: 1 !important;
}

.vjs-big-play-button {
  font-size: 1.5rem !important;
  line-height: 64px !important;
  color: #005BE4 !important;
  left: 0;
  top: 0;
  text-align: center;
  width: 64px;
  height: 64px;
  display: inline-block;
}

.video-js, .vjs-big-play-centered {
  height: 100% !important;
}
`;
document.head.insertAdjacentHTML('beforeend', `<style>${webStyles}</style>`);
