import { ILiveStream } from '@/root/web/api/LivelyApiTypes'
import Button from '@/root/web/components-v2/base/Button'
import { useLocation, useNavigate } from '@/root/web/routing.web'
import theme from '@/root/web/styles-v2/theme'
import { allAvailablePlans, Icon } from 'apptile-core'
import _ from 'lodash-es'
import React from 'react'
import { Animated, Pressable, StyleSheet, View } from 'react-native'
import { useDispatch, useSelector } from 'react-redux'
import { fetchActiveStreams, fetchPastStreams } from '../../../actions/liveSellingActions'
import TextElement from '../../../components-v2/base/TextElement'
import { currentPlanFeaturesSelector } from '../../../selectors/FeatureGatingSelector'
import { EditorRootState } from '../../../store/EditorRootState'
import { STREAM_TYPE } from '../dashboardV2'
import { DeleteStreamConfirmationModal } from '../modals/deleteStreamConfirmationModal'
import Loader from '../shared/Loader'
import NetworkErrorPage from '../shared/NetworkErrorPage'
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes'
import { useMountTransitionAnimation } from '../utils/useMountTransitionAnimation'
import NoStreamsFound from './NoStreamsFound'
import { StreamCard } from './streamCard'
import { setOpenPremiumModal } from '@/root/web/actions/editorActions'

const InProgressStreams: React.FC<{upcomingStreams: ILiveStream[], inProgressStreams: ILiveStream[]}> = ({upcomingStreams, inProgressStreams}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isAuction = location.pathname.includes('auction/auctions')
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);

  return (
    <View>
      <View style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter]}>
        <TextElement color="SECONDARY" fontSize="lg" lineHeight="lg" fontWeight="500">
          {isAuction ? 'Upcoming Auctions' : 'Upcoming Streams'}
        </TextElement>
      </View>

      <View style={[styles.streamsList]}>
        {inProgressStreams.map((entry: ILiveStream, idx: number) => (
          <StreamCard
            idUpcoming={true}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            imageUrl={entry.streaming_thumbnail}
            status={'LIVE'}
            onJoinPress={() => navigate(LIVE_DASHBOARD_ROUTES.STREAM(entry.streaming_id, 1, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}
            onModeratePress={() => navigate(LIVE_DASHBOARD_ROUTES.STREAM(entry.streaming_id, 2, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}
          />
        ))}
        {upcomingStreams.map((entry: ILiveStream, idx: number) => (
          <StreamCard
            idUpcoming={true}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            imageUrl={entry.streaming_thumbnail}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            status={'SCHEDULED'}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            onJoinPress={() => navigate(LIVE_DASHBOARD_ROUTES.STREAM(entry.streaming_id, 1, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}
            onModeratePress={() => navigate(LIVE_DASHBOARD_ROUTES.STREAM(entry.streaming_id, 2, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}
            onEditPress={() => navigate(LIVE_DASHBOARD_ROUTES.EDIT_STREAM(entry.streaming_id, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}
          />
        ))}
        <Pressable
          style={styles.createStreamCardWrapper}
          onPress={() => navigate(LIVE_DASHBOARD_ROUTES.CREATE_STREAM(apptileAppId, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}>
          <View style={styles.plusWrapper}>
            <Icon name="plus" iconType="MaterialCommunityIcons" size={24} color={'#000'} />
          </View>
          <TextElement
            color='SECONDARY'
            fontSize='md'
            fontWeight='500'
            >
            Create {isAuction ? 'auction' : 'stream'}
          </TextElement>
        </Pressable>
      </View>
    </View>
  );
};

const PastStreams: React.FC<{pastStreams: ILiveStream[]}> = ({pastStreams}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const isAuction = location.pathname.includes('auction/auctions')
  
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPlusDisabled = !currentPlanFeatures.includes(allAvailablePlans.PLUS);

  return (
    <View>
      <DeleteStreamConfirmationModal />
      <View style={[styles.rowLayout, styles.alignCenter, {gap: 5, alignSelf: 'flex-start', width: '100%'}]}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>
          <TextElement color="SECONDARY" fontSize="lg" lineHeight="lg" fontWeight="500">
            {isAuction ? 'Your Past Auctions' : 'Your Past Streams'}
          </TextElement>
          <Button onPress={() => navigate(`${isAuction ? LIVE_DASHBOARD_ROUTES.AUCTION_PAST_STREAMS() : LIVE_DASHBOARD_ROUTES.LIVE_PAST_STREAMS()}?page=1`)} color='SECONDARY' variant='PILL' containerStyles={{flexDirection: 'row', alignItems: 'center', paddingVertical: 4, paddingHorizontal: 10}}>
            See all <Icon name="chevron-right" iconType="Feather" size={12} color={theme.SECONDARY} />
          </Button>
        </View>
      </View>

      <View style={[styles.streamsList]}>
        {pastStreams.map((entry: ILiveStream, idx: number) => (
          <StreamCard
            idUpcoming={false}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            imageUrl={entry.streaming_thumbnail}
            status={'ENDED'}
            onJoinPress={() => {}}
            onContainerPress={() => {
              if (isPlusDisabled) {
                dispatch(setOpenPremiumModal(true, allAvailablePlans.PLUS ?? ' ', 'Stream Analytics'));
              } else {
                navigate(`${LIVE_DASHBOARD_ROUTES.LIVE_STREAM_ANALYTICS(entry?.streaming_id)}?shopable-feed-id=${entry?.shoppable_feed_id}`);
              }
            }}
          />
        ))}
      </View>
    </View>
  );
};

const AllStreams = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const isAuction = location.pathname.includes('auction/auctions');
  const { animatedStyle } = useMountTransitionAnimation({ direction: 'x', distance: 0 })
  
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  let {
    data: inProgressStreams,
    loading: inProgressStreamsLoading,
    errorMessage: inProgressStreamError,
  } = useSelector((state: EditorRootState) => state.liveSelling?.inProgressStreams);
  let {
    data: upcomingStreams,
    loading: upcomingStreamsLoading,
    errorMessage: upcomingStreamError,
  } = useSelector((state: EditorRootState) => state.liveSelling?.upcomingStreams);
  let {
    data: pastStreams,
    loading: pastStreamsLoading,
    errorMessage: pastStreamError,
  } = useSelector((state: EditorRootState) => state.liveSelling?.pastStreams);

  React.useEffect(() => {
    if (authToken) {
      dispatch(fetchActiveStreams(isAuction ? 'auction' : 'simple'));
      dispatch(fetchPastStreams(isAuction ? 'auction' : 'simple'));
    }
  }, [authToken, dispatch]);

  if((inProgressStreamError || upcomingStreamError || pastStreamError) && (!_.isEmpty(inProgressStreamError) || !_.isEmpty(upcomingStreamError) || !_.isEmpty(pastStreamError))) return <NetworkErrorPage />

  /* Variables */
  const loading = inProgressStreamsLoading || upcomingStreamsLoading || pastStreamsLoading;
  const noStreamFound = inProgressStreams.length == 0 && upcomingStreams.length == 0 && pastStreams.length == 0;

  return (
    <Animated.View style={[styles.contentContainer, animatedStyle]}>
      <TextElement color="SECONDARY" fontSize="2xl" lineHeight="2xl" fontWeight="600" style={{marginBottom: 40}}>
        {isAuction ? 'All Auctions' : 'All Streams'}
      </TextElement>
      {loading ? <Loader /> : (
        noStreamFound ? <NoStreamsFound streamType={isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE} onNavigate={() => navigate(LIVE_DASHBOARD_ROUTES.CREATE_STREAM(apptileAppId, isAuction ? STREAM_TYPE.AUCTION : STREAM_TYPE.LIVE))}/> : (
          <>
            <InProgressStreams inProgressStreams={inProgressStreams} upcomingStreams={upcomingStreams} />
            <PastStreams pastStreams={pastStreams}/>
          </>
        )
      )}
    </Animated.View>
  )
}

export default AllStreams

export const styles = StyleSheet.create({
  contentContainer: {
    width: '100%',
  },
  infoText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    color: theme.TEXT_COLOR,
    lineHeight: 16,
    fontWeight: '500',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  streamsList: {
    marginVertical: 30,
    // @ts-ignore
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: 16
  },
  emptyStream: {
    height: 175,
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  createStreamCardWrapper: {
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 303,
  },
  plusWrapper: {
    width: 50,
    height: 50,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  iconStyle: {
    padding: 5,
    marginLeft: 5
  },
});
