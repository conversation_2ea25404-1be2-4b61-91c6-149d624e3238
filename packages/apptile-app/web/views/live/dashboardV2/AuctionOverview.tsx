import { putLiveStreamInfo } from '@/root/web/actions/liveSellingActions';
import { ILiveStream } from '@/root/web/api/LivelyApiTypes';
import Button from '@/root/web/components-v2/base/Button';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import { Icon } from 'apptile-core';
import _ from 'lodash';
import moment from 'moment';
import React from 'react';
import { Animated, Image, Pressable, StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import TextElement from '../../../components-v2/base/TextElement';
import { useNavigate } from '../../../routing.web';
import theme from '../../../styles-v2/theme';
import { STREAM_TYPE } from '../dashboardV2';
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes';
import { useMountTransitionAnimation } from '../utils/useMountTransitionAnimation';

const AuctionOverview = () => {
  /* Hooks */
  const dispatch = useDispatch();
  const navigate = useNavigate();

  /* Selectors */
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const {dashboardInfo} = useSelector((state: EditorRootState) => state.liveSelling.auth);
  const {data: inProgressStreams} = useSelector((state: EditorRootState) => state.liveSelling?.inProgressStreams);
  const {data: upcomingStreams} = useSelector((state: EditorRootState) => state.liveSelling?.upcomingStreams);

  /* Variables */
  const liveStreamsInProgress = inProgressStreams?.filter(s => s.allow_auction === true) || [];
  const liveStreamsUpcoming = upcomingStreams?.filter(s => s.allow_auction === true) || [];

  const currStream: ILiveStream | null =
    liveStreamsInProgress.length > 0
      ? { ...liveStreamsInProgress[0], status: 'LIVE' }
      : liveStreamsUpcoming.length > 0
      ? { ..._.minBy(liveStreamsUpcoming, 'start_time_unix'), status: 'SCHEDULED' }
      : null; 
  const timeRemaining = currStream?.start_time_unix! - moment().unix();

  /* Animations */
  const { animatedStyle, triggerUnmount } = useMountTransitionAnimation({ direction: 'y' });

  /* Callbacks */
  const handleAnimatedNavigation = (route: string) => {
    triggerUnmount();
    navigate(route);
  };

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {/* Welcome Section */}
      <View>
        <TextElement style={styles.welcomeText} color="SECONDARY" fontWeight="600" fontSize="3xl">
          Live Auction
        </TextElement>
        <TextElement style={styles.subText} color="EDITOR_LIGHT_BLACK" fontWeight="400" fontSize="md">
          Turn viewers into bidders with engaging live auctions in minutes
        </TextElement>
      </View>

      {/* App Information */}
      <View style={[styles.appInfo, styles.dropShadow]}>
        <View style={styles.infoRow}>
          <View style={[styles.infoChild, {paddingLeft: 0}]}>
            <TextElement color="EDITOR_LIGHT_BLACK" fontWeight="400">
              App name •{' '}
              <TextElement color="SECONDARY" fontWeight="500">
                {dashboardInfo?.name}
              </TextElement>
            </TextElement>
          </View>
          <View style={[styles.infoChild]}>
            <TextElement color="EDITOR_LIGHT_BLACK" fontWeight="400">
              Plan •{' '}
              <TextElement color="SECONDARY" fontWeight="500">
                {dashboardInfo?.planDisplayName || 'Free Trial'}
              </TextElement>
            </TextElement>
          </View>
          {/* <View style={[styles.infoChild, {borderWidth: 0}]}>
            <TextElement color="EDITOR_LIGHT_BLACK" fontWeight="400">
              Theme •{' '}
              <TextElement color="SECONDARY" fontWeight="500">
                {dashboardInfo?.themeName || 'No Theme Selected'}
              </TextElement>
            </TextElement>
          </View> */}
        </View>
        <Button
          variant="FILLED-PILL"
          color="CTA"
          onPress={() =>
            handleAnimatedNavigation(LIVE_DASHBOARD_ROUTES.CREATE_STREAM(apptileAppId, STREAM_TYPE.AUCTION))
          }>
          {`+  Create Auction`}
        </Button>
      </View>

      {/* Streams and Auctions */}
      <View style={[styles.streamCard, styles.dropShadow]}>
          <View style={[styles.streamCardContent]}>
            {currStream ? (
              <>
                <View style={[styles.streamImageWrapper, {padding: 0}]}>
                  <Image
                    source={{
                      uri: currStream.streaming_thumbnail ?? require('@/root/web/assets/images/logo.png'),
                    }}
                    style={[styles.streamCardImage]}
                    resizeMode={currStream.streaming_thumbnail ? 'cover' : 'contain'}
                  />
                </View>

                <View style={styles.streamInfo}>
                  <View style={styles.liveIndicator}>
                    {currStream.status == 'LIVE' ? (
                      <Icon color={theme.PRIMARY_COLOR} iconType="MaterialCommunityIcons" name="circle" size={12} />
                    ) : (
                      <Icon name="schedule" iconType="MaterialIcons" size={12} color={theme.PRIMARY} />
                    )}
                    <TextElement
                      style={{
                        color: theme.PRIMARY,
                        fontWeight: '500',
                        fontSize: 'xs',
                      }}>
                      {currStream.status}
                    </TextElement>
                  </View>
                  <TextElement style={styles.streamName} color="SECONDARY" fontWeight="500" fontSize="3xl">
                    {currStream.streaming_name}
                  </TextElement>

                  <View>
                    <View style={styles.streamMeta}>
                      <TextElement fontWeight="400" fontSize="sm" style={{color: '#656565'}}>
                        {timeRemaining > 0 ? 'Scheduled at:' : 'Started at:' }
                        <TextElement color="SECONDARY" fontSize="sm" style={{marginLeft: 2}}>
                          {moment.unix(currStream.start_time_unix!).format('MMM DD, YYYY | h:mm A')}
                        </TextElement>
                      </TextElement>
                    </View>

                    <View style={styles.streamStats}>
                      <TextElement fontWeight="400" fontSize="sm" style={{color: '#656565'}}>
                        {moment.unix(currStream.start_time_unix!).fromNow()}
                      </TextElement>
                      <TextElement fontWeight="400" fontSize="sm" style={{color: '#656565'}}>
                        {'  '}|{'  '}
                      </TextElement>
                      <TextElement fontWeight="400" fontSize="sm" style={{color: '#656565'}}>
                        {currStream.product_info?.length == 1
                          ? '1 product'
                          : `${currStream.product_info?.length} products`}
                      </TextElement>
                      <TextElement fontWeight="400" fontSize="sm" style={{color: '#656565'}}>
                        {'  '}|{'  '}
                      </TextElement>

                      <View style={styles.streamPlatforms}>
                        <Image
                          style={{width: 15, height: 15}}
                          source={require('@/root/web/assets/images/app_icon1.png')}
                        />
                        {currStream.stream_to?.includes('facebook') && (
                          <Image
                            style={{width: 15, height: 15}}
                            source={require('@/root/web/assets/images/facebook_icon.png')}
                          />
                        )}
                        {currStream.stream_to?.includes('instagram') && (
                          <Image
                            style={{width: 15, height: 15}}
                            source={require('@/root/web/assets/images/instagram_icon.png')}
                          />
                        )}
                      </View>
                    </View>
                  </View>
                  {timeRemaining > 0 ? (
                    <View style={styles.streamActions}>
                      <Button
                        color={'PRIMARY'}
                        variant={'PILL'}
                        containerStyles={{paddingHorizontal: 30}}
                        onPress={() => navigate(LIVE_DASHBOARD_ROUTES.EDIT_STREAM(currStream.streaming_id!, STREAM_TYPE.AUCTION))}>
                        Edit Stream
                      </Button>
                    </View>
                  ) : (
                    <View style={styles.streamActions}>
                      <Button
                        variant="FILLED-PILL"
                        color="CTA"
                        containerStyles={{paddingHorizontal: 30}}
                        onPress={() => { 
                          dispatch(putLiveStreamInfo(currStream))
                          handleAnimatedNavigation(LIVE_DASHBOARD_ROUTES.STREAM(currStream.streaming_id!, 1, STREAM_TYPE.AUCTION))
                        }}>
                        Join as Host
                      </Button>
                      <Button
                        variant="PILL"
                        color="SECONDARY"
                        containerStyles={{paddingHorizontal: 30}}
                        onPress={() => {
                          dispatch(putLiveStreamInfo(currStream))
                          handleAnimatedNavigation(LIVE_DASHBOARD_ROUTES.STREAM(currStream.streaming_id!, 2, STREAM_TYPE.AUCTION))
                        }}>
                        Join as Moderator
                      </Button>
                    </View>
                  )}
                </View>
              </>
            ) : (
              <>
                <View style={[styles.streamImageWrapper, {width: 360}]}>
                  <Image
                    source={require('../../../assets/images/create-auction.png')}
                    style={[styles.streamCardImage]}
                    resizeMode="contain"
                  />
                </View>

                <View style={[styles.streamInfo]}>
                  <View>
                    <TextElement style={styles.streamName} color="SECONDARY" fontWeight="500" fontSize="3xl">
                      {`Create a live auction now!`}
                    </TextElement>
                    <TextElement style={{color: '#656565'}} fontWeight="400" fontSize="md">
                      {`Host exciting auctions and connect with your bidders instantly.`}
                    </TextElement>
                  </View>

                  <View style={{flexDirection: 'column', gap: 12, marginTop: 8}}>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
                      <Icon name="people-outline" iconType="MaterialIcons" size={20} color={theme.PRIMARY} />
                      <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                        Reach real-time bidders
                      </TextElement>
                    </View>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
                      <Icon name="timer-outline" iconType="MaterialCommunityIcons" size={20} color={theme.PRIMARY} />
                      <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                      Set bid increment and auction timer
                      </TextElement>
                    </View>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
                      <Icon name="bell" iconType="SimpleLineIcons" size={20} color={theme.PRIMARY} />
                      <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                      Manage bids and notifications easily
                      </TextElement>
                    </View>
                  </View>

                  <View style={[styles.streamActions, {marginTop: 10}]}>
                    <Button
                      color="CTA"
                      variant="FILLED-PILL"
                      containerStyles={{paddingHorizontal: 30}}
                      onPress={() =>
                        handleAnimatedNavigation(LIVE_DASHBOARD_ROUTES.CREATE_STREAM(apptileAppId, STREAM_TYPE.AUCTION))
                      }>
                      {`+  Create Auction`}
                    </Button>
                  </View>
                </View>
              </>
            )}
          </View>
      </View>

      {/* Cards */}
      <View style={[styles.cardsContainer]}>

          {/* <View style={[styles.actionCard, styles.dropShadow]}>
            <TextElement style={styles.cardTitle} color="SECONDARY" fontWeight="500" fontSize="md">
              {`Add auction to website`}
            </TextElement>
            <TextElement style={styles.cardDescription} fontWeight="400">
              {`Seamlessly add your auction to your website and allow your users to join the stream from the platform of their choice!`}
            </TextElement>
            <Pressable style={styles.seeHow} onPress={() => {}}>
              <TextElement color="PRIMARY" fontWeight="400" fontSize="sm">
                See how
              </TextElement>
              <Icon name="chevron-right" iconType="Feather" size={16} color={theme.PRIMARY_COLOR} />
            </Pressable>
          </View> */}

          <View style={[styles.actionCard, styles.dropShadow]}>
            <TextElement style={styles.cardTitle} color="SECONDARY" fontWeight="500" fontSize="md">
              {`Connect to Instagram`}
            </TextElement>
            <TextElement style={styles.cardDescription} fontWeight="400">
              {`Connect to Instagram to allow your users to join the stream from the platform of their choice!`}
            </TextElement>
            <Pressable style={styles.seeHow} onPress={() => navigate(`../settings`)}>
              <TextElement color="PRIMARY" fontWeight="400" fontSize="sm">
                See how
              </TextElement>
              <Icon name="chevron-right" iconType="Feather" size={16} color={theme.PRIMARY_COLOR} />
            </Pressable>
          </View>
        </View>
      
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    gap: 30,
  },
  welcomeText: {
    marginBottom: 8,
  },
  subText: {
    fontSize: 16,
  },
  appInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
  },
  dropShadow: {
    shadowColor: theme.SECONDARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 5,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoChild: {
    borderRightColor: theme.CONTROL_BORDER,
    borderRightWidth: 1,
    paddingHorizontal: 16,
  },
  streamCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 26,
  },
  streamCardContent: {
    flexDirection: 'row',
    gap: 30,
  },
  streamImageWrapper: {
    width: 260,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    padding: 24,
  },
  streamCardImage: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  streamName: {
    marginBottom: 8,
  },
  streamInfo: {
    flex: 1,
    justifyContent: 'space-between',
    gap: 16,
  },
  streamMeta: {
    marginBottom: 8,
  },
  streamStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  streamActions: {
    flexDirection: 'row',
    gap: 12,
  },
  hostButton: {
    backgroundColor: theme.PRIMARY_COLOR,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  moderatorButton: {
    backgroundColor: 'white',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  streamPlatforms: {
    flexDirection: 'row',
    gap: 8,
    marginLeft: 4,
  },
  cardsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  actionCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 26,
    maxWidth: 350,
    gap: 20,
  },
  cardTitle: {
    fontSize: 16,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: '#656565',
  },
  seeHow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  streamsList: {
    marginTop: 20,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default AuctionOverview;
