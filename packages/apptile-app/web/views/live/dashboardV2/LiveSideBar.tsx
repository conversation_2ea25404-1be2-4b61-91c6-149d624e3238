import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, Animated, Pressable } from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import theme from '../../../styles-v2/theme';
import { Icon } from 'apptile-core';
import { animatedNavigate } from '../utils/animatedNavigation';
import { ApptileWebIcon } from '../../../icons/ApptileWebIcon.web';
import { useNavigate, useLocation } from '../../../routing.web';
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes';
import { useSelector } from 'react-redux';
import { EditorRootState } from '@/root/web/store/EditorRootState';

const StepperItem = ({
  label,
  active,
  locked,
  open, 
  onPress,
  iconName,
  iconType,
  children = [],
}: {
  label: string;
  active?: boolean;
  locked?: boolean;
  open?: boolean;
  onPress?: () => void;
  iconName: string;
  iconType: string;
  children?: React.ReactNode[];
}) => {
  const animatedHeight = React.useRef(new Animated.Value(open ? 1 : 0)).current;

  React.useEffect(() => {
    Animated.timing(animatedHeight, {
      toValue: open ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [open]);

  const maxHeight = children.length * 36;
  const containerStyle = {
    overflow: 'hidden',
    height: animatedHeight.interpolate({
      inputRange: [0, 1],
      outputRange: [0, maxHeight],
    }),
    opacity: animatedHeight,
    paddingLeft: 30,
    marginTop: 10
  };

  return (
    <View style={styles.stepperItem}>
      <Pressable style={[styles.stepperItemLabel]} onPress={onPress} disabled={locked}>
        {iconType == 'apptileWebIcon' ? (
          <ApptileWebIcon name={iconName} size={20} color={locked ? theme.EDITOR_GREY_COLOR : active ? theme.PRIMARY_COLOR_DARK : theme.SECONDARY_COLOR} />
        ) : (
          <Icon name={iconName} iconType={iconType} size={20} color={locked ? theme.EDITOR_GREY_COLOR : active ? theme.PRIMARY_COLOR_DARK : theme.SECONDARY_COLOR} />
        )}
        <TextElement style={locked ? {color: theme.EDITOR_GREY_COLOR} : active ? styles.activeLabel : {}} color="SECONDARY" fontWeight="500">
          {label}
          {locked && (
            <Icon iconType='Foundation' name="lock" size={12} color={theme.EDITOR_GREY_COLOR} style={{position: 'absolute', top: -4, right: -16}} />
          )}
        </TextElement>
      </Pressable>
      <Animated.View style={containerStyle}>
        {children.map((child, idx) => (
          <View key={idx} style={styles.subItem}>
            {child}
          </View>
        ))}
      </Animated.View>
    </View>
  );
};

const SubItem = ({ label, active, onPress }: { label: string; active?: boolean; onPress?: () => void }) => (
  <Pressable onPress={onPress}>
    <TextElement style={active && styles.activeSubItem} color="SECONDARY" fontWeight="400">{label}</TextElement>
  </Pressable>
);

const LiveSideBar = ({
  playAnimation,
  setPlayAnimation,
}: {
  playAnimation: boolean;
  setPlayAnimation: (value: boolean) => void;
}) => {

  const navigate = useNavigate();
  const location = useLocation();

  const settings = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data);
  
  const sidebarOpacity = useRef(new Animated.Value(1)).current;
  const sidebarTranslateX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if(playAnimation) {
      animatedNavigate({
        animations: {
          opacity: { value: sidebarOpacity, toValue: 0, duration: 300 },
          translateX: { value: sidebarTranslateX, toValue: -200, duration: 300 },
        },
      }, undefined, undefined);
    }

    return () => setPlayAnimation(false);
  }, [playAnimation]);

  const DASHBOARD_HEADER_PATH = LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE();
  const DASHBOARD_CHILD_PATHS = [
    LIVE_DASHBOARD_ROUTES.ALL_STREAMS(),
    LIVE_DASHBOARD_ROUTES.LIVE_VIDEO_CLIPS(),
    LIVE_DASHBOARD_ROUTES.LIVE_ANALYTICS(),
    LIVE_DASHBOARD_ROUTES.LIVE_PAST_STREAMS(),
  ];
  const AUCTION_HEADER_PATH = LIVE_DASHBOARD_ROUTES.OVERVIEW_AUCTION();
  const AUCTION_CHILD_PATHS = [
    LIVE_DASHBOARD_ROUTES.ALL_AUCTIONS(),
    LIVE_DASHBOARD_ROUTES.AUCTION_PAST_STREAMS(),
    LIVE_DASHBOARD_ROUTES.AUCTION_ANALYTICS(),
  ];
  const SETTINGS_HEADER_PATH = LIVE_DASHBOARD_ROUTES.SETTINGS();

  const isDashboardHeaderActive = location.pathname === DASHBOARD_HEADER_PATH;
  const isDashboardChildActive = DASHBOARD_CHILD_PATHS.includes(location.pathname);
  const isAuctionHeaderActive = location.pathname === AUCTION_HEADER_PATH;
  const isAuctionChildActive = AUCTION_CHILD_PATHS.includes(location.pathname);
  const isSettingsHeaderActive = location.pathname === SETTINGS_HEADER_PATH;

  let openStepper = null;
  if (isDashboardHeaderActive || isDashboardChildActive) openStepper = 'dashboard';
  else if (isAuctionHeaderActive || isAuctionChildActive) openStepper = 'auction';
  else if (isSettingsHeaderActive) openStepper = 'settings';

  return (
    <Animated.View 
      style={[
        styles.wrapper, 
        {
          opacity: sidebarOpacity, 
          transform: [{translateX: sidebarTranslateX}]
        }
      ]}
    >
      <ScrollView contentContainerStyle={styles.menuWrapper}>
        <StepperItem
          label="Dashboard"
          active={isDashboardHeaderActive}
          open={openStepper === 'dashboard'}
          onPress={() => navigate(DASHBOARD_HEADER_PATH)}
          iconName="storefront-outline"
          iconType="MaterialCommunityIcons"
          children={[
            <SubItem 
              key="1" 
              label="All Streams" 
              active={location.pathname === LIVE_DASHBOARD_ROUTES.ALL_STREAMS() || location.pathname === LIVE_DASHBOARD_ROUTES.LIVE_PAST_STREAMS()}
              onPress={() => navigate(LIVE_DASHBOARD_ROUTES.ALL_STREAMS())}
            />,
            <SubItem 
              key="2" 
              label="Video Clips" 
              active={location.pathname === LIVE_DASHBOARD_ROUTES.LIVE_VIDEO_CLIPS()}
              onPress={() => navigate(LIVE_DASHBOARD_ROUTES.LIVE_VIDEO_CLIPS())}
            />,
            // <SubItem 
            //   key="3" 
            //   label="Analytics" 
            //   active={location.pathname === LIVE_DASHBOARD_ROUTES.LIVE_ANALYTICS()}
            //   onPress={() => navigate(LIVE_DASHBOARD_ROUTES.LIVE_ANALYTICS())}
            // />,
          ]}
        />

        <StepperItem
          label="Auction"
          locked={!settings?.auction}
          active={settings?.auction && isAuctionHeaderActive}
          open={openStepper === 'auction'}
          onPress={() => navigate(AUCTION_HEADER_PATH)}
          iconName="gavel"
          iconType="MaterialIcons"
          children={[
            <SubItem 
              key="4" 
              label="All Auctions" 
              active={location.pathname === LIVE_DASHBOARD_ROUTES.ALL_AUCTIONS() || location.pathname === LIVE_DASHBOARD_ROUTES.AUCTION_PAST_STREAMS()}
              onPress={() => navigate(LIVE_DASHBOARD_ROUTES.ALL_AUCTIONS())}
            />,
            // <SubItem 
            //   key="5" 
            //   label="Analytics" 
            //   active={location.pathname === LIVE_DASHBOARD_ROUTES.AUCTION_ANALYTICS()}
            //   onPress={() => navigate(LIVE_DASHBOARD_ROUTES.AUCTION_ANALYTICS())}
            // />,
          ]}
        />

        <StepperItem
          label="Settings"
          active={isSettingsHeaderActive}
          open={openStepper === 'settings'}
          onPress={() => navigate(LIVE_DASHBOARD_ROUTES.SETTINGS())}
          iconName="settings"
          iconType="Feather"
          children={[]}
        />
      </ScrollView>
    </Animated.View>
  );
};

export default LiveSideBar;

const styles = StyleSheet.create({
  wrapper: {
    width: 240,
    backgroundColor: 'white',
    borderRightWidth: 1,
    borderRightColor: '#e5e5e5',
  },
  logoWrapper: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  logoText: {
    fontSize: 20,
  },
  menuWrapper: {
    paddingTop: 16,
    paddingHorizontal: 30,
  },
  stepperItem: {
    paddingVertical: 6
  },
  stepperItemLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  activeLabel: {
    color: theme.PRIMARY_COLOR_DARK,
  },
  stepperChildren: {
    marginTop: 10,
    paddingLeft: 30,
    gap: 2
  },
  subItem: {
    paddingVertical: 6,
  },
  subItemText: {
    fontSize: 14,
    color: '#666',
  },
  activeSubItem: {
    color: theme.PRIMARY_COLOR_DARK,
  },
});