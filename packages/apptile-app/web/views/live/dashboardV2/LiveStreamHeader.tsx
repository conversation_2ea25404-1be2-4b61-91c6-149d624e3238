import {Lively<PERSON><PERSON>} from '@/root/web/api/LivelyApi';
import {ILiveStream} from '@/root/web/api/LivelyApiTypes';
// import ModalComponent from '@/root/web/components-v2/base/Modal';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {useLocation} from '@/root/web/routing.web';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import {Icon} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {fetchActiveStreams, fetchPastStreams, logoutFromLively} from '../../../actions/liveSellingActions';
import Button from '../../../components-v2/base/Button';
import commonStyles from '../../../styles-v2/commonStyles';
import AuctionMeta, {AuctionMetaState} from '../shared/AuctionMeta';

type LiveStreamHeaderProps = {
  showActionButtons?: boolean;
};

// TODO(NAVEEN): Add ability to change auction settings during live stream.
const LiveStreamHeader = React.forwardRef<View, LiveStreamHeaderProps>((props: LiveStreamHeaderProps, ref) => {
  /* Props */
  const {showActionButtons} = props;

  /* Hooks */
  const dispatch = useDispatch();
  const location = useLocation();

  /* Selectors */
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const currStreamInfoFromState = useSelector((state: EditorRootState) => state.liveSelling.currentStreamInfo);
  // const currencyCode = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser?.currency?.currency_code);

  /* States */
  const [isStreamPage, setIsStreamPage] = useState(false);
  const [currStreamInfo, setCurrStreamInfo] = useState<ILiveStream>(currStreamInfoFromState);
  // const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [auctionMeta, setAuctionMeta] = useState<AuctionMetaState>({
    biddingDuration: currStreamInfo?.auction_duration,
    minimumBidIncrement: currStreamInfo?.product_info?.[0]?.bid_incrementer,
    extendedBidding: !!currStreamInfo?.ext_bidding_duration && !!currStreamInfo?.trigger_ext_bidding_duration,
    triggerOption: currStreamInfo?.trigger_ext_bidding_duration,
    extensionTime: currStreamInfo?.ext_bidding_duration,
  });

  /* Variables */
  const streamId = location.pathname.split('/')[3];
  //  const isAuction = location.pathname.includes('/live-selling/auction');
  const isLoginPage = location.pathname.includes('/login');
  //  const canUpdateAuctionSettings =
  //   currStreamInfo?.auction_duration != auctionMeta.biddingDuration ||
  //   currStreamInfo?.product_info?.[0]?.bid_incrementer != auctionMeta.minimumBidIncrement ||
  //   (!!currStreamInfo?.ext_bidding_duration && !!currStreamInfo?.trigger_ext_bidding_duration) != auctionMeta.extendedBidding ||
  //   currStreamInfo?.trigger_ext_bidding_duration != auctionMeta.triggerOption;
  //   currStreamInfo?.ext_bidding_duration != auctionMeta.extensionTime;

  /* Functions */
  const handleLogout = () => {
    dispatch(logoutFromLively());
  };
  const handleRefresh = useCallback(async () => {
    dispatch(fetchActiveStreams());
    dispatch(fetchPastStreams());
  }, [dispatch]);

  /* Effects */
  useEffect(() => {
    setIsStreamPage(
      location.pathname.includes('/live-selling/auction') || location.pathname.includes('/live-selling/live'),
    );
  }, [location.pathname]);
  useEffect(() => {
    if (!isStreamPage) {
      return;
    }
    if (_.isEmpty(currStreamInfoFromState) && authToken && streamId) {
      (async () => {
        try {
          const response = await LivelyApi.getLiveStreamInfoByID(authToken, streamId);
          setCurrStreamInfo(response.data?.data);
        } catch (error) {
          console.error('Failed to fetch stream name:', error);
        }
      })();
    }
    setCurrStreamInfo(currStreamInfoFromState);
  }, [isStreamPage, currStreamInfoFromState, streamId, authToken]);
  useEffect(() => {
    if (!isStreamPage) return;
    if (!_.isEmpty(currStreamInfo)) {
      setAuctionMeta({
        ...auctionMeta,
        biddingDuration: currStreamInfo?.auction_duration,
        minimumBidIncrement: currStreamInfo?.product_info?.[0]?.bid_incrementer,
        extendedBidding: !!currStreamInfo?.ext_bidding_duration && !!currStreamInfo?.trigger_ext_bidding_duration,
        triggerOption: currStreamInfo?.trigger_ext_bidding_duration,
        extensionTime: currStreamInfo?.ext_bidding_duration,
      });
    }
  }, [currStreamInfo]);

  /* Conditionals */
  if (isLoginPage) return null;

  return (
    <View>
      <View ref={ref} style={[styles.headerContainer, styles.indicator, styles.justifyCenter]}>
        <View style={styles.contentContainer}>
          {isStreamPage ? (
            <View style={{flexDirection: 'row', gap: 16, alignItems: 'center'}}>
              <Image style={styles.logoIcon} source={require('@/root/web/assets/images/apptile_icon.png')} />
              <Text
                style={[
                  commonStyles.baseText,
                  {fontSize: 20, fontWeight: '600', color: theme.SECONDARY_COLOR, maxWidth: 300, lineHeight: 20},
                ]}
                numberOfLines={1}>
                {currStreamInfo?.streaming_name}
              </Text>

              <View style={{flexDirection: 'row', gap: 5, alignItems: 'center'}}>
                <Icon color="PRIMARY" iconType="MaterialCommunityIcons" name="circle" size={12} />
                <TextElement fontSize="xs" color="PRIMARY" fontWeight="500">
                  LIVE
                </TextElement>
              </View>
            </View>
          ) : (
            <Image style={styles.logo} source={require('@/root/web/assets/icons/apptile-live.svg')} />
          )}
          {showActionButtons && (
            <View style={[styles.rowLayout, {gap: 10}]}>
              <Button
                icon={'reload'}
                onPress={handleRefresh}
                textStyles={{fontWeight: '500'}}
                color="PRIMARY"
                containerStyles={{borderWidth: 0, backgroundColor: 'rgb(217,234,255)'}}>
                Refresh
              </Button>
              <Pressable onPress={handleLogout} style={styles.logoutButton}>
                <Text
                  style={[
                    commonStyles.baseText,
                    {
                      fontSize: 13,
                      fontWeight: '500',
                      color: 'black',
                    },
                  ]}>
                  Logout
                </Text>
              </Pressable>
            </View>
          )}
          {/* {isAuction && (
            <Pressable 
              onPress={() => setShowSettingsModal(true)} 
              style={{flexDirection: 'row', alignItems: 'center', gap: 10, borderColor: theme.CONTROL_BORDER, borderWidth: 1, borderRadius: 50, paddingHorizontal: 20, paddingVertical: 8}}
            >
              <Icon name="settings" iconType="Feather" size='md' color="#333333" />
              <TextElement
                fontSize="sm"
                style={{color: '#333333'}}
                fontWeight="500"
              >
                Auction Settings
              </TextElement>
            </Pressable>
          )} */}
        </View>
      </View>
      {/* TODO(NAVEEN): Add API support to update the stream info */}
      {/* {showSettingsModal && (
        <ModalComponent
          visible={showSettingsModal}
          onVisibleChange={setShowSettingsModal}
          content={
            <View style={{width: 500}}>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', borderBottomWidth: 1, borderBottomColor: theme.INPUT_BORDER, ...styles.sectionPadding, paddingVertical: 16}}>
                <TextElement fontSize="md" fontWeight="500" color="SECONDARY">Auction Settings</TextElement>
                <Pressable onPress={() => setShowSettingsModal(false)} style={{padding: 4}}>
                  <Icon name='close' iconType="Ionicons" size={20} color='SECONDARY' />
                </Pressable>
              </View>
              <View style={[styles.sectionPadding, {paddingTop: 0}]}>
                <AuctionMeta 
                  auctionMeta={auctionMeta}
                  setAuctionMeta={setAuctionMeta}
                  currencyCode={currencyCode}
                />
              </View>
              <View style={[styles.sectionPadding, {paddingTop: 0}]}>
                <Button
                  onPress={() => setShowSettingsModal(false)}
                  textStyles={{fontWeight: '500'}}
                  color="PRIMARY"
                  disabled={!canUpdateAuctionSettings}
                  containerStyles={{borderWidth: 0, backgroundColor: canUpdateAuctionSettings ? 'rgb(217,234,255)' : theme.INPUT_BACKGROUND}}>
                  Update
                </Button>
              </View>
            </View>
          }
        />
      )} */}
    </View>
  );
});

const styles = StyleSheet.create({
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  logo: {
    width: '185px',
    height: '35px',
  },
  logoIcon: {
    width: 36,
    height: 36,
    borderRadius: 4,
  },
  indicator: {
    backgroundColor: theme.DEFAULT_COLOR,
  },
  headerContainer: {
    width: '100%',
    height: 61,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: theme.CONTROL_BORDER,
    borderBottomWidth: 1,
  },
  contentContainer: {
    width: '95%',
    paddingVertical: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  joinButton: {
    backgroundColor: theme.PRIMARY_COLOR,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    width: 100,
    borderColor: '#e5e5e5',
    borderRadius: 24,
    borderWidth: 2,
  },
  sectionPadding: {
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
});

export default LiveStreamHeader;
