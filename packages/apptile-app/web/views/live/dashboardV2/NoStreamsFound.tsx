import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Image, StyleSheet, View} from 'react-native';
import { STREAM_TYPE } from '../dashboardV2';
import { Icon } from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';
import Button from '@/root/web/components-v2/base/Button';

const NoStreamsFound = ({streamType, onNavigate} : {streamType: string, onNavigate: () => void;}) => {
  return (
    <View style={styles.noProducts}>
      <View style={styles.imgWrapper}>
        <Image style={styles.emptyImage} source={require('../../../assets/images/snapshot-no-result.png')} resizeMode='contain' />
      </View>
      
      <View style={[styles.streamInfo]}>
        <View>
          <TextElement style={{marginBottom: 8}} color="SECONDARY" fontWeight="500" fontSize="3xl">
            {`Looks like there’s nothing here yet :(`}
          </TextElement>
          <TextElement style={{color: '#656565'}} fontWeight="400" fontSize="md">
            Kick things off by starting your first{" "}
            {streamType == STREAM_TYPE.LIVE
              ? `live stream`
              : `auction`}.
          </TextElement>
        </View>

        {streamType == STREAM_TYPE.LIVE ? (
          <View style={{flexDirection: 'column', gap: 12, marginTop: 8}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="videocam-outline" iconType="Ionicons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Engage with your audience in real-time
              </TextElement>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="chatbubble-outline" iconType="Ionicons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Interact through live chat and comments
              </TextElement>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="analytics-outline" iconType="Ionicons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Track viewer engagement and metrics
              </TextElement>
            </View>
          </View>
        ) : (
          <View style={{flexDirection: 'column', gap: 12, marginTop: 8}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="people-outline" iconType="Ionicons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Reach real-time bidders
              </TextElement>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="timer-outline" iconType="MaterialCommunityIcons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Set bid increment and auction timer
              </TextElement>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
              <Icon name="notifications-outline" iconType="Ionicons" size={20} color={theme.PRIMARY} />
              <TextElement style={{color: '#242424'}} fontSize="md" fontWeight="500">
                Manage bids and notifications easily
              </TextElement>
            </View>
          </View>
        )}

        <View style={[styles.streamActions, {marginTop: 10}]}>
          <Button
            color="CTA"
            variant="FILLED-PILL"
            containerStyles={{paddingHorizontal: 30}}
            onPress={onNavigate}
        >
            {streamType == STREAM_TYPE.LIVE ? `+  Create Stream` : `+  Create Auction`}
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  noProducts: {
    flexDirection: 'row',
    width:"100%",
    backgroundColor:"white",
    padding: 32,
    borderRadius: 12,
    gap: 32
  },
  streamActions: {
    flexDirection: 'row',
    gap: 12,
  },
  streamInfo: {
    flex: 1,
    justifyContent: 'space-between',
    gap: 16,
  },
  emptyImage: {width: '100%', height: 150},
  imgWrapper: {
    width: 360,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default NoStreamsFound ;
