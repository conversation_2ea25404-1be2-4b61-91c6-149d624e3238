import {Lively<PERSON><PERSON>} from '@/root/web/api/LivelyApi';
import {ILiveStream} from '@/root/web/api/LivelyApiTypes';
import {useLocation, useNavigate, useSearchParams} from '@/root/web/routing.web';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import {allAvailablePlans, Icon} from 'apptile-core';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {Animated, Pressable, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import TextElement from '../../../components-v2/base/TextElement';
import AnimatedDropdown from '../shared/AnimatedDropdown';
import Back from '../shared/Back';
import Loader from '../shared/Loader';
import NetworkErrorPage from '../shared/NetworkErrorPage';
import {LIVE_DASHBOARD_ROUTES} from '../utils/liveDashboardRoutes';
import {useMountTransitionAnimation} from '../utils/useMountTransitionAnimation';
import {StreamCard} from './streamCard';
import { setOpenPremiumModal } from '@/root/web/actions/editorActions';
import { currentPlanFeaturesSelector } from '@/root/web/selectors/FeatureGatingSelector';

const DROPDOWN_OPTIONS = [
  {
    label: 'Past 7 days',
    value: moment().subtract(7, 'days').format('YYYY-MM-DD'),
  },
  {
    label: 'Past 2 weeks',
    value: moment().subtract(14, 'days').format('YYYY-MM-DD'),
  },
  {
    label: 'Past 1 month',
    value: moment().subtract(1, 'month').format('YYYY-MM-DD'),
  },
  {
    label: 'Past 2 months',
    value: moment().subtract(2, 'month').format('YYYY-MM-DD'),
  },
];

interface PastStreamsProps {
  live_streams: ILiveStream[];
  pagination: {currentPage: number; totalPages: number} | null;
}

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const PastStreams = () => {
  /* Hooks */
  const {animatedStyle} = useMountTransitionAnimation({distance: -100, direction: 'x'});
  const authToken = useSelector((state: EditorRootState) => state?.liveSelling?.auth?.authToken);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  /* States */
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(DROPDOWN_OPTIONS[0]);
  const [pastStreams, setPastStreams] = useState<ILiveStream[]>([]);
  const [pagination, setPagination] = useState<PastStreamsProps['pagination']>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  /* Variables */
  const page = Number(searchParams.get('page')) || 1;
  const isAuction = location.pathname.includes('auction/auctions/past');

  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPlusDisabled = !currentPlanFeatures.includes(allAvailablePlans.PLUS);

  /* Effects */
  useEffect(() => {
    const getPastStreams = async () => {
      try {
        setLoading(true);
        const response = (await LivelyApi.getPastStreamWithPagination(
          authToken,
          selectedValue.value,
          new Date().toISOString().slice(0, 10),
          page,
          15,
          isAuction ? 'auction' : 'simple',
        )) as {data: {data: PastStreamsProps}};
        setPastStreams(response?.data?.data?.live_streams);
        setPagination(response?.data?.data?.pagination);
      } catch (error) {
        console.error('Error fetching past streams:', error);
        setError(error as Error);
      } finally {
        setLoading(false);
      }
    };
    const timeoutId = setTimeout(() => getPastStreams(), 300);
    return () => clearTimeout(timeoutId);
  }, [selectedValue.value, authToken, page]);

  if (error) return <NetworkErrorPage />;

  return (
    <Animated.View style={[styles.wrapper, animatedStyle]}>
      <View>
        <Back url={isAuction ? LIVE_DASHBOARD_ROUTES.ALL_AUCTIONS() : LIVE_DASHBOARD_ROUTES.ALL_STREAMS()} />
        <View style={{marginTop: 14, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
          <TextElement color="SECONDARY" fontSize="2xl" lineHeight="2xl" fontWeight="600">
            Past Streams
          </TextElement>

          <AnimatedDropdown
            visible={dropdownOpen}
            onVisibleChange={setDropdownOpen}
            options={DROPDOWN_OPTIONS}
            selectedValue={selectedValue.value}
            onSelect={setSelectedValue}
            trigger={
              <Pressable style={styles.dropdownButton}>
                <TextElement fontSize="sm" color="SECONDARY">
                  {selectedValue.label}
                </TextElement>
                <Icon
                  name={dropdownOpen ? 'chevron-up' : 'chevron-down'}
                  iconType="Feather"
                  size={12}
                  color={theme.SECONDARY}
                />
              </Pressable>
            }
          />
        </View>
      </View>

      {loading ? (
        <Loader />
      ) : (
        <View style={styles.streamsWrapper}>
          <View style={styles.streamsList}>
            {pastStreams?.map((entry: ILiveStream, idx: number) => (
              <StreamCard
                idUpcoming={false}
                streamInfo={entry}
                heading={entry.streaming_name || '-title-'}
                description={entry.streaming_description || '-description-'}
                key={idx}
                imageUrl={entry.streaming_thumbnail}
                productInfo={entry.product_info}
                streamTo={entry.stream_to}
                status={'ENDED'}
                startTime={entry.start_time}
                streaming_duration={entry.streaming_duration}
                onContainerPress={() => {
                  if (isPlusDisabled) {
                    dispatch(setOpenPremiumModal(true, allAvailablePlans.PLUS ?? ' ', 'Stream Analytics'));
                  } else {
                    navigate(`${LIVE_DASHBOARD_ROUTES.LIVE_STREAM_ANALYTICS(entry?.streaming_id)}?shopable-feed-id=${entry?.shoppable_feed_id}`);
                  }
                }}
              />
            ))}
          </View>
          {pagination && pagination.totalPages > 1 && (
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={(page: number) => {
                searchParams.set('page', String(page));
                navigate({search: searchParams.toString()});
              }}
            />
          )}
        </View>
      )}
    </Animated.View>
  );
};

const Pagination: React.FC<PaginationProps> = ({currentPage, totalPages, onPageChange}) => {
  if (totalPages <= 1) return null;
  const pageButtons: (number | string)[] = [];

  // Show first pages or 1, ...
  if (currentPage > 2) {
    pageButtons.push(1, '...');
  } else {
    for (let i = 1; i <= Math.min(4, totalPages); i++) {
      pageButtons.push(i);
    }
  }

  // Middle pages: only if not near start or end
  if (currentPage > 2 && currentPage < totalPages - 2) {
    pageButtons.push(currentPage - 1, currentPage, currentPage + 1);
  }

  // Show ... and last pages if not near end
  if (currentPage < totalPages - 2) {
    pageButtons.push('...');
    pageButtons.push(totalPages);
  } else {
    for (let i = Math.max(totalPages - 3, 1); i <= totalPages; i++) {
      if (!pageButtons.includes(i)) pageButtons.push(i);
    }
  }

  // Remove duplicates and sort
  const uniquePages = Array.from(new Set(pageButtons.filter(p => typeof p === 'number' && p >= 1 && p <= totalPages)));
  const finalButtons: (number | string)[] = [];
  let last: number | string | undefined = undefined;
  for (const p of pageButtons) {
    if (p === '...') {
      if (last !== '...') finalButtons.push('...');
    } else {
      if (last !== p) finalButtons.push(p);
    }
    last = p;
  }

  return (
    <View style={styles.paginationContainer}>
      {finalButtons.map((p, idx) =>
        p === '...' ? (
          <TextElement key={idx} color="SECONDARY" fontSize="sm" lineHeight="sm" fontWeight="400">
            ...
          </TextElement>
        ) : (
          <Pressable
            key={p as number}
            onPress={() => onPageChange(p as number)}
            style={[styles.paginationButton, p === currentPage && styles.paginationButtonActive]}>
            <TextElement
              color={p === currentPage ? 'PRIMARY' : 'SECONDARY'}
              fontSize="sm"
              lineHeight="sm"
              fontWeight="400">
              {p}
            </TextElement>
          </Pressable>
        ),
      )}
    </View>
  );
};

export default PastStreams;

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
  },
  streamsWrapper: {
    width: '100%',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 6,
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    minWidth: 180,
    marginBottom: 2,
  },
  streamsList: {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: 16,
    marginVertical: 30,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 24,
    paddingBottom: 10,
    gap: 8,
  },
  paginationButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'white',
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
  },
  paginationButtonActive: {
    backgroundColor: 'rgb(217,234,255)',
  },
});
