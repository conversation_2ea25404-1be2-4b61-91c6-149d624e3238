import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useSelector} from 'react-redux';
import {LivelyApi} from '../../../../api/LivelyApi';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import CheckboxControl from '@/root/web/components/controls/CheckboxControl';
import TextElement from '@/root/web/components-v2/base/TextElement';

const AiPopClipsSetting = () => {
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const onChange = () => {
    const label = isEnabled ? 'disable' : 'enable';
    LivelyApi.SetAipopClips(authToken, label).then(res => {
      setIsEnabled(!isEnabled);
    });
  };
  useEffect(() => {
    LivelyApi.GetAipopClipsValue(authToken).then((res: any) => {
      setIsEnabled(res?.data?.data?.status);
      setIsLoading(false);
    });
  }, [isEnabled]);
  return (
    <View style={[styles.container]}>
      <TextElement
        style={{textAlign: 'left', width: '100%', fontSize: 22}}
        color="SECONDARY"
        fontWeight="600"
      >
        Enable AI Pop Clips
      </TextElement>
      {isLoading ? (
        <TextElement>Loading...</TextElement>
      ) : (
        <CheckboxControl
          value={isEnabled}
          label={'Enable Clips'}
          fullSizeLabel={false}
          reverse={false}
          onChange={onChange}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: 360,
    gap: 10,
    marginBottom: 16
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});

export default AiPopClipsSetting;
