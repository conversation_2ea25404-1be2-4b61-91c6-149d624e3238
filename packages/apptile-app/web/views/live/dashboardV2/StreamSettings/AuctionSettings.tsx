import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes'
import TextElement from '@/root/web/components-v2/base/TextElement'
import TextInput from '@/root/web/components-v2/base/TextInput'
import CheckboxControl from '@/root/web/components/controls/CheckboxControl'
import { EditorRootState } from '@/root/web/store/EditorRootState'
import theme from '@/root/web/styles-v2/theme'
import { Icon } from 'apptile-core'
import React from 'react'
import { StyleSheet, View } from 'react-native'
import { useSelector } from 'react-redux'

const AuctionSettings = ({
  settings,  
  setSettings
} : {
  settings: ILiveStreamSettings;
  setSettings: (settings: ILiveStreamSettings) => void;
}) => {
  const currencyCode = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser.currency.currency_code);
  return (
    <View style={[styles.wrapper]}>
      <TextElement
        style={{textAlign: 'left', width: '100%', fontSize: 22, color: settings.auction ? theme.SECONDARY : theme.CONTROL_PLACEHOLDER_COLOR}}
        fontWeight="600"
      >
        Default Auction settings
        {!settings.auction && (
          <Icon iconType='Foundation' name="lock" size={12} color={theme.CONTROL_PLACEHOLDER_COLOR} style={{position: 'relative', left: 10, bottom: 10}}/>
        )}
      </TextElement>

      {settings.auction && (
        <View style={[styles.container]}>
          <View>
            <TextElement
              color='SECONDARY'
              fontSize='md'
              lineHeight='md'
              fontWeight='500'
            >
              Bidding Duration
            </TextElement>
            <TextElement
              style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
              fontSize='xs'
              lineHeight='xs'
              fontWeight='400'
            >
              Set how long the auction will accept bids
            </TextElement>
            <View style={{marginTop: 10}}>
              <TextInput
                value={settings.defaultBiddingDuration?.toString() || ''}
                onChangeText={value => {
                  let numeric = value.replace(/[^0-9]/g, '');
                  setSettings({
                    ...settings,
                    defaultBiddingDuration: numeric === '' ? undefined : +numeric
                  });
                }}
                icon="clock-outline"
                iconContainerStyle={{
                  backgroundColor: 'transparent'
                }}
                iconColor='#aaa'
                iconPosition="LEFT"
                textInputStyles={{
                  outline: 'none'
                }}
              />
              <TextElement
                fontSize="xs"
                lineHeight="xs"
                fontWeight="400"
                style={{
                  position: 'absolute',
                  top: '50%',
                  right: 10,
                  transform: [{translateY: '-50%'}],
                  color: '#aaa'
                }}
              >
                seconds
              </TextElement>
            </View>
          </View>

          <View>
            <TextElement
              color='SECONDARY'
              fontSize='md'
              lineHeight='md'
              fontWeight='500'
            >
              Bidding Increments
            </TextElement>
            <TextElement
              style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
              fontSize='xs'
              lineHeight='xs'
              fontWeight='400'
            >
              Set the minimum amount each new bid must increase by
            </TextElement>
            <View style={{marginTop: 10}}>
              <TextInput
                value={settings.defaultMinimumBidIncrement?.toString() || ''} 
                onChangeText={value => {
                  const numeric = value.replace(/[^0-9]/g, '');
                  setSettings({
                    ...settings,
                    defaultMinimumBidIncrement: numeric === '' ? undefined : +numeric
                  });
                }}
                icon="cash"
                iconContainerStyle={{
                  backgroundColor: 'transparent'
                }}
                iconColor='#aaa'
                iconPosition="LEFT"
                textInputStyles={{
                  outline: 'none'
                }}
              />
              <TextElement
                fontSize="xs"
                lineHeight="xs"
                fontWeight="400"
                style={{
                  position: 'absolute',
                  top: '50%',
                  right: 10,
                  transform: [{translateY: '-50%'}],
                  color: '#aaa'
                }}
              >
                {currencyCode}
              </TextElement>
            </View>
          </View>

          <View style={{flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
            <View>
              <TextElement
                color='SECONDARY'
                fontSize='md'
                lineHeight='md'
                fontWeight='500'
              >
                Extended Bidding
              </TextElement>
              <TextElement
                style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
                fontSize='xs'
                lineHeight='xs'
                fontWeight='400'
              >
                Settings for extending the bid window to prevent sniping
              </TextElement>
            </View>
            <View style={{justifyContent: 'flex-end'}}>
              <CheckboxControl
                fullSizeLabel={false}
                value={settings.defaultExtendedBidding}
                onChange={(value) => setSettings({...settings, defaultExtendedBidding: value})}
              />
            </View>
          </View>

          <View>
            <TextElement
              color='SECONDARY'
              fontSize='md'
              lineHeight='md'
              fontWeight='500'
            >
              Starting Bid Reduction Percentage
            </TextElement>
            <TextElement
              style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
              fontSize='xs'
              lineHeight='xs'
              fontWeight='400'
            >
              Set the default starting bid reduction percentage
            </TextElement>
            <View style={{marginTop: 10}}>
              <TextInput
                value={settings.defaultStartingBidPercentage?.toString() || ''} 
                onChangeText={value => {
                  const numeric = value.replace(/[^0-9]/g, '');
                  setSettings({
                    ...settings,
                    defaultStartingBidPercentage: numeric === '' ? undefined : +numeric
                  });
                }}
                icon="cash"
                iconContainerStyle={{
                  backgroundColor: 'transparent'
                }}
                iconColor='#aaa'
                iconPosition="LEFT"
                textInputStyles={{
                  outline: 'none'
                }}
              />
              <TextElement
                fontSize="xs"
                lineHeight="xs"
                fontWeight="400"
                style={{
                  position: 'absolute',
                  top: '50%',
                  right: 10,
                  transform: [{translateY: '-50%'}],
                  color: '#aaa'
                }}
              >
                %
              </TextElement>
            </View>
          </View>

          <View>
            <TextElement
              color='SECONDARY'
              fontSize='md'
              lineHeight='md'
              fontWeight='500'
            >
              Bidding Cap
            </TextElement>
            <TextElement
              style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
              fontSize='xs'
              lineHeight='xs'
              fontWeight='400'
            >
              Set the default bidding cap
            </TextElement>
            <View style={{marginTop: 10}}>
              <TextInput
                value={settings.defaultBiddingCap?.toString() || ''} 
                onChangeText={value => {
                  const numeric = value.replace(/[^0-9]/g, '');
                  setSettings({
                    ...settings,
                    defaultBiddingCap: numeric === '' ? undefined : +numeric
                  });
                }}
                icon="cash"
                iconContainerStyle={{
                  backgroundColor: 'transparent'
                }}
                iconColor='#aaa'
                iconPosition="LEFT"
                textInputStyles={{
                  outline: 'none'
                }}
              />
              <TextElement
                fontSize="xs"
                lineHeight="xs"
                fontWeight="400"
                style={{
                  position: 'absolute',
                  top: '50%',
                  right: 10,
                  transform: [{translateY: '-50%'}],
                  color: '#aaa'
                }}
              >
                {currencyCode}
              </TextElement>
            </View>
          </View>
        </View>
      )}
      
    </View>
  )
}

export default AuctionSettings

const styles = StyleSheet.create({
  wrapper: {
    width: 400
  },
  container: {
    marginTop: 20,
    gap: 24,
  }
})