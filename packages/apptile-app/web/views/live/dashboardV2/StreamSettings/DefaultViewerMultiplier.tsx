import {makeToast} from '@/root/web/actions/toastActions';
import {LivelyApi} from '@/root/web/api/LivelyApi';
import TextElement from '@/root/web/components-v2/base/TextElement';
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import NetworkErrorCmp from '../../shared/NetworkErrorCmp';
import { fetchStreamSettings } from '@/root/web/actions/liveSellingActions';

const DefaultViewerMultiplier = () => {
  const {
    data: streamSettings,
    loading: streamSettingsLoading,
    isNetworkError: streamSettingsIsNetworkError
  } = useSelector((state: EditorRootState) => state.liveSelling?.streamSettings ?? {});
  const authToken = useSelector((state: EditorRootState) => state?.liveSelling?.auth?.authToken);

  const dispatch = useDispatch();
  const [loading, _] = useState(streamSettingsLoading);
  
  const [viewerMultiplier, setViewerMultiplier] = useState<number | null>(streamSettings?.default_multiplier);

  useEffect(() => {
    setViewerMultiplier(streamSettings?.default_multiplier);
  }, [streamSettings?.default_multiplier])

  const onViewerMultiplierChange = async (value: number) => {
    try {
      await LivelyApi.addMetadataToCompany(authToken, {...streamSettings, default_multiplier: value});
      setViewerMultiplier(value);
      dispatch(fetchStreamSettings());
    } catch (error) {
      console.error('Error setting viewer multiplier', error);
      dispatch(
        makeToast({
          content: 'Error setting viewer multiplier. Please try again!',
          appearances: 'error',
          duration: 5000,
        }),
      );
    }
  };

  return (
    <View style={[styles.container]}>
      <TextElement
        color='SECONDARY'
        fontSize="md"
        lineHeight="md"
        fontWeight="500"
      >
        Default Viewer Multiplier
      </TextElement>
      <TextElement
        style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
        fontSize='xs'
        lineHeight='xs'
        fontWeight='400'
      >
        Choose a default viewer multiplier to be used for all your live shows.
      </TextElement>
      {streamSettingsIsNetworkError ? (
        <NetworkErrorCmp 
          title="Network Connection Lost!" 
          desc="We couldn't connect to the server. Please check your internet connection and try again." 
          CTA="Retry" 
        />
      ) : (
        <View style={{marginTop: 10, width: 310}}>
          {loading ? (
            <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
          ) : (
            <RadioGroupControl
              disableBinding={true}
              value={viewerMultiplier?.toString()}
              options={[
                {text: '1x', value: '1'},
                {text: '2x', value: '2'},
                {text: '3x', value: '3'},
                {text: '4x', value: '4'},
              ]}
              disabled={loading}
              onChange={onViewerMultiplierChange}
            />
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 20,
    width: '100%',
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});

export default DefaultViewerMultiplier;
