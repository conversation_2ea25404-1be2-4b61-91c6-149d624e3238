import { fetchFacebookPages } from '@/root/web/actions/liveSellingActions';
import { LivelyApi } from '@/root/web/api/LivelyApi';
import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes';
import TextElement from '@/root/web/components-v2/base/TextElement';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import { Icon, selectAppConfig } from 'apptile-core';
import React, { useEffect, useState } from 'react';
import { Image, Pressable, StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { FACEBOOK_AUTH_APP_ID, FACEBOOK_AUTH_CONFIG_ID } from '../../../../../.env.json';
import NetworkErrorCmp from '../../shared/NetworkErrorCmp';

// TODO: Check unauth state

export const FacebookSettings: React.FC<{
  settings: ILiveStreamSettings;
  setSettings: (settings: ILiveStreamSettings) => void;
}> = ({settings, setSettings}) => { 
  const dispatch = useDispatch();

  const appConfig = useSelector(selectAppConfig);
  const authToken = useSelector((state: EditorRootState) => state.liveSelling?.auth?.authToken || '');
  const facebookLoading = useSelector((state: EditorRootState) => state.liveSelling?.facebookChannelLoading || false);
  const facebookFound = useSelector((state: EditorRootState) => state.liveSelling?.facebookChannelFound || false);
  const facebookChannels = useSelector((state: EditorRootState) => state.liveSelling?.facebookChannelAuths || []);
  const isFacebookConnectionError = useSelector((state: EditorRootState) => state.liveSelling?.isFacebookConeectionNetworkError || false);

  const [loading, setLoading] = useState(facebookLoading);

  const handlePageSelection = async (fbAuthId: string) => {
    if (settings.streamToFacebookAuthId == fbAuthId) setSettings({
      ...settings,
      streamToFacebookAuthId: ''
    });
    else setSettings({
      ...settings,
      streamToFacebookAuthId: fbAuthId
    });
  }

  const handleLoginFacebook = async () => {
    window.FB.login(
      (response: any) => {
        console.log(response);
        if (response.status === 'connected') {
          LivelyApi.updateFacebookStatus(
            authToken,
            response?.authResponse?.accessToken,
            response?.authResponse?.userID,
          ).then(() => {
            dispatch(fetchFacebookPages());
          });
          dispatch(fetchFacebookPages());
        }
      },
      {
        config_id: FACEBOOK_AUTH_CONFIG_ID,
      },
    );
  };

  useEffect(() => {
    setLoading(facebookLoading)
  }, [facebookLoading])

  useEffect(() => {
    console.log("REACHED")
    if (!facebookLoading && !facebookFound) {
      (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
          return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js';
        fjs.parentNode.insertBefore(js, fjs);
      })(document, 'script', 'facebook-jssdk');

      console.log("REACHED INSIDE IF")
      window.fbAsyncInit = function () {
        window.FB.init({
          appId: FACEBOOK_AUTH_APP_ID,
          cookie: true,
          xfbml: true,
          version: 'v19.0',
        });
        window.FB.AppEvents.logPageView();
      };
    }
  }, [appConfig, facebookFound, facebookLoading]);

  return (
    <View style={styles.container}>
      <View style={styles.streamSettings}>
        <View>
          <TextElement
            color='SECONDARY'
            fontSize="md"
            lineHeight="md"
            fontWeight="500"
          >
            Facebook Streaming
          </TextElement>
          <TextElement
            style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}  
            fontSize='xs'
            lineHeight='xs'
            fontWeight='400'
          >
            Select a facebook page for streaming your live shows.
          </TextElement>
          {isFacebookConnectionError ? (
            <NetworkErrorCmp 
              title="Facebook Connection Lost!" 
              desc="We couldn't connect to Facebook. Please check your internet connection and try again." 
              CTA="Retry" 
            />
          ) : (
            <View style={styles.connectionSection}>
              {(facebookLoading || loading) ? <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} /> : (
                <>
                  {!facebookLoading && !facebookFound && (
                    <Pressable onPress={handleLoginFacebook} style={styles.facebookButton}>
                      <TextElement
                        fontSize="sm"
                        lineHeight="sm"
                        fontWeight="400"
                        style={{color: 'white'}}
                      >
                        Connect with facebook
                      </TextElement>
                      <Icon color={'#fff'} name="facebook" size={20} iconType={'MaterialCommunityIcons'} />
                    </Pressable>
                  )}
                  {!facebookLoading &&
                    facebookFound &&
                      <View style={styles.channelsWrapper}>
                        {facebookChannels.map((e: any) => (
                        <Pressable
                          key={e.fb_auth_id}
                          onPress={() => handlePageSelection(e.fb_auth_id)}
                          style={[{
                            width: 100,
                            overflow: 'hidden',
                            alignItems: 'center',
                            borderRadius: 8,
                            backgroundColor: settings.streamToFacebookAuthId == e.fb_auth_id ? 'white' : undefined,
                            paddingVertical: 10,
                            paddingHorizontal: 20,
                            margin: theme.PRIMARY_MARGIN
                          }, settings.streamToFacebookAuthId == e.fb_auth_id && {boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.25)'}]}
                        >
                          <View>
                            <Image
                              source={{uri: e.fb_page.profile_picture}}
                              style={{width: 60, height: 60, borderRadius: 80}}
                              resizeMode="cover"
                            />
                            <Image
                              source={require('@/root/web/assets/images/logos_facebook.png')}
                              style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                              resizeMode="contain"
                            />
                          </View>
                          <TextElement
                            style={{
                              textAlign: 'center',
                              fontSize: 12,
                              marginTop: 10,
                              color: settings.streamToFacebookAuthId == e.fb_auth_id ? theme.CTA : theme.EDITOR_LIGHT_BLACK_COLOR
                            }}
                            > 
                            {e.fb_page.name}
                          </TextElement>
                        </Pressable>
                      ))}
                    </View>
                  }
                </>
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
  sectionContainer: {
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  streamSettings: {
    width: '100%',
  },
  connectionSection: {
    marginTop: 15,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  channelsWrapper: {
    backgroundColor: theme.INPUT_BACKGROUND,
    flexDirection: 'row',
    borderRadius: 8
  },
  buttonContainer: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  pageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginVertical: 5,
  },
  facebookButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#0766FF',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
});
