import React from 'react'
import {StyleSheet, View} from 'react-native'
import TextElement from '@/root/web/components-v2/base/TextElement'
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl'
import { FacebookSettings } from './FacebookSettings'
import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes'
import InstagramSettings from './InstagramSettings'

const StreamSettings = ({
  settings,
  setSettings
}: {
  settings: ILiveStreamSettings;
  setSettings: (settings: ILiveStreamSettings) => void;
}) => {
  return (
    <View>
      <TextElement
        style={{textAlign: 'left', width: '100%', fontSize: 22}}
        color="SECONDARY"
        fontWeight="600"
      >
        Default Stream settings
      </TextElement>
      <View style={styles.streamSettings}>
        <View style={[styles.viewerMultiplierContainer]}>
          <TextElement
            color='SECONDARY'
            fontSize="md"
            lineHeight="md"
            fontWeight="500"
          >
            Viewer Multiplier
          </TextElement>
          <TextElement
            style={{color: '#B0B0B0', marginTop: 4}}  
            fontSize='xs'
            lineHeight='xs'
            fontWeight='400'
          >
            Choose a default viewer multiplier to be used for all your live shows.
          </TextElement>
          <View style={{marginTop: 10, width: 310}}>
            <RadioGroupControl
              label=""
              disableBinding={true}
              value={settings.default_multiplier}
              options={[
                {text: '1x', value: 1},
                {text: '2x', value: 2},
                {text: '3x', value: 3},
                {text: '4x', value: 4},
              ]}
              onChange={(val: string) => setSettings({
                ...settings,
                default_multiplier: +val
              })}
            />
          </View>
        </View>
        <View>
          <TextElement
            color='SECONDARY'
            fontSize="md"
            lineHeight="md"
            fontWeight="500"
          >
            Reactions Type
          </TextElement>
          <TextElement
            style={{color: '#B0B0B0', marginTop: 4}}  
            fontSize='xs'
            lineHeight='xs'
            fontWeight='400'
          >
            Choose a default reaction type to your live shows.
          </TextElement>
          <View style={{marginTop: 10, width: 310}}>
            <RadioGroupControl
              label=""
              disableBinding={true}
              value={settings.defaultReactions}
              options={[
                {text: 'Single', value: 'single'},
                {text: 'Multiple', value: 'multiple'},
              ]}
              onChange={(val: string) => setSettings({
                ...settings,
                defaultReactions: val
              })}
            />
          </View>
        </View>
        <FacebookSettings 
          settings={settings}
          setSettings={setSettings}
        />
        {(settings?.instagram ?? false) && (
          <InstagramSettings
            settings={settings}
            setSettings={setSettings}
          />
        )}
      </View>
    </View>
  )
}

export default StreamSettings

const styles = StyleSheet.create({
  streamSettings: {
    width: '100%',
    gap: 24,
    marginTop: 20,
  },
  viewerMultiplierContainer: {
    marginRight: 20,
    width: '100%',
  },
})
