import React, { useEffect, useState } from 'react';
import {StyleSheet, Animated, View, Pressable} from 'react-native';
import { useMountTransitionAnimation } from '../../utils/useMountTransitionAnimation';
import { useDispatch, useSelector } from 'react-redux';
import { fetchStreamSettings, saveStreamSettings } from '@/root/web/actions/liveSellingActions';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import AuctionSettings from './AuctionSettings';
import StreamSettings from './StreamSettings';
import TextElement from '@/root/web/components-v2/base/TextElement';
import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes';
import Button from '@/root/web/components-v2/base/Button';
import { makeToast } from '@/root/web/actions/toastActions';
import AiPopClipsSetting from './AiPopClipsSetting';

const Settings: React.FC = () => {
  const dispatch = useDispatch();
  const {animatedStyle} = useMountTransitionAnimation({direction: 'y'});
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);

  const streamSettingsFromState = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data);
  const streamSettingsLoading = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.loading);

  const [settings, setSettings] = useState<ILiveStreamSettings>({
    app_id: streamSettingsFromState?.app_id!,
    polling: streamSettingsFromState?.polling!,
    auction: streamSettingsFromState?.auction!,
    defaultMinimumBidIncrement: streamSettingsFromState?.defaultMinimumBidIncrement!,
    defaultBiddingDuration: (streamSettingsFromState?.defaultBiddingDuration ? streamSettingsFromState.defaultBiddingDuration / 1000 : undefined),
    defaultExtendedBidding: streamSettingsFromState?.defaultExtendedBidding!,
    default_multiplier: streamSettingsFromState?.default_multiplier!,
    streamToFacebookAuthId: streamSettingsFromState?.streamToFacebookAuthId!,
    streamToInstagramAuthId: streamSettingsFromState?.streamToInstagramAuthId!,
    defaultStartingBidPercentage: streamSettingsFromState?.defaultStartingBidPercentage!,
    defaultBiddingCap: streamSettingsFromState?.defaultBiddingCap!,
    defaultReactions: streamSettingsFromState?.defaultReactions!,
    instagram: streamSettingsFromState?.instagram!,
  })

  // Change detection
  let changesMade =
    settings.defaultMinimumBidIncrement !== streamSettingsFromState?.defaultMinimumBidIncrement ||
    settings.defaultBiddingDuration !== (streamSettingsFromState?.defaultBiddingDuration ? streamSettingsFromState.defaultBiddingDuration / 1000 : undefined) ||
    settings.defaultExtendedBidding !== streamSettingsFromState?.defaultExtendedBidding ||
    settings.default_multiplier !== streamSettingsFromState?.default_multiplier ||
    settings.streamToFacebookAuthId !== streamSettingsFromState?.streamToFacebookAuthId ||
    settings.streamToInstagramAuthId !== streamSettingsFromState?.streamToInstagramAuthId ||
    settings.defaultStartingBidPercentage !== streamSettingsFromState?.defaultStartingBidPercentage ||
    settings.defaultBiddingCap !== streamSettingsFromState?.defaultBiddingCap ||
    settings.defaultReactions !== streamSettingsFromState?.defaultReactions ||
    settings.instagram !== streamSettingsFromState?.instagram;

  // Handlers
  const handleDiscardChanges = () => {
    setSettings({
      app_id: streamSettingsFromState?.app_id!,
      polling: streamSettingsFromState?.polling!,
      auction: streamSettingsFromState?.auction!,
      defaultMinimumBidIncrement: streamSettingsFromState?.defaultMinimumBidIncrement!,
      defaultBiddingDuration: (streamSettingsFromState?.defaultBiddingDuration ? streamSettingsFromState.defaultBiddingDuration / 1000 : undefined),
      defaultExtendedBidding: streamSettingsFromState?.defaultExtendedBidding!,
      default_multiplier: streamSettingsFromState?.default_multiplier!,
      streamToFacebookAuthId: streamSettingsFromState?.streamToFacebookAuthId!,
      streamToInstagramAuthId: streamSettingsFromState?.streamToInstagramAuthId!,
      defaultStartingBidPercentage: streamSettingsFromState?.defaultStartingBidPercentage!,
      defaultBiddingCap: streamSettingsFromState?.defaultBiddingCap!,
      defaultReactions: streamSettingsFromState?.defaultReactions!,
      instagram: streamSettingsFromState?.instagram!,
    })
  };

  const handleSaveChanges = () => {
    dispatch(saveStreamSettings({
      ...settings,
      defaultBiddingDuration: settings.defaultBiddingDuration ? settings.defaultBiddingDuration * 1000 : undefined
    }));
    setTimeout(() => dispatch(makeToast({
      content: 'Settings saved successfully',
      appearances: 'success',
      duration: 3000
    })), 500);
  };

  useEffect(() => {
    if(streamSettingsFromState) {
      setSettings({
        app_id: streamSettingsFromState?.app_id,
        polling: streamSettingsFromState?.polling,
        auction: streamSettingsFromState?.auction,
        defaultMinimumBidIncrement: streamSettingsFromState?.defaultMinimumBidIncrement,
        defaultBiddingDuration: streamSettingsFromState?.defaultBiddingDuration ? streamSettingsFromState.defaultBiddingDuration / 1000 : undefined,
        defaultExtendedBidding: streamSettingsFromState?.defaultExtendedBidding,
        default_multiplier: streamSettingsFromState?.default_multiplier,
        streamToFacebookAuthId: streamSettingsFromState?.streamToFacebookAuthId,
        streamToInstagramAuthId: streamSettingsFromState?.streamToInstagramAuthId,
        defaultStartingBidPercentage: streamSettingsFromState?.defaultStartingBidPercentage,
        defaultBiddingCap: streamSettingsFromState?.defaultBiddingCap,
        defaultReactions: streamSettingsFromState?.defaultReactions,
        instagram: streamSettingsFromState?.instagram,
      })
    }
  }, [streamSettingsFromState])

  useEffect(() => {
    if(authToken) dispatch(fetchStreamSettings());
  }, [dispatch, authToken])

  return (
    <Animated.View style={[animatedStyle, {width: '100%'}]}>

      <View style={{flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'space-between'}}>
        <View style={[styles.container, {flex: 1}]}>
          <StreamSettings 
            settings={settings}
            setSettings={setSettings}
          />
          <AuctionSettings
            settings={settings}
            setSettings={setSettings}
          />
          <AiPopClipsSetting />
        </View>

        <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
          <Button disabled={!changesMade || streamSettingsLoading} onPress={handleDiscardChanges} 
            containerStyles={[
              styles.button,
              {backgroundColor: 'white'},
              !changesMade && styles.disabledButton
            ]}
          >
            <TextElement
              fontSize='sm'
              fontWeight='500'
              color='SECONDARY'
            >
              Discard
            </TextElement>
          </Button>
          <Button loading={streamSettingsLoading} disabled={!changesMade || streamSettingsLoading} onPress={handleSaveChanges} 
            containerStyles={[
              styles.button,
              {backgroundColor: 'rgb(217,234,255)'},
              !changesMade && styles.disabledButton
            ]}
          >
            <TextElement  
              fontSize='sm'
              fontWeight='500'
              color='PRIMARY'
            >
              Save
            </TextElement>
          </Button>
        </View>
      </View>


    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    gap: 50,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    width: 100,
    borderColor: '#e5e5e5',
    borderRadius: 24,
    borderWidth: 2,
    padding: 8
  },
  disabledButton: {
    opacity: 0.5,
  },
});

export default Settings;
  