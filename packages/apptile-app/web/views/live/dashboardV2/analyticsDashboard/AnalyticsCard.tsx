import {StyleSheet, View} from 'react-native';
import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Icon} from 'apptile-core';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';

type AnalyticsCardProps = {
  // iconName?: string;
  eventName: string;
  value: any;
  isAmount?: boolean;
  currencyCode?: any;
  isfirst?: boolean;
  // iconType:string;
  showWarning?: boolean;
  isReach?: boolean;
};

const AnalyticsCard: React.FC<AnalyticsCardProps> = props => {
  const {eventName, value, isAmount, currencyCode, isfirst, showWarning, isReach} = props;

  return (
    <View style={[styles.innerConatiner, {paddingLeft: isfirst ? 0 : 20}]}>
      <View style={styles.leftConatiner}>
        <View style={{width: '100%', flexDirection: 'row', alignItems: 'flex-start'}}>
          <TextElement
            style={{textAlign: 'left', opacity: '0.6'}}
            color="EDITOR_LIGHT_BLACK"
            fontSize="md"
            lineHeight="md"
            fontWeight="500">
            {eventName}
          </TextElement>
          {isReach && (
            <Tooltip
              tooltip={'Viewer count for Live streams occurred through Apptile Live Broadcaster App'}
              position="top"
              toolTipMenuStyles={{width: 200, height: 50,left:30}}>
              <Icon
                name="information-outline"
                iconType="MaterialCommunityIcons"
                size={20}
                color={'black'}
                style={{
                  margin: 0,
                  verticalAlign: 'middle',
                  opacity: '0.6',
                  fontWeight: 300,
                  alignSelf: 'cenetr',
                  marginLeft: 10,
                }}
              />
            </Tooltip>
          )}
        </View>
        <View style={styles.rightConatiner}>
          <View style={styles.value}>
            <TextElement style={{textAlign: 'left'}} color="SECONDARY" fontSize="3xl" lineHeight="3xl" fontWeight="600">
              {isAmount && currencyCode && showWarning === false ? currencyCode(value) : value}
            </TextElement>
          </View>
          {/* <View style={[styles.Icon,{marginRight: isSecondRow ? 10 : 30}]}>
              <Icon
              name={iconName}
              iconType={iconType}
              size={24}
              color={'black'}
              style={{margin: 0, verticalAlign: 'middle',opacity:'0.6',padding:10,fontWeight:300}}
            />
          </View> */}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  innerConatiner: {
    paddingTop: 10,
    paddingBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    width: '-webkit-fill-available',
  },
  leftConatiner: {
    flexDirection: 'column',
    width: '100%',
    alignSelf: 'center',
  },
  rightConatiner: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // alignSelf: 'center',
  },
  Icon: {
    alignSelf: 'center',
    backgroundColor: 'rgba(235, 235, 235, 1)',
    borderRadius: 50,
    // paddingRight: 10,
    // marginRight:10
  },
  value: {
    alignSelf: 'center',
  },
});

export default AnalyticsCard;
