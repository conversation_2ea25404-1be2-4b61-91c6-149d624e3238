import theme from '@/root/web/styles-v2/theme';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {Pressable, StyleSheet, View} from 'react-native';
import AnalyticsCard from './AnalyticsCard';
import React, {useEffect, useState} from 'react';
import {Icon} from 'apptile-core';
import _ from 'lodash-es';
import {useSafeAreaFrame} from 'react-native-safe-area-context';

interface StreamAnalyticsProps {
  streamATC: any;
  streamViews: any;
  orderCount: any;
  streamtotalSales: any;
  commentsCount: any;
  currencyCode: any;
  avgOrderValue: any;
  showWarning: boolean;
  commentError: string;
  reachError: string;
  salesError: string;
  handleToast: any;
  atcError: string;
}
const StreamAnalytics: React.FC<StreamAnalyticsProps> = ({
  streamATC,
  streamViews,
  orderCount,
  streamtotalSales,
  commentsCount,
  currencyCode,
  avgOrderValue,
  showWarning,
  reachError,
  commentError,
  salesError,
  handleToast,
  atcError,
}) => {
  return (
    <View style={styles.container}>
      <TextElement
        style={{textAlign: 'left', width: '100%', opacity: '0.7'}}
        color="SECONDARY"
        fontSize="xl"
        lineHeight="xl"
        fontWeight="500">
        Analytics
      </TextElement>
      <View style={styles.contentConatainer}>
        <View>
          <View
            style={[
              styles.salesConatiner,
              {
                opacity: !_.isEmpty(salesError) || !_.isEmpty(atcError) ? 0.9 : 1,
                filter: !_.isEmpty(salesError) || !_.isEmpty(atcError) ? 'blur(2px)' : 'none',
              },
            ]}>
            <View style={[styles.heading]}>
              <TextElement style={{textAlign: 'left', width: '100%'}} fontSize="lg" lineHeight="l" fontWeight="500">
                Sales
              </TextElement>
            </View>

            <View style={[styles.dataConatiner]}>
              <View style={[styles.CardContainer, {borderRightWidth: 1}]}>
                <AnalyticsCard
                  // iconName="shopping-cart"
                  eventName="Add to Cart"
                  value={streamATC}
                  isfirst={true}
                  // iconType="Feather"
                  showWarning={showWarning}
                />
              </View>
              <View style={[styles.CardContainer, {borderRightWidth: 1}]}>
                <AnalyticsCard
                  // iconName="shopping-bag"
                  eventName="Order Count"
                  value={orderCount}
                  isfirst={false}
                  // iconType="Feather"
                  showWarning={showWarning}
                />
              </View>
              <View style={[styles.CardContainer, {borderRightWidth: 1}]}>
                <AnalyticsCard
                  // iconName="cash-outline"
                  eventName="Total Order Value"
                  value={streamtotalSales}
                  isAmount={true}
                  currencyCode={currencyCode}
                  isfirst={false}
                  showWarning={showWarning}
                  // iconType="Ionicons"
                />
              </View>
              <View style={[styles.CardContainer]}>
                <AnalyticsCard
                  // iconName="checklist"
                  eventName="Average Order Value"
                  value={avgOrderValue}
                  isAmount={true}
                  currencyCode={currencyCode}
                  isfirst={false}
                  showWarning={showWarning}
                  // iconType="MaterialIcons"
                />
              </View>
            </View>
          </View>
          {(!_.isEmpty(salesError) || !_.isEmpty(atcError)) && (
            <Pressable
              onPress={() => handleToast(!_.isEmpty(salesError) ? salesError : atcError)}
              style={styles.iconStyle}>
              <Icon
                name={'error'}
                iconType={'MaterialIcons'}
                size={30}
                color={'#AA2217'}
                style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
              />
            </Pressable>
          )}
        </View>

        <View style={{flexDirection: 'row', marginTop: 45}}>
          <View style={[styles.reachContainer]}>
            <View
              style={[
                styles.heading,
                {
                  opacity: !_.isEmpty(reachError) ? 0.9 : 1,
                  filter: !_.isEmpty(reachError) ? 'blur(1px)' : 'none',
                },
              ]}>
              <TextElement
                style={{textAlign: 'left', width: '100%'}}
                color="SECONDARY"
                fontSize="lg"
                lineHeight="l"
                fontWeight="500">
                Reach
              </TextElement>
            </View>
            <View
              style={[
                styles.dataConatiner,
                {
                  opacity: !_.isEmpty(reachError) ? 0.9 : 1,
                  filter: !_.isEmpty(reachError) ? 'blur(2px)' : 'none',
                },
              ]}>
              <View style={[styles.CardContainer]}>
                <AnalyticsCard
                  // iconName="eye"
                  eventName="Viewers"
                  value={streamViews}
                  isfirst={true}
                  // iconType="Feather"
                  showWarning={showWarning}
                  isReach={true}
                />
              </View>
            </View>
            {!_.isEmpty(reachError) && (
              <Pressable onPress={() => handleToast(reachError)} style={{...styles.iconStyle, padding: 20}}>
                <Icon
                  name={'error'}
                  iconType={'MaterialIcons'}
                  size={30}
                  color={'#AA2217'}
                  style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
                />
              </Pressable>
            )}
          </View>

          <View style={[styles.reachContainer, {marginLeft: 40}]}>
            <View
              style={[
                styles.heading,
                {
                  opacity: !_.isEmpty(commentError) ? 0.9 : 1,
                  filter: !_.isEmpty(commentError) ? 'blur(1px)' : 'none',
                },
              ]}>
              <TextElement
                style={{textAlign: 'left', width: '100%'}}
                color="SECONDARY"
                fontSize="lg"
                lineHeight="l"
                fontWeight="500">
                Engagement
              </TextElement>
            </View>
            <View
              style={[
                styles.dataConatiner,
                {
                  opacity: !_.isEmpty(commentError) ? 0.9 : 1,
                  filter: !_.isEmpty(commentError) ? 'blur(2px)' : 'none',
                },
              ]}>
              <View style={[styles.CardContainer]}>
                <AnalyticsCard
                  // iconName="comment-text-outline"
                  eventName="Comments"
                  value={commentsCount}
                  // iconType="MaterialCommunityIcons"
                  showWarning={showWarning}
                  isfirst={true}
                />
              </View>
            </View>
            {!_.isEmpty(commentError) && (
              <Pressable onPress={() => handleToast(commentError)} style={{...styles.iconStyle, padding: 20}}>
                <Icon
                  name={'error'}
                  iconType={'MaterialIcons'}
                  size={30}
                  color={'#AA2217'}
                  style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
                />
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    width: '70%',
  },
  contentConatainer: {
    flexDirection: 'column',
    width: '100%',
    justifyContent: 'flex-start',
    marginTop: 20,
  },
  reachContainer: {
    width: '27%',
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 10,
    flexDirection: 'column',
    paddingBottom: 20,
    paddingLeft: 25,
    paddingRight: 25,
    paddingTop: 20,
  },
  salesConatiner: {
    width: '100%',
    height: 'auto',
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 10,
    flexDirection: 'column',
    paddingTop: 20,
    paddingBottom: 20,
    paddingLeft: 35,
    paddingRight: 30,
  },
  heading: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(206, 206, 206, 1)',
    paddingBottom: 20,
  },
  dataConatiner: {
    width: '100%',
    paddingTop: 20,
    paddingBottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  CardContainer: {
    width: '100%',
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    borderRightColor: 'rgba(206, 206, 206, 1)',
    backgroundColor: theme.DEFAULT_COLOR,
  },
  iconStyle: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    width: '100%',
    height: '100%',
  },
});

export default StreamAnalytics;
