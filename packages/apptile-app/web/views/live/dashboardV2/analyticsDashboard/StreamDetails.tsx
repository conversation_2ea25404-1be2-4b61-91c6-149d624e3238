import theme from '@/root/web/styles-v2/theme';
import TextElement from '@/root/web/components-v2/base/TextElement';
// import React from 'react';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import {Icon} from 'apptile-core';
import _ from 'lodash-es';

const StreamDetails = ({streamInfo, streamInfoError, handleToast}) => {
  function formatDuration(duration: any) {
    if (!duration) {
      return '0 seconds';
    }
    const parts = duration.split(':').map(Number);
    let hours = 0;
    let minutes = 0;
    let seconds = 0;

    if (parts.length === 3) {
      [hours, minutes, seconds] = parts;
    } else if (parts.length === 2) {
      [minutes, seconds] = parts;
    } else if (parts.length === 1) {
      [seconds] = parts;
    }

    const formattedParts = [];

    if (hours > 0) {
      formattedParts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    }
    if (minutes > 0) {
      formattedParts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
    }
    if (seconds > 0 || formattedParts.length === 0) {
      formattedParts.push(`${seconds} second${seconds !== 1 ? 's' : ''}`);
    }
    return formattedParts.join(' ');
  }

  const TextColor = () => {
    if (!_.isEmpty(streamInfoError)) {
      return 'EDITOR_LIGHT_BLACK';
    } else {
      return 'SECONDARY';
    }
  };

  const platforms = streamInfo?.stream_to || [];
  return (
    <View style={styles.container}>
      <TextElement
        style={{textAlign: 'left', width: '100%', opacity: '0.7'}}
        color="SECONDARY"
        fontSize="xl"
        lineHeight="xl"
        fontWeight="500">
        Stream Details
      </TextElement>
      <View>
      <View style={[styles.streamContent, {opacity: !_.isEmpty(streamInfoError) ? 0.8: 1, filter: !_.isEmpty(streamInfoError)  ? 'blur(2px)' : 'none'}]}>
          <Image
            style={styles.image}
            resizeMode={streamInfo?.streaming_thumbnail ? 'cover' : 'contain'}
            source={streamInfo?.streaming_thumbnail}
          />
          <View style={styles.detailsConatiner}>
            <View style={styles.details}>
              <TextElement style={{textAlign: 'left'}} color={TextColor()} fontSize="l" lineHeight="l" fontWeight="400">
                Start Time
              </TextElement>
              <TextElement
                style={{textAlign: 'right'}}
                color={TextColor()}
                fontSize="l"
                lineHeight="l"
                fontWeight="400">
                {new Date(streamInfo?.actual_start_time).toLocaleDateString('en-US', {
                  month: 'short',
                  day: '2-digit',
                  year: '2-digit',
                })}{' '}
                |{' '}
                {new Date(streamInfo?.actual_start_time).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </TextElement>
            </View>

            <View style={styles.details}>
              <TextElement style={{textAlign: 'left'}} color={TextColor()} fontSize="l" lineHeight="l" fontWeight="400">
                End Time
              </TextElement>
              <TextElement
                style={{textAlign: 'right'}}
                color={TextColor()}
                fontSize="l"
                lineHeight="l"
                fontWeight="400">
                {new Date(streamInfo?.actual_end_time).toLocaleDateString('en-US', {
                  month: 'short',
                  day: '2-digit',
                  year: '2-digit',
                })}{' '}
                |{' '}
                {new Date(streamInfo?.actual_end_time).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </TextElement>
            </View>

            <View style={styles.details}>
              <TextElement style={{textAlign: 'left'}} color={TextColor()} fontSize="l" lineHeight="l" fontWeight="400">
                Duration
              </TextElement>
              <TextElement
                style={{textAlign: 'right'}}
                color={TextColor()}
                fontSize="l"
                lineHeight="l"
                fontWeight="400">
                {formatDuration(streamInfo?.streaming_duration)}
              </TextElement>
            </View>

            <View style={styles.details}>
              <TextElement style={{textAlign: 'left'}} color={TextColor()} fontSize="l" lineHeight="l" fontWeight="400">
                Products
              </TextElement>
              <TextElement
                style={{textAlign: 'right'}}
                color={TextColor()}
                fontSize="l"
                lineHeight="l"
                fontWeight="400">
                {streamInfo?.product_info?.length} {streamInfo?.product_info?.length > 1 ? 'products' : 'product'}
              </TextElement>
            </View>

            <View style={[styles.details, {borderBottomWidth: 0}]}>
              <TextElement style={{textAlign: 'left'}} color={TextColor()} fontSize="l" lineHeight="l" fontWeight="400">
                Platform
              </TextElement>
              <View style={{flexDirection: 'row', alignSelf: 'center', gap: 7}}>
                <Image style={{width: 22, height: 22}} source={require('@/root/web/assets/images/app_icon1.png')} />
                {platforms.includes('instagram') && (
                  <Image
                    style={{width: 22, height: 22}}
                    source={require('@/root/web/assets/images/instagram_icon.png')}
                  />
                )}
                {platforms.includes('facebook') && (
                  <Image
                    style={{width: 22, height: 22}}
                    source={require('@/root/web/assets/images/facebook_icon.png')}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
        {!_.isEmpty(streamInfoError) && (
          <Pressable onPress={() => handleToast(streamInfoError)} style={styles.iconStyle}>
            <Icon
              name={'error'}
              iconType={'MaterialIcons'}
              size={30}
              color={'#AA2217'}
              style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
            />
          </Pressable>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '25%',
  },
  streamContent: {
    width: '100%',
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 10,
    marginTop: 20,
    flexDirection: 'column',
    padding: 25,
  },
  image: {
    width: '100%',
    height: '170px',
    borderRadius: 10,
    backgroundColor: 'grey',
    marginBottom: 20,
  },
  detailsConatiner: {
    marginTop: 8,
  },
  details: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 10,
    paddingBottom: 10,
    borderBottomColor: 'rgba(228, 228, 228, 1)',
    borderBottomWidth: 1.5,
  },
  iconStyle: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    padding: 0,
    width: '100%',
    height: '100%',
  },
});

export default StreamDetails;
