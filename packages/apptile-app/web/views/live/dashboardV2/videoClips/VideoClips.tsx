import React from 'react';
import {Animated, Image, Pressable, View, StyleSheet} from 'react-native';
import TextElement from '@/root/web/components-v2/base/TextElement';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {Icon} from 'apptile-core';
import Loader from '../../shared/Loader';
import { useMountTransitionAnimation } from '../../utils/useMountTransitionAnimation';
const _ = require('lodash');

const VideoClips = ({clipLoading, productVideoClips, handleActiveClipModal, handleVideOModalOpen}) => {

  const { animatedStyle, triggerUnmount } = useMountTransitionAnimation({direction: 'x', distance: 0})

  return (
    <Animated.View style={[styles.VideoClipDetailsContainer, animatedStyle]}>
        <TextElement style={{textAlign: 'left', width: '100%', fontSize: 15}} color="SECONDARY" fontWeight="400">
          All video clips
        </TextElement>

        {clipLoading ? (
          <View style={{marginTop: 40}}>
            <Loader />
          </View>
        ) : productVideoClips.length !== 0 ? (
          <View style={{marginVertical: 30, display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', marginTop: 10, gap: 18}}>
            {productVideoClips.map((entry, idx) => (
              <>
                <View
                  key={idx}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    height: 'max-content',
                    borderRadius: 6,
                    borderColor: 'rgba(206, 206, 206, 1)',
                    borderWidth: 1,
                  }}>
                  <Pressable
                    style={{width: '100%', height: 213}}
                    onPress={() => handleVideOModalOpen(entry?.streaming_url)}>
                    <Image
                      style={{width: '100%', height: '100%'}}
                      resizeMode={entry.thumbnail_image ? 'cover' : 'contain'}
                      source={
                        entry.thumbnail_image
                          ? entry.thumbnail_image
                          : require('../../../../assets/images/placeholder-image.png')
                      }
                    />

                    <Icon
                      reverse
                      reverseColor="gray"
                      name="playcircleo"
                      iconType="AntDesign"
                      size={32}
                      color={'white'}
                      style={{
                        margin: 0,
                        fontWeight: 300,
                        alignSelf: 'center',
                        position: 'absolute',
                        width: '100%',
                        height: '100%',
                        alignContent: 'center',
                        textAlign: 'center',
                      }}
                    />

                    {entry?.pdp_rank === 1 && (
                      <View style={{width: '100%', position: 'absolute', alignItems: 'flex-end'}}>
                        <View style={styles.LabelText}>
                          <TextElement fontWeight="500" style={{color: 'rgba(16, 96, 224, 1)', fontSize: 13}}>
                            {'Featured'}
                          </TextElement>
                        </View>
                      </View>
                    )}
                  </Pressable>
                  <View
                    style={{
                      paddingTop: 10,
                      paddingBottom: 10,
                      paddingLeft: 6,
                      paddingRight: 6,
                      width: '100%',
                      flexDirection: 'column',
                    }}>
                    <TextElement style={{fontSize: 15}} color="SECONDARY" lineHeight="md" fontWeight="400">
                      {_.truncate(entry?.name, {
                        length: 23,
                        omission: '...',
                      })}
                    </TextElement>

                    <View style={{flexDirection: 'row', gap: 7, alignItems: 'center', marginTop: 7}}>
                      <TextElement style={{fontSize: 13}} color="SECONDARY" lineHeight="md" fontWeight="500">
                        Date:
                      </TextElement>{' '}
                      <TextElement
                        style={{
                          opacity: 0.6,
                          fontSize: 13,
                        }}
                        color="SECONDARY"
                        lineHeight="md"
                        fontWeight="400">
                        {new Date(entry?.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: '2-digit',
                          year: '2-digit',
                        })}
                      </TextElement>
                    </View>
                    <View style={{flexDirection: 'row', gap: 7, alignItems: 'center'}}>
                      <TextElement
                        style={{fontSize: 13}}
                        color="SECONDARY"
                        fontSize="md"
                        lineHeight="md"
                        fontWeight="500">
                        Time:
                      </TextElement>{' '}
                      <TextElement
                        style={{
                          opacity: 0.6,
                          fontSize: 13,
                        }}
                        color="SECONDARY"
                        fontSize="md"
                        lineHeight="md"
                        fontWeight="400">
                        {new Date(entry?.createdAt).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit'})}
                      </TextElement>
                    </View>
                    <Pressable
                      onPress={() =>
                        (!entry?.pdp_rank || entry?.pdp_rank !== 1) &&
                        handleActiveClipModal(
                          entry?.product_info[entry?.product_info.length - 1]?.product_id,
                          entry?._id,
                        )
                      }
                      style={[
                        styles.customerAddButton,
                        {
                          borderColor: entry?.pdp_rank === 1 ? 'rrgba(210, 209, 209, 1)' : 'rgba(16, 96, 224, 1)',
                        },
                      ]}>
                      <TextElement
                        fontWeight="500"
                        style={[
                          commonStyles.baseText,
                          {
                            fontSize: 13,
                            color: entry?.pdp_rank === 1 ? 'rgba(210, 209, 209, 1)' : 'rgba(16, 96, 224, 1)',
                          },
                        ]}>
                        {'Feature Clip'}
                      </TextElement>
                    </Pressable>
                  </View>
                </View>
              </>
            ))}
          </View>
        ) : (
          <View style={styles.noProducts}>
          <Image style={styles.emptyImage} source={require('../../../../assets/images/snapshot-no-result.png')} />
          <TextElement style={[commonStyles.baseText, {fontSize: 13, fontWeight: '500', color: '#000'}]}>
            No clips found for this product
          </TextElement>
        </View>
        )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  customerProductCardNew: {
    width: '-webkit-fill-available',
    marginRight: 12,
    marginLeft: 22,
    height: 70,
    borderRadius: 12,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    top: 70,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    position: 'absolute',
  },
  customerProductCard: {
    position: 'absolute',
    bottom: 30,
    width: 240,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#A5A5A5',
  },
  customerAddButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    borderRadius: 100,
    borderWidth: 1,
    borderColor: 'rgba(16, 96, 224, 1)',
    backgroundColor: '#ffffff',
    height: 30,
    marginTop: 10,
  },
  LabelText: {
    width: 'fit-content',
    borderRadius: 100,
    backgroundColor: '#ffffff',
    paddingHorizontal: 10,
    alignContent: 'flex-end',
    marginRight: 5,
    marginTop: 5,
    paddingVertical: 3,
  },
  VideoClipDetailsContainer: {
    width: '100%',
    marginTop: 30,
  },
  noProducts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 45,
    marginTop:50
  },
  emptyImage: {width: 150, height: 150},
});

export default VideoClips;
