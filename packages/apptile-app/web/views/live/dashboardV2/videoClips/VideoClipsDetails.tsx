import React, {useEffect, useState} from 'react';
import TextElement from '../../../../components-v2/base/TextElement';
import {View, Text, StyleSheet, Pressable, ScrollView, Image} from 'react-native';
import {Icon} from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '../../../../api/LivelyApi';
import apolloQueryRunner from '@/root/app/plugins/datasource/ApolloWrapper/model';
import {shopifyProductTransformer} from '../../utils/transformers';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import {formatDisplayPrice} from '@/root/app/plugins/datasource/ShopifyV_22_10/utils/utils';
import {GET_SHOP} from '../../../../../app/plugins/datasource/ShopifyV_22_10/queries/shopDetails';
import {useNavigate, useParams} from 'react-router';
import LiveStreamHeader from '../LiveStreamHeader';
import commonStyles from '../../../../styles-v2/commonStyles';
import Loader from '../../shared/Loader';
import VideoClips from './VideoClips';
import {makeToast} from '@/root/web/actions/toastActions';
import ModalComponent from '@/root/web/components-v2/base/Modal';
import VideoPlayModal from './VideoPlayModal';
import ActivateClipModal from './ActivateClipModal';
import _ from 'lodash-es';
import {VIDEO_CLIPPING} from '../../../../../app/common/utils/apiErrorMessages/specificFeature';
import {handleApiError, handleApiErrorWithoutNetwork} from '../../shared/CommonError';
import {COMMON_ERROR_MESSAGE} from '../../../../../app/common/utils/apiErrorMessages/generalMessages';
import NetworkErrorPage from '../../shared/NetworkErrorPage';
import VideoUploadModal from './VideoUploadModal';

const VideoClipsDetails = ({productId, setActiveProduct}: {productId: string, setActiveProduct: (activeProduct: any) => void}) => {
  const authToken = useSelector(state => state.liveSelling?.auth?.authToken);
  const [queryRunner, setQueryRunner] = useState(null);
  const [currencyCode, setCurrencyCode] = useState(null);
  const [product, setProduct] = useState();
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const [isClipActive, setClipActive] = useState<boolean>();
  const [productVideoClips, setProductVideoClips] = useState([]);
  const companyId = useSelector(state => state.liveSelling?.auth?.livelyUser?.company_id);
  const dispatch = useDispatch();
  const [clipLoading, setClipLoading] = useState(true);
  const [VideoPlayerModal, setVideoPlayerModal] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [activeClipModal, setActiveClipModal] = useState(false);
  const [shoppableFeedId, setShoppableFeedId] = useState('');
  const [livelyProductId, setLivelyProductId] = useState('');
  const [activeClipInfo, setActiveClipInfo] = useState([]);
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [openVideoUploadModal, setOpenVideoUploadModal] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);

  const FILTER_STREAM_CLIPS = 'live_stream_chunks';

  useEffect(() => {
    if (authToken) {
      LivelyApi.getShopifyCreds(authToken)
        .then(response => {
          const newQueryRunner = apolloQueryRunner();
          newQueryRunner
            .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
              return {
                headers: {
                  ...headers,
                  'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                },
              };
            })
            .then(async () => {
              setQueryRunner(newQueryRunner);
              await newQueryRunner.runQuery('query', GET_SHOP, {}, {}).then(res => {
                const moneyFormatter = formatDisplayPrice(res.data.shop);
                setCurrencyCode(() => moneyFormatter);
              });
            });
        })
        .catch(error => {
          if (error.code === 'ERR_NETWORK') {
            setIsNetworkError(true);
          }
          setLoading(false);
          setClipLoading(false);
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getShopifyCreds', false);
        });
    }
  }, [authToken]);

  const addNewProduct = async (productId: any) => {
    let productMetadata = {};

    await queryRunner
      ?.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
        productId: productId,
      })
      .then(res => {
        if (res.data) {
          updateProductclips(productId);
          productMetadata = shopifyProductTransformer(res?.data?.product) ?? {};
          setProduct({
            featuredImage: productMetadata?.variants.length > 0 ? productMetadata?.variants[0].image : null,
            title: res?.data?.product?.title ?? '',
            ...productMetadata,
          });
          setLoading(false);
        }
      })
      .catch((error: any) => {
        if (error.code === 'ERR_NETWORK') {
          setIsNetworkError(true);
        }
        setClipLoading(false);
        handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'addNewProduct', false);
      });
  };

  useEffect(() => {
    if (productId) {
      addNewProduct(productId);
    } else {
      console.error('Product id is undefined', productId);
      dispatch(
        makeToast({
          content: COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
          appearances: 'error',
          duration: 5000,
        }),
      );
    }
  }, [productId, queryRunner]);

  const fetchActiveClipPublicApi = async () => {
    try {
      const response = await LivelyApi.getProductStreamClipsPublicApi(productId.split('/').pop(), companyId);

      const clips = response.data.data;
      // if (!Array.isArray(clips) || clips.length === 0) {
      //   handleApiErrorWithoutNetwork("", COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR, dispatch,"etchActiveClipPublicApi")
      //   setClipActive(isClipActive);
      //   setActiveClipInfo([]);
      //   return;
      // }

      const filteredActiveClips = clips.filter(e => e.pdp_rank === 1);
      const anyLiveClip = filteredActiveClips.length > 0;

      setClipActive(anyLiveClip);
      setActiveClipInfo(filteredActiveClips);
    } catch (error) {
      let showNetworkToast;
      if (error.code === 'ERR_NETWORK') {
        showNetworkToast = false;
      }
      handleApiError(
        error,
        COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
        dispatch,
        'fetchActiveClipPublicApi',
        showNetworkToast,
      );
      setActiveClipInfo(activeClipInfo);
      setClipActive(isClipActive);
      onCloseActiveClipModal();
    }
  };

  const setStreamActive = async (livelyProductIdParam?: string, shoppableFeedIdParam?: string) => {
    setClipLoading(true);

    try {
      await fetchActiveClipPublicApi().then(() => {
        const shoppableFeeds = [];
        let livelyProdId;

        if (!_.isEmpty(activeClipInfo)) {
          livelyProdId = activeClipInfo[0]?.product_info[0]?.product_id;
        } else {
          livelyProdId = livelyProductId || livelyProductIdParam;
        }
        for (let i = 0; i < activeClipInfo.length; i++) {
          const feedId = activeClipInfo[i]._id;
          shoppableFeeds.push({
            shoppable_feed_id: feedId,
            rank: -1,
          });
        }
        shoppableFeeds.push({
          shoppable_feed_id: shoppableFeedId || shoppableFeedIdParam,
          rank: 1,
        });
        LivelyApi.updateProductClipRank(authToken, livelyProdId, shoppableFeeds)
          .then(res => {
            onCloseActiveClipModal();
            setClipLoading(false);
            setLoading(false);
            updateProductclips(productId);
            if (res?.data?.message == 'Success') {
              dispatch(
                makeToast({
                  content: VIDEO_CLIPPING.SUCCESS.CLIP_ACTIVATION,
                  appearances: 'success',
                }),
              );
            } else {
              dispatch(
                makeToast({
                  content: COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                  appearances: 'error',
                }),
              );
            }
          })
          .catch(error => {
            setLoading(false);
            setClipLoading(false);
            onCloseActiveClipModal();
            handleApiError(
              error,
              COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
              dispatch,
              'setStreamActive - updateProductClipRank',
            );
          });
      });
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'fetchActiveClipPublicApi');
      setActiveClipInfo(activeClipInfo);
      setClipActive(isClipActive);
      onCloseActiveClipModal();
    }
  };

  const setStreamInActive = async () => {
    setClipLoading(true);
    onCloseActiveClipModal();

    await fetchActiveClipPublicApi().then(() => {
      const shoppableFeeds = [];

      if (_.isArray(activeClipInfo) && !_.isEmpty(activeClipInfo)) {
        const livelyProdId = activeClipInfo[0].product_info[0].product_id;
        for (let i = 0; i < activeClipInfo.length; i++) {
          const feedId = activeClipInfo[i]._id;
          shoppableFeeds.push({
            shoppable_feed_id: feedId,
            rank: -1,
          });
        }
        LivelyApi.updateProductClipRank(authToken, livelyProdId, shoppableFeeds)
          .then(res => {
            setLoading(false);
            updateProductclips(productId);
            if (res?.data?.message == 'Success') {
              dispatch(
                makeToast({
                  content: VIDEO_CLIPPING.SUCCESS.CLIP_DELETION,
                  appearances: 'success',
                }),
              );
            } else {
              dispatch(
                makeToast({
                  content: COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                  appearances: 'error',
                }),
              );
            }
          })
          .catch(error => {
            setLoading(false);
            setClipLoading(false);
            handleApiError(
              error,
              COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
              dispatch,
              'setStreamInActive - updateProductClipRank',
            );
          });
      }
    });
  };

  const updateProductclips = async (productId: any) => {
    setClipLoading(true);
    await fetchActiveClipPublicApi();
    try {
      const response = await LivelyApi.getProductStreamClips(
        authToken,
        productId.split('/').pop(),
        companyId,
      );

      const clips = response?.data?.data;
      if (!Array.isArray(clips) || clips.length === 0) {
        setProductVideoClips([]);
        setClipActive(false);
        setClipLoading(false);
        return;
      }

      setProductVideoClips(clips);
      setClipLoading(false);
    } catch (error) {
      if (error.code === 'ERR_NETWORK') {
        setIsNetworkError(true);
      }
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'updateProductclips', false);
      setClipLoading(false);
      setProductVideoClips([]);
      setClipActive(false);
      setClipLoading(false);
    }
  };

  const handleVideOModalOpen = (url: string) => {
    if (_.isEmpty(url)) {
      handleApiErrorWithoutNetwork(
        '',
        COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
        dispatch,
        'handleVideOModalOpen - empty url',
      );
      return;
    }
    setVideoPlayerModal(true);
    setVideoUrl(url);
  };

  const onVideoModalClosePress = () => {
    setVideoPlayerModal(false);
    setVideoUrl('');
  };

  const handleActiveClipModal = (productId: string, feedId: string) => {
    if (_.isEmpty(productId) && _.isEmpty(feedId)) {
      handleApiErrorWithoutNetwork(
        '',
        COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
        dispatch,
        'handleActiveClipModal - empty productId || feedId',
      );
      return;
    }
    setActiveClipModal(true);
    setShoppableFeedId(feedId);
    setLivelyProductId(productId);
  };

  const onCloseActiveClipModal = () => {
    setActiveClipModal(false);
    setShoppableFeedId('');
    setLivelyProductId('');
  };

  const generateCustomPattern = (): string => {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const getRandomChar = () => charset[Math.floor(Math.random() * charset.length)];
  
    const segment = (length: number) =>
      Array.from({ length }, () => getRandomChar()).join('');
  
    const lastChar = Math.floor(Math.random() * 10);
  
    const part1 = segment(4);
    const part2 = segment(4);
    const part3 = segment(3) + lastChar;
  
    return `${part1}-${part2}-${part3}`;
  }

  const handleFeatureClip = async (file: File) => {
    try {
      setUploadingVideo(true);
      const response = await LivelyApi.getSignedURL(authToken);
      if(response.data) {
        const {Key: key, uploadURL} = response.data.data;
        await LivelyApi.uploadVideo(uploadURL, file);

        if(key && uploadURL) {
          const name = generateCustomPattern();
          const videoURL = uploadURL.split('?')[0];
          const feedResp = await LivelyApi.createFeed(authToken, videoURL, name);
          
          if(feedResp) {
            const {_id: feedId, name} = feedResp?.data?.data
            await LivelyApi.updateFeed(authToken, {feed_id: feedId, is_active: true});
            const product_ids_body = [
              {
                store_product_id: product?.id.split('/').pop(),
                variant_ids: [product?.variants?.[0]?.id.split('/').pop()]
              }
            ]
            const createSFResp = await LivelyApi.createShoppableFeed(authToken, feedId, name, 'shopify', product_ids_body);
            const { _id: shoppableFeedId, product_info } = createSFResp?.data?.data;
            await setStreamActive(product_info[0]?.product_id, shoppableFeedId);
            await updateProductclips(product_info[0]?.product_id);
          }
        }
      }
    } catch (error) {
      console.log('Error in Featuring Clip', error);
      dispatch(makeToast({
        content: COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
        appearances: 'error',
        duration: 2000
      }))
    } finally { 
      setUploadingVideo(false);
      setOpenVideoUploadModal(false);
    }
  }

  return (
    <>
      {isNetworkError ? (
        <NetworkErrorPage />
      ) : (
        <View style={styles.container}>
          <ScrollView style={{width: '100%'}} contentContainerStyle={styles.wrapper}>
            <View style={styles.contentContainer}>
              <TextElement
                style={{textAlign: 'left', width: '100%', fontSize: 22}}
                color="SECONDARY"
                fontWeight="600">
                Video Clips
              </TextElement>
              <TextElement
                style={{textAlign: 'left', width: '100%', marginTop: 8, opacity: 0.7, fontSize: 14}}
                color="SECONDARY"
                fontWeight="400">
                View and select video clips to show in product page in app
              </TextElement>

              <View style={styles.productConatiner}>
                <View style={styles.innerProductConatiner}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      borderBottomWidth: 1,
                      borderBottomColor: 'rgba(206, 206, 206, 1)',
                      paddingBottom: 20,
                    }}>
                    <Pressable style={{flexDirection: 'row', alignItems: 'center', gap: 10}} onPress={() => setActiveProduct({})}>
                      <Icon
                        name="chevron-left"
                        iconType="MaterialIcons"
                        size={20}
                        color="SECONDARY"
                      />
                      <TextElement
                        style={{
                          fontSize: 16,
                        }}
                        color="SECONDARY"
                        fontWeight="500">
                        Feature video clip on product page
                      </TextElement>
                    </Pressable>

                    {!isClipActive && (
                      <Pressable onPress={() => setOpenVideoUploadModal(true)} style={styles.EnableButton}>
                        <TextElement
                          fontWeight='500'
                          style={[
                            commonStyles.baseText,
                            {
                              color: '#ffffff',
                              fontSize: 13
                            }
                          ]}>
                          Feature Clip Upload
                        </TextElement>
                      </Pressable>
                    )}
                    {isClipActive && (
                      <Pressable onPress={() => setStreamInActive()} style={styles.EnableButton}>
                        <TextElement
                          fontWeight="500"
                          style={[
                            commonStyles.baseText,
                            {
                              color: '#ffffff',
                              fontSize: 13,
                            },
                          ]}>
                          Disable
                        </TextElement>
                      </Pressable>
                    )}
                  </View>
                  <View style={{height: '530px', overflow: 'scroll', marginTop: 25}}>
                    <TextElement
                      style={{textAlign: 'left', width: '100%', fontSize: 15}}
                      color="SECONDARY"
                      fontWeight="400">
                      Product Details
                    </TextElement>

                    {loading ? (
                      <View style={{marginBottom: 20, marginTop: 20}}>
                        <Loader />
                      </View>
                    ) : (
                      <>
                        <View style={styles.productDetailsConatiner}>
                          <Image
                            style={{width: 47, height: 47}}
                            resizeMode={product?.featuredImage ? 'cover' : 'contain'}
                            source={
                              product?.featuredImage
                                ? product?.featuredImage
                                : require('../../../../assets/images/placeholder-image.png')
                            }
                          />

                          <View style={{justifyContent: 'center', gap: 5}}>
                            <TextElement
                              style={{textAlign: 'left', width: '100%', fontSize: 14}}
                              color="SECONDARY"
                              fontWeight="400">
                              {product?.title}
                            </TextElement>
                            <TextElement
                              style={{textAlign: 'left', width: '100%', opacity: 0.7, fontSize: 13}}
                              color="SECONDARY"
                              fontWeight="400">
                              {currencyCode && currencyCode(product?.price)}
                            </TextElement>
                          </View>
                        </View>
                        <VideoClips
                          clipLoading={clipLoading}
                          productVideoClips={productVideoClips}
                          handleActiveClipModal={handleActiveClipModal}
                          handleVideOModalOpen={handleVideOModalOpen}
                        />
                        <ModalComponent
                          visible={VideoPlayerModal}
                          modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
                          onVisibleChange={setVideoPlayerModal}
                          content={<VideoPlayModal onClosePress={onVideoModalClosePress} videoUrl={videoUrl} />}
                        />
                        <ModalComponent
                          visible={activeClipModal}
                          modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
                          onVisibleChange={setActiveClipModal}
                          content={
                            <ActivateClipModal
                              onClosePress={onCloseActiveClipModal}
                              setStreamActive={setStreamActive}
                            />
                          }
                        />
                        <ModalComponent
                          visible={openVideoUploadModal}
                          onVisibleChange={setOpenVideoUploadModal}
                          modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
                          content={
                            <VideoUploadModal
                              onFeatureClip={handleFeatureClip}
                              onClosePress={() => setOpenVideoUploadModal(false)}
                              loading={uploadingVideo}
                            />
                          }
                        />
                      </>
                    )}
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    alignItems: 'center',
    flex: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  contentContainer: {
    width: '100%'
  },
  BackButton: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 5,
  },
  productDetailsConatiner: {
    width: 'fit-content',
    borderWidth: 1,
    padding: 10,
    marginTop: 15,
    flexDirection: 'row',
    gap: 15,
    justifyContent: 'flex-start',
    borderRadius: 6,
    borderColor: 'rgba(206, 206, 206, 1)',
  },
  productConatiner: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 10,
    marginTop: 20,
  },
  innerProductConatiner: {
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 10,
    paddingTop: 20,
    width: '100%',
  },
  EnableButton: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    backgroundColor: 'rgba(16, 96, 224, 1)',
    height: 30,
    paddingHorizontal: 15,
    paddingVertical: 7,
  },
});

export default VideoClipsDetails;
