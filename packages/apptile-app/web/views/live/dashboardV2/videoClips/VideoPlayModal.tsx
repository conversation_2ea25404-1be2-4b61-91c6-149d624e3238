import TextElement from '@/root/web/components-v2/base/TextElement';
import React, {useEffect, useRef} from 'react';
import {StyleSheet, View, Pressable} from 'react-native';
import {Icon} from 'apptile-core';
import videojs from 'video.js';

const VideoPlayer = ({options}) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);

  useEffect(() => {
    if (videoRef.current && !playerRef.current) {
      const player = videojs(videoRef.current, options, () => {
        console.log('player is ready');
      });
      playerRef.current = player;
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [options]);

  return (
    <div style={{margin: '2.5px', padding: '2.5px', borderRadius: '2.5px'}}>
      <div data-vjs-player style={{width: '150px', borderRadius: '2.5px'}}>
        <video style={{borderRadius: '2.5px'}} ref={videoRef} className="video-js vjs-big-play-centered" />
      </div>
    </div>
  );
};

const VideoPlayModal = ({onClosePress, videoUrl}) => {
  return (
    <View style={styles.ModalComponent}>
      <View style={styles.ModalInnerComponent}>
        <View style={styles.Header}>
          <TextElement style={{textAlign: 'left', width: '100%', fontSize: 16}} color="SECONDARY" fontWeight="500">
            Video clip
          </TextElement>
          <Pressable onPress={onClosePress}>
            <Icon
              name="close"
              iconType="AntDesign"
              size={16}
              color={'black'}
              style={{
                margin: 0,
                fontWeight: 300,
              }}
            />
          </Pressable>
        </View>

        <View style={styles.ModalContent}>
          <VideoPlayer
            options={{
              controls: true,
              responsive: true,
              fluid: true,
              aspectRatio: '4:3',
              preload: 'auto',
              sources: [
                {
                  src: videoUrl.toString(),
                  type: 'video/mp4',
                },
              ],
            }}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ModalComponent: {},
  ModalInnerComponent: {
    width: 600,
    marginTop: 30,
    marginHorizontal: 25,
    marginBottom: 25,
  },
  Header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ModalContent: {
    width: '100%',
    marginTop: 15,
  },
});

export default VideoPlayModal;
