import TextElement from '@/root/web/components-v2/base/TextElement'
import React, { useRef, useState } from 'react'
import { Pressable, StyleSheet, View } from 'react-native'
import {Icon} from 'apptile-core'
import theme from '@/root/web/styles-v2/theme';
import _ from 'lodash';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import Button from '@/root/web/components-v2/base/Button';

const MAX_FILE_SIZE_MB = 300;
const FORMAT = 'mp4';
const ASPECT_RATIO = '[9:16, 16:9]';
const conditions = [
  {
    id: 1,
    content: `Max Video Size: ${MAX_FILE_SIZE_MB}MB`,
  },
  {
    id: 2,
    content: `Format: ${FORMAT}`,
  },
  {
    id: 3,
    content: `Aspect Ratio: ${ASPECT_RATIO}`,
  }
]

const VideoUploadModal = ({onClosePress, onFeatureClip, loading}) => {

  const [videoFile, setVideoFile] = useState<File>();
  const [videoUrl, setVideoUrl] = useState<string | null>('');
  const [errors, setErrors] = useState<{ id: number; content: string }[]>([]);

  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setVideoFile(file);
    setVideoUrl('');
    setErrors([]);

    // Validate file type
    if (file.type !== 'video/mp4') {
      setErrors((prev: any) => [
        ...prev,
        {
          id: 2,
          content: 'Only MP4 videos are allowed.'
        }
      ]);
      return;
    }

    // Validate file size
    const sizeInMB = file.size / (1024 * 1024);
    if (sizeInMB > MAX_FILE_SIZE_MB) {
      setErrors((prev: any) => [
        ...prev, 
        {
          id: 1,
          content: 'File size should be less than 300MB.'
        }
      ]);
      return;
    }

    // Validate aspect ratio
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = () => {
      URL.revokeObjectURL(video.src);
      const { videoWidth: width, videoHeight: height } = video;
      const aspectRatio = width / height;
      const rounded = parseFloat((aspectRatio).toFixed(2));

      const isValidAspect =
        rounded === parseFloat((16 / 9).toFixed(2)) ||
        rounded === parseFloat((9 / 16).toFixed(2));

      if (!isValidAspect) {
        setErrors((prev: any) => [...prev, {id: 3, content: 'Only 16:9 or 9:16 aspect ratios are allowed.'}]);
        return;
      }

      setVideoUrl(URL.createObjectURL(file));
    };

    video.onerror = () => {
      setErrors((prev: any) => [...prev, {id: 4, content: 'Invalid video file.'}]);
    };

    video.src = URL.createObjectURL(file);
  };

  return (
    <View style={styles.ModalComponent}>
      <View style={styles.ModalInnerComponent}>
        <View style={styles.Header}>
          <TextElement style={{textAlign: 'left', width: '100%', fontSize: 20}} color="SECONDARY" fontWeight="600">
            Feature Clip Upload
          </TextElement>
          <Pressable onPress={onClosePress}>
            <Icon
              name="close"
              iconType="AntDesign"
              size={16}
              color={'black'}
              style={{
                margin: 0,
                fontWeight: 300,
              }}
            />
          </Pressable>
        </View>

        <Pressable onPress={() => inputRef.current?.click()} style={styles.VideoInputContainer}>
          <Icon
            name="cloud-upload"
            iconType="MaterialCommunityIcons"
            size={50}
            color={theme.INPUT_BORDER}
            style={{margin: 0}}
          />
          <TextElement fontSize='xs' fontWeight='400' style={{color: theme.INPUT_BORDER}}>
            {videoFile ? 'Replace Video' : ' Click to Upload'}
          </TextElement>
          {videoFile && (
            <TextElement fontSize='xs' fontWeight='400' style={{textAlign: 'center', marginTop: 4, width: '90%', color: theme.SECONDARY_BORDER}}>
              {videoFile.name}
            </TextElement>
          )}
        </Pressable>
        <input ref={inputRef} type="file" accept="video/mp4" hidden onChange={handleFileChange} />

        <View style={styles.ModalNote}>
          <TextElement
            style={{textAlign: 'left', fontSize: 15, marginRight: 7}}
            fontWeight="600"
          >
            Upload Conditions:
          </TextElement>
          <View style={{flexDirection: 'column', gap: 6, marginTop: 10}}>
            {conditions.map((condition, i) => {
               const isErrored = errors?.some(e => e.id === condition.id);
               return (
                <View key={i} style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View style={[
                    styles.ConditionsCircle
                  ]}>
                    {isErrored && <Icon name='close' iconType="AntDesign" size={12} color={theme.ERROR} />}
                    {videoFile && !isErrored && <Icon name='check' iconType="AntDesign" size={12} color={theme.SUCCESS} />}
                  </View>
                  <TextElement style={{marginLeft: 8, fontSize: 13}} color="SECONDARY" fontWeight="400">{condition.content}</TextElement>
                </View>
               )
            })}
          </View>

          <TextElement
            style={{textAlign: 'left', fontSize: 15, color: 'rgba(232, 70, 70, 1)', marginTop: 24}}
            fontWeight="600">
            Note:
          </TextElement>
          <TextElement style={{textAlign: 'left', fontSize: 13, marginTop: 4}} color="SECONDARY" fontWeight="400">
            Your video will be automatically featured after being uploaded and processed, which may take 5 to 10 minutes. Please avoid making any changes to this product until the clip is successfully featured.
          </TextElement>
        </View>

        <View style={styles.ModalButtons}>
          <Button color='CTA' loading={loading} onPress={() => onFeatureClip(videoFile)} innerContainerStyles={{backgroundColor: 'transparent'}} containerStyles={[
            styles.EnableButton,
            (!videoFile || !_.isEmpty(errors)) && {
              backgroundColor: theme.INPUT_BACKGROUND,
            }
          ]}>
          <TextElement
            fontWeight="500"
            style={[
              commonStyles.baseText,
              {
                color: '#ffffff',
                fontSize: 13,
              },
              (!videoFile || !_.isEmpty(errors)) && {
                color: theme.INPUT_BORDER
              }
            ]}>
            Feature Clip
          </TextElement>
        </Button>
        </View>
      </View>
    </View>
  )
}

export default VideoUploadModal


const styles = StyleSheet.create({
  ModalComponent: {},
  ModalInnerComponent: {
    width: 500,
    marginVertical: 35,
    marginHorizontal: 30,
  },
  Header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ModalNote: {
    marginTop: 20,
    flexDirection: 'column',
  },
  ButtonContainer: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 30,
  },
  EnableButton: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    backgroundColor: 'rgba(16, 96, 224, 1)',
    height: 40
  },
  ConditionsCircle: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center'
  },
  VideoInputContainer: {
    width: '100%',
    marginTop: 30,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    backgroundColor: theme.INPUT_BACKGROUND,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    paddingHorizontal: 10
  },
  ModalButtons: {
    marginTop: 20
  }
});