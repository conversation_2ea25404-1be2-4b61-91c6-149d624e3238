import TextElement from '@/root/web/components-v2/base/TextElement';
import React, {useState} from 'react';
import {StyleSheet, View, Image} from 'react-native';
import Button from '@/root/web/components-v2/base/Button';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';

export const DashboardUpgradeModal = ({onConfirmPress}: {onConfirmPress: () => void}) => {
  const [isLoading, setIsLoading] = useState(false);
  return (
    <View style={styles.wrapper}>
      {isLoading ? (
        <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
      ) : (
        <>
          <TextElement fontSize="xl" style={{marginBottom: 20, marginHorizontal: 40}}>
            <ApptileWebIcon name={'clock-refresh'} size={28} color={'#1060E0'} />
          </TextElement>
          <TextElement fontWeight={'bold'} color="SECONDARY" fontSize="xl" style={{marginBottom: 20}}>
            Please Refresh
          </TextElement>
          <TextElement color="SECONDARY" fontSize="md">
            Please refresh to the incorporate new changes!
          </TextElement>
        </>
      )}
      <View style={styles.contentWrapper}>
        <View style={styles.inputWrapper}>
          <Button
            disabled={isLoading}
            color={'QUATERNARY'}
            containerStyles={styles.buttonStyles}
            onPress={onConfirmPress}>
            Cancel
          </Button>
          <Button
            disabled={isLoading}
            color={'CTA'}
            containerStyles={styles.buttonStyles}
            onPress={() => {
              setIsLoading(true);
              window.location.reload(true);
              setIsLoading(false);
            }}>
            Refresh
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputStyles: {
    width: 281,
    height: 47,
    borderRadius: 8,
  },
  contentWrapper: {
    alignItems: 'center',
  },
  wrapper: {
    paddingVertical: 40,
    paddingHorizontal: 56,
    alignItems: 'center',
    width: 500,
  },
  noteWrapper: {
    flexDirection: 'row',
    marginTop: 15,
    width: 315,
    gap: 10,
  },
  activateText: {
    marginTop: 41,
    textAlign: 'center',
    fontSize: 14,
  },
  inputWrapper: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 30,
  },
  buttonStyles: {
    width: 143,
    height: 47,
  },
  font14: {
    fontSize: 14,
  },
  loaderImage: {
    width: 40,
    height: 40,
  },
});
