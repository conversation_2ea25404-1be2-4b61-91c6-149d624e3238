import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import Button from '@/root/web/components-v2/base/Button';

export const DeleteConfirmationModal = ({
  onConfirmPress,
  loading = false,
}: {
  onConfirmPress: () => void;
  loading?: boolean;
}) => {
  return (
    <View style={styles.wrapper}>
      <TextElement fontSize="xl" style={{marginBottom: 20, marginHorizontal: 40}}>
       Delete stream
      </TextElement>
      <TextElement color="SECONDARY" fontSize="md">
        Are you sure you want to delete the stream?
      </TextElement>
      <View style={styles.contentWrapper}>
        <View style={styles.inputWrapper}>
          <Button
            loading={loading}
            containerStyles={styles.buttonStyles}
            onPress={() => {
              onConfirmPress();
            }}
            color="ERROR">
            Delete
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputStyles: {
    width: 281,
    height: 47,
    borderRadius: 8,
  },
  contentWrapper: {
    alignItems: 'center',
  },
  wrapper: {
    paddingVertical: 40,
    paddingHorizontal: 56,
    alignItems: 'center',
  },
  noteWrapper: {
    flexDirection: 'row',
    marginTop: 15,
    width: 315,
    gap: 10,
  },
  activateText: {
    marginTop: 41,
    textAlign: 'center',
    fontSize: 14,
  },
  inputWrapper: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 40,
  },
  buttonStyles: {
    width: 143,
    height: 47,
  },
  font14: {
    fontSize: 14,
  },
});
