import {closeStreamDeleteConfirm, deleteStream, deleteUpcomingStream} from '@/root/web/actions/liveSellingActions';
import React from 'react';
import {useDispatch, useSelector} from 'react-redux';
import ModalComponent from '../../../components-v2/base/Modal';
import {DeleteConfirmationModal} from './deleteConfirmationModal';
import { useLocation } from '../../../routing.web'

export const DeleteStreamConfirmationModal = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const isAuction = location.pathname.includes('auction/auctions');
  const deleteStreamState = useSelector(state => state.liveSelling.deleteStream);
  const displayDeleteStreamConfirm = useSelector(state => state.liveSelling.deleteStream.displayDeleteStreamConfirm);

  const {streamId, feedFileId, loading, onCreateClose} = deleteStreamState;

  return (
    <ModalComponent
      onVisibleChange={visible => {
        if (!visible) dispatch(closeStreamDeleteConfirm());
      }}
      visible={!!displayDeleteStreamConfirm}
      modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
      content={
        <DeleteConfirmationModal
          onConfirmPress={() => {
            if (streamId && feedFileId!) {
              dispatch(deleteStream(streamId, feedFileId, isAuction ? 'auction' : 'simple'));
            }
            else if (streamId && !feedFileId){
              dispatch(deleteUpcomingStream(streamId, onCreateClose, isAuction ? 'auction' : 'simple'));
            }
          }}
          loading={loading}
        />
      }
    />
  );
};
