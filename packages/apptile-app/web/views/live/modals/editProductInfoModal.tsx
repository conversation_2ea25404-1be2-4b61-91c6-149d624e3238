import TextElement from '@/root/web/components-v2/base/TextElement'
import TextInput from '@/root/web/components-v2/base/TextInput'
import NumericInputControl from '@/root/web/components/controls/NumericInputControl'
import { EditorRootState } from '@/root/web/store/EditorRootState'
import theme from '@/root/web/styles-v2/theme'
import { Icon } from 'apptile-core'
import React, { useState } from 'react'
import { Image, Pressable, StyleSheet, View } from 'react-native'
import { useSelector } from 'react-redux'

const EditProductInfoModal = ({
  appendLotNumber,
  scannedBarcode,
  productScannedRef,
  product,
  setShowEditProductInfoModal,
  baseBidPrice,
  bidIncrementer,
  highestLotNumber,
  addNewProduct,
  addNewScannedProduct,
  setIsBarCodeResultLoading
}: any) => {

  const fromScanning = !!productScannedRef.current;
  const resolvedProduct = fromScanning ? productScannedRef.current : product;
  const [startingBid, setStartingBid] = useState(Math.ceil((resolvedProduct?.maxSalePrice * baseBidPrice) / 100))
  const [lotNumber, setLotNumber] = useState(highestLotNumber + 1)
  const currencyCode = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser.currency.currency_code);

  const handleOnPress = () => {
    const reformedProduct = {
      ...resolvedProduct,
      baseBidPrice: Number(startingBid),
      bidIncrementer: Number(bidIncrementer),
      lotNumber: Number(lotNumber)
    }
    if(fromScanning) addNewScannedProduct(scannedBarcode.current, reformedProduct)
    else addNewProduct(reformedProduct)
    setShowEditProductInfoModal(false)
  }

  const handleOnCancel = () => {
    productScannedRef.current = '';
    scannedBarcode.current = '';
    setIsBarCodeResultLoading(false);
    setShowEditProductInfoModal(false)
  }
  
  return (
    <View style={[styles.wrapper]}>
      {!appendLotNumber && (
        <View style={[styles.noteCtn]}>
          <Icon
            name='warning-outline'
            iconType='Ionicons'
            size={20}
            color={'#5D4037'}
          />
          <TextElement style={{color: '#5D4037', marginLeft: 4}} fontSize='xs' fontWeight='500'>
            Lot number appending is disabled
          </TextElement>
        </View>  
      )}
      <View style={[styles.productCard]}>
        <Image 
          source={{uri: resolvedProduct.featuredImage}}
          style={{height: 60, width: 60, borderRadius: 6}}
          resizeMode='cover'
        />
        <TextElement color='SECONDARY' fontSize='sm' fontWeight='500'>
          {resolvedProduct.title}
        </TextElement>
      </View>
      <View style={{marginVertical: 10}}>
        <TextElement
          color='SECONDARY'
          fontSize='sm'
          lineHeight='sm'
          fontWeight='500'
        >
          Starting Bid
        </TextElement>
        <View style={{marginTop: 10}}>
          <TextInput
            value={startingBid?.toString() || ''} 
            onChangeText={value => {
              const numeric = value.replace(/[^0-9]/g, '');
              setStartingBid(numeric === '' ? undefined : +numeric)
            }}
            textInputStyles={{
              outline: 'none'
            }}
          />
          <TextElement
            fontSize="xs"
            lineHeight="xs"
            fontWeight="400"
            style={{
              position: 'absolute',
              top: '50%',
              right: 10,
              transform: [{translateY: '-50%'}],
              color: '#aaa'
            }}
          >
            {currencyCode}
          </TextElement>
        </View>
      </View>
      <View style={{marginVertical: 10}}>
        <TextElement
          color='SECONDARY'
          fontSize='sm'
          lineHeight='sm'
          fontWeight='500'
        >
          Lot Number
        </TextElement>
        <View style={{marginTop: 10}}>
          <TextInput
            value={lotNumber?.toString() || ''} 
            onChangeText={value => {
              const numeric = value.replace(/[^0-9]/g, '');
              setLotNumber(numeric === '' ? undefined : +numeric)
            }}
            textInputStyles={{
              outline: 'none'
            }}
          />
          <TextElement
            fontSize="xs"
            lineHeight="xs"
            fontWeight="400"
            style={{
              position: 'absolute',
              top: '50%',
              right: 10,
              transform: [{translateY: '-50%'}],
              color: '#aaa'
            }}
          >
            #
          </TextElement>
        </View>
      </View>
      <View style={{flexDirection: 'row', alignItems: 'center', gap: 10, flex: 1, marginTop: 10}}>
        <Pressable onPress={handleOnCancel} style={[styles.cancelBtn]}>
          <TextElement fontSize='sm' fontWeight='400'>
            Cancel
          </TextElement>
        </Pressable>    
        <Pressable onPress={handleOnPress} style={[styles.confirmBtn]}>
          <TextElement fontSize='sm' fontWeight='400' style={{color: 'white'}}>
            Confirm
          </TextElement>
        </Pressable>
      </View>
    </View>
  )
}

export default EditProductInfoModal

const styles = StyleSheet.create({
  noteCtn: {
    paddingHorizontal: 10,
    paddingVertical: 8,
    backgroundColor: '#FFF9C4',
    borderRadius: 6,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  wrapper: {
    padding: 20,
    width: 350,
    maxWidth: 350,
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    gap: 16,
    borderColor: theme.CONTROL_BORDER,
    borderWidth: 1,
    borderRadius: 6,
    padding: 16
  },
  justifyBetween: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  cancelBtn: {
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.PRIMARY,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmBtn: {
    flex: 1,
    backgroundColor: theme.PRIMARY,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  }
})