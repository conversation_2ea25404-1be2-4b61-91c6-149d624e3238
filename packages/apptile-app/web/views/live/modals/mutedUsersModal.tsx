import theme from '@/root/web/styles-v2/theme';
import { Icon } from 'apptile-core';
import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';

const MutedUsersModal = ({
  streamId,
  roomId,
  mutedUsers,
  handleMuteUsers
}: {
  streamId: any,
  roomId: any,
  mutedUsers: any,
  handleMuteUsers: any;
}) => {
  return (
    <View style={[styles.modalWrapper]}>
      <View style={[styles.header]}>
        <Text style={[styles.themedText, { fontSize: 20 } ]}>Muted Users</Text>
      </View>

      <View style={{ marginTop: 28 }}>
        {mutedUsers.length > 0 ? (
          <View style={[styles.mapWrapper]}>
            {mutedUsers.map((user: any) => (
              <View style={[styles.itemContainer]}>
                <View style={[styles.itemHead]}>
                  <Image
                    source={require('@/root/web/assets/images/apptile_icon.png')}
                    style={{height: 35, width: 35, borderRadius: 50}}
                  />
                  <Text style={[styles.themedText, { fontSize: 12 }]}>{user.name}</Text>
                </View>
                <Pressable
                  onPress={() => handleMuteUsers(user)}
                  style={[styles.unMuteUserContainer]}
                > 
                  <Icon
                    name={'volume-2'}
                    iconType={'Feather'}
                    size={14}
                    color={'#444'}
                  />
                  <Text style={[styles.themedText]}>Unmute User</Text>
                </Pressable>
              </View>
            ))}
          </View>
        ) : (
          <View style={[styles.noResultsContainer]}>
            <Icon
              name={'volume-off'}
              iconType={'SimpleLineIcons'}
              size={48}
              color={"#d9d9d9"}
            />
            <Text style={[styles.themedText, { color: '#d9d9d9' }]}>No Muted Users Found</Text>
          </View>
        )}
      </View>
    </View>
  )
}

export default MutedUsersModal

const styles = StyleSheet.create({
  modalWrapper: {
    minWidth: 500,
    padding: 30
  },
  themedText: {
    fontFamily: theme.FONT_FAMILY,
    color: theme.TEXT_COLOR,
    fontSize: 14,
    lineHeight: 15,
    fontWeight: '500'
  },
  header: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  mapWrapper: {
    flexDirection: 'column',
    flex: 1,
    flexGrow: 1,
    maxHeight: 250,
    overflow: 'scroll',
  },
  itemContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    minHeight: 50,
  },
  itemHead: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  unMuteUserContainer: {
    minWidth: 160,
    borderWidth: 1,
    borderColor: '#D9D9D9',
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 10,
    paddingHorizontal: 20
  },
  noResultsContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
    padding: 40,
    borderWidth: 1,
    borderColor: '#f1f1f1',
    borderRadius: 8
  }
})