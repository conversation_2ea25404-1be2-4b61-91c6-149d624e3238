import React, { useEffect, useRef, useState } from 'react';
import { Animated, Pressable, StyleSheet, View } from 'react-native';
import {Portal} from '@gorhom/portal';
import TextElement from '../../../components-v2/base/TextElement';
import { Icon } from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';

// TypeScript workaround for React Native Web compatibility
const ViewComponent = View as any;


// TODO(Naveen): Expose styles

export type DropdownOption = {
  label: string;
  value: string | number;
  icon?: string;
};

export interface AnimatedDropdownProps {
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
  options: DropdownOption[];
  selectedValue: string | number;
  onSelect: React.Dispatch<React.SetStateAction<{ label: string; value: string; }>>;
  trigger: React.ReactElement;
  dropdownContainerStyle?: any;
  dropdownItemStyle?: any;
  dropdownLabelStyle?: any;
}

const AnimatedDropdown: React.FC<AnimatedDropdownProps> = ({
  visible: controlledVisible,
  onVisibleChange,
  options,
  selectedValue,
  onSelect,
  trigger,
  dropdownContainerStyle,
  dropdownItemStyle,
  dropdownLabelStyle
}) => {
  const triggerRef = useRef<any>(null);
  const dropdownMenuRef = useRef<any>(null);
  const [coords, setCoords] = useState({ top: 0, left: 0, width: 180 });
  const [uncontrolledVisible, setUncontrolledVisible] = useState(false);
  const isControlled = typeof controlledVisible === 'boolean' && typeof onVisibleChange === 'function';
  const visible = isControlled ? controlledVisible! : uncontrolledVisible;

  // Animation logic
  const animation = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    Animated.timing(animation, {
      toValue: visible ? 1 : 0,
      duration: 180,
      useNativeDriver: false,
    }).start();
  }, [visible]);
  const height = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 43.2 * options.length],
  });
  const opacity = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  // Measure trigger position
  useEffect(() => {
    if (visible && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect?.();
      if (rect) setCoords({ top: rect.bottom, left: rect.left, width: rect.width });
    }
  }, [visible]);

  // Hide dropdown on scroll
  useEffect(() => {
    if (!visible) return;
    const handleScroll = () => {
      if (isControlled && onVisibleChange) onVisibleChange(false);
      else setUncontrolledVisible(false);
    };
    window.addEventListener('scroll', handleScroll, true);
    return () => window.removeEventListener('scroll', handleScroll, true);
  }, [visible, isControlled, onVisibleChange]);

  // Hide dropdown on click outside
  useEffect(() => {
    if (!visible) return;
    function handleClickOutside(event: MouseEvent) {
      const triggerNode = triggerRef.current;
      const dropdownNode = dropdownMenuRef.current;
      if (
        triggerNode && dropdownNode &&
        !triggerNode.contains(event.target) &&
        !dropdownNode.contains(event.target)
      ) {
        if (isControlled && onVisibleChange) onVisibleChange(false);
        else setUncontrolledVisible(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside, true);
    return () => document.removeEventListener('mousedown', handleClickOutside, true);
  }, [visible, isControlled, onVisibleChange]);

  // Handle trigger click
  const handleTriggerPress = (e: any) => {
    if (isControlled && onVisibleChange) onVisibleChange(!visible);
    else setUncontrolledVisible(v => !v);
    if (trigger.props.onPress) trigger.props.onPress(e);
  };

  // Clone trigger with ref and onPress
  const triggerWithRef = React.cloneElement(trigger, {
    ref: triggerRef,
    onPress: handleTriggerPress,
  });

  return (
    <>
      {triggerWithRef}
      <Portal>
        {visible && (
          <Animated.View
            ref={dropdownMenuRef}
            style={[
              styles.dropdownMenu,
              {
                position: 'absolute',
                top: coords.top,
                left: coords.left,
                width: coords.width,
                opacity,
                overflow: 'auto',
                maxHeight: 320,
              },
              dropdownContainerStyle
            ]}
          >
            {options.map((option, i) => {
              const isActive = selectedValue === option.value;
              return (
                <Pressable
                  key={i}
                  style={[
                    styles.dropdownItem,
                    isActive && styles.dropdownItemActive,
                    dropdownItemStyle
                  ]}
                  onPress={() => {
                    onSelect(option);
                    if (isControlled && onVisibleChange) onVisibleChange(false);
                    else setUncontrolledVisible(false);
                  }}
                >
                  <ViewComponent style={styles.dropdownItemContent}>
                    {option.icon && (
                      <Icon
                        name={option.icon}
                        iconType="MaterialCommunityIcons"
                        size={16}
                        color={theme.TEXT_COLOR}
                        style={styles.dropdownItemIcon}
                      />
                    )}
                    <TextElement fontSize="sm" color="SECONDARY" style={dropdownLabelStyle}>
                      {option.label}
                    </TextElement>
                  </ViewComponent>
                  {isActive && (
                    <Icon
                      name="check"
                      iconType="Feather"
                      size={12}
                      color={theme.CTA}
                      style={styles.checkIcon}
                    />
                  )}
                </Pressable>
              );
            })}
          </Animated.View>
        )}
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
  },
  dropdownItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dropdownItemIcon: {
    marginRight: 8,
  },
  checkIcon: {
    marginLeft: 8,
  },
  dropdownItemActive: {
    backgroundColor: 'rgb(217,234,255)',
  },
  dropdownMenu: {
    position: 'absolute',
    top: 48,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 6,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    zIndex: 100,
  },
});

export default AnimatedDropdown;
