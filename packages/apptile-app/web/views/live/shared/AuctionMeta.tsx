import React, { useState } from 'react'
import {View, Text, Pressable, StyleSheet} from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import InfoTooltip from './InfoTooltip';
import TextInput from '../../../components-v2/base/TextInput';
import CheckboxControl from '../../../components/controls/CheckboxControl';
import AnimatedDropdown from '../shared/AnimatedDropdown';
import { Icon } from 'apptile-core';
import { EXTENSION_TIME_OPTIONS, TRIGGER_TIME_OPTIONS } from '../utils/dropDownValues';
import theme from '@/root/web/styles-v2/theme';

export interface AuctionMetaState {
  biddingDuration?: number;
  minimumBidIncrement?: number;
  extendedBidding: boolean;
  triggerOption: number;
  extensionTime: number;
  biddingCap?: number;
}

interface AuctionMetaProps {
  auctionMeta: AuctionMetaState;
  setAuctionMeta: React.Dispatch<React.SetStateAction<AuctionMetaState>>;
  currencyCode: string | null;
}

const AuctionMeta: React.FC<AuctionMetaProps> = ({ auctionMeta, setAuctionMeta, currencyCode }) => {
  const [triggerOptionDropdown, setTriggerOptionDropdown] = useState(false);
  const [extensionTimeDropdown, setExtensionTimeDropdown] = useState(false);

  return (
    <>
      <View style={{paddingTop: 16}}>
        <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
          Bidding Duration*
          <InfoTooltip
            tooltip="Set how long the auction will accept bids"
            position="right"
          />
        </TextElement>
        <View style={{marginTop: 4}}>
          <TextInput
            value={auctionMeta?.biddingDuration?.toString() || ''}
            onChangeText={value => {
              let numeric = value.replace(/[^0-9]/g, '');
              setAuctionMeta({...auctionMeta, biddingDuration: numeric === '' ? undefined : +numeric});
            }}
            icon="clock-outline"
            iconSize={'18'}
            iconContainerStyle={{
              backgroundColor: 'transparent'
            }}
            iconColor='#aaa'
            iconPosition="LEFT"
            textInputStyles={{
              outline: 'none'
            }}
          />
          <TextElement
            fontSize="xs"
            lineHeight="xs"
            fontWeight="400"
            style={{
              position: 'absolute',
              top: '50%',
              right: 10,
              transform: [{translateY: '-50%'}],
              color: '#aaa'
            }}
          >
            seconds
          </TextElement>
        </View>
      </View>
      <View style={[styles.paddingTop12, {paddingTop: 16}]}>
        <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
          Minimum Bid Increment*
          <InfoTooltip
            tooltip="Set the minimum amount each new bid must increase by"
            position="right"
          />
        </TextElement>
        <View style={{marginTop: 4}}>
          <TextInput
            value={auctionMeta.minimumBidIncrement?.toString() || ''} 
            onChangeText={value => {
              const numeric = value.replace(/[^0-9]/g, '');
              setAuctionMeta({...auctionMeta, minimumBidIncrement: numeric === '' ? undefined : +numeric});
            }}
            icon="cash"
            iconSize={'18'}
            iconContainerStyle={{
              backgroundColor: 'transparent'
            }}
            iconColor='#aaa'
            iconPosition="LEFT"
            textInputStyles={{
              outline: 'none'
            }}
          />
          <TextElement
            fontSize="xs"
            lineHeight="xs"
            fontWeight="400"
            style={{
              position: 'absolute',
              top: '50%',
              right: 10,
              transform: [{translateY: '-50%'}],
              color: '#aaa'
            }}
          >
            {currencyCode}
          </TextElement>
        </View>
      </View>

      <View style={[styles.paddingTop12, {paddingTop: 16}]}>
        <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>
          Bidding Cap*
          <InfoTooltip
            tooltip="Set the maximum amount a bidding can be reached"
            position="right"
          />
        </TextElement>
        <View style={{marginTop: 4}}>
          <TextInput
            value={auctionMeta.biddingCap?.toString() || ''} 
            onChangeText={value => {
              const numeric = value.replace(/[^0-9]/g, '');
              setAuctionMeta({...auctionMeta, biddingCap: numeric === '' ? undefined : +numeric});
            }}
            icon="cash"
            iconSize={'18'}
            iconContainerStyle={{
              backgroundColor: 'transparent'
            }}
            iconColor='#aaa'
            iconPosition="LEFT"
            textInputStyles={{
              outline: 'none'
            }}
          />
          <TextElement
            fontSize="xs"
            lineHeight="xs"
            fontWeight="400"
            style={{
              position: 'absolute',
              top: '50%',
              right: 10,
              transform: [{translateY: '-50%'}],
              color: '#aaa'
            }}
          >
            {currencyCode}
          </TextElement>
        </View>
      </View>

      <View style={[styles.paddingTop12]}>
        <View style={{flexDirection: 'row', alignItems: 'center', width: '100%', justifyContent: 'space-between'}}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 2}}>
            <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">
              Extended Bidding
            </TextElement>
            <InfoTooltip
              tooltip='Settings for extending the bid window to prevent sniping'
              position='right'
            />
          </View>
          <View>
            <CheckboxControl
              label=""
              value={auctionMeta.extendedBidding}
              onChange={value => setAuctionMeta({...auctionMeta, extendedBidding: value})}
            />
          </View>
        </View>
        {auctionMeta.extendedBidding && (
          <>
            <View style={[styles.paddingTop12, {gap: 10}]}>
              <TextElement fontSize="sm" fontWeight="500" color="SECONDARY">Trigger Time</TextElement>
              <TextElement fontSize="xs" fontWeight="400" color="EDITOR_LIGHT_BLACK">
                If a user places a bid in the final seconds of the auction, this is the time window during which the bid will trigger an extension
              </TextElement>

              <AnimatedDropdown
                visible={triggerOptionDropdown}
                onVisibleChange={setTriggerOptionDropdown}
                options={TRIGGER_TIME_OPTIONS}
                selectedValue={auctionMeta.triggerOption?.value}
                onSelect={(option) => setAuctionMeta({...auctionMeta, triggerOption: +option.value})}
                dropdownLabelStyle={{fontSize: 14}}
                dropdownItemStyle={{padding: 8}}
                trigger={
                  <Pressable
                    style={styles.dropDownTrigger}
                  >
                    <TextElement fontSize="sm" fontWeight="400" style={{color: '#3C3C3C'}}>
                      {TRIGGER_TIME_OPTIONS.find(e => e.value == auctionMeta.triggerOption)?.label}
                    </TextElement>
                    <Icon iconType="MaterialCommunityIcons" name={triggerOptionDropdown ? 'chevron-up' : 'chevron-down'} size={18} color="#aaa" />
                  </Pressable>
                }
              />
            </View>

            <View style={[styles.paddingTop12, {gap: 10, marginTop: 16}]}>
              <TextElement fontSize="sm" fontWeight="500" color="SECONDARY" style={{marginBottom: 2}}>Extension Time</TextElement>
              <TextElement fontSize="xs" fontWeight="400" color="EDITOR_LIGHT_BLACK">
                This is how much extra time gets added to the auction whenever a last-moment bid is placed
              </TextElement>

              <AnimatedDropdown
                visible={extensionTimeDropdown}
                onVisibleChange={setExtensionTimeDropdown}
                options={EXTENSION_TIME_OPTIONS}
                selectedValue={auctionMeta.extensionTime?.value}
                onSelect={(option) => setAuctionMeta({...auctionMeta, extensionTime: +option.value})}
                dropdownLabelStyle={{fontSize: 14}}
                dropdownItemStyle={{padding: 8}}
                trigger={
                  <Pressable
                    style={styles.dropDownTrigger}
                  >
                    <TextElement fontSize="sm" fontWeight="400" style={{color: '#3C3C3C'}}>
                      {EXTENSION_TIME_OPTIONS.find(e => e.value == auctionMeta.extensionTime)?.label}
                    </TextElement>
                    <Icon iconType="MaterialCommunityIcons" name={extensionTimeDropdown ? 'chevron-up' : 'chevron-down'} size={18} color="#aaa" />
                  </Pressable>
                }
              />
            </View>
          </>
        )}
      </View>
    </>
  )
}

export default AuctionMeta

const styles = StyleSheet.create({
  paddingTop12: {
    paddingTop: 12,
  },
  dropDownTrigger: {
    backgroundColor: theme.INPUT_BACKGROUND,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    borderRadius: 8,
    width: 200,
  },
})
  