import React from 'react'
import { Pressable } from 'react-native'
import { Icon } from 'apptile-core'
import TextElement from '../../../components-v2/base/TextElement'
import { useNavigate } from '@/root/web/routing.web'

const Back = ({url, text}: {url?: string, text?: string}) => {
  const navigate = useNavigate();
  return (  
    <Pressable onPress={() => navigate(url || -1)} style={{ flexDirection: 'row', alignItems: 'center', width: 'fit-content', gap: 4 }}>
      <Icon name="chevron-left" iconType="Feather" size={14} color={'#565656'}/>
      <TextElement style={{color: '#565656'}} fontSize="sm" lineHeight="sm" fontWeight="400">
        {text || 'Back'}
      </TextElement>
    </Pressable>
  )
}

export default Back