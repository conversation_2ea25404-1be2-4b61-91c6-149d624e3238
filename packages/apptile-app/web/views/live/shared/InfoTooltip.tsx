import React from 'react';
import Tooltip from '../../../components-v2/base/SimpleTooltip';
import { Icon } from 'apptile-core';

export type InfoTooltipProps = {
  tooltip: string;
  position?: 'top' | 'right' | 'bottom' | 'left';
  positionOffset?: number;
  children?: React.ReactElement;
};

const InfoTooltip: React.FC<InfoTooltipProps> = ({
  tooltip,
  position = 'top',
  children,
}) => {
  const containerStyles = { zIndex: 1000, marginLeft: 6 };
  const tooltipStyles: any = {
    flexWrap: 'wrap',
    color: 'black',
    paddingVertical: 4,
    paddingHorizontal: 6,
  };
  let toolTipMenuStyles: any = {
    width: 180,
    left: 80,
    zIndex: 1000,
    paddingVertical: 4,
    paddingHorizontal: 6,
    backgroundColor: 'white',
    borderColor: 'black',
    borderWidth: 0.4,
    borderStyle: 'solid' as 'solid',
  };

  return (
    <Tooltip
      tooltip={tooltip}
      position={position}
      containerStyles={containerStyles}
      tooltipStyles={tooltipStyles}
      toolTipMenuStyles={toolTipMenuStyles}
    >
      {children || <Icon iconType="MaterialCommunityIcons" name="information-outline" size={16} />}
    </Tooltip>
  );
};

export default InfoTooltip;