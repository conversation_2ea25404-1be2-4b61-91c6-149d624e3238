import TextElement from '@/root/web/components-v2/base/TextElement';
import theme from '@/root/web/styles-v2/theme';
import { Icon } from 'apptile-core';
import React from 'react'
import { StyleSheet, View } from 'react-native';
import Button from '@/root/web/components-v2/base/Button';

const NetworkErrorCmp = ({title, desc, CTA}: {title: string, desc: string, CTA: string}) => {
  return (
    <View style={styles.facebookNetworkErrorContainer}>
      <Icon
        name="error"
        iconType="MaterialIcons"
        size={48}
        color="#AA2217"
        style={styles.facebookNetworkErrorIcon}
      />
      <TextElement
        color='SECONDARY'
        fontSize="md"
        lineHeight="md"
        fontWeight="500"
      >
        {title}
      </TextElement>
      <TextElement
        style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}
        fontSize='xs'
        lineHeight='xs'
        fontWeight='400'
      >
        {desc}
      </TextElement>
      <Button
        color="CTA"
        innerContainerStyles={styles.facebookNetworkErrorBtn}
        containerStyles={{marginTop: 24}}
        onPress={() => { window.location.reload(); }}
      >
        {CTA}
      </Button>
    </View>
  )
}

export default NetworkErrorCmp

const styles = StyleSheet.create({
  facebookNetworkErrorContainer: {
    width: '100%',
    minHeight: 200,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    marginTop: 15,
  },
  facebookNetworkErrorIcon: {
    marginBottom: 8,
  },
  facebookNetworkErrorBtn: {
    paddingLeft: 28,
    paddingRight: 28,
  },
})
