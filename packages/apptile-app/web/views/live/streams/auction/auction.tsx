import { COMMON_ERROR_MESSAGE } from '@/root/app/common/utils/apiErrorMessages/generalMessages';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import ReactionEmojis from '@/root/app/plugins/widgets/LivelyLiveSellingWidgetV2/shared/ReactionEmojis';
import { AuctionApi } from '@/root/web/api/AuctionApi';
import { LivelyApi } from '@/root/web/api/LivelyApi';
import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes';
import { ApptileWebIcon } from '@/root/web/icons/ApptileWebIcon.web';
import { useNavigate, useParams } from '@/root/web/routing.web';
import theme from '@/root/web/styles-v2/theme';
import { Icon, LocalStorage as localStorage } from 'apptile-core';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Image, LayoutChangeEvent, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import { ZegoStreamList } from 'zego-express-engine-webrtc/sdk/code/zh/ZegoExpressEntity.web';
import { ZegoUser } from 'zego-express-engine-webrtc/sdk/src/common/zego.entity';
import { ZegoBarrageMessageInfo, ZegoBroadcastMessageInfo } from 'zego-express-engine-webrtm/sdk/code/zh/ZegoExpressEntity';
import { LIVE_STREAM } from '../../../../../app/common/utils/apiErrorMessages/specificFeature';
import { fetchFacebookToken, fetchInstagramPages, fetchInstagramToken, fetchStreamSettings } from '../../../../actions/liveSellingActions';
import { makeToast, removeToast } from '../../../../actions/toastActions';
import Button from '../../../../components-v2/base/Button';
import ModalComponent from '../../../../components-v2/base/Modal';
import RadioGroupControl from '../../../../components/controls/RadioGroupControl';
import { ShopifyItemObjectPicker } from '../../../../integrations/shopify/components/ShopifyItemPicker';
import { EditorRootState } from '../../../../store/EditorRootState';
import commonStyles from '../../../../styles-v2/commonStyles';
import MutedUsersModal from '../../modals/mutedUsersModal';
import Back from '../../shared/Back';
import { handleApiError, handleApiErrorWithoutNetwork } from '../../shared/CommonError';
import NetworkErrorPage from '../../shared/NetworkErrorPage';
import { LIVE_DASHBOARD_ROUTES } from '../../utils/liveDashboardRoutes';
import { shopifyProductTransformer } from '../../utils/transformers';
import { initZego, zegoExpEngine } from '../../zegoEngine';
import CommentsView from '../shared/commentsView';
import { CustomerViews } from '../shared/customerViews';
import Counter from '../shared/timeCounter';
import { useShopify } from '../useShopify';
import { ZegoWebEvent } from '../zeego/eventTypes';
import { ZEEGO_CUSTOM_HANDLERS } from '../zeego/zeegoCustomHandlers';
import AuctionCard from './auctionCard';
import BidInfo from './bidInfo';
import TabsLite from '@/root/web/components-v2/composite/TabsLite';
import PollView from '../live/pollView';
import EditProductInfoModal from '../../modals/editProductInfoModal';

export enum CommentPlatform {
  FACEBOOK = 'Facebook',
  INSTAGRAM = 'Instagram',
  APP = 'App',
  ALL = 'All',
}

const facebookAPIPollingTime = 2500;
const pollResultsPollingTime = 3000;

// TODO(LIVE): When adding new products, prompt them to confirm starting_bid_price

const Auction = () => {
  /* Hooks */
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  let {streamId = '', role} = params as any;
  role = parseInt(role) ?? 2;
  const {shopifyQueryRunner, getProductsInfoByIds, searchProductsForDropDown} = useShopify();

  /* Selectors */
  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const settingsFromState = useSelector((state: EditorRootState) => state.liveSelling.streamSettings.data);
  const currencyCode = useSelector(
    (state: EditorRootState) => state.liveSelling.auth.livelyUser?.currency?.currency_code,
  );
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const livelyUser = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser);
  const companyInfo = useSelector((state: EditorRootState) => state.liveSelling.auth.companyInfo);
  const instagramLoading = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelLoading);
  const instagramChannels = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelAuths);
  const isInstagramConnectionError = useSelector((state: EditorRootState) => state.liveSelling.isInstagramConnectionNetworkError);
  const facebookStream = useSelector((state: EditorRootState) => state.liveSelling.facebookTokens[streamId]);
  const instagramStream = useSelector((state: EditorRootState) => state.liveSelling.instagramTokens[streamId]);

  /* Refs */
  const zeegoLocalStreamRef = useRef<any>(null);
  const appMessagesToPush = useRef<any>();
  const viewerMultiplierRef = useRef(null);
  const viewerMultiplierIntervalRef = useRef(null);
  const emojiRef = useRef<any>(null);
  const scannedBarcode = useRef('');
  const streamInfoRef = useRef<any>(null);
  const cardRef = useRef<any>(null);

  const fbInterval = useRef(null);
  const fbErrorInterval = useRef(null);
  const fbAfterCursor = useRef(null);
  const fbMessagesToPush = useRef<any[]>([]);

  const instaInterval = useRef(null);
  const instaErrorInterval = useRef(null);
  const instaAfterCursor = useRef(0);
  const instaMessagesToPush = useRef<any[]>([]);
  const connection = useRef(null);

  const activePollRef = useRef<any>(null);
  const pollIntervalRef = useRef<any>(null);

  const initialEndTimeStamp = useRef<any>(null);
  const biddingStartTimeStamp = useRef<number>(0);
  const endTimeStampRef = useRef<number>(0);

  const productScannedRef = useRef<any>(null);

  const bidQueueRef = useRef<any[]>([]);
  const isProcessingBidRef = useRef<boolean>(false);
  const moderatorInCharge = useRef<{userID: string, userName: string}>();
  const bidStartedModerator = useRef<{userID: string, userName: string}>();

  /* States */
  const [streamInfo, setStreamInfo] = useState<any>();
  const [roomId, setRoomId] = useState('');
  const [userCount, setUserCount] = useState(0);
  const [products, setProducts] = useState<any[]>([]);
  const [microPhoneFlag, setMicrophoneFlag] = useState(false);
  const [cameraFlag, setcameraFlag] = useState(role == 2);
  const [speakerFlag, setSpeakerFlag] = useState(true);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [newProduct, setNewProduct] = useState<any>();
  const [showCustomerView, setShowCustomerView] = useState(false);
  const [requiredCustomerView, setRequiredCustomerView] = useState(1);
  const [viewerMultiplier, setViewerMultiplier] = useState(1);
  const [isBarCodeResultLoading, setIsBarCodeResultLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [videoScreenDimensions, setVideoScreenDimensions] = useState({width: 0, height: 0});
  const [isNetworkError, setIsNetworkError] = useState<boolean>(false);
  const [showCommentsAction, setShowCommentsAction] = useState(false);
  const [settings, setSettings] = useState<ILiveStreamSettings | null>(settingsFromState);
  const [highestBid, setHighestBid] = useState<number>(0);
  const [latestBidInfo, setLatestBidInfo] = useState<any>({});
  const [callToken, setCallToken] = useState(null);
  const [instaFetched, setInstaFetched] = useState(false);

  const [comments, setComments] = useState<any[]>([]);
  const [allComments, setAllComments] = useState<any[]>([]);
  const [facebookComments, setFacebookComments] = useState([]);
  // const [showFBComments, setShowFBComments] = useState(false);
  const [facebookCommentsLoading, setFacebookCommentsLoading] = useState(false);
  const [instagramComments, setInstagramComments] = useState([]);
  // const [showFBComments, setShowFBComments] = useState(false);
  const [instagramCommentsLoading, setInstagramCommentsLoading] = useState(false);
  const [hasNewFacebookComments, setHasNewFacebookComments] = useState(false);
  const [hasNewInstagramComments, setHasNewInstagramComments] = useState(false);
  const [activeCommentTab, setActiveCommentTab] = useState(CommentPlatform.ALL);
  const [availableCommentPlatforms, setAvailableCommentPlatforms] = useState<CommentPlatform[] | []>([
    CommentPlatform.APP,
  ]);

  // Polls
  const [showPolls, setShowPolls] = useState(false);
  const [polls, setPolls] = useState([]);
  const [activePoll, setActivePoll] = useState({});
  const [expandedPolls, setExpandedPolls] = useState({});
  const [pollsLoading, setPollsLoading] = useState(false);
  const [pollsUpdated, setPollsUpdated] = useState(false);
  const [showCreatePoll, setShowCreatePoll] = useState(false);

  const [mutedUsers, setMutedUsers] = useState<any[]>([]);
  const [mutedUsersModal, setMutedUsersModal] = useState(false);

  const [showEditProductInfoModal, setShowEditProductInfoModal] = useState<boolean>(false);

  /* Variables */
  const currentSelectedProduct = _.find(products, {product_id: selectedProductId});
  const AuctionAPI = new AuctionApi(apptileAppId, authToken);
  const highestLotNumber = products.length > 0 
    ? Math.max(...products
      .filter(product => product?.meta?.lot_number !== undefined)
      .map(product => Number(product?.meta?.lot_number))
    ) : 200;

  /* Triggers */
  const triggerEmoji = (reactionUrl: string, userName: string) => {
    emojiRef.current?.triggerAddEmoji(reactionUrl, userName);
  };

  /* Handlers */
  const changeCallStatus = async (status: string) => {
    await LivelyApi.changeStreamStatus(authToken, streamId, status).catch(error => {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'changeCallStatus');
    });
  };
  const toggleMicrophone = () => {
    setMicrophoneFlag(prev => {
      ZEEGO_CUSTOM_HANDLERS.toggleMicrophone(zeegoLocalStreamRef?.current, prev);
      return !prev;
    });
  };
  const toggleCamera = () => {
    setcameraFlag(prev => {
      ZEEGO_CUSTOM_HANDLERS.toggleCamera(zeegoLocalStreamRef?.current, !prev);
      let localVideoRef = document?.getElementById('local-video') as HTMLVideoElement;
      if (localVideoRef) localVideoRef.style.display = !prev ? 'block' : 'none';
      return !prev;
    });
  };
  const handleEndCall = () => {
    if (zegoExpEngine) ZEEGO_CUSTOM_HANDLERS.endCall(zeegoLocalStreamRef?.current, streamId, roomId);
    zeegoLocalStreamRef.current = null;
    // TODO(NAVEEN): Case when a live can end abruptly, this call will not happen, later the products can't be searched inside app.
    role == 1 && AuctionAPI.removeAuctionTags(
      products.map(p => `gid://shopify/Product/${p.store_product_id}`),
      streamId,
    );
    changeCallStatus('3');
    setTimeout(() => navigate(-1), 250);
  };
  const toggleMuteStream = () => {
    zegoExpEngine.muteAllPlayAudioStreams(speakerFlag);
    setSpeakerFlag(prev => !prev);
  };
  const updateViewMultiplier = useCallback(
    _.debounce(count => {
      viewerMultiplierRef.current = count;
      setViewerMultiplier(count);
    }, 300),
    [],
  );
  const fetchViewerMultiplier = async () => {
    const response = await LivelyApi.getCompanyInfo(authToken);
    if (response?.data?.data?.meta?.default_multiplier) {
      updateViewMultiplier(response?.data?.data?.meta?.default_multiplier);
    }
  };
  const viewerMultiplierChange = (count: any) => {
    if (count == null) return;
    if (role == 1)
      zegoExpEngine.setStreamExtraInfo(
        streamId,
        JSON.stringify({
          count,
          event: ZegoWebEvent.MULTIPLIER_CHANGE,
        }),
      );
    else if (role == 2) {
      setRoomId(roomId => {
        setStreamInfo((streamInfo: any) => {
          zegoExpEngine.sendCustomCommand(
            roomId,
            JSON.stringify({
              count,
              type: ZegoWebEvent.CHANGE_MULTIPLIER_EVENT,
            }),
            [streamInfo?.user_id],
          );
          return streamInfo;
        });
        return roomId;
      });
    }
    updateViewMultiplier(count);
  };
  const onSetProducts = async (products: any[]) => {
    if (!products || products.length === 0) return;
    const productIds = products.map((e: any) => 'gid://shopify/Product/' + e.store_product_id);
    let productMetadata = await getProductsInfoByIds(productIds, 'onSetProducts');
    if (productMetadata?.totalInventory <= 0) return dispatch(
      makeToast({
        content: LIVE_STREAM.ERROR.OUT_OF_STOCK,
        appearances: 'error',
        duration: 2000,
      }),
    );
    const enhancedProducts = products.map((product: any) => {
      const productMetadataItem = productMetadata?.find((e: any) => e.store_product_id == product.store_product_id);
      return {...product, ...productMetadataItem};
    });
    setProducts(enhancedProducts);
  };
  const onSetNewProduct = (item: any) => {
    if (!_.isEmpty(item) && !item.onlineStoreUrl) {
      dispatch(
        makeToast({
          content: 'Product is not available on online store sales channel',
          appearances: 'error',
          duration: 2000,
        }),
      );
      return;
    }
    setNewProduct(item);
  };
  const addComment = (newComment: any) => {
    setComments(prevComments => prevComments.concat(newComment).slice(prevComments.length - 200).sort((a, b) => a.timeStamp - b.timeStamp));
    setAllComments(prevComments => prevComments.concat(newComment).slice(prevComments.length - 400).sort((a, b) => a.timeStamp - b.timeStamp));
  };
  const addFacebookComments = (newCommentsRaw: string[]) => {
    setFacebookCommentsLoading(false);
    if (newCommentsRaw.length !== 0) setHasNewFacebookComments(true);
    setFacebookComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 200);
    });
    setAllComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 400);
    });
  };
  const addInstagramComments = (newCommentsRaw: any[]) => {
    setInstagramCommentsLoading(false);
    if (newCommentsRaw.length !== 0) setHasNewInstagramComments(true);
    setInstagramComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 200);
    });
    setAllComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 400);
    });
  };
  const getFacebookComments = async () => {
    try {
      const response = await LivelyApi.getFacebookComments(
        facebookStream?.token,
        facebookStream?.fb_stream_id,
        fbAfterCursor?.current,
      );
      if (!response.status === 200) {
        throw new Error('Failed to fetch Facebook comments');
      }
      const data = response.data;
      if (data?.paging?.cursors?.after) fbAfterCursor.current = data?.paging?.cursors?.after;
      const fbComments = data?.data
        ?.map(commentInfo => ({
          name: commentInfo?.from?.id !== facebookStream?.page_id ? commentInfo?.from?.name || 'Unknown' : 'YOU',
          comment: commentInfo.message,
          imgUrl: streamInfo?.streaming_thumbnail,
          id: commentInfo?.id,
          source: commentInfo?.from?.id !== facebookStream?.page_id ? 'USER' : 'HOST',
          isReplied: false,
          from: 'FACEBOOK',
        }))
        .filter((e: any) => !!e);
      addFacebookComments(fbComments);
      const historyComments = data?.data?.map(item => ({
        comment: item.message,
        uuid: item?.id,
        user_type: item?.from?.id !== facebookStream?.page_id ? 'viewer' : 'host',
        user_name: item?.from?.name || 'Unknown',
        from: 'FACEBOOK',
        ...(item?.parent && {parent_uuid: item?.parent?.id}),
      }));
      fbMessagesToPush.current = fbMessagesToPush.current.concat(historyComments ?? []);
    } catch (error) {
      handleApiError(error, LIVE_STREAM.ERROR.FB_COMMENTS, dispatch, 'getFacebookComments');
    }
  };
  const commentsHistoryUpdate = async (streamId: string) => {
    if (streamInfo?.streaming_name) {
      if (appMessagesToPush?.current && appMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          appMessagesToPush?.current,
          'app',
        ).catch(error => {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'commentsHistoryUpdate - app');
        });
        appMessagesToPush.current = [];
      }
      if (fbMessagesToPush?.current && fbMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          fbMessagesToPush?.current,
          'facebook',
        ).catch(error => {
          handleApiError(error, LIVE_STREAM.ERROR.FB_COMMENTS, dispatch, 'commentsHistoryUpdate - facebook');
        });
        fbMessagesToPush.current = [];
      }
      if (instaMessagesToPush?.current && instaMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          instaMessagesToPush?.current,
          'instagram',
        ).catch(error => {
          handleApiError(error, LIVE_STREAM.ERROR.INSTA_COMMENTS, dispatch, 'commentsHistoryUpdate - facebook');
        });
        instaMessagesToPush.current = [];
      }
    }
  };
  const handleChangeActiveProduct = (item: any) => {
    if (role == 1) {
      setSelectedProductId(item?.product_id);
      LivelyApi.changeCurrentProduct(authToken, streamId, item?.product_id);
      zegoExpEngine.setStreamExtraInfo(
        streamId,
        JSON.stringify({
          data: {
            product_id: item?.product_id,
            store_product_id: item?.store_product_id,
            base_bid_price: item?.base_bid_price,
            bid_incrementer: item?.bid_incrementer,
          },
          event: ZegoWebEvent.PRODUCT_CHANGE,
        }),
      );
    } else if (role == 2) {
      zegoExpEngine.sendCustomCommand(
        roomId,
        JSON.stringify({
          type: ZegoWebEvent.SHOW_PRODUCT_EVENT,
          id: item?.product_id,
        }),
        [streamInfo?.user_id],
      );
    }
  };
  const handleDeleteProduct = (item: any) => {
    const productInfo = {
      product_id: item?.product_id,
      store_product_id: item?.store_product_id,
    };
    if (role == 1) {
      zegoExpEngine.setStreamExtraInfo(
        streamId,
        JSON.stringify({
          data: productInfo,
          event: ZegoWebEvent.DELETE_PRODUCT,
        }),
      );
      LivelyApi.removeProductFromStream(authToken, streamId, [productInfo])
        .then(() => {
          LivelyApi.getLiveStreamInfoByID(authToken, streamId)
            .then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              setStreamInfo(newStreamInfo);
              onSetProducts(newProductsData);
            })
            .catch((error: any) => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'handleDeleteProduct - getLiveStreamInfoByID',
              );
            });
            dispatch(
              makeToast({
                content: LIVE_STREAM.SUCCESS.PRODUCT_REMOVED,
                appearances: 'success',
                duration: 2000,
              }),
            );
        })
        .then(() => {
          AuctionAPI.removeAuctionTags([`gid://shopify/Product/${productInfo.store_product_id}`], streamId);
        })
        .catch((error: any) => {
          handleApiError(
            error,
            COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
            dispatch,
            'handleDeleteProduct - removeProductFromStream',
          );
        })
    } else if (role == 2) {
      zegoExpEngine.sendCustomCommand(
        roomId,
        JSON.stringify({
          type: ZegoWebEvent.DELETE_PRODUCT_EVENT,
          id: productInfo.product_id,
        }),
        [streamInfo?.user_id],
      );
    }
  };
  const addNewProduct = async (reformedProduct?: any) => {
    const finalProduct = (reformedProduct && typeof reformedProduct === 'object' && !reformedProduct.nativeEvent) 
      ? reformedProduct 
      : newProduct;
    
    if (finalProduct) {
      if (products.find(e => e.store_product_id == finalProduct?.id.split('/').pop())) return handleApiErrorWithoutNetwork('product exist', LIVE_STREAM.ERROR.PRODUCT_EXIST, dispatch, 'addNewProduct');
      else {
        let productMetadata = {};
        try {
          const queryResponse = await shopifyQueryRunner.runQuery('query', ProductCollectionGqls.GET_PRODUCT, {
            productId: finalProduct.id
          })
          productMetadata = shopifyProductTransformer(queryResponse?.data?.product) ?? {};
        } catch (error) {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'addNewProduct');
        }
        if (productMetadata?.totalInventory <= 0) {
          setNewProduct('');
          return dispatch(
            makeToast({
              content: LIVE_STREAM.ERROR.OUT_OF_STOCK,
              appearances: 'warning',
              duration: 2000,
            }),
          );
        }
        if(productMetadata?.title?.includes('LOT #')) {
          setNewProduct('');
          return dispatch(
            makeToast({
              content: LIVE_STREAM.ERROR.ALREADY_AUCTIONED,
              appearances: 'error',
              duration: 2000,
            })
          )
        }

        if(!finalProduct.lotNumber || !finalProduct.baseBidPrice || !finalProduct.bidIncrementer) return setShowEditProductInfoModal(true);
        const productInfo = {
          store_product_id: finalProduct?.id.split('/').pop(),
          product_url: finalProduct?.onlineStoreUrl ?? 'https://apptile.io/',
          product_name: finalProduct?.title,
          product_thumbnail: finalProduct?.featuredImage,
          product_price: finalProduct?.maxSalePrice,
          base_bid_price: finalProduct?.baseBidPrice,
          bid_incrementer: finalProduct?.bidIncrementer,
          meta: {
            lot_number: Number(finalProduct?.lotNumber)
          }
        };
        if (role == 2) {
          zegoExpEngine.sendCustomCommand(
            roomId,
            JSON.stringify({
              type: ZegoWebEvent.ADD_NEW_PRODUCT,
              data: productInfo,
            }),
            [streamInfo?.user_id],
          );
          setTimeout(() => {
            LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              if (newProductsData) {
                onSetProducts(newProductsData);
                dispatch(
                  makeToast({
                    content: LIVE_STREAM.SUCCESS.PRODUCT_ADDED,
                    appearances: 'success',
                    duration: 2000,
                  }),
                );
              } else {
                handleApiErrorWithoutNetwork(
                  'undefined newProductsData',
                  COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR,
                  dispatch,
                  'addNewProduct',
                );
              }
            });
          }, 1500);
        } else if (role == 1) {
          LivelyApi.addNewProductToStream(authToken, streamId, [productInfo]).then(async () => {
            LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              setStreamInfo(newStreamInfo);
              const updatedProduct = newStreamInfo?.product_info?.find((e: any) => e.store_product_id == productInfo.store_product_id?.split('/')?.pop())
              streamInfo?.meta?.appendLotNumber && AuctionAPI.addedProductToStream(
                `gid://shopify/Product/${updatedProduct.store_product_id}`, 
                streamId,
                `LOT #${updatedProduct.meta?.lot_number} - ${updatedProduct.name}`
              )
              let newProductsData = newStreamInfo?.product_info;
              if (newProductsData) {
                onSetProducts(newProductsData);
                dispatch(
                  makeToast({
                    content: LIVE_STREAM.SUCCESS.PRODUCT_ADDED,
                    appearances: 'success',
                    duration: 2000,
                  }),
                );
              } else {
                handleApiErrorWithoutNetwork(
                  'undefined newProductsData',
                  COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR,
                  dispatch,
                  'addNewProduct',
                );
              }
            });
          })
        }
        onSetNewProduct('');
      }
    }
  };
  const addNewScannedProduct = _.debounce(async (barcode: any, reformedProduct?: any) => {
    setIsBarCodeResultLoading(true);
    const scanningId = uuidv4();
    try {
      const transformedData = await searchProductsForDropDown(barcode);
      if (transformedData.length != 0) {
        const productScanned = transformedData.find(
          (e: any) => e.handle == barcode || !!e.variants.find((f: any) => f.barcode == barcode),
        );
        productScannedRef.current = (reformedProduct && typeof reformedProduct === 'object' && !reformedProduct.nativeEvent) ? {...productScanned, ...reformedProduct} : productScanned;
        if (productScannedRef.current) {
          let productFoundInList;
          setProducts(streamProducts => {
            productFoundInList = streamProducts.find(e => 'gid://shopify/Product/' + e.store_product_id == productScannedRef.current.id)
            return streamProducts;
          });
          if (!productFoundInList) {
            if (productScannedRef.current?.totalInventory <= 0) {
              dispatch(
                makeToast({
                  content: LIVE_STREAM.ERROR.OUT_OF_STOCK,
                  appearances: 'warning',
                  duration: 2000,
                }),
              );
            } else if (productScannedRef.current?.title?.includes('LOT #')) {
              dispatch(
                makeToast({
                  content: LIVE_STREAM.ERROR.ALREADY_AUCTIONED,
                  appearances: 'error',
                  duration: 2000,
                })
              )
            } else if (!productScannedRef.current?.bidIncrementer || !productScannedRef.current?.baseBidPrice || !productScannedRef.current?.lotNumber) {
              return setShowEditProductInfoModal(true);
            } else {
              dispatch(removeToast([scanningId]));
              dispatch(
                makeToast({
                  content: `${productScannedRef.current.title} added to live`,
                  appearances: 'success',
                }),
              );
              const productInfo = {
                store_product_id: productScannedRef.current.id.split('/').pop(),
                product_url: productScannedRef.current?.onlineStoreUrl ?? 'https://apptile.io/',
                product_name: productScannedRef.current?.title,
                product_thumbnail: productScannedRef.current?.featuredImage,
                product_price: productScannedRef.current?.maxSalePrice,
                base_bid_price: Number(productScannedRef.current?.baseBidPrice),
                bid_incrementer: Number(productScannedRef.current?.bidIncrementer),
                meta: {
                  lot_number: Number(productScannedRef.current?.lotNumber)
                }
              };
              if (role == 2) {
                zegoExpEngine.sendCustomCommand(
                  roomId,
                  JSON.stringify({
                    type: ZegoWebEvent.ADD_NEW_PRODUCT,
                    data: productInfo,
                  }),
                  [streamInfo?.user_id],
                );
                setTimeout(() => {
                  LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                    const newStreamInfo = streamResponse?.data?.data;
                    let newProductsData = newStreamInfo?.product_info;
                    if (newProductsData) {
                      onSetProducts(newProductsData);
                      const scannedProduct = newProductsData.find(
                        (e: any) => e?.store_product_id == productScannedRef.current?.id?.split('/')?.pop(),
                      );
                      if (scannedProduct) handleChangeActiveProduct(scannedProduct);
                    }
                  });
                }, 1500);
              } else if (role == 1) {
                LivelyApi.addNewProductToStream(authToken, streamId, [productInfo]).then(() => {
                  LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                    const newStreamInfo = streamResponse?.data?.data;
                    let newProductsData = newStreamInfo?.product_info;
                    if (newProductsData) {
                      onSetProducts(newProductsData);
                      const scannedProduct = newProductsData.find(
                        (e: any) => e?.store_product_id == productScannedRef.current?.id?.split('/')?.pop(),
                      );
                      if (scannedProduct) {
                        handleChangeActiveProduct(scannedProduct);
                        newStreamInfo?.meta?.appendLotNumber && AuctionAPI.addedProductToStream(
                         `gid://shopify/Product/${scannedProduct.store_product_id}`, 
                         streamId, 
                         `LOT #${scannedProduct.meta?.lot_number} - ${scannedProduct.name}`
                        )
                      }
                    }
                  })
                })
              }
            }
            scannedBarcode.current = '';
          } else {
            dispatch(removeToast([scanningId]));
            handleChangeActiveProduct(productFoundInList);
            dispatch(
              makeToast({
                content: 'Product found. Showing Now!',
                appearances: 'success',
                duration: 2000,
              }),
            );
            scannedBarcode.current = '';
          }
        } else {
          scannedBarcode.current = '';
          dispatch(removeToast([scanningId]));
          dispatch(
            makeToast({
              content: 'Invalid barcode, please try again',
              appearances: 'error',
              duration: 2000,
            }),
          );
        }
      } else {
        scannedBarcode.current = '';
        dispatch(removeToast([scanningId]));
        dispatch(
          makeToast({
            content: 'Invalid barcode, please try again',
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } catch (err) {
      console.log(err);
      scannedBarcode.current = '';
      dispatch(removeToast([scanningId]));
      dispatch(
        makeToast({
          content: 'Something went wrong, please try again with different barcode',
          appearances: 'error',
          duration: 4000,
        }),
      );
    }
    setIsBarCodeResultLoading(false);
  }, 300);

  /* Shadow Ban Handlers */
  const handleMuteUsers = async (comment: any) => {
    const updatedMutedUsers = mutedUsers.find((user: any) => user.deviceId === comment.deviceId)
      ? mutedUsers.filter(user => user.deviceId !== comment.deviceId)
      : [...mutedUsers, comment];
    setMutedUsers(updatedMutedUsers);
    LivelyApi.updateStreamMeta(authToken, streamId, { mutedUsers: updatedMutedUsers })
    .then(() => {
      const muteObj = {
        type: 'MUTE_USER',
        data: { mutedUsers: updatedMutedUsers }
      }
      zegoExpEngine.sendBroadcastMessage(streamInfoRef.current?.room_id, JSON.stringify(muteObj))
    })
    .catch(error => {
      console.log('ERROR IN MUTING USERS: ', error)
      dispatch(
        makeToast({
          content: 'Something went wrong while muting the user!',
          appearances: 'error',
          duration: 1500,
        }),
      );
    })
  }

  /* Auction Handlers */
  const startBidding = () => {
    setLatestBidInfo({});
    endTimeStampRef.current = Date.now() + _.get(streamInfoRef.current, 'auction_duration', 0);
    biddingStartTimeStamp.current = Date.now();
    bidStartedModerator.current = moderatorInCharge.current;
    zegoExpEngine.sendBroadcastMessage(
      streamInfoRef.current.room_id,
      JSON.stringify({
        type: ZegoWebEvent.START_BIDDING,
        data: {endTimeStamp: endTimeStampRef.current, moderatorInCharge: moderatorInCharge.current},
      })
    )
    initialEndTimeStamp.current = endTimeStampRef.current;
    setSelectedProductId(selectedProductId => {
      setStreamInfo((streamInfo: any) => {
        const currProd = _.find(streamInfo?.product_info, (e: any) => e.product_id == selectedProductId)
        setHighestBid(currProd?.base_bid_price);
        return streamInfo;
      })
      return selectedProductId;
    })
  };
  const cancelBidding = () => {
    zegoExpEngine.sendBroadcastMessage(
      streamInfoRef.current.room_id,
      JSON.stringify({
        type: ZegoWebEvent.CLOSE_BIDDING,
      })
    )
    clearAuctionStates();
  }
  const handleStartBidding = (info: any, serverSentTime: number) => {
    const { endTimeStamp, moderatorInCharge } = info;
    setLatestBidInfo({});
    endTimeStampRef.current = endTimeStamp;
    initialEndTimeStamp.current = endTimeStamp;
    biddingStartTimeStamp.current = serverSentTime;
    bidStartedModerator.current = moderatorInCharge;
    setSelectedProductId(selectedProductId => {
      setStreamInfo((streamInfo: any) => {
        const currProd = _.find(streamInfo?.product_info, (e: any) => e.product_id == selectedProductId)
        setHighestBid(currProd?.base_bid_price);
        return streamInfo;
      })
      return selectedProductId;
    })
  }
  const clearAuctionStates = () => {
    setHighestBid(0);
    endTimeStampRef.current = 0;
    setLatestBidInfo({});
    initialEndTimeStamp.current = null;
    biddingStartTimeStamp.current = 0;
    bidQueueRef.current = [];
    isProcessingBidRef.current = false;
    bidStartedModerator.current = undefined;
  };
  const closeBidding = async () => {
    if(role != 2 || bidStartedModerator.current?.userID != moderatorInCharge.current?.userID) return;
    setLatestBidInfo(async (latestBidInfo: any) => {
      if (!_.isEmpty(latestBidInfo)) {
        await LivelyApi.putBidWinner(authToken, streamId, latestBidInfo?.user?.userId, selectedProductId!, {
          winningBid: latestBidInfo?.newHighestBid,
          user: latestBidInfo?.user,
          currencyCode,
        })
          .then(async ({data}: any) => {
            const {product_info} = data?.data;
            const auctionWinners = product_info.find((e: any) => e.product_id == selectedProductId)?.auction_winner;
            const bidWinner = auctionWinners?.pop();
            if (bidWinner) {
              zegoExpEngine.sendBroadcastMessage(
                roomId,
                JSON.stringify({
                  type: ZegoWebEvent.BID_WINNER_ANNOUNCEMENT,
                  data: {
                    bidWinner,
                  },
                }),
              );
              setSelectedProductId(selectedProductId => {
                setStreamInfo((streamInfo: any) => {
                  const currProd = _.find(streamInfo?.product_info, (e: any) => e.product_id == selectedProductId)
                  const comment = `Congratulations!! ${bidWinner?.meta?.user?.userName} is the winner of LOT #${currProd?.meta?.lot_number}`;
                  const commentObj = {
                    name: 'You',
                    comment,
                    imgUrl: streamInfo?.streaming_thumbnail,
                    id: `${new Date().getTime()}${Math.floor(Math.random() * 10000)}`,
                    source: `HOST`,
                    isPinned: false,
                    from: 'APP',
                    isBidComment: true,
                  }
                  addComment(commentObj);
                  zegoExpEngine.sendBarrageMessage(roomId, JSON.stringify(commentObj));
                  appMessagesToPush.current = appMessagesToPush.current?.concat([
                    {
                      comment: commentObj.comment,
                      user_type: 'host',
                      user_name: commentObj.name,
                      from: 'APP'
                    }
                  ])
                  const winnerBody = {
                    ...bidWinner?.meta,
                    productId: currProd?.store_product_id,
                    variantId: latestBidInfo?.userOptedVariantId,
                    streamId,
                  }
                  AuctionAPI.putBidWinner(winnerBody);
                  return streamInfo;
                })
                return selectedProductId;
              })
            }
          })
          .catch((err: any) => {
            handleApiError(err, COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR, dispatch, 'putBidWinner');
          })
      } else cancelBidding();
      return latestBidInfo;
    });
    clearAuctionStates();
  }
  const processBidQueue = () => {
    if(isProcessingBidRef.current || bidQueueRef.current.length == 0) return;
    isProcessingBidRef.current = true;

    const bidToProcess = bidQueueRef.current.shift();
    if(bidToProcess && endTimeStampRef.current) handleBidPlaced(bidToProcess);
    setTimeout(() => {
      isProcessingBidRef.current = false;
      processBidQueue();
  }, 0);
  }

  const handleBidPlaced = (payload: any) => {
    console.log('xxx reached here')
    const {
      data: { bidAmount, timeStamp, user, userOptedVariantId },
    } = payload;

    setHighestBid((prevBid: number) => {
      const newHighestBid = Math.max(prevBid, bidAmount);
      let bidIncrementer;
      setSelectedProductId(selectedProductId => {
        let newEndTimeStamp = endTimeStampRef.current;
        setStreamInfo((streamInfo: any) => {
          const currProd = streamInfo?.product_info?.find((e: any) => e.product_id == selectedProductId);
          bidIncrementer = currProd?.bid_incrementer;

          const extBiddingDuration = streamInfo?.ext_bidding_duration;
          const triggerExtBiddingDuration = streamInfo?.trigger_ext_bidding_duration;
          const timeLeft = endTimeStampRef.current - timeStamp;
          if (timeLeft > 0 && timeLeft < triggerExtBiddingDuration) {
            newEndTimeStamp = endTimeStampRef.current + extBiddingDuration;
            biddingStartTimeStamp.current = Date.now();
          }
          return streamInfo;
        });
        endTimeStampRef.current = newEndTimeStamp
        const nextBid = newHighestBid + bidIncrementer!;
        if (prevBid < nextBid) {
          const infoObj = {
            type: ZegoWebEvent.BROADCAST_BID_INFO,
            data: {
              newHighestBid,
              nextBid,
              user,
              newEndTimeStamp,
              timestamp: Date.now(),
              userOptedVariantId,
            },
          };
          setLatestBidInfo(infoObj.data);
          zegoExpEngine.sendBroadcastMessage(streamInfoRef.current?.room_id, JSON.stringify(infoObj));
        }
        const commentInfoObj = {
          type: ZegoWebEvent.ADD_BID_COMMENT,
          data: {
            timeStamp: Date.now(),
            user,
            bid: bidAmount,
          }
        }
        zegoExpEngine.sendCustomCommand(streamInfoRef.current?.room_id, JSON.stringify(commentInfoObj), []);
        return selectedProductId;
      });
      return newHighestBid;
    });
  }
  const handleBroadcastBidInfo = (payload: any, serverSentTime: number) => {
    const { newHighestBid, newEndTimeStamp } = payload;
    if(newEndTimeStamp > endTimeStampRef.current) {
      endTimeStampRef.current = newEndTimeStamp;
      biddingStartTimeStamp.current = serverSentTime;
    }
    setHighestBid(newHighestBid);
  }

  /* Zego Listeneres */
  const handleRoomStreamUpdate = (roomId: string, updateType: string, streamList: ZegoStreamList[]) => {
    if (role == 2 && updateType === 'DELETE') {
      setTimeout(() => {
        LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
          if (streamResponse?.data?.data?.streaming_status === 3) {
            streamList.forEach(stream => {
              console.log(`Stream with ID ${stream.streamID} has ended.`);
              dispatch(
                makeToast({
                  content: 'Stream ended by host',
                  appearances: 'info',
                  duration: 2000,
                }),
              );
              navigate(LIVE_DASHBOARD_ROUTES.OVERVIEW_AUCTION());
            });
          }
        });
      }, 3000);
    }
  };
  const handleStreamExtraInfoUpdate = useCallback(
    (roomID: string, streamList: {streamID: string; user: ZegoUser; extraInfo: string}[]) => {
      const extraInfo = JSON.parse(streamList[0].extraInfo);
      if (extraInfo.event == 'PRODUCT_CHANGE') {
        const newProductId = extraInfo.data.product_id;
        if (role == 2) {
          LivelyApi.getLiveStreamInfoByID(authToken, streamId)
            .then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              setStreamInfo(streamResponse?.data?.data);
              setRoomId(newStreamInfo?.room_id);
              let newProductsData = newStreamInfo?.product_info;
              onSetProducts(newProductsData);
              setSelectedProductId(newProductId);
            })
            .catch(error => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'streamInfoUpdate - PRODUCT_CHANGE',
              );
            });
        }
      } else if (extraInfo.event == 'DELETE_PRODUCT') {
        setTimeout(() => {
          LivelyApi.getLiveStreamInfoByID(authToken, streamId)
            .then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              onSetProducts(newProductsData);
              dispatch(
                makeToast({
                  content: LIVE_STREAM.SUCCESS.PRODUCT_REMOVED,
                  appearances: 'success',
                  duration: 2000,
                }),
              );
            })
            .catch(error => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'treamInfoUpdate - DELETE_PRODUCT',
              );
            });
        }, 500);
      } else if (extraInfo.event == 'MULTIPLIER_CHANGE') {
        _.debounce(count => {
          viewerMultiplierRef.current = count;
          setViewerMultiplier(count);
        }, 300);
      }
    },
    [products],
  );
  const handleBarrageMessage = (roomID: string, messageInfo: ZegoBarrageMessageInfo[]) => {
    // TODO(LIVE): Show different colored comments to bid comments
    const messageObj = JSON.parse(messageInfo[0]?.message);
    if(messageObj.comment) {
      let obj = {
        name:
          messageObj?.source == 'MODERATOR' || messageObj?.source == 'HOST'
            ? livelyUser.company_name
            : messageInfo[0]?.fromUser?.userName?.startsWith('Anonymous') && messageObj?.name != 'You'
            ? messageObj?.name
            : messageInfo[0]?.fromUser?.userName || 'Anonymous',
        comment: messageObj?.comment,
        imgUrl: streamInfo?.streaming_thumbnail,
        id: `${new Date().getTime()}${Math.floor(Math.random() * 10000)}`,
        source: messageObj.source,
        from: 'APP',
        deviceId: messageObj?.deviceId,
        muted: messageObj?.muted,
        timeStamp: messageObj?.timeStamp || Date.now(),
      };
      addComment(obj);
      appMessagesToPush.current = appMessagesToPush.current.concat([
        {
          comment: obj.comment,
          user_type: obj.source == 'HOST' ? 'host' : obj.source == 'MODERATOR' ? 'moderator' : 'viewer',
          user_name: obj?.name,
          from: 'APP',
        },
      ]);
      commentsHistoryUpdate(streamId);
    }
  };
  const handleCustomCommand = async (roomID: string, fromUser: ZegoUser, command: string) => {
    console.log('CUSTOM COMMAND RECEIVED', command);
    let parsedCommand = JSON.parse(command);
    if (parsedCommand?.type == 'SHOW_PRODUCT_EVENT') {
      if (role == 1) {
        setSelectedProductId(parsedCommand?.id);
        let newProduct = products?.find(e => e.product_id == parsedCommand?.id);
        setProducts(oldProducts => {
          newProduct = oldProducts?.find(e => e.product_id == parsedCommand?.id);
          return oldProducts;
        });
        const infoObj = {
          data: {
            product_id: newProduct?.product_id,
            store_product_id: newProduct?.store_product_id,
            base_bid_price: newProduct?.base_bid_price,
            bid_incrementer: newProduct?.bid_incrementer,
          },
          event: 'PRODUCT_CHANGE',
        };
        if (newProduct) {
          LivelyApi.changeCurrentProduct(authToken, streamId, newProduct?.product_id).catch(error => {
            handleApiError(
              error,
              COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
              dispatch,
              'initiateZeegoSDK - SHOW_PRODUCT_EVENT',
            );
          });
          zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
        }
      }
    } else if (parsedCommand?.type == 'DELETE_PRODUCT_EVENT') {
      if (role == 1) {
        let existingProduct = products?.find(e => e.product_id == parsedCommand?.id);
        setProducts(oldProducts => {
          existingProduct = oldProducts?.find((e: any) => e.product_id == parsedCommand?.id);
          return oldProducts;
        });
        const infoObj = {
          data: {
            product_id: existingProduct?.product_id,
            store_product_id: existingProduct?.store_product_id,
          },
          event: 'DELETE_PRODUCT',
        };
        if (existingProduct) {
          zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
          LivelyApi.removeProductFromStream(authToken, streamId, [infoObj.data])
            .then(() => {
              LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                const newStreamInfo = streamResponse?.data?.data;
                let newProductsData = newStreamInfo?.product_info;
                onSetProducts(newProductsData);
              });
            })
            .catch(error => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'initiateZeegoSDK - DELETE_PRODUCT_EVENT',
              );
            });
        }
      }
    } else if (parsedCommand?.type == 'ADD_NEW_PRODUCT') {
      if (role == 1) {
        LivelyApi.addNewProductToStream(authToken, streamId, [parsedCommand.data])
          .then(() => {
            LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              onSetProducts(newProductsData);
            });
          })
          .catch(error => {
            handleApiError(
              error,
              COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
              dispatch,
              'initiateZeegoSDK - ADD_NEW_PRODUCT',
            );
          });
      }
    } else if (parsedCommand?.type == 'CHANGE_MULTIPLIER_EVENT') {
      if (parsedCommand?.count != null) {
        const infoObj = {
          count: parsedCommand?.count,
          event: 'MULTIPLIER_CHANGE',
        };
        viewerMultiplierRef.current = parsedCommand?.count;
        zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
      }
    } else if (parsedCommand?.type == ZegoWebEvent.BID_PLACED) {
      if(role == 2) {
        if(!bidQueueRef.current) {
          bidQueueRef.current = [];
        }
        bidQueueRef.current.push(parsedCommand);
        processBidQueue();
      }
    }
  };
  // TODO(LIVE): Change if making this single source
  const handleRoomOnlineUserCountUpdate = (roomID: string, count: number) => {
    setUserCount(count);
    if (role == 1) {
      setSelectedProductId(selectedProductId => {
        setStreamInfo((streamInfo: any) => {
          const product = streamInfo?.product_info?.find(e => e.product_id == selectedProductId);
          if(product) {
            const infoObj = {
              data: {
                product_id: product?.product_id,
                store_product_id: product?.store_product_id,
                base_bid_price: product?.base_bid_price,
                bid_incrementer: product?.bid_incrementer,
              },
              event: 'PRODUCT_CHANGE',
            };
            zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
          }
          return streamInfo;
        });
        return selectedProductId;
      });
      viewerMultiplierChange(viewerMultiplierRef.current)
    }
  };
  const handleRoomUserUpdate = (roomID: string, updateType: 'DELETE' | 'ADD', userList: ZegoUser[]) => {
    if(role == 2) {
      if (updateType == 'ADD') {
        setSelectedProductId(selectedProductId => {
          if (endTimeStampRef.current > Date.now()) {
            zegoExpEngine.sendCustomCommand(
              roomID,
              JSON.stringify({
                type: ZegoWebEvent.START_BIDDING,
                data: {
                  endTimeStamp: endTimeStampRef.current,
                  selectedProductId,
                  timeStamp: Date.now(),
                  moderatorInCharge: moderatorInCharge.current,
                },
              }),
              userList.map(user => user.userID),
            );
          }
          return selectedProductId;
        });
        setActivePoll((activePoll: any) => {
          if(!_.isEmpty(activePoll)) {
            zegoExpEngine.sendCustomCommand(
              roomID,
              JSON.stringify({
                type: 'START_POLL',
                poll: activePoll,
              }),
              userList.map(user => user.userID),
            );
          }
          return activePoll;
        })
        setMutedUsers((mutedUsers: any) => {
          if(!_.isEmpty(mutedUsers)) {
            zegoExpEngine.sendCustomCommand(
              roomID,
              JSON.stringify({
                type: 'MUTE_USER',
                mutedUsers,
              }),
              userList.map(user => user.userID),
            );
          }
          return mutedUsers;
        })
      }
    }
  };
  const handleBroadcastMessage = (roomID: string, messageList: ZegoBroadcastMessageInfo[]) => {
    console.log('BROADCAST MESSAGE RECEIVED', messageList);
    const serverSentTime = messageList[0].sendTime;
    const payload = JSON.parse(messageList[0].message);
    const payloadData = payload?.data;

    switch (payload.type as ZegoWebEvent) {
      case ZegoWebEvent.START_BIDDING:
        handleStartBidding(payloadData, serverSentTime);
        break;
      case ZegoWebEvent.CLOSE_BIDDING:
      case ZegoWebEvent.BID_WINNER_ANNOUNCEMENT:
        clearAuctionStates();
        break;
      case ZegoWebEvent.BROADCAST_BID_INFO:
        handleBroadcastBidInfo(payloadData, serverSentTime);
        break;
      case ZegoWebEvent.REACTION_ANNOUCEMENT:
        triggerEmoji(payloadData?.reactionUrl, payloadData?.userName);
      default:
        break;
    }
  }

  /* Init */
  useEffect(() => {
    const getStreamInfoAndUpdateStates = async () => {
      try {
        const streamResponse: any = await LivelyApi.getLiveStreamInfoByID(authToken, streamId);
        const newStreamInfo = streamResponse?.data?.data;
        if(newStreamInfo.stream_to.includes('facebook')) {
          dispatch(fetchFacebookToken(streamId));
          setAvailableCommentPlatforms(platforms => {
            return [CommentPlatform.ALL, ...platforms, CommentPlatform.FACEBOOK];
          });
        }
        if (newStreamInfo.stream_to.includes('instagram')) {
          const instaChannel = instagramChannels.find((e: any) => e?._id == newStreamInfo.insta_auth_id);
          if (instaChannel) {
            dispatch(fetchInstagramToken(streamId, newStreamInfo.insta_auth_id));
            setAvailableCommentPlatforms(platforms => {
              const newPlatforms = [...platforms, CommentPlatform.INSTAGRAM];
              if (!newPlatforms.includes(CommentPlatform.ALL)) newPlatforms.unshift(CommentPlatform.ALL);
              return newPlatforms;
            });
          } else {
            handleApiErrorWithoutNetwork(
              'Instagram Connection Error',
              LIVE_STREAM.ERROR.INSTA_CHANNEL_DISCONNECT,
              dispatch,
              'initLively',
            );
            navigate(-1);
          }
        }
        try {
          const response: any = await LivelyApi.getStreamComments(authToken, streamId, 'app');
          setComments(
            (response?.data?.data || []).map((e: any) => ({...e, name: e.user_name, id: e.unix_time, from: 'APP'})),
          );
          setAllComments(
            (response?.data?.data || []).map((e: any) => ({...e, name: e.user_name, id: e.unix_time, from: 'APP'})),
          );
        } catch (error) {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'initAuction - getStreamComments');
        }

        streamInfoRef.current = newStreamInfo;
        setStreamInfo(newStreamInfo);
        setRoomId(newStreamInfo?.room_id);
        setMutedUsers(newStreamInfo?.meta?.mutedUsers ?? [])

        const newProductsData = newStreamInfo?.product_info || [];
        if (newProductsData.length > 0) {
          setSelectedProductId(newProductsData[0]?.product_id);
        }
        onSetProducts(newProductsData);
      } catch (error: any) {
        if (error.code === 'ERR_NETWORK') {
          setIsNetworkError(true);
        }
        handleApiError(
          error,
          COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
          dispatch,
          'initAuction - getLiveStreamInfoByID',
          false,
        );
        return Promise.reject(error);
      }
    };
    const initiateZeegoSDK = async () => {
      try {
        zegoExpEngine.logoutRoom();
        const newUserId =
          role === 2
            ? `${livelyUser.user_id}Moderator${`${new Date().getTime()}`.slice(-8)}`
            : streamInfoRef.current?.user_id;
        const {room_id, user_name} = streamInfoRef.current;
        moderatorInCharge.current = {userID: newUserId, userName: user_name};
        let token = '';
        try {
          const tokenResponse: any = await LivelyApi.generateZeegoToken(authToken, newUserId, streamId);
          token = tokenResponse?.data?.data;
          if (role === 1) await changeCallStatus('2');
        } catch (error) {
          handleApiError(error, LIVE_STREAM.ERROR.CONNECTION_ISSUE, dispatch, 'initAuction - generateZeegoToken');
          return;
        }

        const user = {userID: newUserId, userName: user_name};
        const roomConfig = {userUpdate: true};
        const result = await zegoExpEngine.loginRoom(room_id, token, user, roomConfig);

        if (result) {
          if (role === 1) {
            const localStream = await ZEEGO_CUSTOM_HANDLERS.playLocalStream(streamId);
            zeegoLocalStreamRef.current = localStream;
          } else if (role === 2) {
            const remoteStreamInitial = await zegoExpEngine.startPlayingStream(streamId);
            const remoteView = zegoExpEngine.createRemoteStreamView(remoteStreamInitial);
            remoteView.play('local-video');
          }
        }
        setIsLoggedIn(true);
      } catch (error) {
        console.error('Error initializing Zeego SDK:', error);
        setIsLoggedIn(false);
      }
    };
    const init = async () => {
      initZego();
      await getStreamInfoAndUpdateStates();
      await initiateZeegoSDK();
      zegoExpEngine.on('roomStreamUpdate', handleRoomStreamUpdate);
      zegoExpEngine.on('streamExtraInfoUpdate', handleStreamExtraInfoUpdate);
      zegoExpEngine.on('IMRecvBarrageMessage', handleBarrageMessage);
      zegoExpEngine.on('IMRecvBroadcastMessage', handleBroadcastMessage);
      zegoExpEngine.on('IMRecvCustomCommand', handleCustomCommand);
      zegoExpEngine.on('roomOnlineUserCountUpdate', handleRoomOnlineUserCountUpdate);
      zegoExpEngine.on('roomUserUpdate', handleRoomUserUpdate);
    };
    if (!instagramLoading && !instaFetched) {
      dispatch(fetchInstagramPages());
      setInstaFetched(true);
    }
    if (authToken && streamId && role && !streamInfo && !instagramLoading && instaFetched && !isInstagramConnectionError) {
      if (role == 1) {
        LivelyApi.getHostPublishingRights(authToken, streamId)
          .then((response: any) => {
            if (response) init();
            else alert('You are not allowed to join as a Host');
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') setIsNetworkError(true);
            handleApiError(error, LIVE_STREAM.ERROR.CONNECTION_ISSUE, dispatch, 'getHostPublishingRights', false);
          });
      } else init();
    } else if (
      authToken &&
      streamId &&
      role &&
      !streamInfo &&
      !instagramLoading &&
      instaFetched &&
      isInstagramConnectionError
    ) {
      handleApiErrorWithoutNetwork(
        'Instagram Connection Error',
        LIVE_STREAM.ERROR.REFRSH,
        dispatch,
        'instagramLoading',
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authToken, role, streamId, streamInfo, instagramLoading, instaFetched, isInstagramConnectionError]);

  /* Effects */
  useEffect(() => {
    if (streamInfo?.streaming_name && streamInfo?.stream_to?.includes('facebook')) {
      if (streamId && facebookStream) {
        if (facebookStream.token) {
          if (fbErrorInterval?.current) clearInterval(fbErrorInterval.current);
          if (fbInterval.current) clearInterval(fbInterval.current);
          setFacebookCommentsLoading(true);
          fbInterval.current = setInterval(getFacebookComments, facebookAPIPollingTime);
        }
        if (facebookStream.error) {
          if (fbInterval?.current) clearInterval(fbInterval.current);
          if (fbErrorInterval?.current) clearInterval(fbErrorInterval.current);
          fbErrorInterval.current = setInterval(() => dispatch(fetchFacebookToken(streamId)), facebookAPIPollingTime);
        }
      }
    }
    if (streamInfo?.streaming_name && streamInfo?.stream_to?.includes('instagram')) {
      if (streamId && instagramStream) {
        if (instagramStream.token) {
          if (instaErrorInterval?.current) clearInterval(instaErrorInterval.current);
          setInstagramCommentsLoading(true);
        }
        if (instagramStream.error) {
          if (instaErrorInterval?.current) clearInterval(instaErrorInterval.current);
          instaErrorInterval.current = setInterval(
            () => dispatch(fetchInstagramToken(streamId, streamInfo?.insta_auth_id)),
            facebookAPIPollingTime,
          );
        }
      }
    }
  }, [
    authToken,
    dispatch,
    streamInfo,
    facebookStream,
    instagramStream,
    instaInterval.current,
    instaErrorInterval.current,
    fbInterval.current,
    fbErrorInterval.current,
    streamId,
  ]);
  useEffect(() => {
    document.onkeydown = function (e: any) {
      const textInput = e.key || String.fromCharCode(e.keyCode);
      const targetName = e.target?.localName;
      if (textInput && textInput.length === 1 && targetName !== 'input' && targetName !== 'textarea') {
        scannedBarcode.current = scannedBarcode.current + textInput;
        addNewScannedProduct(scannedBarcode.current);
      }
    };
    return async () => {
      if (fbInterval?.current) clearInterval(fbInterval?.current);
      if (instaInterval?.current) clearInterval(instaInterval?.current);
      if (fbErrorInterval?.current) clearInterval(fbErrorInterval?.current);
      if (instaErrorInterval?.current) clearInterval(instaErrorInterval?.current);
      if(zegoExpEngine && roomId) zegoExpEngine.logoutRoom(roomId);
      if (zeegoLocalStreamRef?.current && zegoExpEngine && roomId) {
        try {
          if (viewerMultiplierIntervalRef?.current) clearInterval(viewerMultiplierIntervalRef.current);
          ZEEGO_CUSTOM_HANDLERS.unMount(zeegoLocalStreamRef?.current, streamId, roomId);
          document.onkeydown = null;
        } catch (err) {
          console.error(err);
        }
      }
    };
  }, [roomId, dispatch]);
  useEffect(() => {
    if (instagramStream?.redis_token) {
      const socket = new WebSocket(
        `wss://253ul4moik.execute-api.ap-south-1.amazonaws.com/production?id=${instagramStream?.redis_token}&type=lv_agent`,
      );

      // Connection opened
      socket.addEventListener('open', event => {
        socket.send('Connection established');
        setInstagramCommentsLoading(false);
      });

      // Listen for messages
      socket.addEventListener('message', event => {
        try {
          const eventData = JSON.parse(event?.data);
          console.log('Message from server ', eventData.message);
          const instaComment = eventData.message;
          const newComment = {
            name: instaComment?.username,
            comment: instaComment?.text,
            imgUrl: '',
            id: instaComment?.comment_id,
            source: instaComment?.user_id !== instagramStream?.page_id ? 'USER' : 'HOST',
            isReplied: false,
            from: 'INSTAGRAM',
          };
          if (newComment.id) {
            addInstagramComments([newComment]);
            const historyComments = [
              {
                comment: newComment.comment,
                uuid: newComment.id,
                user_type: 'viewer',
                user_name: newComment.username || 'Unknown',
                from: 'INSTAGRAM',
              },
            ];
            instaMessagesToPush.current = instaMessagesToPush.current.concat(historyComments ?? []);
          }
        } catch (err) {
          handleApiError(err, LIVE_STREAM.ERROR.INSTA_COMMENTS, dispatch, 'INSTAGRAM COMMENTS WSS');
        }
      });

      connection.current = socket;
    }
    return () => connection.current && connection.current?.close();
  }, [dispatch, instagramStream, instagramStream?.redis_token]);
  useEffect(() => {
    fetchViewerMultiplier();
    if (_.isEmpty(settingsFromState) && authToken) {
      dispatch(fetchStreamSettings());
    }
    setSettings(settingsFromState);
  }, [settingsFromState, authToken, dispatch]);
  useEffect(() => {
    products.length > 0 && streamId && role == 1 &&
      AuctionAPI.addAuctionTags(
        products.map(p => `gid://shopify/Product/${p.store_product_id}`),
        streamId,
      );
  }, [products, streamId]);
  useEffect(() => {
    const getPolls = async () => {
      try {
        setPollsLoading(true);
        const response = await LivelyApi.getPolls(authToken, streamId);
        setPolls(_.reverse(response?.data?.data));
        const activePoll = response?.data?.data?.find(poll => poll.status == 'active') || '';
        const newPoll = response?.data?.data?.find(poll => poll.status == 'new') || '';
        if (newPoll) {
          // (Don't Remove) Edge case: When poll is created but never started.
          await LivelyApi.startPoll(authToken, newPoll.poll_uuid);
          setPollsUpdated(true);
        }
        if (activePoll) {
          activePollRef.current = {poll_uuid: activePoll?.poll_uuid};
          const pollResultsResponse = await LivelyApi.getPollResults(authToken, activePoll.poll_uuid);
          setExpandedPolls({[activePoll.poll_uuid]: true});
          setActivePoll({...activePoll, pollResults: pollResultsResponse?.data?.data});
        }
        setPollsUpdated(false);
        return {polls: response?.data?.data, activePoll};
      } catch (error) {
        if (error?.response?.data?.message == 'No polls found.' && error?.response?.status == 400) {
          setPolls([]);
        }
      } finally {
        setPollsLoading(false);
      }
    };
    if (role === 2 && settings?.polling) getPolls();
  }, [pollsUpdated]);
  useEffect(() => {
    const fetchAndBroadcastPollResults = async () => {
      try {
        if (activePollRef?.current?.poll_uuid) {
          const pollResults = await LivelyApi.getPollResults(authToken, activePollRef?.current?.poll_uuid);
          if (pollResults?.data?.data) {
            setActivePoll(prev => ({...prev, pollResults: pollResults?.data?.data}));
            const broadCastPollResultsObj = {
              type: 'BROADCAST_POLL_RESULTS',
              data: {
                pollResults: pollResults?.data?.data,
              }
            };
            zegoExpEngine.sendBroadcastMessage(streamInfoRef.current.room_id, JSON.stringify(broadCastPollResultsObj));
          }
        }
      } catch (error) {
        console.error('Error fetching poll results:', error);
      }
    };
    if (role === 2 && settings?.polling) {
      if(pollIntervalRef?.current) clearInterval(pollIntervalRef?.current);
      if(activePollRef?.current?.poll_uuid) pollIntervalRef.current = setInterval(fetchAndBroadcastPollResults, pollResultsPollingTime);

      return () => {
        if (pollIntervalRef?.current) clearInterval(pollIntervalRef?.current);
      }
    }
  }, [activePollRef.current?.poll_uuid])

  return (
    <>
      {authToken ? (
        <>
          {isNetworkError ? (
            <NetworkErrorPage />
          ) : (
            <View style={{flexDirection: 'column', flex: 1, padding: 20, paddingHorizontal: 40}}>
              <View style={{position: 'absolute'}}>
                {showEditProductInfoModal && (
                  <ModalComponent 
                    visible={showEditProductInfoModal}
                    onVisibleChange={setShowEditProductInfoModal}
                    disableOutsideClick={true}
                    content={
                      <EditProductInfoModal
                        setIsBarCodeResultLoading={setIsBarCodeResultLoading}
                        appendLotNumber={streamInfoRef.current?.meta?.appendLotNumber}
                        scannedBarcode={scannedBarcode}
                        productScannedRef={productScannedRef}
                        addNewScannedProduct={addNewScannedProduct}
                        product={newProduct}
                        setNewProduct={setNewProduct}
                        highestLotNumber={highestLotNumber}
                        setShowEditProductInfoModal={setShowEditProductInfoModal}
                        addNewProduct={addNewProduct}
                        baseBidPrice={settings?.defaultStartingBidPercentage ? Math.abs(100 - settings?.defaultStartingBidPercentage) : 50}
                        bidIncrementer={settings?.defaultMinimumBidIncrement || streamInfoRef.current?.product_info[0]?.bid_incrementer}
                      />
                    }
                  />
                )}
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                <Back text="Back to Dashboard" url={LIVE_DASHBOARD_ROUTES.OVERVIEW_AUCTION()} />
                <View style={[styles.rowLayout]}>
                  <View style={[styles.rowLayout, {gap: 10, alignItems: 'center', width: 310}]}>
                    {userCount != 0 && (
                      <>
                        <Text style={[commonStyles.baseText]}>View Multiplier</Text>
                        <RadioGroupControl
                          disableBinding={true}
                          value={+viewerMultiplier}
                          options={[
                            {text: '1x', value: 1},
                            {text: '2x', value: 2},
                            {text: '3x', value: 3},
                            {text: '4x', value: 4},
                          ]}
                          onChange={viewerMultiplierChange}
                        />
                      </>
                    )}
                    {userCount == 0 && (
                      <View
                        style={{
                          flex: 1,
                          width: '100%',
                          height: '100%',
                          alignItems: 'flex-end',
                          justifyContent: 'center',
                          // backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          position: 'absolute',
                          zIndex: 2,
                          gap: 16,
                          top: 0,
                          left: 0,
                        }}>
                        <Image
                          style={{width: 30, height: 30}}
                          source={require('@/root/web/assets/images/preloader.svg')}
                        />
                      </View>
                    )}
                  </View>
                  {role === 2 && (
                    <View
                      style={{
                        width: 350,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 10,
                        marginLeft: 20,
                      }}>
                      <Text style={[commonStyles.baseText]}>Show customer view</Text>
                      <RadioGroupControl
                        value={showCustomerView}
                        onChange={value => setShowCustomerView(value)}
                        disableBinding
                        options={[
                          {text: 'No', value: false},
                          {text: 'Yes', value: true},
                        ]}
                      />
                    </View>
                  )}
                </View>
              </View>
              {role === 2 && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} color="#3f9fba" />
                  <Text style={[commonStyles.baseText, {fontSize: 12}]}>
                    Products will not jump in the Shoppable video if not shown during the live streaming.
                  </Text>
                </View>
              )}
              <View style={[styles.container, styles.rowLayout, {marginTop: 10}]} key={authToken}>
                <View style={styles.section}>
                  <View style={[{borderBottomWidth: 1, borderBottomColor: theme.INPUT_BORDER}, styles.sectionPadding]}>
                    <Text style={[commonStyles.baseText, styles.sectionLabel]}>Products</Text>
                  </View>
                  {isBarCodeResultLoading && (
                    <View
                      style={{
                        flex: 1,
                        width: '100%',
                        height: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        position: 'absolute',
                        zIndex: 2,
                        gap: 16,
                        top: 0,
                        left: 0,
                      }}>
                      <Image
                        style={{width: 30, height: 30}}
                        source={require('@/root/web/assets/images/preloader.svg')}
                      />
                      <Text style={{color: '#4F4F4F', fontFamily: theme.FONT_FAMILY}}>Scanning Items</Text>
                    </View>
                  )}
                  <View style={[styles.rowLayout, styles.sectionPadding, {gap: 10, paddingVertical: 16}]}>
                    <ShopifyItemObjectPicker
                      value={newProduct?.handle ?? ''}
                      itemType={'product'}
                      defaultOpen={false}
                      onChange={onSetNewProduct}
                      queryRunner={shopifyQueryRunner}
                    />
                    <Button
                      containerStyles={{paddingHorizontal: 20, paddingVertical: 6, borderRadius: 8, marginVertical: 3}}
                      onPress={addNewProduct}>
                      {' '}
                      Add{' '}
                    </Button>
                  </View>
                  <View style={[styles.sectionPadding, {flex: 1, overflow: 'auto', gap: 14, paddingTop: 0}]}>
                    {products?.map(product => (
                      <AuctionCard
                        key={product?.product_id}
                        product={product}
                        selectedProductId={selectedProductId}
                        handleChangeActiveProduct={handleChangeActiveProduct}
                        handleDeleteProduct={handleDeleteProduct}
                        endTimeStamp={endTimeStampRef.current}
                      />
                    ))}
                  </View>
                </View>

                <View
                  onLayout={(e: LayoutChangeEvent) =>
                    setVideoScreenDimensions({
                      width: e.nativeEvent.layout.width,
                      height: e.nativeEvent.layout.height,
                    })
                  }
                  style={[styles.section, {paddingHorizontal: 0, paddingVertical: 0}]}>
                  <View style={{flex: 1}}>
                    <div style={{width: '100%', height: '100%', overflow: 'hidden', borderWidth: 6}} id="local-video" />
                  </View>
                  {!cameraFlag && (
                    <Image
                      source={
                        streamInfo?.streaming_thumbnail
                          ? {uri: streamInfo?.streaming_thumbnail}
                          : require('@/root/web/assets/images/logo.png')
                      }
                      style={{width: '100%', height: '100%'}}
                    />
                  )}
                  <View
                    style={{
                      position: 'absolute',
                      padding: 10,
                      width: '100%',
                      height: '100%',
                      alignItems: 'flex-end',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 10,
                        flexWrap: 'wrap',
                      }}>
                      {streamInfo?.start_time && <Counter startTime={streamInfo?.start_time} />}
                      <View style={styles.streamCountWrapper}>
                        {userCount == 0 ? (
                          <Image
                            style={{width: 15, height: 15}}
                            source={require('@/root/web/assets/images/preloader-blue.svg')}
                          />
                        ) : (
                          <>
                            <Icon iconType={'MaterialCommunityIcons'} color={'#FFF'} name={'eye'} size={15} />
                            <Text style={[commonStyles.baseText, {fontSize: 15, color: '#FFF'}]}>
                              {userCount * viewerMultiplier}
                            </Text>
                          </>
                        )}
                      </View>
                    </View>
                    {showCustomerView && (
                      <CustomerViews
                        currentSelectedProduct={currentSelectedProduct}
                        requiredView={requiredCustomerView}
                        fromAuction={true}
                      />
                    )}
                  </View>

                  {role == 1 ? (
                    <View
                      style={[
                        styles.rowLayout,
                        {position: 'absolute', bottom: 0, justifyContent: 'center', width: '100%'},
                      ]}>
                      {!isLoggedIn ? (
                        <View
                          style={[
                            styles.rowLayout,
                            {
                              backgroundColor: '#FFFFFF',
                              width: '100%',
                              minHeight: '100px',
                              justifyContent: 'center',
                              alignItems: 'center',
                            },
                          ]}>
                          <ActivityIndicator size={20} />
                        </View>
                      ) : (
                        <View
                          style={[
                            styles.rowLayout,
                            {
                              position: 'absolute',
                              bottom: 0,
                              paddingBottom: 20,
                              justifyContent: 'center',
                              width: '100%',
                            },
                          ]}>
                          <Pressable style={styles.ctaButton} onPress={toggleMicrophone}>
                            <Icon color={'#ddd'} name={microPhoneFlag ? 'microphone' : 'microphone-off'} size={26} />
                          </Pressable>
                          <Pressable style={styles.ctaButton} onPress={toggleCamera}>
                            <Icon color={'#ddd'} name={cameraFlag ? 'camera' : 'camera-off'} size={26} />
                          </Pressable>
                          <Pressable
                            style={[styles.ctaButton, {backgroundColor: theme.ERROR_BACKGROUND}]}
                            onPress={handleEndCall}>
                            <Icon iconType={'MaterialIcons'} color={'#ddd'} name={'call-end'} size={26} />
                          </Pressable>
                        </View>
                      )}
                    </View>
                  ) : (
                    <>
                      {showCustomerView && <ReactionEmojis ref={emojiRef} dimensions={videoScreenDimensions} reactionsAnimation={settings?.defaultReactions ?? 'single'} />}
                      <View
                        style={[
                          styles.rowLayout,
                          {position: 'absolute', bottom: 0, paddingBottom: 20, justifyContent: 'center', width: '100%'},
                        ]}>
                        <Pressable style={styles.ctaButton} onPress={toggleMuteStream}>
                          <Icon color={'#ddd'} name={speakerFlag ? 'speaker' : 'speaker-off'} size={26} />
                        </Pressable>
                      </View>
                    </>
                  )}
                </View>

                <View style={{flex: 1, flexDirection: 'column', gap: 16}}>
                  <BidInfo
                    role={role}
                    isLoggedIn={isLoggedIn}
                    selectedProductId={selectedProductId!}
                    cancelBidding={cancelBidding}
                    closeBidding={closeBidding}
                    startBidding={startBidding}
                    endTimeStamp={endTimeStampRef.current}
                    biddingStartTimeStamp={biddingStartTimeStamp.current}
                    highestBid={highestBid}
                  />
                  <View style={styles.section}> 
                    <View
                      style={[
                        {
                          borderBottomWidth: 1,
                          borderBottomColor: theme.INPUT_BORDER,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          position: 'relative',
                        },
                        styles.sectionPadding,
                      ]}>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                          {showPolls && (
                            <Pressable onPress={() => setShowPolls(false)} style={{marginRight: 8}}>
                              <Icon
                                iconType={'AntDesign'}
                                name={'arrowleft'}
                                color='#000'
                                size={16}
                              />
                            </Pressable>
                          )}
                          <Text style={[commonStyles.baseText, styles.sectionLabel ]}>{showPolls ? 'Polls' : 'Chat'}</Text>
                        </View>
                      {mutedUsersModal && (
                        <ModalComponent
                          onVisibleChange={setMutedUsersModal}
                          visible={mutedUsersModal}
                          content={
                            <MutedUsersModal
                              streamId={streamId}
                              roomId={roomId}
                              mutedUsers={mutedUsers}
                              handleMuteUsers={handleMuteUsers}
                            />
                          }
                        />
                      )}
                      {role == 2 && (
                        <>
                          {showPolls ? 
                            !!polls?.length ? (
                              <Button disabled={!_.isEmpty(activePoll)} onPress={() => setShowCreatePoll(true)} color="CTA" size='SMALL'>
                                Create Poll
                              </Button>
                            ) : null : settings?.shadowComments ? (
                              <Pressable onPress={() => setShowCommentsAction(prev => !prev)}>
                                <ApptileWebIcon name={'menu-vertical'} size={20} />
                              </Pressable>
                            ) : null
                          }
                          
                          {showCommentsAction && (
                            <View style={[styles.popOverContainer, {padding: 0, top: 10, right: 50}]}>
                              <Pressable
                                style={{paddingHorizontal: 10, paddingVertical: 6, width: 'fit-content'}}
                                onPress={() => setMutedUsersModal(true)}>
                                <Text style={{fontSize: 12}}>Show Muted Users</Text>
                              </Pressable>
                            </View>
                          )}
                        </>
                      )}
                    </View>

                    <View style={[styles.sectionPadding, {flex: 1}]}>
                      {showPolls && (
                        <PollView
                          showCreatePoll={showCreatePoll}
                          setShowCreatePoll={setShowCreatePoll}
                          activePollRef={activePollRef}
                          polls={polls}
                          setPolls={setPolls}
                          activePoll={activePoll}
                          setActivePoll={setActivePoll}
                          expandedPolls={expandedPolls}
                          setExpandedPolls={setExpandedPolls}
                          pollsLoading={pollsLoading}
                          setPollsUpdated={setPollsUpdated}
                          roomId={roomId}
                          authToken={authToken}
                          streamId={streamId}
                          setShowPolls={setShowPolls}
                          viewerMultiplier={viewerMultiplier}
                        />
                      )}
                      {!showPolls && availableCommentPlatforms.length > 0 && (
                        <>
                          <TabsLite
                            onTabChange={tab => {
                              setActiveCommentTab(tab);
                              if (tab === CommentPlatform.FACEBOOK) {
                                setHasNewFacebookComments(false);
                              }
                              if (tab === CommentPlatform.INSTAGRAM) {
                                setHasNewInstagramComments(false);
                              }
                            }}
                            tabs={availableCommentPlatforms.map(platform => ({
                              title: platform,
                              disableScroll: true,
                              component: (
                                <CommentsView
                                  facebookCommentsLoading={facebookCommentsLoading}
                                  facebookComments={facebookComments}
                                  instagramCommentsLoading={instagramCommentsLoading}
                                  instagramComments={instagramComments}
                                  addInstagramComments={addInstagramComments}
                                  comments={comments}
                                  commentsHistoryUpdate={commentsHistoryUpdate}
                                  streamInfo={streamInfo}
                                  addComment={addComment}
                                  appMessagesToPush={appMessagesToPush}
                                  streamId={streamId}
                                  commentPlatformType={platform}
                                  setComments={setComments}
                                  getFacebookComments={getFacebookComments}
                                  roomId={roomId}
                                  role={role}
                                  allComments={allComments}
                                  isShadowCommentsEnabled={settings?.shadowComments}
                                  mutedUsers={mutedUsers}
                                  muteComment={handleMuteUsers}
                                  setShowPolls={setShowPolls}
                                  activePoll={activePoll}
                                  isPollingEnabled={settings?.polling}
                                />
                              ),
                            }))}
                            noOfLines={1}
                            contentWrapperStyles={{gap: 6}}
                            activeVariant="FILLED-PILL"
                            inactiveVariant="FILLED-PILL"
                            activeColor="PRIMARY"
                            inactiveColor="NEW_TAB"
                            activeOpaque={true}
                            rootStyles={{flex: 1}}
                            size="SMALL"
                          />
                          {hasNewFacebookComments && activeCommentTab !== CommentPlatform.FACEBOOK && (
                            <View style={styles.facebookCount} />
                          )}
                          {hasNewInstagramComments && activeCommentTab !== CommentPlatform.INSTAGRAM && (
                            <View style={styles.facebookCount} />
                          )}
                        </>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            </View>
          )}
        </>
      ) : (
        <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      )}
    </>
  );
};

export default Auction;

const styles = StyleSheet.create({
  note: {
    backgroundColor: '#ecf5f8',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 6,
    gap: 10,
    alignSelf: 'baseline',
    marginTop: 10,
    width: '100%',
    padding: 10,
    borderColor: '#3f9fba',
    borderWidth: 1,
  },
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
    justifyContent: 'space-between',
    gap: 25,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {
    width: '95%',
    marginBottom: 50,
    paddingVertical: 40,
  },
  infoText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    color: theme.TEXT_COLOR,
    lineHeight: 16,
    fontWeight: '500',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  streamsList: {
    marginVertical: 20,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  emptyStream: {
    height: 175,
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  section: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#CDCDCD',
  },
  sectionPadding: {
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
  commentSection: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    overflow: 'hidden',
    height: '49%',
  },
  commentsSection: {
    width: '30%',
    margin: 5,
    gap: 10,
    backgroundColor: '#fff',
    paddingVertical: 24,
    borderRadius: 8,
    paddingHorizontal: 20,
  },
  commentBox: {
    marginLeft: 6,
    flexDirection: 'column',
    flex: 1,
  },
  commentText: {
    flexWrap: 'wrap',
    width: '100%',
  },
  popOverContainer: {
    position: 'absolute',
    top: 20,
    right: 15,
    padding: 5,
    borderRadius: 10,
    borderWidth: 1,
    backgroundColor: 'white',
    borderColor: theme.INPUT_BORDER,
    zIndex: 10,
    elevation: 10,
  },
  ctaButton: {
    width: 45,
    height: 45,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
    borderWidth: 0.1,
    borderColor: '#aaa',
    backgroundColor: '#0005',
  },
  streamCountWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 5,
    borderRadius: 5,
    backgroundColor: '#0005',
    gap: 5,
  },
  labelText: {
    textAlign: 'center',
    width: 170,
    fontSize: 12,
    color: '#000',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  facebookCount: {
    borderRadius: 10,
    marginBottom: 10,
    position: 'absolute',
    top: 50,
    fontSize: 9,
    left: 146,
    color: '#FFF', 
    backgroundColor: 'red',
    width: 15,
    height: 15,
  },

});