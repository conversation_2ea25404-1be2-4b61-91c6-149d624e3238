import { ILiveStreamProductInfo } from '@/root/web/api/LivelyApiTypes';
import TextElement from '@/root/web/components-v2/base/TextElement';
import { Button } from '@/root/web/icons/_ApptileWebIcons';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import theme from '@/root/web/styles-v2/theme';
import { Icon } from 'apptile-core';
import React from 'react'
import {Image, Pressable, StyleSheet, Text, View} from 'react-native'

interface AuctionCardProps {
  product: ILiveStreamProductInfo & { title: string, price: string, currencyCode: string, totalInventory: number };
  selectedProductId?: string | null;
  handleChangeActiveProduct: (product: ILiveStreamProductInfo) => void;
  handleDeleteProduct: (product: ILiveStreamProductInfo) => void;
  endTimeStamp: number;
}

const AuctionCard: React.FC<AuctionCardProps> = ({
  product,
  selectedProductId,
  handleChangeActiveProduct,
  handleDeleteProduct,
  endTimeStamp
}) => {

  return (
    <View style={styles.cardContainer}>

      {product.totalInventory <= 0 && (
        <View style={styles.soldOutWrapper}>
          <View style={{flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end', padding: 12}}>
            <Icon
              iconType={'MaterialIcons'}
              name={'circle'}
              size={11}
              color={theme.ERROR}
              style={{marginRight: 4}}
            />
            <Text style={[commonStyles.baseText, {color: '#000', fontSize: 12, fontWeight: '500'}]}>Sold out</Text>
          </View>
        </View>
      )}

      <View style={{position: 'relative', height: '100%', width: 70, justifyContent: 'center', alignItems: 'center', marginRight: 16}}>
        <Image
          source={{ uri: product.product_thumbnail }}
          style={styles.image}
        />
        <View style={{position: 'absolute', bottom: 0, backgroundColor: 'white', paddingHorizontal: 6, borderRadius: 4, borderWidth: 1, borderColor: theme.CONTROL_BORDER}}>
          <TextElement style={{fontSize: 11, lineHeight: 16}} fontWeight='500'>
            #{product.meta.lot_number}
          </TextElement>
        </View>
      </View>
    
      {/* Product Info and Actions */}
      <View style={styles.infoContainer}>
        <View style={styles.infoHeadingContainer}>
          <Text numberOfLines={2} style={[commonStyles.baseText, styles.title]}>{product.title}</Text>
          {selectedProductId != product.product_id && product.totalInventory > 0 && (
            <Pressable onPress={() => handleDeleteProduct(product)}>
              <Icon name="delete-outline" size={20} color="#595959" iconType="MaterialCommunityIcons" />
            </Pressable>
          )}
        </View>
        <View style={{gap: 8}}>
          <Text style={[commonStyles.baseText, styles.startingBid]}>Starting Bid: {product.base_bid_price?.toFixed(2)} {product.currencyCode}</Text>
          <Text style={[commonStyles.baseText, styles.price]}>Price: {(Number(product.price))} {product.currencyCode}</Text>
        </View>
        {product.totalInventory > 0 && (
          <View style={{width: '100%'}}>
            {selectedProductId == product.product_id ? (
              <View style={{flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end'}}>
                <Icon
                  iconType={'MaterialIcons'}
                  name={'circle'}
                  size={11}
                  color={theme.SUCCESS_BACKGROUND}
                  style={{marginRight: 4}}
                />
                <Text style={[commonStyles.baseText, {color: '#000', fontSize: 12, fontWeight: '500'}]}>Live</Text>
              </View>
            ) : (
              <Pressable 
                style={[styles.commonButtonContainer, endTimeStamp > 0 && {borderColor: theme.TILE_BORDER_COLOR}]}
                onPress={() => handleChangeActiveProduct(product)}
                disabled={selectedProductId == product.product_id || endTimeStamp > 0}
              >
                <Text style={[commonStyles.baseText, {
                  color: endTimeStamp > 0 ? theme.TILE_BORDER_COLOR : theme.PRIMARY_COLOR_DARK,
                  fontSize: 11
                  }, ]}
                  >Show Product</Text>
              </Pressable>
            )}
          </View>
        )}
      </View>
    </View>
  )
}

export default AuctionCard

const styles = StyleSheet.create({
  commonButtonContainer: {
    borderWidth: 1,
    borderRadius: 24,
    paddingVertical: 2,
    paddingHorizontal: 12,
    borderColor: theme.PRIMARY_COLOR_DARK,
    alignSelf: 'flex-end',
  },
  cardContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    padding: 12,
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  infoContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 8
  },
  title: {
    fontSize: 13,
    fontWeight: '600',
    color: '#000',
    lineHeight: 16,
  },
  startingBid: {
    fontSize: 13,
    fontWeight: '500',
    color: '#000',
  },
  price: {
    fontSize: 12,
    fontWeight: '400',
    color: '#6B6B6B',
  },
  actionsContainer: {
    position: 'absolute',
    top: 5,
    right: 5,
    zIndex: 1000,
  },
  descContainer: {
    flex: 1, 
    width: '100%', 
    flexDirection: 'row', 
    alignItems: 'flex-end', 
    justifyContent: 'space-between', 
    gap: 8,
    marginTop: 8
  },
  infoHeadingContainer: {
    width: '100%', 
    flexDirection: 'row', 
    alignItems: 'center', 
    justifyContent: 'space-between', 
    gap: 8
  },
  soldOutWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.90)',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    zIndex: 1000,
    borderRadius: 8,
  },
});