import React, {useEffect, useState, useRef, useCallback, useMemo} from 'react';
import {View, StyleSheet, Text, Image, Pressable, ActivityIndicator} from 'react-native';
import {useNavigate, useParams} from 'react-router';
import _, {debounce} from 'lodash';
import theme from '@/root/web/styles-v2/theme';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '../../../../api/LivelyApi';
import {initZego, zegoExpEngine} from '../../zegoEngine';
import {Icon, LocalStorage as localStorage} from 'apptile-core';
import Button from '../../../../components-v2/base/Button';
import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {fetchFacebookToken, fetchInstagramPages, fetchInstagramToken} from '../../../../actions/liveSellingActions';
import {ShopifyItemObjectPicker} from '../../../../integrations/shopify/components/ShopifyItemPicker';
import commonStyles from '../../../../styles-v2/commonStyles';
import {makeToast, removeToast} from '../../../../actions/toastActions';
import Counter from '../shared/timeCounter';
import {ProductCard} from './productCard';
import {shopifyProductTransformer} from '../../utils/transformers';
import RadioGroupControl from '../../../../components/controls/RadioGroupControl';
import {CustomerViews} from '../shared/customerViews';
import CommentsView from '../shared/commentsView';
import TabsLite from '../../../../components-v2/composite/TabsLite';
import apolloQueryRunner from '@/root/app/plugins/datasource/ApolloWrapper/model';
import LiveStreamHeader from '../../dashboardV2/LiveStreamHeader';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {TransformGetProductsPaginatedQuery} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {v4 as uuidv4} from 'uuid';
import {handleApiError, handleApiErrorWithoutNetwork} from '../../shared/CommonError';
import {LIVE_STREAM} from '../../../../../app/common/utils/apiErrorMessages/specificFeature';
import {COMMON_ERROR_MESSAGE} from '../../../../../app/common/utils/apiErrorMessages/generalMessages';
import NetworkErrorPage from '../../shared/NetworkErrorPage';
import { ApptileWebIcon } from '@/root/web/icons/ApptileWebIcon.web';
import ModalComponent from '../../../../components-v2/base/Modal';
import MutedUsersModal from '../../modals/mutedUsersModal';
import ReactionEmojis from '@/root/app/plugins/widgets/LivelyLiveSellingWidgetV2/shared/ReactionEmojis';
import PollView from './pollView';
import AddToCartReactionCards from '@/root/app/plugins/widgets/LivelyLiveSellingWidgetV2/shared/AddToCartReactionCards';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';
import Back from '../../shared/Back';
import { LIVE_DASHBOARD_ROUTES } from '../../utils/liveDashboardRoutes';

export enum CommentPlatform {
  FACEBOOK = 'Facebook',
  INSTAGRAM = 'Instagram',
  APP = 'App',
  ALL = 'All',
}

const LiveStream = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const params = useParams();
  let {streamId = '', role} = params;
  role = parseInt(role) ?? 2;
  const livelyUser = useSelector(state => state.liveSelling.auth.livelyUser);
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const instagramLoading = useSelector(state => state.liveSelling.instagramChannelLoading);
  const instagramChannels = useSelector(state => state.liveSelling.instagramChannelAuths);
  const isInstagramConnectionError = useSelector(state => state.liveSelling.isInstagramConnectionNetworkError);
  const [instaFetched, setInstaFetched] = useState(false);

  console.log({authToken, streamId, role});

  const [streamInfo, setStreamInfo] = useState<{} | null>(null);
  const [roomId, setRoomId] = useState('');
  const [callToken, setCallToken] = useState(null);
  const [allComments, setAllComments] = useState([]);
  const [comments, setComments] = useState([]);
  const [facebookComments, setFacebookComments] = useState([]);
  // const [showFBComments, setShowFBComments] = useState(false);
  const [facebookCommentsLoading, setFacebookCommentsLoading] = useState(false);
  const [instagramComments, setInstagramComments] = useState([]);
  // const [showFBComments, setShowFBComments] = useState(false);
  const [instagramCommentsLoading, setInstagramCommentsLoading] = useState(false);
  const [userCount, setUserCount] = useState(0);
  const [products, setProducts] = useState<any[]>([]);
  const [microPhoneFlag, setMicrophoneFlag] = useState(false);
  const [cameraFlag, setcameraFlag] = useState(role == 2);
  const [speakerFlag, setSpeakerFlag] = useState(true);
  const zeegoLocalStreamRef = useRef(null);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [newProduct, setNewProduct] = useState('');
  const appMessagesToPush = useRef<any[]>([]);
  const [showCustomerView, setShowCustomerView] = useState(false);
  const [requiredCustomerView, setRequiredCustomerView] = useState(1);
  const [hasNewFacebookComments, setHasNewFacebookComments] = useState(false);
  const [hasNewInstagramComments, setHasNewInstagramComments] = useState(false);
  const [activeCommentTab, setActiveCommentTab] = useState(CommentPlatform.ALL);
  const [viewerMultiplier, setViewerMultiplier] = useState(1);
  const viewerMultiplierRef = useRef(null);
  const viewerMultiplierIntervalRef = useRef(null);
  const [availableCommentPlatforms, setAvailableCommentPlatforms] = useState<CommentPlatform[] | []>([
    CommentPlatform.APP,
  ]);
  const [isBarCodeResultLoading, setIsBarCodeResultLoading] = useState(false);

  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [videoScreenDimensions, setVideoScreenDimensions] = useState({width: 0, height: 0});

  const fbInterval = useRef(null);
  const fbErrorInterval = useRef(null);
  const fbAfterCursor = useRef(null);
  const fbMessagesToPush = useRef<any[]>([]);

  const instaInterval = useRef(null);
  const instaErrorInterval = useRef(null);
  const instaAfterCursor = useRef(0);
  const instaMessagesToPush = useRef<any[]>([]);
  const connection = useRef(null);

  const facebookAPIPollingTime = 2500;
  const pollResultsPollingTime = 3000;
  const ShopifyDSModel = null;
  const queryRunnerRef = useRef(null);
  const emojiRef = useRef<any>(null);
  const cardRef = useRef<any>(null);

  const facebookStream = useSelector(state => state.liveSelling.facebookTokens[streamId]);
  const instagramStream = useSelector(state => state.liveSelling.instagramTokens[streamId]);
  const [isNetworkError, setIsNetworkError] = useState(false);

  const [isShadowCommentsEnabled, setIsShadowCommentsEnabled] = useState(false);
  const [mutedUsers, setMutedUsers] = useState<any[]>([]);
  const [showCommentsAction, setShowCommentsAction] = useState(false);
  const [mutedUsersModal, setMutedUsersModal] = useState(false);

  // Polls
  const [showCreatePoll, setShowCreatePoll] = useState(false);
  const [showPolls, setShowPolls] = useState(false);
  const [polls, setPolls] = useState([]);
  const [activePoll, setActivePoll] = useState({});
  const [expandedPolls, setExpandedPolls] = useState({});
  const [pollsLoading, setPollsLoading] = useState(false);
  const [pollsUpdated, setPollsUpdated] = useState(false);
  const [isPollingEnabled, setIsPollingEnabled] = useState(false);
  const [isInstagramEnabled, setIsInstagramEnabled] = useState(false);
  const activePollRef = useRef<any>(null);
  const pollIntervalRef = useRef<any>(null);

  // const [potentialRevenue, setPotentialRevenue] = useState(0);
  const [currentRevenue, setCurrentRevenue] = useState('0');
  async function initiateMeta() {
    const output = {};
    const streamInfo = await LivelyApi.getLiveStreamInfoByID(authToken, streamId);
    try {
      let productMetadata = [];
      const productIds = streamInfo?.data?.data?.product_info.map(e => 'gid://shopify/Product/' + e.store_product_id);
      const queryResponse = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT_BY_IDS, {
        productIds,
      });

      productMetadata = queryResponse?.data?.nodes?.map(product => ({
        ...shopifyProductTransformer(product),
        store_product_id: product?.id.replace('gid://shopify/Product/', ''),
      }));
      const productMetaMap = Object.keys(streamInfo?.meta || {}).filter(e => `${parseInt(e)}` == e);
      for (const product of productMetadata || []) {
        const pid = product.id.split('/').pop();
        if (!productMetaMap.includes(pid)) {
          output[pid] = output[pid] || {};
          for (const variant of product.variants || []) {
            const vid = variant.id.split('/').pop();
            output[pid][vid] = {
              initialInventory: variant.inventoryQuantity ?? 0,
              finalInventory: null,
            };
          }
        }
      }
      await LivelyApi.updateStreamMeta(authToken, streamId, output).then((response: any) => {
        if (response?.data?.data) {
          setStreamInfo(response?.data?.data);
        }
      });
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'onSetProducts');
    }
  }
  // async function calculatePotentialRevenue(meta, productInfo) {
  //   let totalPotentialRevenue = 0;
  //   const productMap = Object.fromEntries(productInfo.map(p => [p.store_product_id, p]));
  //   for (const storeProductId in meta) {
  //     let product = productMap[storeProductId];
  //     // If product info is not available, fetch it
  //     if (!product) {
  //       try {
  //         product = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
  //           productId: `gid://shopify/Product/${storeProductId}`,
  //         });
  //         product = shopifyProductTransformer(product.data.product);
  //         if (!product) continue;
  //       } catch (err) {
  //         console.error(`Failed to fetch product info for ${storeProductId}`, err);
  //         continue;
  //       }
  //     }
  //     const variantInventoryMap = meta[storeProductId];
  //     for (const variantId in variantInventoryMap) {
  //       const variantMeta = variantInventoryMap[variantId];
  //       const {initialInventory = 0, finalInventory = 0} = variantMeta || {};
  //       let price = 0;
  //       const variant = product.variants?.find(v => v.id?.split('/').pop() === variantId);
  //       if (variant) {
  //         price = parseFloat(variant.price ?? '0');
  //       }
  //       const quantity = Math.max(0, initialInventory - finalInventory);
  //       totalPotentialRevenue += quantity * price;
  //     }
  //   }

  //   setPotentialRevenue(totalPotentialRevenue);
  //   console.log(totalPotentialRevenue);
  //   return totalPotentialRevenue;
  // }
  async function calculateCurrentRevenue(meta: any, productInfo: any[]): number {
    let totalRevenue = 0;
    const productMap = Object.fromEntries(productInfo.map(p => [p.store_product_id, p]));
    const productIds = Object.keys(meta || {}).filter(e => `${parseInt(e)}` == e);
    for (const storeProductId in productIds) {
      let product = productMap[storeProductId];
      if (!product) {
        try {
          product = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
            productId: `gid://shopify/Product/${storeProductId}`,
          });
          product = shopifyProductTransformer(product.data.product);
          if (!product) continue;
        } catch (err) {
          console.error(`Failed to fetch product info for ${storeProductId}`, err);
          continue;
        }
      }
      const variantInventoryMap = meta[storeProductId];
      for (const variantId in variantInventoryMap) {
        const variantMeta = variantInventoryMap[variantId];
        const {initialInventory = 0, finalInventory} = variantMeta || {};
        const variant = product.variants?.find(v => v.id?.split('/').pop() === variantId);
        if (!variant) continue;
        const currentInventory = finalInventory ?? variant.inventoryQuantity ?? 0;
        const sold = Math.max(0, initialInventory - currentInventory);
        if (sold > 0) {
          const price = parseFloat(variant.price ?? '0');
          totalRevenue += sold * price;
        }
      }
    }
    setCurrentRevenue(totalRevenue.toFixed(2));
    return totalRevenue;
  }

  const triggerEmoji = (reactionUrl: string, userName: string) => {
    emojiRef.current?.triggerAddEmoji(reactionUrl, userName);
  };

  const triggerAddCard = (productTitle: string, userName: string) => {
    cardRef.current?.triggerAddCard(productTitle, userName);
  };

  const getScreenDimensions = (e: any) => {
    const {width, height} = e.nativeEvent.layout;
    setVideoScreenDimensions({width, height});
  };

  const updateViewMultiplier = useCallback(
    _.debounce(count => {
      viewerMultiplierRef.current = count;
      setViewerMultiplier(count);
    }, 300),
    [],
  );

  const fetchViewerMultiplier = async () => {
    const response = await LivelyApi.getCompanyInfo(authToken);
    if (response?.data?.data?.meta?.default_multiplier) {
      updateViewMultiplier(response?.data?.data?.meta?.default_multiplier);
    }
  };

  const checkIfNewFeaturesEnabled = async () => {
    const response = await LivelyApi.getCompanyInfo(authToken);
    if (response?.data?.data?.meta?.shadowComments) {
      setIsShadowCommentsEnabled(true);
    }
    if (response?.data?.data?.meta?.polling) {
      setIsPollingEnabled(true);
    }
  };

  const onSetProducts = async (products: any[]) => {
    if (!products || products.length === 0) return;
    const productIds = products.map(e => 'gid://shopify/Product/' + e.store_product_id);
    let productMetadata = [];

    try {
      const queryResponse = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT_BY_IDS, {
        productIds,
      });
      productMetadata = queryResponse?.data?.nodes?.map(product => {
        return {
          ...shopifyProductTransformer(product),
          store_product_id: product?.id.replace('gid://shopify/Product/', ''),
        };
      });
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'onSetProducts');
    }

    //merge the data from productMetadata into products using store_product_id as the key
    const enhancedProducts = products.map(product => {
      const productMetadataItem = productMetadata?.find(e => e.store_product_id == product.store_product_id);
      return {...product, ...productMetadataItem};
    });
    setProducts(enhancedProducts);
  };
  const productsRef = useRef(products);
  const streamRef = useRef(streamInfo);
  // Always update the ref when products state changes
  useEffect(() => {
    streamRef.current = streamInfo;
  }, [streamInfo]);
  // Always update the ref when products state changes
  useEffect(() => {
    productsRef.current = products;
  }, [products]);

  useEffect(() => {
    let isCancelled = false;

    const poll = async () => {
      console.log('Polling the product APIs at', new Date().toISOString());

      const currentProducts = productsRef.current;
      const currentStreamInfo = streamRef.current;
      if (!currentProducts || currentProducts.length === 0) {
        if (!isCancelled) setTimeout(poll, 3000);
        return;
      }

      const productIds = currentProducts.map(e => 'gid://shopify/Product/' + e.store_product_id);
      let productMetadata = [];

      try {
        const queryResponse = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT_BY_IDS, {
          productIds,
        });

        productMetadata = queryResponse?.data?.nodes?.map(product => ({
          ...shopifyProductTransformer(product),
          store_product_id: product?.id.replace('gid://shopify/Product/', ''),
        }));
      } catch (error) {
        handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'onSetProducts');
      }

      const updatedMeta: Record<string, Record<string, number>> = {};

      for (const updatedProduct of productMetadata) {
        const productId = updatedProduct.store_product_id;
        const matchingOldProduct = currentProducts.find(p => p.store_product_id === productId);
        if (!matchingOldProduct) continue;

        let hasIncreased = false;
        const variantMeta: any = {};

        for (const updatedVariant of updatedProduct.variants || []) {
          const variantId = updatedVariant.id.split('/').pop();
          const newQty = updatedVariant.inventoryQuantity;

          const oldVariant = matchingOldProduct.variants?.find(v => v.id.split('/').pop() === variantId);
          const oldQty = oldVariant?.inventoryQuantity ?? 0;
          const streamMetaQty = currentStreamInfo?.meta?.[productId]?.[variantId]?.initialInventory ?? 0;
          if (newQty > oldQty) {
            hasIncreased = true;
            variantMeta[variantId] = {initialInventory: streamMetaQty + (newQty - oldQty), finalInventory: null};
          } else {
            variantMeta[variantId] = {initialInventory: streamMetaQty, finalInventory: null};
          }
        }
        if (hasIncreased) {
          updatedMeta[productId] = variantMeta;
        }
      }
      if (Object.keys(updatedMeta).length > 0) {
        try {
          await LivelyApi.updateStreamMeta(authToken, streamId, updatedMeta); // Implement this call
        } catch (err) {
          console.error('Failed to update stream meta:', err);
        }
      }
      // Update product state
      const enhancedProducts = currentProducts.map(product => {
        const meta = productMetadata?.find(e => e.store_product_id === product.store_product_id);
        return {...product, ...meta};
      });
      if (!!currentStreamInfo?.meta) {
        // calculatePotentialRevenue(currentStreamInfo?.meta, enhancedProducts);
        calculateCurrentRevenue(currentStreamInfo?.meta, enhancedProducts);
      }
      setProducts(enhancedProducts);
      if (!isCancelled) setTimeout(poll, 3000);
    };
    poll(); // start polling
    return () => {
      isCancelled = true;
    };
  }, [authToken, dispatch, streamId]);

  const onSetNewProduct = (item: any) => {
    if (!_.isEmpty(item) && !item.onlineStoreUrl) {
      dispatch(
        makeToast({
          content: 'Product is not available on online store sales channel',
          appearances: 'error',
          duration: 2000,
        }),
      );
      return;
    }
    setNewProduct(item);
  };

  const addComment = (newComment: any) => {
    setComments(prevComments => prevComments.concat(newComment).slice(prevComments.length - 200));
    setAllComments(prevComments => prevComments.concat(newComment).slice(prevComments.length - 400));
  };

  const addFacebookComments = (newCommentsRaw: string[]) => {
    setFacebookCommentsLoading(false);
    if (newCommentsRaw.length !== 0) setHasNewFacebookComments(true);
    setFacebookComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 200);
    });
    setAllComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 400);
    });
  };

  const addInstagramComments = (newCommentsRaw: any[]) => {
    setInstagramCommentsLoading(false);
    if (newCommentsRaw.length !== 0) setHasNewInstagramComments(true);
    setInstagramComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 200);
    });
    setAllComments(prevComments => {
      const commentIds = prevComments.map(e => e.id);
      const newComments = newCommentsRaw.filter(e => !commentIds.includes(e.id));
      return prevComments.concat(newComments).slice(prevComments.length - 400);
    });
  };

  const handleChangeActiveProduct = (item: any) => {
    const obj = {
      type: 'SHOW_PRODUCT_EVENT',
      id: item?.product_id,
    };
    const infoObj = {
      data: {
        product_id: item?.product_id,
        store_product_id: item?.store_product_id,
      },
      event: 'PRODUCT_CHANGE',
    };
    if (role == 1) {
      setSelectedProductId(item?.product_id);
      LivelyApi.changeCurrentProduct(authToken, streamId, item?.product_id);
      zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
    } else if (role == 2) {
      zegoExpEngine.sendCustomCommand(roomId, JSON.stringify(obj), [streamInfo?.user_id]);
    }
  };
  const handleDeleteProduct = (item: any) => {
    const obj = {
      type: 'DELETE_PRODUCT_EVENT',
      id: item?.product_id,
    };
    const infoObj = {
      data: {
        product_id: item?.product_id,
        store_product_id: item?.store_product_id,
      },
      event: 'DELETE_PRODUCT',
    };
    if (role == 1) {
      zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
      LivelyApi.removeProductFromStream(authToken, streamId, [infoObj.data])
        .then(async () => {
          const productData = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
            productId: `gid://shopify/Product/${infoObj.data.store_product_id}`,
          });
          let productMetaData = {
            [infoObj.data.store_product_id]: Object.fromEntries(
              shopifyProductTransformer(productData.data.product)?.variants.map((variant: any) => [
                variant.id.split('/').pop(),
                {
                  initialInventory:
                    streamInfo?.meta?.[infoObj.data.store_product_id]?.[variant.id.split('/').pop()].initialInventory,
                  finalInventory: variant?.inventoryQuantity,
                },
              ]),
            ),
          };
          LivelyApi.updateStreamMeta(authToken, streamId, productMetaData).then(() => {
            LivelyApi.getLiveStreamInfoByID(authToken, streamId)
              .then((streamResponse: any) => {
                const newStreamInfo = streamResponse?.data?.data;
                let newProductsData = newStreamInfo?.product_info;
                setStreamInfo(newStreamInfo);
                onSetProducts(newProductsData);
              })
              .catch((error: any) => {
                handleApiError(
                  error,
                  COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                  dispatch,
                  'handleDeleteProduct - getLiveStreamInfoByID',
                );
              });
            dispatch(
              makeToast({
                content: LIVE_STREAM.SUCCESS.PRODUCT_REMOVED,
                appearances: 'success',
                duration: 2000,
              }),
            );
          });
        })
        .catch((error: any) => {
          handleApiError(
            error,
            COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
            dispatch,
            'handleDeleteProduct - removeProductFromStream',
          );
        });
    } else if (role == 2) {
      zegoExpEngine.sendCustomCommand(roomId, JSON.stringify(obj), [streamInfo?.user_id]);
    }
  };

  const addNewProduct = () => {
    if (newProduct) {
      if (products.find(e => e.store_product_id == newProduct?.id.split('/').pop())) {
        handleApiErrorWithoutNetwork('product exist', LIVE_STREAM.ERROR.PRODUCT_EXIST, dispatch, 'addNewProduct');
        onSetNewProduct('');
        return;
      } else {
        const obj = {
          type: 'ADD_NEW_PRODUCT',
          data: {
            store_product_id: newProduct?.id.split('/').pop(),
            product_url: newProduct?.onlineStoreUrl ?? 'https://apptile.io/',
            product_name: newProduct?.title,
            product_thumbnail: newProduct?.featuredImage,
          },
        };

        onSetNewProduct('');
        if (role == 2) {
          zegoExpEngine.sendCustomCommand(roomId, JSON.stringify(obj), [streamInfo?.user_id]);
          setTimeout(() => {
            LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              if (newProductsData) {
                onSetProducts(newProductsData);
                dispatch(
                  makeToast({
                    content: LIVE_STREAM.SUCCESS.PRODUCT_ADDED,
                    appearances: 'success',
                    duration: 2000,
                  }),
                );
              } else {
                handleApiErrorWithoutNetwork(
                  'undefined newProductsData',
                  COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR,
                  dispatch,
                  'addNewProduct',
                );
              }
            });
          }, 1500);
        } else if (role == 1) {
          LivelyApi.addNewProductToStream(authToken, streamId, [obj.data]).then(async () => {
            const productData = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
              productId: `gid://shopify/Product/${obj.data.store_product_id}`,
            });
            let productMetaData = {
              [obj.data.store_product_id]: Object.fromEntries(
                shopifyProductTransformer(productData.data.product)?.variants.map((variant: any) => [
                  variant.id.split('/').pop(),
                  {
                    initialInventory: streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]
                      ? streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]?.initialInventory -
                        streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]?.finalInventory +
                        variant.inventoryQuantity
                      : variant.inventoryQuantity,
                    finalInventory: null,
                  },
                ]),
              ),
            };
            LivelyApi.updateStreamMeta(authToken, streamId, productMetaData).then(() => {
              LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                const newStreamInfo = streamResponse?.data?.data;
                setStreamInfo(newStreamInfo);
                let newProductsData = newStreamInfo?.product_info;
                if (newProductsData) {
                  onSetProducts(newProductsData);
                  dispatch(
                    makeToast({
                      content: LIVE_STREAM.SUCCESS.PRODUCT_ADDED,
                      appearances: 'success',
                      duration: 2000,
                    }),
                  );
                } else {
                  handleApiErrorWithoutNetwork(
                    'undefined newProductsData',
                    COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR,
                    dispatch,
                    'addNewProduct',
                  );
                }
              });
            });
          });
        }
      }
    }
  };
  const initLively = () => {
    console.log('lively initiated');
    initZego();
    LivelyApi.getLiveStreamInfoByID(authToken, streamId)
      .then((streamResponse: any) => {
        const newStreamInfo = streamResponse?.data?.data;
        if (newStreamInfo.stream_to.includes('facebook')) {
          dispatch(fetchFacebookToken(streamId));
          setAvailableCommentPlatforms(platforms => {
            return [CommentPlatform.ALL, ...platforms, CommentPlatform.FACEBOOK];
          });
        }
        if (newStreamInfo.stream_to.includes('instagram')) {
          const instaChannel = instagramChannels.find((e: any) => e?._id == newStreamInfo.insta_auth_id);
          if (instaChannel) {
            dispatch(fetchInstagramToken(streamId, newStreamInfo.insta_auth_id));
            setAvailableCommentPlatforms(platforms => { 
              const newPlatforms = [...platforms, CommentPlatform.INSTAGRAM];
              if (!newPlatforms.includes(CommentPlatform.ALL)) newPlatforms.unshift(CommentPlatform.ALL);
              return newPlatforms;
            });
          } else {
            handleApiErrorWithoutNetwork(
              'Instagram Connection Error',
              LIVE_STREAM.ERROR.INSTA_CHANNEL_DISCONNECT,
              dispatch,
              'initLively',
            );
            navigate(-1);
          }
        }
        LivelyApi.getStreamComments(authToken, streamId, 'app')
          .then(response => {
            setComments(
              (response?.data?.data || []).map(e => ({...e, name: e.user_name, id: e.unix_time, from: 'APP'})),
            );
            setAllComments(
              (response?.data?.data || []).map(e => ({...e, name: e.user_name, id: e.unix_time, from: 'APP'})),
            );
          })
          .catch(error => {
            handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'initLively - getStreamComments');
          });
        const newUserId =
          role === 2 ? livelyUser.user_id + 'Moderator' + `${new Date().getTime()}`.slice(-8) : newStreamInfo?.user_id;
        setStreamInfo(streamResponse?.data?.data);
        setRoomId(newStreamInfo?.room_id);
        let newProductsData = newStreamInfo?.product_info;
        setSelectedProductId(newProductsData[0]?.product_id);
        onSetProducts(newProductsData);
        zegoExpEngine.logoutRoom();
        LivelyApi.generateZeegoToken(authToken, newUserId, streamId)
          .then((tokenResponse: any) => {
            setCallToken(tokenResponse?.data?.data);
            if (role == 1) {
              changeCallStatus(2);
              initiateMeta();
            }
            initiateZeegoSDK(tokenResponse?.data?.data, newStreamInfo?.room_id, newUserId, newStreamInfo?.user_name);
          })
          .catch(error => {
            handleApiError(error, LIVE_STREAM.ERROR.CONNECTION_ISSUE, dispatch, 'initLively - generateZeegoToken');
          });
      })
      .catch(error => {
        if (error.code === 'ERR_NETWORK') {
          setIsNetworkError(true);
        }
        handleApiError(
          error,
          COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
          dispatch,
          'initLively - getLiveStreamInfoByID',
          false,
        );
      });
  };

  useEffect(() => {
    if (!instagramLoading && !instaFetched) {
      dispatch(fetchInstagramPages());
      setInstaFetched(true);
    }
    // console.log({authToken, streamId, role, streamInfo});
    if (
      authToken &&
      streamId &&
      role &&
      !streamInfo &&
      !instagramLoading &&
      instaFetched &&
      !isInstagramConnectionError
    ) {
      console.log({authToken, streamId, role});
      if (role == 1) {
        LivelyApi.getHostPublishingRights(authToken, streamId)
          .then((response: any) => {
            if (response) {
              fetchViewerMultiplier();
              initLively();
            } else {
              alert('You are not allowed to join as a Host');
            }
          })
          .catch(error => {
            if (error.code === 'ERR_NETWORK') {
              setIsNetworkError(true);
            }
            handleApiError(error, LIVE_STREAM.ERROR.CONNECTION_ISSUE, dispatch, 'getHostPublishingRights', false);
          });
      } else {
        initLively();
        checkIfNewFeaturesEnabled();
      }
    } else if (
      authToken &&
      streamId &&
      role &&
      !streamInfo &&
      !instagramLoading &&
      instaFetched &&
      isInstagramConnectionError
    ) {
      handleApiErrorWithoutNetwork(
        'Instagram Connection Error',
        LIVE_STREAM.ERROR.REFRSH,
        dispatch,
        'instagramLoading',
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authToken, role, streamId, streamInfo, instagramLoading, instaFetched, isInstagramConnectionError]);

  const streamInfoUpdate = useCallback(
    (roomID, streamList) => {
      const extraInfo = JSON.parse(streamList[0].extraInfo);
      if (extraInfo.event == 'PRODUCT_CHANGE') {
        const newProductId = extraInfo.data.product_id;
        const newProduct = products?.find(e => e.product_id == newProductId);
        if (role == 2) {
          LivelyApi.getLiveStreamInfoByID(authToken, streamId)
            .then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              setStreamInfo(streamResponse?.data?.data);
              setRoomId(newStreamInfo?.room_id);
              let newProductsData = newStreamInfo?.product_info;
              onSetProducts(newProductsData);
              setSelectedProductId(newProductId);
              // dispatch(
              //   makeToast({
              //     content: LIVE_STREAM.SUCCESS.PRODUCT_ADDED,
              //     appearances: 'success',
              //     duration: 2000,
              //   }),
              // );
            })
            .catch(error => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'streamInfoUpdate - PRODUCT_CHANGE',
              );
            });
        }
      } else if (extraInfo.event == 'DELETE_PRODUCT') {
        setTimeout(() => {
          LivelyApi.getLiveStreamInfoByID(authToken, streamId)
            .then((streamResponse: any) => {
              const newStreamInfo = streamResponse?.data?.data;
              let newProductsData = newStreamInfo?.product_info;
              onSetProducts(newProductsData);
              dispatch(
                makeToast({
                  content: LIVE_STREAM.SUCCESS.PRODUCT_REMOVED,
                  appearances: 'success',
                  duration: 2000,
                }),
              );
            })
            .catch(error => {
              handleApiError(
                error,
                COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                dispatch,
                'treamInfoUpdate - DELETE_PRODUCT',
              );
            });
        }, 500);
      } else if (extraInfo.event == 'MULTIPLIER_CHANGE') {
        updateViewMultiplier(extraInfo.count);
      }
    },
    [products],
  );

  //Initiate the Zegoo Process After All details are fetched
  const initiateZeegoSDK = async (token: string, roomId: string, userID: string, userName: string) => {
    //Play Local Stream
    console.log(token && roomId && userID && userName, 'xxx', token, roomId, userID, userName);
    if (token && roomId && userID && userName) {
      const playLocalStream = async () => {
        let option = {
          audioBitrate: 48,
        };

        option = {
          camera: {
            audio: true,

            video: {
              quality: 4,
              width: 720,
              height: 1280,
              frameRate: 30,
              bitRate: 1500,
              // facingMode: isFrontCamera ? "user" : "environment"
            },
          },
        };
        try {
          const localStream = await zegoExpEngine.createZegoStream(option);
          zegoExpEngine.setVideoConfig(localStream, {
            width: 720,
            height: 1280,
            frameRate: 20,
          });

          zeegoLocalStreamRef.current = localStream;
          localStream.playVideo(document.querySelector('#local-video'), {
            enableAutoplayDialog: true,
            mirror: false,
          });

          //Publish Stream
          const publishStream = async () => {
            console.log('Started Publishing Stream');
            zegoExpEngine.startPublishingStream(streamId, localStream);
            setTimeout(() => {
              zegoExpEngine.muteMicrophone(true);
              localStream.stopCaptureVideo(true);
              zegoExpEngine.enableVideoCaptureDevice(localStream, false);
            }, 300);
          };

          publishStream();
        } catch (error) {
          handleApiError(error, LIVE_STREAM.ERROR.CONNECTION_ISSUE, dispatch, 'initiateZeegoSDK');
        }
      };
      const result = await zegoExpEngine.loginRoom(
        roomId,
        token,
        {
          userID,
          userName,
        },
        {
          userUpdate: true,
        },
      );
      if (result) {
        if (role == 1) {
          playLocalStream();
        } else if (role == 2) {
          const remoteStreamInitial = await zegoExpEngine.startPlayingStream(streamId);
          const remoteView = zegoExpEngine.createRemoteStreamView(remoteStreamInitial);
          remoteView.play('local-video');
        }
      }

      setIsLoggedIn(true);
      if (role === 2) {
        zegoExpEngine.on('roomStreamUpdate', (roomId, updateType, streamList) => {
          if (updateType === 'DELETE') {
            setTimeout(() => {
              LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                if (streamResponse?.data?.data?.streaming_status === 3) {
                  streamList.forEach(stream => {
                    console.log(`Stream with ID ${stream.streamID} has ended.`);
                    dispatch(
                      makeToast({
                        content: 'Stream ended by host',
                        appearances: 'info',
                        duration: 2000,
                      }),
                    );
                    navigate(-1);
                  });
                }
              });
            }, 3000);
          }
        });
      }
      zegoExpEngine.on('streamExtraInfoUpdate', streamInfoUpdate);
      zegoExpEngine.on('IMRecvBarrageMessage', (roomID, messageInfo) => {
        const messageObj = JSON.parse(messageInfo[0]?.message);
        if (!messageObj.type && messageObj.comment) {
          let obj = {
            name:
              messageObj?.source == 'MODERATOR' || messageObj?.source == 'HOST'
                ? livelyUser.company_name
                : messageInfo[0]?.fromUser?.userName?.startsWith('Anonymous') && messageObj?.name != 'You'
                ? messageObj?.name
                : messageInfo[0]?.fromUser?.userName || 'Anonymous',
            comment: messageObj?.comment,
            imgUrl: streamInfo?.streaming_thumbnail,
            id: `${new Date().getTime()}${Math.floor(Math.random() * 10000)}`,
            source: messageObj.source,
            from: 'APP',
            deviceId: messageObj?.deviceId,
            muted: messageObj?.muted,
            isBanned: messageObj?.isBanned,
          };
          addComment(obj);
          appMessagesToPush.current = appMessagesToPush.current.concat([
            {
              comment: obj.comment,
              user_type: obj.source == 'HOST' ? 'host' : obj.source == 'MODERATOR' ? 'moderator' : 'viewer',
              user_name: obj?.name,
              from: 'APP',
            },
          ]);
          commentsHistoryUpdate(streamId);
        }
      });
      zegoExpEngine.on('IMRecvCustomCommand', async (roomID, fromUser, command) => {
        console.log('CUSTOM COMMAND RECEIVED', command);
        let parsedCommand = JSON.parse(command);
        if (parsedCommand?.type == 'SHOW_PRODUCT_EVENT') {
          if (role == 1) {
            setSelectedProductId(parsedCommand?.id);
            let newProduct = productsRef.current?.find(e => e.product_id == parsedCommand?.id);
            //HACK TO BE REMOVED
            setProducts(oldProducts => {
              newProduct = oldProducts?.find(e => e.product_id == parsedCommand?.id);
              return oldProducts;
            });
            const infoObj = {
              data: {
                product_id: newProduct?.product_id,
                store_product_id: newProduct?.store_product_id,
              },
              event: 'PRODUCT_CHANGE',
            };
            if (newProduct) {
              LivelyApi.changeCurrentProduct(authToken, streamId, newProduct?.product_id).catch(error => {
                handleApiError(
                  error,
                  COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                  dispatch,
                  'initiateZeegoSDK - SHOW_PRODUCT_EVENT',
                );
              });
              zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
            }
          }
        } else if (parsedCommand?.type == 'DELETE_PRODUCT_EVENT') {
          if (role == 1) {
            let existingProduct = productsRef.current?.find(e => e.product_id == parsedCommand?.id);
            //HACK TO BE REMOVED
            setProducts(oldProducts => {
              existingProduct = oldProducts?.find(e => e.product_id == parsedCommand?.id);
              return oldProducts;
            });
            const infoObj = {
              data: {
                product_id: existingProduct?.product_id,
                store_product_id: existingProduct?.store_product_id,
              },
              event: 'DELETE_PRODUCT',
            };
            if (existingProduct) {
              zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
              LivelyApi.removeProductFromStream(authToken, streamId, [infoObj.data])
                .then(async () => {
                  const productData = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
                    productId: `gid://shopify/Product/${infoObj.data.store_product_id}`,
                  });
                  let productMetaData = {
                    [infoObj.data.store_product_id]: Object.fromEntries(
                      shopifyProductTransformer(productData.data.product)?.variants.map((variant: any) => [
                        variant.id.split('/').pop(),
                        {
                          initialInventory:
                            streamRef.current?.meta?.[infoObj.data.store_product_id]?.[variant.id.split('/').pop()]
                              .initialInventory,
                          finalInventory: variant?.inventoryQuantity,
                        },
                      ]),
                    ),
                  };
                  LivelyApi.updateStreamMeta(authToken, streamId, productMetaData).then(() => {
                    LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                      const newStreamInfo = streamResponse?.data?.data;
                      let newProductsData = newStreamInfo?.product_info;
                      setStreamInfo(newStreamInfo);
                      onSetProducts(newProductsData);
                    });
                  });
                })
                .catch(error => {
                  handleApiError(
                    error,
                    COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                    dispatch,
                    'initiateZeegoSDK - DELETE_PRODUCT_EVENT',
                  );
                });
            }
          }
        } else if (parsedCommand?.type == 'ADD_NEW_PRODUCT') {
          if (role == 1) {
            const productData = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
              productId: `gid://shopify/Product/${parsedCommand?.data?.store_product_id}`,
            });
            let productDataTransformed = await shopifyProductTransformer(productData.data.product);
            LivelyApi.addNewProductToStream(authToken, streamId, [parsedCommand.data])
              .then(() => {
                let productMetaData = {
                  [parsedCommand?.data?.store_product_id]: Object.fromEntries(
                    productDataTransformed?.variants.map((variant: any) => [
                      variant.id.split('/').pop(),
                      {
                        initialInventory: streamRef.current?.meta?.[parsedCommand?.data?.store_product_id]?.[
                          variant.id.split('/').pop()
                        ]
                          ? streamRef.current?.meta?.[parsedCommand?.data?.store_product_id]?.[
                              variant.id.split('/').pop()
                            ]?.initialInventory -
                            streamRef.current?.meta?.[parsedCommand?.data?.store_product_id]?.[
                              variant.id.split('/').pop()
                            ]?.finalInventory +
                            (variant.quantityAvailableForSale || variant.inventoryQuantity)
                          : variant.quantityAvailableForSale || variant.inventoryQuantity,
                        finalInventory: null,
                      },
                    ]),
                  ),
                };
                LivelyApi.updateStreamMeta(authToken, streamId, productMetaData).then(() => {
                  LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                    const newStreamInfo = streamResponse?.data?.data;
                    let newProductsData = newStreamInfo?.product_info;
                    setStreamInfo(newStreamInfo);
                    onSetProducts(newProductsData);
                  });
                });
              })
              .catch(error => {
                handleApiError(
                  error,
                  COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR,
                  dispatch,
                  'initiateZeegoSDK - ADD_NEW_PRODUCT',
                );
              });
          }
        } else if (parsedCommand?.type == 'CHANGE_MULTIPLIER_EVENT') {
          if (parsedCommand?.count != null) {
            const infoObj = {
              count: parsedCommand?.count,
              event: 'MULTIPLIER_CHANGE',
            };
            viewerMultiplierRef.current = parsedCommand?.count;
            zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
          }
        } else if (parsedCommand?.type == 'REACTION_ANNOUCEMENT') {
          triggerEmoji(parsedCommand?.reactionUrl, parsedCommand?.userName);
        } else if (parsedCommand?.type == 'CART_ANNOUCEMENT') {
          console.log('messageInfo parsedMessage', parsedCommand?.productId);
          triggerAddCard(parsedCommand?.productTitle, parsedCommand?.userName);
        }
      });
      zegoExpEngine.on('roomOnlineUserCountUpdate', (roomID, count) => {
        setUserCount(count);
        if (role == 1) {
          //HACK TO BE REMOVED
          setSelectedProductId(selectedProductId => {
            setStreamInfo(streamInfo => {
              const product = streamInfo?.product_info?.find(e => e.product_id == selectedProductId);
              const infoObj = {
                data: {
                  product_id: product?.product_id,
                  store_product_id: product?.store_product_id,
                  base_bid_price: product?.base_bid_price,
                  bid_increment: product?.bid_increment,
                },
                event: 'PRODUCT_CHANGE',
              };
              zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
              return streamInfo;
            });
            return selectedProductId;
          });
          viewerMultiplierChange(viewerMultiplierRef.current);
        }
      });
      zegoExpEngine.on('roomUserUpdate', (roomID, updateType, userList) => {
        if(role == 2) {
          if(updateType == "ADD") {
            setActivePoll((activePoll: any) => {
              if(!_.isEmpty(activePoll)) {
                zegoExpEngine.sendCustomCommand(
                  roomID,
                  JSON.stringify({
                    type: 'START_POLL',
                    poll: activePoll,
                  }),
                  userList.map(user => user.userID),
                );
                return activePoll;
              }
            })
            setMutedUsers((mutedUsers: any) => {
              if(!_.isEmpty(mutedUsers)) {
                zegoExpEngine.sendCustomCommand(
                  roomID,
                  JSON.stringify({
                    type: 'MUTE_USER',
                    mutedUsers,
                  }),
                  userList.map(user => user.userID),
                );
              }
              return mutedUsers;
            })
          }
        }
      })
    }
  };
  //Handlers
  const handleMuteMicrophone = () => {
    if (zeegoLocalStreamRef?.current) {
      zegoExpEngine.muteMicrophone(true);
      zegoExpEngine.mutePublishStreamAudio(zeegoLocalStreamRef?.current, true);
      setMicrophoneFlag(false);
    }
  };
  const handleOnMicrophone = () => {
    if (zeegoLocalStreamRef?.current) {
      zegoExpEngine.muteMicrophone(false);
      zegoExpEngine.mutePublishStreamAudio(zeegoLocalStreamRef?.current, false);
      setMicrophoneFlag(true);
    }
  };
  const handleOffCamera = () => {
    if (zeegoLocalStreamRef?.current) {
      zeegoLocalStreamRef?.current.stopCaptureVideo(true);
      zegoExpEngine.enableVideoCaptureDevice(zeegoLocalStreamRef?.current, false);
      zegoExpEngine.mutePublishStreamVideo(zeegoLocalStreamRef?.current, true);
    }
    let localVideoRef = document?.getElementById('local-video');
    if (localVideoRef && localVideoRef.children[0]) {
      localVideoRef.children[0].style.display = 'none';
    }
    setcameraFlag(false);
  };
  const handleStartCamera = async () => {
    setcameraFlag(true);
    if (zeegoLocalStreamRef?.current) {
      zeegoLocalStreamRef?.current.stopCaptureVideo(false);
      zegoExpEngine.enableVideoCaptureDevice(zeegoLocalStreamRef?.current, true);
      zegoExpEngine.mutePublishStreamVideo(zeegoLocalStreamRef?.current, false);
    }
    let localVideoRef = document?.getElementById('local-video');
    if (localVideoRef && localVideoRef.children[0]) {
      localVideoRef.children[0].style.display = 'block';
    }
  };
  const handleEndCall = () => {
    // zegoExpEngine.stopPublishingStream('lively123');
    // zegoExpEngine.destroyStream(zeegoLocalStreamRef?.current);
    // // zegoExpEngine.destroyEngine();
    // zegoExpEngine.logoutRoom(roomId);

    if (zegoExpEngine) {
      zegoExpEngine.stopPublishingStream(streamId);
      zegoExpEngine.logoutRoom(roomId);
    }

    if (zegoExpEngine && zeegoLocalStreamRef?.current) {
      zeegoLocalStreamRef?.current;
      zegoExpEngine.destroyStream(zeegoLocalStreamRef?.current);
    }

    // zegoExpEngine.destroyEngine();
    if (zegoExpEngine) zegoExpEngine.destroyEngine();

    zeegoLocalStreamRef.current = null;

    changeCallStatus(3);
    setTimeout(() => {
      navigate(-1);
    }, 250);
  };
  const changeCallStatus = async status => {
    await LivelyApi.changeStreamStatus(authToken, streamId, status).catch(error => {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'changeCallStatus');
    });
  };
  const getFacebookComments = async () => {
    try {
      const response = await LivelyApi.getFacebookComments(
        facebookStream?.token,
        facebookStream?.fb_stream_id,
        fbAfterCursor?.current,
      );
      if (!response.status === 200) {
        throw new Error('Failed to fetch Facebook comments');
      }
      const data = response.data;
      if (data?.paging?.cursors?.after) fbAfterCursor.current = data?.paging?.cursors?.after;
      const fbComments = data?.data
        ?.map(commentInfo => ({
          name: commentInfo?.from?.id !== facebookStream?.page_id ? commentInfo?.from?.name || 'Unknown' : 'YOU',
          comment: commentInfo.message,
          imgUrl: streamInfo?.streaming_thumbnail,
          id: commentInfo?.id,
          source: commentInfo?.from?.id !== facebookStream?.page_id ? 'USER' : 'HOST',
          isReplied: false,
          from: 'FACEBOOK',
        }))
        .filter((e: any) => !!e);
      addFacebookComments(fbComments);
      const historyComments = data?.data?.map(item => ({
        comment: item.message,
        uuid: item?.id,
        user_type: item?.from?.id !== facebookStream?.page_id ? 'viewer' : 'host',
        user_name: item?.from?.name || 'Unknown',
        from: 'FACEBOOK',
        ...(item?.parent && {parent_uuid: item?.parent?.id}),
      }));
      fbMessagesToPush.current = fbMessagesToPush.current.concat(historyComments ?? []);
    } catch (error) {
      handleApiError(error, LIVE_STREAM.ERROR.FB_COMMENTS, dispatch, 'getFacebookComments');
    }
  };
  const toggleMuteStream = () => {
    if (speakerFlag) {
      zegoExpEngine.muteAllPlayAudioStreams(true);
    } else {
      zegoExpEngine.muteAllPlayAudioStreams(false);
    }
    setSpeakerFlag(!speakerFlag);
  };

  const commentsHistoryUpdate = async (streamId: string) => {
    if (streamInfo?.streaming_name) {
      if (appMessagesToPush?.current && appMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          appMessagesToPush?.current,
          'app',
        ).catch(error => {
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'commentsHistoryUpdate - app');
        });
        appMessagesToPush.current = [];
      }
      if (fbMessagesToPush?.current && fbMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          fbMessagesToPush?.current,
          'facebook',
        ).catch(error => {
          handleApiError(error, LIVE_STREAM.ERROR.FB_COMMENTS, dispatch, 'commentsHistoryUpdate - facebook');
        });
        fbMessagesToPush.current = [];
      }
      if (instaMessagesToPush?.current && instaMessagesToPush?.current.length > 0) {
        await LivelyApi.postStreamComments(
          authToken,
          streamId,
          streamInfo?.streaming_name,
          instaMessagesToPush?.current,
          'instagram',
        ).catch(error => {
          handleApiError(error, LIVE_STREAM.ERROR.INSTA_COMMENTS, dispatch, 'commentsHistoryUpdate - facebook');
        });
        instaMessagesToPush.current = [];
      }
    }
  };

  const addNewScannedProduct = _.debounce(async barcode => {
    setIsBarCodeResultLoading(true);
    const scanningId = uuidv4();
    // dispatch(
    //   makeToast({
    //     content: 'Scanning product',
    //     appearances: 'info',
    //     id: scanningId,
    //     cancellable: false,
    //   }),
    // );
    try {
      const queryResponse = await queryRunnerRef.current.runQuery(
        'query',
        ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN,
        {
          first: 50,
          query: barcode,
          countryCode: 'US',
        },
      );
      scannedBarcode.current = '';
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        {},
        null,
      );
      if (transformedData.length != 0) {
        const productScanned = transformedData.find(
          (e: any) => e.handle == barcode || !!e.variants.find((f: any) => f.barcode == barcode),
        );
        if (productScanned) {
          let productFoundInList;
          setProducts(streamProducts => {
            productFoundInList = streamProducts.find(e => 'gid://shopify/Product/' + e.store_product_id == productScanned.id)
            return streamProducts;
          });
          if (!productFoundInList) {
            dispatch(removeToast([scanningId]));
            dispatch(
              makeToast({
                content: `${productScanned.title} added to live`,
                appearances: 'success',
              }),
            );
            const obj = {
              type: 'ADD_NEW_PRODUCT',
              data: {
                store_product_id: productScanned?.id.split('/').pop(),
                product_url: productScanned?.onlineStoreUrl ?? 'https://apptile.io/',
                product_name: productScanned?.title,
                product_thumbnail: productScanned?.featuredImage,
              },
            };
            if (role == 2) {
              zegoExpEngine.sendCustomCommand(roomId, JSON.stringify(obj), [streamInfo?.user_id]);
              setTimeout(() => {
                LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                  const newStreamInfo = streamResponse?.data?.data;
                  let newProductsData = newStreamInfo?.product_info;
                  if (newProductsData) {
                    onSetProducts(newProductsData);
                    const scannedProduct = newProductsData.find(
                      (e: any) => e?.store_product_id == productScanned?.id?.split('/')?.pop(),
                    );
                    if (scannedProduct) handleChangeActiveProduct(scannedProduct);
                  }
                });
              }, 1500);
            } else if (role == 1) {
              const productData = await queryRunnerRef.current?.runQuery('query', ProductGqls.GET_PRODUCT, {
                productId: `gid://shopify/Product/${productScanned?.id.split('/').pop()}`,
              });
              let productDataTransformed = await shopifyProductTransformer(productData.data.product);
              LivelyApi.addNewProductToStream(authToken, streamId, [obj.data]).then(() => {
                let productMetaData = {
                  [obj.data.store_product_id]: Object.fromEntries(
                    productDataTransformed?.variants.map((variant: any) => [
                      variant.id.split('/').pop(),
                      {
                        initialInventory: streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]
                          ? streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]
                              ?.initialInventory -
                            streamInfo?.meta?.[obj.data.store_product_id]?.[variant.id.split('/').pop()]
                              ?.finalInventory +
                            variant.inventoryQuantity
                          : variant.inventoryQuantity,
                        finalInventory: null,
                      },
                    ]),
                  ),
                };
                LivelyApi.updateStreamMeta(authToken, streamId, productMetaData).then(() => {
                  LivelyApi.getLiveStreamInfoByID(authToken, streamId).then((streamResponse: any) => {
                    const newStreamInfo = streamResponse?.data?.data;
                    let newProductsData = newStreamInfo?.product_info;
                    if (newProductsData) {
                      onSetProducts(newProductsData);
                      const scannedProduct = newProductsData.find(
                        (e: any) => e?.store_product_id == productScanned?.id?.split('/')?.pop(),
                      );
                      if (scannedProduct) handleChangeActiveProduct(scannedProduct);
                    }
                  });
                });
              });
            }
          } else {
            dispatch(removeToast([scanningId]));
            handleChangeActiveProduct(productFoundInList);
            dispatch(
              makeToast({
                content: 'Product found. Showing Now!',
                appearances: 'success',
                duration: 2000,
              }),
            );
          }
        } else {
          dispatch(removeToast([scanningId]));
          dispatch(
            makeToast({
              content: 'Invalid barcode, please try again',
              appearances: 'error',
              duration: 2000,
            }),
          );
        }
      } else {
        dispatch(removeToast([scanningId]));
        dispatch(
          makeToast({
            content: 'Invalid barcode, please try again',
            appearances: 'error',
            duration: 2000,
          }),
        );
      }
    } catch (err) {
      console.log(err);
      dispatch(removeToast([scanningId]));
      dispatch(
        makeToast({
          content: 'Something went wrong, please try again with different barcode',
          appearances: 'error',
          duration: 4000,
        }),
      );
    }
    setIsBarCodeResultLoading(false);
  }, 300);

  useEffect(() => {
    if (streamInfo?.streaming_name && streamInfo?.stream_to?.includes('facebook')) {
      if (streamId && facebookStream) {
        if (facebookStream.token) {
          if (fbErrorInterval?.current) clearInterval(fbErrorInterval.current);
          if (fbInterval.current) clearInterval(fbInterval.current);
          setFacebookCommentsLoading(true);
          fbInterval.current = setInterval(getFacebookComments, facebookAPIPollingTime);
        }
        if (facebookStream.error) {
          if (fbInterval?.current) clearInterval(fbInterval.current);
          if (fbErrorInterval?.current) clearInterval(fbErrorInterval.current);
          fbErrorInterval.current = setInterval(() => dispatch(fetchFacebookToken(streamId)), facebookAPIPollingTime);
        }
      }
    }
    if (streamInfo?.streaming_name && streamInfo?.stream_to?.includes('instagram')) {
      if (streamId && instagramStream) {
        if (instagramStream.token) {
          if (instaErrorInterval?.current) clearInterval(instaErrorInterval.current);
          setInstagramCommentsLoading(true);
        }
        if (instagramStream.error) {
          if (instaErrorInterval?.current) clearInterval(instaErrorInterval.current);
          instaErrorInterval.current = setInterval(
            () => dispatch(fetchInstagramToken(streamId, streamInfo?.insta_auth_id)),
            facebookAPIPollingTime,
          );
        }
      }
    }
    authToken &&
      LivelyApi.getShopifyCreds(authToken)
        .then(response => {
          const newQueryRunner = apolloQueryRunner();
          newQueryRunner
            .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
              return {
                headers: {
                  ...headers,
                  'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                },
              };
            })
            .then(() => {
              queryRunnerRef.current = newQueryRunner;
            });
        })
        .catch(error => {
          console.log(error);
        });
  }, [
    authToken,
    dispatch,
    streamInfo,
    facebookStream,
    instagramStream,
    instaInterval.current,
    instaErrorInterval.current,
    fbInterval.current,
    fbErrorInterval.current,
    streamId,
  ]);

  const scannedBarcode = useRef('');
  useEffect(() => {
    document.onkeypress = function (e) {
      const textInput = e.key || String.fromCharCode(e.keyCode);
      const targetName = e.target.localName;
      if (textInput && textInput.length === 1 && targetName !== 'input' && targetName !== 'textarea') {
        scannedBarcode.current = scannedBarcode.current + textInput;
        addNewScannedProduct(scannedBarcode.current);
      }
    };
    return async () => {
      if (fbInterval?.current) clearInterval(fbInterval?.current);
      if (instaInterval?.current) clearInterval(instaInterval?.current);
      if (fbErrorInterval?.current) clearInterval(fbErrorInterval?.current);
      if (instaErrorInterval?.current) clearInterval(instaErrorInterval?.current);
      // Don't remove, (logs out mod)
      if(zegoExpEngine && roomId) zegoExpEngine.logoutRoom(roomId);
      if (zeegoLocalStreamRef?.current && zegoExpEngine && roomId) {
        try {
          if (viewerMultiplierIntervalRef?.current) clearInterval(viewerMultiplierIntervalRef.current);
          // zegoExpEngine.stopPublishingStream('lively123');
          if (!zeegoLocalStreamRef?.current) {
            zeegoLocalStreamRef?.current?.stopVideo();
          }
          zegoExpEngine.off('streamExtraInfoUpdate');
          zegoExpEngine.off('roomOnlineUserCountUpdate');
          zegoExpEngine.off('roomUserUpdate');
          zegoExpEngine.off('IMRecvBarrageMessage');
          zegoExpEngine.off('IMRecvBroadcastMessage');
          zegoExpEngine.off('IMRecvCustomCommand');
          zegoExpEngine.stopPublishingStream(streamId);
          zegoExpEngine.destroyStream(zeegoLocalStreamRef?.current);
          document.onkeypress = null;
        } catch (err) {
          console.error(err);
        }
      }
    };
  }, [roomId, dispatch]);

  useEffect(() => {
    if (instagramStream?.redis_token) {
      const socket = new WebSocket(
        `wss://253ul4moik.execute-api.ap-south-1.amazonaws.com/production?id=${instagramStream?.redis_token}&type=lv_agent`,
      );

      // Connection opened
      socket.addEventListener('open', event => {
        socket.send('Connection established');
        setInstagramCommentsLoading(false);
      });

      // Listen for messages
      socket.addEventListener('message', event => {
        try {
          const eventData = JSON.parse(event?.data);
          console.log('Message from server ', eventData.message);
          const instaComment = eventData.message;
          const newComment = {
            name: instaComment?.username,
            comment: instaComment?.text,
            imgUrl: '',
            id: instaComment?.comment_id,
            source: instaComment?.user_id !== instagramStream?.page_id ? 'USER' : 'HOST',
            isReplied: false,
            from: 'INSTAGRAM',
          };
          if (newComment.id) {
            addInstagramComments([newComment]);
            const historyComments = [
              {
                comment: newComment.comment,
                uuid: newComment.id,
                user_type: 'viewer',
                user_name: newComment.username || 'Unknown',
                from: 'INSTAGRAM',
              },
            ];
            instaMessagesToPush.current = instaMessagesToPush.current.concat(historyComments ?? []);
          }
        } catch (err) {
          handleApiError(err, LIVE_STREAM.ERROR.INSTA_COMMENTS, dispatch, 'INSTAGRAM COMMENTS WSS');
        }
      });

      connection.current = socket;
    }
    return () => connection.current && connection.current?.close();
  }, [dispatch, instagramStream, instagramStream?.redis_token]);

  const currentSelectedProduct = _.find(products, {product_id: selectedProductId});
  // let availableCommentPlatforms: CommentPlatform[] = [];
  // if (!showFBComments) {
  //   availableCommentPlatforms.splice(availableCommentPlatforms.indexOf(CommentPlatform.FACEBOOK), 1);
  // availableCommentPlatforms.splice(availableCommentPlatforms.indexOf(CommentPlatform.ALL), 1);
  // }
  const viewerMultiplierChange = (count: any) => {
    if (count == null) return;
    const infoObj = {
      count: count,
      event: 'MULTIPLIER_CHANGE',
    };
    const obj = {
      count: count,
      type: 'CHANGE_MULTIPLIER_EVENT',
    };
    if (role == 1) {
      zegoExpEngine.setStreamExtraInfo(streamId, JSON.stringify(infoObj));
    } else if (role == 2) {
      setRoomId(roomId => {
        setStreamInfo(streamInfo => {
          zegoExpEngine.sendCustomCommand(roomId, JSON.stringify(obj), [streamInfo?.user_id]);
          return streamInfo;
        });
        return roomId;
      });
    }
    updateViewMultiplier(count);
  };
  const handleMuteUsers = async (comment: any) => {
    const updatedMutedUsers = mutedUsers.find((user: any) => user.deviceId === comment.deviceId)
      ? mutedUsers.filter(user => user.deviceId !== comment.deviceId)
      : [...mutedUsers, comment];
    setMutedUsers(updatedMutedUsers);
    LivelyApi.updateStreamMeta(authToken, streamId, { mutedUsers: updatedMutedUsers })
    .then(() => {
      const muteObj = {
        type: 'MUTE_USER',
        data: { mutedUsers: updatedMutedUsers }
      }
      zegoExpEngine.sendBroadcastMessage(roomId, JSON.stringify(muteObj))
    })
    .catch(error => {
      console.log('ERROR IN MUTING USERS: ', error)
      dispatch(
        makeToast({
          content: 'Something went wrong while muting the user!',
          appearances: 'error',
          duration: 1500,
        }),
      );
    })
  }

  useEffect(() => {
    const getPolls = async () => {
      try {
        setPollsLoading(true);
        const response = await LivelyApi.getPolls(authToken, streamId);
        setPolls(_.reverse(response?.data?.data));
        const activePoll = response?.data?.data?.find(poll => poll.status == 'active') || '';
        const newPoll = response?.data?.data?.find(poll => poll.status == 'new') || '';
        if (newPoll) {
          // (Don't Remove) Edge case: When poll is created but never started.
          await LivelyApi.startPoll(authToken, newPoll.poll_uuid);
          setPollsUpdated(true);
        }
        if (activePoll) {
          activePollRef.current = {poll_uuid: activePoll?.poll_uuid};
          const pollResultsResponse = await LivelyApi.getPollResults(authToken, activePoll.poll_uuid);
          setExpandedPolls({[activePoll.poll_uuid]: true});
          setActivePoll({...activePoll, pollResults: pollResultsResponse?.data?.data});
        }
        setPollsUpdated(false);
        return {polls: response?.data?.data, activePoll};
      } catch (error) {
        if (error?.response?.data?.message == 'No polls found.' && error?.response?.status == 400) {
          setPolls([]);
        }
      } finally {
        setPollsLoading(false);
      }
    };
    if (role === 2 && isPollingEnabled) getPolls();
  }, [pollsUpdated, isPollingEnabled]);

  useEffect(() => {
    const fetchAndBroadcastPollResults = async () => {
      try {
        if (activePollRef?.current?.poll_uuid) {
          const pollResults = await LivelyApi.getPollResults(authToken, activePollRef?.current?.poll_uuid);
          if (pollResults?.data?.data) {
            setActivePoll(prev => ({...prev, pollResults: pollResults?.data?.data}));
  
            const broadCastPollResultsObj = {
              type: 'BROADCAST_POLL_RESULTS',
              data: {
                pollResults: pollResults?.data?.data,
              }
            };
            zegoExpEngine.sendBroadcastMessage(roomId, JSON.stringify(broadCastPollResultsObj));
          }
        }
      } catch (error) {
        console.error('Error fetching poll results:', error);
      }
    };
    if (role === 2 && isPollingEnabled) {
      if (pollIntervalRef?.current) clearInterval(pollIntervalRef?.current);
      if (activePollRef?.current?.poll_uuid)
        pollIntervalRef.current = setInterval(fetchAndBroadcastPollResults, pollResultsPollingTime);

      return () => {
        if (pollIntervalRef?.current) clearInterval(pollIntervalRef?.current);
      };
    }
  }, [activePollRef.current?.poll_uuid]);

  return (
    <>
      {authToken ? (
        <>
          {isNetworkError ? (
            <NetworkErrorPage />
          ) : (
            <View style={{flexDirection: 'column', flex: 1, padding: 20, paddingHorizontal: 40}}>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                <Back text="Back to Dashboard" url={LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE()}/>
                <View style={[styles.rowLayout]}>
                  <View style={[styles.rowLayout, {gap: 10, alignItems: 'center', width: 310}]}>
                    {userCount != 0 && (
                      <>
                        <Text style={[commonStyles.baseText]}>View Multiplier</Text>
                        <RadioGroupControl
                          disableBinding={true}
                          value={viewerMultiplier}
                          options={[
                            {text: '1x', value: 1},
                            {text: '2x', value: 2},
                            {text: '3x', value: 3},
                            {text: '4x', value: 4},
                          ]}
                          onChange={viewerMultiplierChange}
                        />
                      </>
                    )}
                    {userCount == 0 && (
                      <View
                        style={{
                          flex: 1,
                          width: '100%',
                          height: '100%',
                          alignItems: 'flex-end',
                          justifyContent: 'center',
                          // backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          position: 'absolute',
                          zIndex: 2,
                          gap: 16,
                          top: 0,
                          left: 0,
                        }}>
                        <Image
                          style={{width: 30, height: 30}}
                          source={require('@/root/web/assets/images/preloader.svg')}
                        />
                      </View>
                    )}
                  </View>
                  {role === 2 && (
                    <View
                      style={{width: 350, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 10, marginLeft: 20}}>
                      <Text style={[commonStyles.baseText]}>Show customer view</Text>
                      <RadioGroupControl
                        value={showCustomerView}
                        onChange={value => setShowCustomerView(value)}
                        disableBinding
                        options={[
                          {text: 'No', value: false},
                          {text: 'Yes', value: true},
                        ]}
                      />
                    </View>
                  )}
                  {/* {showCustomerView && (
                    <View
                      style={{
                        width: 300,
                        marginLeft: 30,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 10,
                      }}>
                      <Text style={[commonStyles.baseText]}>Variant</Text>
                      <RadioGroupControl
                        value={requiredCustomerView}
                        onChange={value => setRequiredCustomerView(value)}
                        disableBinding
                        options={[
                          {text: 'Variant one', value: 1},
                          {text: 'Variant two', value: 2},
                        ]}
                      />
                    </View>
                  )} */}
                </View>
              </View>
              {role === 2 && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} color="#3f9fba" />
                  <Text style={[commonStyles.baseText, {fontSize: 12}]}>
                    Products will not jump in the Shoppable video if not shown during the live streaming.
                  </Text>
                </View>
              )}
              
              <View style={[styles.container, styles.rowLayout, { marginTop: 10 }]} key={authToken}>
                <View style={styles.section}>
                  {isBarCodeResultLoading && (
                    <View
                      style={{
                        flex: 1,
                        width: '100%',
                        height: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        position: 'absolute',
                        zIndex: 2,
                        gap: 16,
                        top: 0,
                        left: 0,
                      }}>
                      <Image
                        style={{width: 30, height: 30}}
                        source={require('@/root/web/assets/images/preloader.svg')}
                      />
                      <Text style={{color: '#4F4F4F', fontFamily: theme.FONT_FAMILY}}>Scanning Items</Text>
                    </View>
                  )}
                  <Text style={[commonStyles.baseText, styles.sectionLabel]}>Show products</Text>
                  <View style={[styles.rowLayout, {gap: 10, paddingBottom: 10}]}>
                    <ShopifyItemObjectPicker
                      value={newProduct?.handle ?? ''}
                      itemType={'product'}
                      defaultOpen={false}
                      onChange={onSetNewProduct}
                      queryRunner={queryRunnerRef.current}
                    />
                    <Button onPress={addNewProduct}> Add </Button>
                  </View>
                  <View style={{marginTop: 10, flex: 1, overflow: 'auto', gap: 10, paddingRight: 5}}>
                    {products?.map((product, index) => (
                      <ProductCard
                        index={index}
                        key={product?.product_id}
                        product={product}
                        selectedProductId={selectedProductId}
                        handleChangeActiveProduct={handleChangeActiveProduct}
                        fromLiveStream
                        handleDeleteProduct={handleDeleteProduct}
                      />
                    ))}
                  </View>
                </View>
                <View
                  onLayout={getScreenDimensions}
                  style={[styles.section, {paddingHorizontal: 0, paddingVertical: 0}]}>
                  <View style={{flex: 1}}>
                    <div style={{width: '100%', height: '100%', overflow: 'hidden', borderWidth: 6}} id="local-video" />
                  </View>
                  {!cameraFlag && (
                    <Image
                      source={
                        streamInfo?.streaming_thumbnail
                          ? {uri: streamInfo?.streaming_thumbnail}
                          : require('@/root/web/assets/images/logo.png')
                      }
                      style={{width: '100%', height: '100%'}}
                    />
                  )}
                  <View
                    style={{
                      position: 'absolute',
                      padding: 10,
                      width: '100%',
                      height: '100%',
                      alignItems: 'flex-end',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 10,
                        flexWrap: 'wrap',
                      }}>
                      {streamInfo?.start_time && <Counter startTime={streamInfo?.start_time} />}
                      <View style={styles.streamCountWrapper}>
                        {userCount == 0 ? (
                          <Image
                            style={{width: 15, height: 15}}
                            source={require('@/root/web/assets/images/preloader-blue.svg')}
                          />
                        ) : (
                          <>
                            <Icon iconType={'MaterialCommunityIcons'} color={'#FFF'} name={'eye'} size={15} />
                            <Text style={[commonStyles.baseText, {fontSize: 15, color: '#FFF'}]}>
                              {userCount * viewerMultiplier}
                            </Text>
                          </>
                        )}
                      </View>
                    </View>
                    {showCustomerView && (
                      <CustomerViews
                        currentSelectedProduct={currentSelectedProduct}
                        requiredView={requiredCustomerView}
                      />
                    )}
                  </View>

                  {role == 1 ? (
                    <View
                      style={[
                        styles.rowLayout,
                        {position: 'absolute', bottom: 0, justifyContent: 'center', width: '100%'},
                      ]}>
                      {!isLoggedIn ? (
                        <View
                          style={[
                            styles.rowLayout,
                            {
                              backgroundColor: '#FFFFFF',
                              width: '100%',
                              minHeight: '100px',
                              justifyContent: 'center',
                              alignItems: 'center',
                            },
                          ]}>
                          <ActivityIndicator size={20} />
                        </View>
                      ) : (
                        <View
                          style={[
                            styles.rowLayout,
                            {
                              position: 'absolute',
                              bottom: 0,
                              paddingBottom: 20,
                              justifyContent: 'center',
                              width: '100%',
                            },
                          ]}>
                          <Pressable
                            style={styles.ctaButton}
                            onPress={microPhoneFlag ? handleMuteMicrophone : handleOnMicrophone}>
                            <Icon color={'#ddd'} name={microPhoneFlag ? 'microphone' : 'microphone-off'} size={26} />
                          </Pressable>
                          <Pressable
                            style={styles.ctaButton}
                            onPress={cameraFlag ? handleOffCamera : handleStartCamera}>
                            <Icon color={'#ddd'} name={cameraFlag ? 'camera' : 'camera-off'} size={26} />
                          </Pressable>
                          <Pressable
                            style={[styles.ctaButton, {backgroundColor: theme.ERROR_BACKGROUND}]}
                            onPress={handleEndCall}>
                            <Icon iconType={'MaterialIcons'} color={'#ddd'} name={'call-end'} size={26} />
                          </Pressable>
                        </View>
                      )}
                    </View>
                  ) : (
                    <>
                      {showCustomerView && <ReactionEmojis ref={emojiRef} dimensions={videoScreenDimensions} />}
                      {showCustomerView && <AddToCartReactionCards ref={cardRef} dimensions={videoScreenDimensions} />}
                      <View
                        style={[
                          styles.rowLayout,
                          {position: 'absolute', bottom: 0, paddingBottom: 20, justifyContent: 'center', width: '100%'},
                        ]}>
                        <Pressable style={styles.ctaButton} onPress={toggleMuteStream}>
                          <Icon color={'#ddd'} name={speakerFlag ? 'speaker' : 'speaker-off'} size={26} />
                        </Pressable>
                      </View>
                    </>
                  )}
                </View>
                <View style={{flex: 1}}>
                  {!!streamInfo?.meta && (
                    <View style={styles.revenueContainer}>
                      <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                        <Text style={[commonStyles.baseText, styles.sectionLabel]}>Revenue Earned</Text>
                        <Tooltip
                          tooltip={'Revenue earned in real-time during the ongoing stream'}
                          position="top"
                          toolTipMenuStyles={{
                            width: 207,
                            height: 51,
                            left: 5,
                            paddingHorizontal: 6,
                            paddingVertical: 4,
                          }}>
                          <Icon
                            name="information-outline"
                            iconType="MaterialCommunityIcons"
                            size={20}
                            color={'black'}
                            style={{
                              margin: 0,
                              verticalAlign: 'middle',
                              opacity: '0.6',
                              fontWeight: 300,
                              alignSelf: 'cenetr',
                              marginLeft: 10,
                            }}
                          />
                        </Tooltip>
                      </View>

                      <View style={styles.revenueProgressBarContainer}>
                        <View style={styles.revenueAmounts}>
                          <Text style={styles.currentRevenue}>${currentRevenue}</Text>
                        </View>
                      </View>
                    </View>
                  )}
                  <View
                    style={{
                      flex: 1,
                      paddingHorizontal: 18,
                      paddingVertical: 16,
                      backgroundColor: '#f9f9f9',
                      borderRadius: 8,
                      overflow: 'hidden',
                    }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          position: 'relative',
                          marginBottom: 14,
                        }}>
                          <View style={[{flexDirection: 'row', alignItems: 'center'}]}>
                            {showPolls && (
                              <Pressable onPress={() => setShowPolls(false)} style={{marginRight: 8}}>
                                <Icon
                                  iconType={'AntDesign'}
                                  name={'arrowleft'}
                                  color='#000'
                                  size={16}
                                />
                              </Pressable>
                            )}
                            <Text style={[commonStyles.baseText, styles.sectionLabel, {margin: 0}]}>
                              {showPolls ? 'Polls' : 'Comments'}
                            </Text>
                          </View>
                        {mutedUsersModal && (
                          <ModalComponent
                            onVisibleChange={setMutedUsersModal}
                            visible={mutedUsersModal}
                            content={
                              <MutedUsersModal
                                streamId={streamId}
                                roomId={roomId}
                                mutedUsers={mutedUsers}
                                handleMuteUsers={handleMuteUsers}
                              />
                            }
                          />
                        )}

                        {role == 2 && isShadowCommentsEnabled && (
                          <>
                            {showPolls ? 
                              !!polls?.length ? (
                                <Button disabled={!_.isEmpty(activePoll)} onPress={() => setShowCreatePoll(true)} color="CTA" size='SMALL'>
                                  Create Poll
                                </Button>
                              ) : null : isShadowCommentsEnabled ? (
                                <Pressable onPress={() => setShowCommentsAction(prev => !prev)}>
                                  <ApptileWebIcon name={'menu-vertical'} size={20} />
                                </Pressable>
                              ) : null
                            }

                            {showCommentsAction && (
                              <View style={[styles.popOverContainer, {padding: 0, top: -16}]}>
                                <Pressable
                                  style={{paddingHorizontal: 10, paddingVertical: 6}}
                                  onPress={() => setMutedUsersModal(true)}>
                                  <Text style={{fontSize: 12}}>Show Muted Users</Text>
                                </Pressable>
                              </View>
                            )}
                          </>
                        )}
                      </View>
                      {showPolls && (
                        <PollView
                          showCreatePoll={showCreatePoll}
                          setShowCreatePoll={setShowCreatePoll}
                          activePollRef={activePollRef}
                          polls={polls}
                          setPolls={setPolls}
                          activePoll={activePoll}
                          setActivePoll={setActivePoll}
                          expandedPolls={expandedPolls}
                          setExpandedPolls={setExpandedPolls}
                          pollsLoading={pollsLoading}
                          setPollsUpdated={setPollsUpdated}
                          roomId={roomId}
                          authToken={authToken}
                          streamId={streamId}
                          setShowPolls={setShowPolls}
                          viewerMultiplier={viewerMultiplier}
                        />
                      )}
                      {!showPolls && availableCommentPlatforms.length > 0 && (
                        <>
                          <TabsLite
                            onTabChange={tab => {
                              setActiveCommentTab(tab);
                              if (tab === CommentPlatform.FACEBOOK) {
                                setHasNewFacebookComments(false);
                              }
                              if (tab === CommentPlatform.INSTAGRAM) {
                                setHasNewInstagramComments(false);
                              }
                            }}
                            tabs={availableCommentPlatforms.map(platform => ({
                              title: platform,
                              disableScroll: true,
                              component: (
                                <CommentsView
                                  facebookCommentsLoading={facebookCommentsLoading}
                                  facebookComments={facebookComments}
                                  instagramCommentsLoading={instagramCommentsLoading}
                                  instagramComments={instagramComments}
                                  addInstagramComments={addInstagramComments}
                                  comments={comments}
                                  commentsHistoryUpdate={commentsHistoryUpdate}
                                  streamInfo={streamInfo}
                                  addComment={addComment}
                                  appMessagesToPush={appMessagesToPush}
                                  streamId={streamId}
                                  commentPlatformType={platform}
                                  setComments={setComments}
                                  getFacebookComments={getFacebookComments}
                                  roomId={roomId}
                                  role={role}
                                  allComments={allComments}
                                  isShadowCommentsEnabled={isShadowCommentsEnabled}
                                  mutedUsers={mutedUsers}
                                  muteComment={handleMuteUsers}
                                  setShowPolls={setShowPolls}
                                  activePoll={activePoll}
                                  isPollingEnabled={isPollingEnabled}
                                />
                              ),
                            }))}
                            noOfLines={1}
                            contentWrapperStyles={{gap: 6}}
                            activeVariant="FILLED-PILL"
                            inactiveVariant="FILLED-PILL"
                            activeColor="PRIMARY"
                            inactiveColor="NEW_TAB"
                            activeOpaque={true}
                            rootStyles={{flex: 1}}
                            size="SMALL"
                          />
                          {hasNewFacebookComments && activeCommentTab !== CommentPlatform.FACEBOOK && (
                            <View style={styles.facebookCount} />
                          )}
                          {hasNewInstagramComments && activeCommentTab !== CommentPlatform.INSTAGRAM && (
                            <View style={styles.facebookCount} />
                          )}
                        </>
                      )}
                  </View>
                </View>
              </View>
            </View>
          )}
        </>
      ) : (
        <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
      )}
    </>
  );
};

export default LiveStream;

const styles = StyleSheet.create({
  note: {
    backgroundColor: '#ecf5f8',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 6,
    gap: 10,
    alignSelf: 'baseline',
    marginVertical: 10,
    width: '100%',
    padding: 10,
    borderColor: '#3f9fba',
    borderWidth: 1,
  },
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
    justifyContent: 'space-between',
    gap: 25,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {
    width: '95%',
    marginBottom: 50,
    paddingVertical: 40,
  },
  infoText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    color: theme.TEXT_COLOR,
    lineHeight: 16,
    fontWeight: '500',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  streamsList: {
    marginVertical: 20,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  emptyStream: {
    height: 175,
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  section: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingVertical: 24,
    paddingHorizontal: 20,
    overflow: 'hidden',
  },
  commentSection: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    overflow: 'hidden',
    height: '49%',
  },
  commentsSection: {
    width: '30%',
    margin: 5,
    gap: 10,
    backgroundColor: '#fff',
    paddingVertical: 24,
    borderRadius: 8,
    paddingHorizontal: 20,
  },
  commentBox: {
    marginLeft: 6,
    flexDirection: 'column',
    flex: 1,
  },
  commentText: {
    flexWrap: 'wrap',
    width: '100%',
  },
  popOverContainer: {
    position: 'absolute',
    top: 20,
    right: 15,
    padding: 5,
    borderRadius: 10,
    borderWidth: 1,
    backgroundColor: 'white',
    borderColor: theme.INPUT_BORDER,
    zIndex: 10,
    elevation: 10,
  },
  ctaButton: {
    width: 45,
    height: 45,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
    borderWidth: 0.1,
    borderColor: '#aaa',
    backgroundColor: '#0005',
  },
  streamCountWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 5,
    borderRadius: 5,
    backgroundColor: '#0005',
    gap: 5,
  },
  labelText: {
    textAlign: 'center',
    width: 170,
    fontSize: 12,
    color: '#000',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginVertical: 12,
  },
  facebookCount: {
    borderRadius: 10,
    marginBottom: 10,
    position: 'absolute',
    top: 50,
    fontSize: 9,
    left: 146,
    color: '#FFF',
    backgroundColor: 'red',
    width: 15,
    height: 15,
  },
  revenueContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    paddingHorizontal: 18,
    paddingVertical: 16,
    marginBottom: 16,
  },
  revenueProgressBarContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  revenueAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  currentRevenue: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 26,
    fontWeight: '600',
    color: '#005BE4',
  },
});
