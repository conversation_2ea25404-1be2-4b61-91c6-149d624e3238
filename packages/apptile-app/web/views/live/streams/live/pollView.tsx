import { makeToast } from '@/root/web/actions/toastActions';
import { LivelyApi } from '@/root/web/api/LivelyApi';
import Button from '@/root/web/components-v2/base/Button';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';
import theme from '@/root/web/styles-v2/theme';
import { Icon } from 'apptile-core';
import { debounce } from 'lodash';
import moment from 'moment';
import React, { SetStateAction, useState } from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch } from 'react-redux';
import { zegoExpEngine } from '../../zegoEngine';

interface PollViewProps {
  polls: any;
  setPolls: React.Dispatch<SetStateAction<any>>;
  activePoll: any;
  setActivePoll: React.Dispatch<SetStateAction<any>>;
  setShowPolls: React.Dispatch<SetStateAction<boolean>>;
  expandedPolls: any;
  setExpandedPolls: React.Dispatch<SetStateAction<any>>;
  pollsLoading: boolean;
  setPollsUpdated: React.Dispatch<SetStateAction<boolean>>;
  authToken: string;
  streamId: string;
  roomId: string;
  viewerMultiplier: number;
  activePollRef: any;
  showCreatePoll: boolean;
  setShowCreatePoll: React.Dispatch<SetStateAction<boolean>>;
}
enum POLLSTATUS {
  ACTIVE = 'active',
  NEW = 'new',
  CLOSED = 'closed',
}

const PollView = ({
  polls, 
  activePoll, 
  setActivePoll, 
  setShowPolls,
  expandedPolls,
  setExpandedPolls,
  pollsLoading,
  setPollsUpdated,
  authToken, 
  streamId, 
  roomId,
  viewerMultiplier,
  activePollRef,
  showCreatePoll,
  setShowCreatePoll
} : PollViewProps) => {
  const dispatch = useDispatch();
  
  const [createPollBody, setCreatePollBody] = useState({
    question: '',
    options: [
      {
        optionId: 1,
        text: '',
      },
      {
        optionId: 2,
        text: '',
      },
    ],
  });
  
  /* CALLBACKS */
  const togglePollInfo = (poll: any) => {
    if(poll.status == POLLSTATUS.ACTIVE) return;
    setExpandedPolls(prev => ({
      ...prev,
      [poll.poll_uuid]: !prev[poll.poll_uuid],
    }));
  };
  const createPoll = async () => {
    try {
      const createdPoll = await LivelyApi.createPoll(authToken, {streaming_id: streamId, ...createPollBody});
      const startedPoll = await LivelyApi.startPoll(authToken, createdPoll?.data?.data?.poll_uuid);
      if(startedPoll?.data?.data?.poll_uuid) activePollRef.current = {
        poll_uuid: startedPoll?.data?.data?.poll_uuid
      }
      const startPollObj = {
        type: 'START_POLL',
        data: {
          poll: startedPoll?.data?.data
        }
      }
      zegoExpEngine.sendBroadcastMessage(roomId, JSON.stringify(startPollObj));
    } catch (error) {
      dispatch(
        makeToast({
          content: 'Something went wrong, please try again later!',
          appearances: 'error',
          duration: 2000,
        }),
      )
      console.error('Error while creating poll', error);
    } finally {
      setCreatePollBody({question: '', options: [{optionId: 1, text: ''}, {optionId: 2, text: ''}]});
      setShowCreatePoll(false);
      setPollsUpdated(true);
    }
  };
  const endPoll = async () => {
    if(activePoll) {
      try {
        await LivelyApi.endPoll(authToken, activePoll.poll_uuid);
        activePollRef.current = null;
        const endPollObj = { type: 'END_POLL' };
        zegoExpEngine.sendBroadcastMessage(roomId, JSON.stringify(endPollObj));
      } catch (error) {
        dispatch(
          makeToast({
            content: 'Something went wrong, please try again later!',
            appearances: 'error',
            duration: 2000,
          })
        )
      } finally {
        setActivePoll({});
        setPollsUpdated(true);
      }
    }
  }
  
  const debouncedOnChange = debounce((value: any, key: string) => {
    if (key == 'question') {
      setCreatePollBody(prev => ({
        ...prev,
        question: value,
      }));
    } else {
      setCreatePollBody(prev => ({
        ...prev,
        options: prev.options.map(option => (option.optionId === +key ? {...option, text: value} : option)),
      }));
    }
  }, 200);
  const addOption = () => {
    setCreatePollBody(prev => {
      return {
        ...prev,
        options: [
          ...prev.options,
          {
            optionId: prev.options.length + 1,
            text: '',
          },
        ],
      };
    });
  };
  const deleteOption = (key: number) => {
    setCreatePollBody(prev => {
      const updatedOptions = prev.options
        .filter(option => option.optionId !== key)
        .map((option, index) => ({
          ...option,
          optionId: index + 1,
        }));

      return {
        ...prev,
        options: updatedOptions,
      };
    });
  };

  if(pollsLoading) return <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
  </View>

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      {(!polls.length && !showCreatePoll) ? <NoPolls setShowCreatePoll={setShowCreatePoll} /> : (
        <View style={[styles.section, {width: '100%'}]}>
          {showCreatePoll && (
            <CreatePoll
              setShowCreatePoll={setShowCreatePoll}
              createPollBody={createPollBody}
              onValueChange={debouncedOnChange}
              onAddOption={addOption}
              onDeleteOption={deleteOption}
              onCreatePoll={createPoll}
            />
          )}
          {!!polls?.length && (
            <View style={[styles.column, {alignItems: 'flex-start', justifyContent: 'flex-start', gap: 10}]}>
              {polls.map((poll: any) => (
                <Pressable key={poll._id} style={[styles.pollBox, {width: '100%'}]} onPress={() => togglePollInfo(poll)}>
                  <Image
                    source={require('@/root/web/assets/images/apptile_icon.png')}
                    style={{height: 40, width: 40, borderRadius: 40}}
                  />

                  <View style={[styles.pollInfoBox, {flex: 1}]}>
                    <View style={[styles.row, {justifyContent: 'space-between', width: '100%', marginBottom: 8}]}>
                      <View style={[styles.row]}>
                        {poll.status == POLLSTATUS.ACTIVE && (
                          <View
                            style={[styles.pollStatus, {backgroundColor: '#D23030', marginRight: 10}]}>
                            <Text
                              style={[
                                styles.defaultFont,
                                {fontSize: 10, color: '#FFF'}
                              ]}>
                                Live
                            </Text>
                          </View>
                        )}
                        {poll.status == POLLSTATUS.CLOSED && (
                          <View
                            style={[styles.pollStatus, {backgroundColor: '#CECECE', marginRight: 10}]}>
                            <Text
                              style={[
                                styles.defaultFont,
                                {fontSize: 10, color: '#FFF'},
                              ]}>
                                Closed
                            </Text>
                          </View>

                        )}
                        <Text
                          style={[styles.defaultFont, {color: '#3C3C3C', fontSize: 10, fontWeight: '400'}]}>
                          {moment(poll.createdAt).fromNow()}
                        </Text>
                      </View>

                      {expandedPolls[poll.poll_uuid] ? (
                        <Icon name="chevron-thin-down" iconType="Entypo" size={15} color={'#898989'} />
                      ) : (
                        <Icon name="chevron-thin-up" iconType="Entypo" size={15} color={'#898989'} />
                      )}
                    </View>

                    <Text style={[styles.defaultFont]}>{poll.question}</Text>

                    {expandedPolls[poll.poll_uuid] && (
                      <View style={[styles.defaultColumn, {gap: 16, marginTop: 16, width: '100%'}]}>
                        {poll.options.map((option: any) => (
                          <ProgressBar key={option.optionId} option={option} poll={poll} activePoll={activePoll} />
                        ))}
                        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', marginTop: 6}}>
                          <Text style={[styles.defaultFont, {fontSize: 12, color: theme.CTA, fontWeight: '400'}]}>
                            {poll.poll_uuid == activePoll.poll_uuid ? (viewerMultiplier * activePoll?.pollResults?.total_votes) : poll?.total_votes} votes
                          </Text>

                          {poll.status !== POLLSTATUS.CLOSED && (
                            <Button onPress={endPoll} variant="OUTLINED" size="SMALL" containerStyles={{borderColor: theme.CTA, borderRadius: 40, paddingHorizontal: 14, paddingVertical: 3}} textStyles={{color: theme.CTA, fontWeight: '400', fontSize: 12}}>
                              Stop          
                            </Button>
                          )}
                        </View>
                      </View>
                    )}
                  </View>
                </Pressable>
              ))}
            </View>
          )}
      </View>
      )}
    </View>
  );
};

const CreatePoll = ({
  setShowCreatePoll,
  createPollBody,
  onValueChange,
  onAddOption,
  onDeleteOption,
  onCreatePoll,
}: {
  setShowCreatePoll: (value: boolean) => void;
  createPollBody: any;
  onValueChange: (id: string, key: string) => void;
  onAddOption: () => void;
  onDeleteOption: (key: number) => void;
  onCreatePoll: () => void;
}) => {
  const canCreateStream =
    createPollBody?.question &&
    createPollBody?.options?.length >= 2 &&
    createPollBody?.options?.every((option: any) => !!option?.text);
  return (
    <View style={[styles.defaultBorder, {padding: 20, marginBottom: 10}]}>
      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: "space-between"}}>
        <Text style={[styles.defaultFont]}>Create Poll</Text>
        <Pressable onPress={() => setShowCreatePoll(false)} style={{padding: 6}}>
          <Icon iconType="EvilIcons" name="close" size={16} color={'#535353'}/>
        </Pressable>
      </View>
      <View style={[styles.column, {marginTop: 12}]}>
        <CodeInputControlV2
          key="question"
          placeholder={'Question'}
          containerStyles={styles.inputContainerStyles}
          noOfLines={1}
          value={createPollBody?.question}
          onChange={value => onValueChange(value, 'question')}
        />

        <View
          style={[styles.column, {paddingLeft: 16, marginTop: 12, gap: 8, width: '100%', alignItems: 'flex-start'}]}>
          {createPollBody?.options?.map((option: any) => (
            <View key={option?.optionId} style={{position: 'relative', width: '100%'}}>
              <CodeInputControlV2
                key={option?.optionId}
                placeholder={`Option ${option?.optionId}`}
                containerStyles={styles.inputContainerStyles}
                noOfLines={1}
                value={option?.text}
                onChange={value => onValueChange(value, `${option?.optionId}`)}
              />

              {option?.optionId > 2 && (
                <Pressable
                  onPress={() => onDeleteOption(option?.optionId)}
                  style={{position: 'absolute', right: 6, top: 12, padding: 4}}>
                  <Icon iconType="EvilIcons" name="close" size={12} color={'#535353'} />
                </Pressable>
              )}
            </View>
          ))}
          {createPollBody?.options?.length < 4 && (
            <Pressable style={{marginTop: 2}} onPress={onAddOption}>
              <Text style={[styles.defaultFont, {fontSize: 12}]}>+ Add Option</Text>
            </Pressable>
          )}
        </View>

        <Button
          onPress={onCreatePoll}
          disabled={!canCreateStream}
          color="CTA"
          containerStyles={{marginTop: 16, width: '100%', borderRadius: 8}}>
          Share in stream
        </Button>
      </View>
    </View>
  );
};

const NoPolls = ({setShowCreatePoll}: {setShowCreatePoll: React.Dispatch<SetStateAction<boolean>>}) => {
  return (
    <View style={[styles.noPolls_wrapper]}>
      <View style={[styles.column, {gap: 10}]}>
        <Image style={{width: 105, height: 105}} source={require('@/root/web/assets/images/snapshot-no-result.png')} />
        <Text style={[styles.defaultFont, {textAlign: 'center'}]}>
          No polls created yet. Create a poll to engage your audience!
        </Text>
        <Button onPress={() => setShowCreatePoll(true)} color="CTA" containerStyles={{marginTop: 10}}>
          Create Poll
        </Button>
      </View>
    </View>
  );
};

const ProgressBar = ({option, poll, activePoll}: {option: any; poll: any, activePoll: any}) => {
  const calculatePercentage = (option: any) => poll?.total_votes > 0 ? (Math.round((option?.votes / poll?.total_votes) * 100).toFixed(2)) + '%' : '0%';
  return (
    <View key={option.optionId} style={styles.progressBar_container}>
      <View style={styles.progressBar_header}>
        <Text style={[styles.defaultFont, {color: '#3C3C3C', fontWeight: '400', fontSize: 12, marginRight: 20}]}>{option.text}</Text>
        <Text style={[styles.defaultFont, {color: '#3C3C3C', fontWeight: '400', fontSize: 12}]}>
          {poll.poll_uuid == activePoll.poll_uuid ? activePoll?.pollResults?.options?.find((o: any) => o.optionId == option.optionId)?.percentage || 0 : calculatePercentage(option)}
        </Text>
      </View>
      <View style={styles.progressBar_bar}>
        <View style={[styles.progressBar_fill, {width: poll.poll_uuid == activePoll.poll_uuid ? activePoll?.pollResults?.options?.find((o: any) => o.optionId == option.optionId)?.percentage : calculatePercentage(option)}]} />
      </View>
    </View>
  );
};

export default PollView;``

const styles = StyleSheet.create({
  section: {
    flex: 1,
    overflow: 'scroll',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultColumn: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  column: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  noPolls_wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultFont: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    fontWeight: '500',
  },
  defaultBorder: {
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    borderStyle: 'solid',
    borderRadius: 8,
  },
  inputContainerStyles: {
    borderRadius: 4,
  },
  pollBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 8,
    padding: 14,
  },
  pollInfoBox: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginLeft: 14,
  },
  pollStatus: {
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 10,
  },
  progressBar_container: {
    width: '100%', 
  },
  progressBar_header: {
    flexDirection: 'row', 
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6
  },
  progressBar_bar: {
    height: 6, 
    backgroundColor: '#F6F6F6', 
    borderRadius: 4, 
    overflow: 'hidden'
  },
  progressBar_fill: {
    height: '100%', 
    backgroundColor: theme.PRIMARY, 
    borderRadius: 4
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 14,
  },
});
