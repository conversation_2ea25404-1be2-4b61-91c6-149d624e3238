import commonStyles from '@/root/web/styles-v2/commonStyles';
import React, {useEffect} from 'react';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import {Icon} from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';
import Button from '@/root/web/components-v2/base/Button';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';
import {getDisplayOptions} from '@/root/app/plugins/state/ShopifyPDP/actions';
import _ from 'lodash';

export interface IProductCardProps {
  product: any;
  onProductDel?: (product: any) => void;
  fromLiveStream?: boolean;
  selectedProductId?: string | null;
  handleChangeActiveProduct: (product: any) => void;
  handleDeleteProduct: (product: any) => void;
  key: string;
  index: number;
}

export const ProductCard: React.FC<IProductCardProps> = ({
  product,
  onProductDel,
  fromLiveStream,
  selectedProductId,
  handleChangeActiveProduct,
  handleDeleteProduct,
  key,
  index,
}) => {
  const [selectedOptions, setSelectedOptions] = React.useState<any>({});
  const [displayOptions, setDisplayOptions] = React.useState<any>({});
  const [showExpandedView, setShowExpandedView] = React.useState<boolean>(index === 0);
  const displayOptionsRender = [];
  const defaultVariant = product.variants?.[0];

  useEffect(() => {
    if (product.variants?.length > 0) {
      const defaultOptions = {...defaultVariant?.variantOptions};
      setSelectedOptions(_.keyBy(defaultOptions, option => option?.name));
    }
  }, [product.id]);

  const getInventoryForSelectedOptions = () => {
    const selectedOptionsArray = Object.values(selectedOptions);
    const selectedVariant = product.variants?.find(variant => {
      return _.isEqual(selectedOptionsArray, variant.variantOptions);
    });
    return selectedVariant?.inventoryQuantity ?? 0;
  };

  const checkInventoryForGivenOptions = (options: any) => {
    const selectedOptionsArray = Object.values(options);
    const selectedVariant = product.variants?.find(variant => {
      return _.isEqual(selectedOptionsArray, variant.variantOptions);
    });
    return selectedVariant?.inventoryQuantity ?? 0;
  };

  useEffect(() => {
    setDisplayOptions(getDisplayOptions(product, selectedOptions));
  }, [selectedOptions]);

  if (!_.isEmpty(displayOptions)) {
    for (const key in displayOptions ?? {}) {
      const lastIndex = Object.keys(displayOptions).length - 1;
      const index = Object.keys(displayOptions).indexOf(key);
      const value = displayOptions[key];
      const labelRender = <Text style={[commonStyles.baseText, styles.sectionLabel]}>{key}</Text>;
      const valueRender = value?.map((option: any) => {
        const isSelected = selectedOptions[key]?.value === option;
        //The key will be static for the current loop
        const isInventoryZero =
          checkInventoryForGivenOptions({
            ...selectedOptions,
            [key]: {
              name: key,
              value: option,
            },
          }) === 0;

        const isCurrentIsLast = index === lastIndex;
        const isDisabled = isInventoryZero && isCurrentIsLast;

        return (
          <Pressable
            disabled={isDisabled}
            key={option}
            style={[
              styles.variantButton,
              isSelected && styles.variantSelectedButton,
              isDisabled && {backgroundColor: '#D9D9D9'},
            ]}
            onPress={() => {
              setSelectedOptions({
                ...selectedOptions,
                [key]: {
                  name: key,
                  value: option,
                },
              });
            }}>
            <Text
              style={[
                commonStyles.baseText,
                {fontSize: 11, fontWeight: '400', color: '#000'},
                isSelected && styles.variantSelectedText,
                isDisabled && {color: '#000'},
              ]}>
              {option}
            </Text>
          </Pressable>
        );
      });

      displayOptionsRender.push(
        <View key={key}>
          {labelRender}
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 5, flexWrap: 'wrap', marginTop: 5}}>
            {valueRender}
          </View>
        </View>,
      );
    }
  }

  const hasProductInventory = product.totalInventory && product.totalInventory > 0;
  const isOutOfStock = !showExpandedView ? !hasProductInventory : getInventoryForSelectedOptions() === 0;

  return (
    <View
      style={{
        padding: 10,
        borderWidth: 1,
        borderColor: '#D9D9D9',
        backgroundColor: '#FFF',
        borderRadius: 10,
      }}>
      <View style={[styles.productCardWrapper]} key={key}>
        <View
          style={[
            !fromLiveStream
              ? {width: 70, height: 53, overflow: 'hidden', borderRadius: 10}
              : {
                  width: 81,
                  height: 72,
                  overflow: 'hidden',
                  borderRadius: 10,
                },
          ]}>
          <Image
            source={{uri: product.image ?? product.product_thumbnail}}
            style={{width: '100%', height: '100%'}}
            resizeMode="cover"
          />
        </View>
        <View style={{flex: 1}}>
          <Pressable
            style={{
              position: 'absolute',
              right: 0,
              top: -5,
              borderRadius: 5,
              backgroundColor: '#FFF',
              zIndex: 1000,
            }}
            onPress={() => setShowExpandedView(!showExpandedView)}>
            {showExpandedView ? (
              <Icon name="chevron-up" size={20} color="#000" iconType="MaterialCommunityIcons" />
            ) : (
              <Icon name="chevron-down" size={20} color="#000" iconType="MaterialCommunityIcons" />
            )}
          </Pressable>
          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 20}}>
            <View style={{flex: 1}}>
              <Text numberOfLines={1} style={[commonStyles.baseText, styles.labelText]}>
                {product.title ?? product.product_name}
              </Text>
              <View style={[commonStyles.baseText, styles.claimCodePriceTextContainer]}>
                < Text style={[commonStyles.baseText, styles.claimCode]}>Claim code : {product.sell_id}</Text>
                <Text style={[commonStyles.baseText, styles.priceText]}>
                  Price : {product.price} {product.currencyCode}
                </Text>
              </View>
              <View style={styles.inventoryWrapper}>
                {!isOutOfStock ? (
                  <>
                    {!showExpandedView && (
                      <Text numberOfLines={1} style={[commonStyles.baseText, styles.inventoryLabel]}>
                        In stock:
                      </Text>
                    )}
                    {showExpandedView && (
                      <Text numberOfLines={1} style={[commonStyles.baseText, styles.inventoryLabel]}>
                        Selected stock:
                      </Text>
                    )}
                    <Text numberOfLines={1} style={[commonStyles.baseText, styles.inventoryQuantity]}>
                      {!showExpandedView ? product.totalInventory : getInventoryForSelectedOptions()} products
                    </Text>
                  </>
                ) : (
                  <Text style={[commonStyles.baseText, styles.inventoryLabel, {color: '#D92626'}]}>Out of stock</Text>
                )}
              </View>
            </View>

            {fromLiveStream && selectedProductId == product.product_id ? (
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Icon
                  iconType={'MaterialIcons'}
                  name={'circle'}
                  size={11}
                  color={theme.SUCCESS_BACKGROUND}
                  style={{marginRight: 4}}
                />
                <Text style={[commonStyles.baseText, {color: '#000', fontSize: 12, fontWeight: '500'}]}>Live</Text>
              </View>
            ) : (
              <Button
                size={'EXTRA-SMALL'}
                containerStyles={{width: 51}}
                variant={'PILL'}
                disabled={selectedProductId == product.product_id}
                color={'PRIMARY'}
                onPress={() => {
                  setShowExpandedView(true);
                  handleChangeActiveProduct(product);
                }}>
                Show
              </Button>
            )}
          </View>
        </View>
      </View>
      {showExpandedView && (
        <View>
          {displayOptionsRender.map(option => option)}
          <View style={{position: 'absolute', right: 0, bottom: 10}}>
            {!fromLiveStream && (
              <Pressable onPress={() => onProductDel(product)}>
                <Icon name="delete-outline" size={20} color="#595959" iconType="MaterialCommunityIcons" />
              </Pressable>
            )}
            {selectedProductId !== product?.product_id && (
              <Tooltip tooltip="Delete Product" position="left" toolTipMenuStyles={{width: 60, height: 30, right: 30}}>
                <Pressable style={[]} onPress={() => handleDeleteProduct(product)}>
                  <Icon color={'#595959'} iconType="MaterialIcons" name="delete" size={17} />
                </Pressable>
              </Tooltip>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  labelText: {
    fontSize: 12,
    color: '#000',
    fontWeight: '600',
  },
  priceText: {color: '#000', fontSize: 12, fontWeight: '500', marginTop: 10, width: '50%'},
  claimCode: {color: '#000', fontSize: 12, fontWeight: '500', marginTop: 10, width: '50%'},
  claimCodePriceTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inventoryWrapper: {
    flexDirection: 'row',
    gap: 5,
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 10,
  },
  inventoryLabel: {
    color: '#6B6B6B',
    fontSize: 12,
    fontWeight: '500',
  },
  inventoryQuantity: {
    color: '#6B6B6B',
    fontSize: 12,
    fontWeight: '500',
  },
  productCardWrapper: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'space-between',
  },
  sectionLabel: {color: '#666', fontSize: 11, fontWeight: '600', marginTop: 8},
  variantButton: {borderRadius: 17, borderWidth: 0.5, borderColor: '#000', paddingHorizontal: 8, paddingVertical: 4},
  variantSelectedButton: {
    backgroundColor: '#000',
  },
  variantSelectedText: {
    color: '#FFF',
  },
});
