import commonStyles from '@/root/web/styles-v2/commonStyles';
import React from 'react';
import {StyleSheet, Text} from 'react-native';
import {Image, View} from 'react-native';

export const CustomerViews: React.FC<{currentSelectedProduct: any; requiredView: number; fromAuction?: boolean}> = ({
  currentSelectedProduct,
  requiredView,
  fromAuction,
}) => {
  const viewNew = () => (
    <View style={styles.customerProductCardNew}>
      <Image source={{uri: currentSelectedProduct?.product_thumbnail}} style={{width: 70, height: 70}} />
      <View style={{padding: 10, flex: 1}}>
        <Text numberOfLines={2} style={[commonStyles.baseText, styles.customerLabelText]}>
          {currentSelectedProduct?.product_name}
        </Text>
        <Text style={[commonStyles.baseText, {color: '#FFF', fontSize: 12, fontWeight: '400'}]}>
          {fromAuction ? 
           `Starting Bid: ${currentSelectedProduct?.base_bid_price} ${currentSelectedProduct?.currencyCode}` : 
           `${currentSelectedProduct?.price} ${currentSelectedProduct?.currencyCode}`
          }
        </Text>
      </View>
      <View style={styles.customerAddButtonNew}>
        <Text style={[commonStyles.baseText, {color: '#000', fontSize: 12, fontWeight: '500'}]}>ADD</Text>
      </View>
    </View>
  );

  const viewOne = () => (
    <View style={styles.customerProductCard}>
      <Image source={{uri: currentSelectedProduct?.product_thumbnail}} style={{width: 60, height: 60}} />
      <View style={{padding: 10, flex: 1}}>
        <Text numberOfLines={2} style={[commonStyles.baseText, styles.customerLabelText]}>
          {currentSelectedProduct?.product_name}
        </Text>
        <Text style={[commonStyles.baseText, {color: '#FFF', fontSize: 12, fontWeight: '400'}]}>
          {currentSelectedProduct?.price} {currentSelectedProduct?.currencyCode}
        </Text>
      </View>
      <View style={styles.customerAddButton}>
        <Text style={[commonStyles.baseText, {color: '#FFF', fontSize: 12, fontWeight: '500'}]}>ADD</Text>
      </View>
    </View>
  );


  const viewTwo = () => (
    <View style={styles.customerProductCardV2}>
      <Image
        source={{uri: currentSelectedProduct?.product_thumbnail}}
        style={{width: '100%', height: 100, borderRadius: 10}}
      />
      <Text style={[commonStyles.baseText, styles.customerLabelText, {marginTop: 5}]} numberOfLines={2}>
        {currentSelectedProduct?.product_name}
      </Text>
      <Text style={[commonStyles.baseText, {color: '#FFF', fontSize: 12, fontWeight: '400'}]}>
        {currentSelectedProduct?.price} {currentSelectedProduct?.currencyCode}
      </Text>
      <View style={[styles.customerAddButtonV2]}>
        <Text style={[commonStyles.baseText, {color: '#FFF', fontSize: 12, fontWeight: '500'}]}>Add to cart</Text>
      </View>
    </View>
  );

  const resultantView = () => {
    switch (requiredView) {
      case 1:
        return viewNew();
      case 2:
        return viewOne();
      case 3:
        return viewTwo();
      default:
        return viewNew();
    }
  };

  return resultantView();
};

const styles = StyleSheet.create({
  customerProductCardNew: {
    width: '-webkit-fill-available',
    marginRight: 12,
    marginLeft: 22,
    height: 70,
    borderRadius: 12,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    top: 70,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    position: 'absolute',
  },
  customerProductCard: {
    width: 300,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#A5A5A5',
  },
  customerAddButtonNew: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#000',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  customerAddButton: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#A5A5A5',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  customerAddButtonV2: {
    height: 35,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 5,
    backgroundColor: '#000',
  },
  customerLabelText: {
    fontSize: 12,
    color: '#FFF',
    overflow: 'hidden',
    fontWeight: '600',
    marginBottom: 5,
  },
  customerProductCardV2: {
    width: 150,
    borderRadius: 10,
    backgroundColor: '#0005',
    justifyContent: 'center',
    marginTop: 10,
    padding: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ffffff99',
  },
});
