import React, {useRef, useEffect} from 'react';
import {View, TextInput, StyleSheet} from 'react-native';
import moment from 'moment';
import {Icon} from 'apptile-core';
import commonStyles from '../../../../styles-v2/commonStyles';

interface CounterProps {
  startTime?: string;
}

const Counter: React.FC<CounterProps> = ({startTime}) => {
  const textRef = useRef();
  useEffect(() => {
    const interval = setInterval(() => {
      if (textRef.current) {
        const now = moment();
        const duration = moment.duration(now.diff(startTime));
        const minutes = String(duration.minutes()).padStart(2, '0');
        const seconds = String(duration.seconds()).padStart(2, '0');
        const hours = String(Math.floor(duration.asHours())).padStart(2, '0');
        const time = `${hours}:${minutes}:${seconds}`;
        textRef.current.value = time;
      }
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, [textRef.current]);

  return (
    <View style={styles.container}>
      <Icon iconType={'MaterialCommunityIcons'} color={'#FFF'} name={'clock-outline'} size={15} />
      <TextInput ref={textRef} style={[commonStyles.baseText, {fontSize: 15, color: '#FFF', width: 65}]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    padding: 5,
    borderRadius: 5,
    backgroundColor: '#0005',
    gap: 5,
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    marginBottom: 20,
    width: '80%',
    paddingHorizontal: 10,
  },
  counterText: {
    fontSize: 48,
    fontWeight: 'bold',
  },
});

export default Counter;
