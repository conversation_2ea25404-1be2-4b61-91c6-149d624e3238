import { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { EditorRootState } from "../../../store/EditorRootState";
import { LivelyApi } from "@/root/web/api/LivelyApi";
import apolloQueryRunner from "@/root/app/plugins/datasource/ApolloWrapper/model";
import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import { shopifyProductTransformer } from "../utils/transformers";
import { COMMON_ERROR_MESSAGE } from "@/root/app/common/utils/apiErrorMessages/generalMessages";
import { handleApiError } from "../shared/CommonError";
import { processShopifyGraphqlQueryResponse } from "@/root/app/plugins/datasource/utils";
import { TransformGetProductsPaginatedQuery } from "@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer";

export const useShopify = () => {

  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const dispatch = useDispatch();

  const shopifyQueryRunner = useRef<any>(null);

  useEffect(() => {
    authToken &&
      LivelyApi.getShopifyCreds(authToken)
        .then(response => {
          const newQueryRunner = apolloQueryRunner();
          newQueryRunner
            .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
              return {
                headers: {
                  ...headers,
                  'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                },
              };
            })
            .then(() => {
              shopifyQueryRunner.current = newQueryRunner;
            });
        })
        .catch(error => {
          console.log(error);
        });
  }, [authToken, dispatch]);

  const getProductsInfoByIds = async (productIds: string[], place: string) => {
    if(!shopifyQueryRunner.current) return;
    try {
      const queryResponse = await shopifyQueryRunner.current?.runQuery(
        'query',
        ProductGqls.GET_PRODUCT_BY_IDS, 
        { productIds }
      )
      const productMetadata = queryResponse?.data?.nodes?.map((product: any) => {
        return {
          ...shopifyProductTransformer(product),
          store_product_id: product?.id.replace('gid://shopify/Product/', ''),
        };
      });
      return productMetadata;
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, place);
    }
  }

  const searchProductsForDropDown = async (barcode: string) => {
    if(!shopifyQueryRunner.current) return;
    try {
      const queryResponse = await shopifyQueryRunner.current?.runQuery(
        'query',
        ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN, 
        { 
          first: 50,
          query: barcode,
          countryCode: 'US'
        }
      )
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        {},
        null
      )
      return transformedData;
    } catch (error) {
      handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'searchProductsForDropDown');
    }
  }

  return {
    shopifyQueryRunner: shopifyQueryRunner.current,
    getProductsInfoByIds,
    searchProductsForDropDown,
  };
};