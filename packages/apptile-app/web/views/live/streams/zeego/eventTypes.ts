export enum ZegoWebEvent {
  MULTIPLIER_CHANGE = 'MULTIPLIER_CHANGE',
  CHANGE_MULTIPLIER_EVENT = 'CHANGE_MULTIPLIER_EVENT',

  USER_JOIN = 'USER_JOIN',

  REACTION_ANNOUCEMENT = 'REACTION_ANNOUCEMENT',


  MUTE_USER = 'MUTE_USER',
  UNMUTE_USER = 'UNMUTE_USER',
  REQUEST_MUTED_USERS = 'REQUEST_MUTED_USERS',

  SHOW_PRODUCT_EVENT = 'SHOW_PRODUCT_EVENT',
  PRODUCT_CHANGE = 'PRODUCT_CHANGE',

  DELETE_PRODUCT = 'DELETE_PRODUCT',
  DELETE_PRODUCT_EVENT = 'DELETE_PRODUCT_EVENT',

  ADD_NEW_PRODUCT = 'ADD_NEW_PRODUCT',

  /* Auction Events */
  START_BIDDING = 'START_BIDDING',
  CANCEL_BIDDING = 'CANCEL_BIDDING',
  CLOSE_BIDDING = 'CLOSE_BIDDING',
  START_BIDDING_REQUEST = 'START_BIDDING_REQUEST',
  CANCEL_BIDDING_REQUEST = 'CANCEL_BIDDING_REQUEST',
  BID_PLACED = 'BID_PLACED',
  BROADCAST_BID_INFO = 'BROADCAST_BID_INFO',
  BID_WINNER_ANNOUNCEMENT = 'BID_WINNER_ANNOUNCEMENT',
  ADD_BID_COMMENT = 'ADD_BID_COMMENT',
}

export enum BidEvent {
  CLOSE = 'CLOSE',
  CANCEL = 'CANCEL',
}