import { zegoExpEngine } from "../../zegoEngine";
import ZegoLocalStream from "zego-express-engine-webrtc/sdk/code/zh/ZegoLocalStream.web";
import { ZegoStreamOptions } from "zego-express-engine-webrtc/sdk/src/common/zego.entity";

const DEFAULT_STREAM_OPTIONS: ZegoStreamOptions = {
  audioBitrate: 48,
  videoBitrate: 1500,
  camera: {
    audio: true,
    video: {
      quality: 4,
      width: 720,
      height: 1280,
      frameRate: 30,
    },
  }
};
const DEFAULT_VIDEO_CONFIG = {
  width: 720,
  height: 1280,
  frameRate: 20,
};

export const ZEEGO_CUSTOM_HANDLERS = {
  toggleCamera: (localStream: ZegoLocalStream, video: boolean) => {
    if (localStream) {
      if (!video) localStream.stopCaptureVideo();
      zegoExpEngine.enableVideoCaptureDevice(localStream, video);
      zegoExpEngine.mutePublishStreamVideo(localStream, !video);
    }
  },
  toggleMicrophone: (localStream: ZegoLocalStream, mute: boolean) => {
    if (localStream) {
      zegoExpEngine.muteMicrophone(mute);
      zegoExpEngine.mutePublishStreamAudio(localStream, mute);
    }
  },
  endCall: (localStream: ZegoLocalStream, streamId: string, roomId: string) => {
    if (localStream) {
      zegoExpEngine.destroyStream(localStream);
    }
    zegoExpEngine.stopPublishingStream(streamId);
    zegoExpEngine.logoutRoom(roomId);
    zegoExpEngine.destroyEngine();
  },
  unMount: (localStream: ZegoLocalStream, streamId: string, roomId: string) => {
    if (localStream) localStream.stopVideo();
    zegoExpEngine.stopPublishingStream(streamId);
    zegoExpEngine.destroyStream(localStream);
    zegoExpEngine.off('roomStreamUpdate');
    zegoExpEngine.off('streamExtraInfoUpdate');
    zegoExpEngine.off('IMRecvBarrageMessage');
    zegoExpEngine.off('IMRecvBroadcastMessage');
    zegoExpEngine.off('IMRecvCustomCommand');
    zegoExpEngine.off('roomOnlineUserCountUpdate');
    zegoExpEngine.off('roomUserUpdate');
    zegoExpEngine.logoutRoom(roomId);
  },
  playLocalStream: async (streamId: string) => {
    try {
      const localStream = await zegoExpEngine.createZegoStream(DEFAULT_STREAM_OPTIONS);
      zegoExpEngine.setVideoConfig(localStream, DEFAULT_VIDEO_CONFIG);
      const videoElement = document.querySelector('#local-video') as HTMLElement;
      if (videoElement) {
        localStream.playVideo(videoElement, {
          enableAutoplayDialog: true,
          mirror: false,
        });
      }
      zegoExpEngine.startPublishingStream(streamId, localStream);
      setTimeout(() => {
        zegoExpEngine.muteMicrophone(true);
        localStream.stopCaptureVideo();
        zegoExpEngine.enableVideoCaptureDevice(localStream, false);
      }, 300);
      return localStream;
    } catch (error) {
      console.error('Error playing local stream:', error);
    }
  },
  playModeratorStream: async (streamId: string) => {
    try {
      const remoteStreamInitial = await zegoExpEngine.startPlayingStream(streamId);
      const remoteView = zegoExpEngine.createRemoteStreamView(remoteStreamInitial);

      const videoElement = document.getElementById('local-video');
      if (videoElement) {
        remoteView.play('local-video');
      }
    } catch (error) {
      console.error('Error playing moderator stream:', error);
    }
  }
}