import { Animated } from 'react-native';
import { NavigateFunction } from 'react-router';

type AnimationType = 'translateX' | 'translateY' | 'opacity' | 'scale' | 'rotate';

interface IndividualAnimationOption {
  value?: Animated.Value;
  toValue?: number | string;
  duration?: number;
}

interface AnimatedNavigationOptions {
  animations: Partial<Record<AnimationType, IndividualAnimationOption>>;
  onAnimationEnd?: () => void;
}

export const animatedNavigate = (
  options: AnimatedNavigationOptions,
  navigate?: NavigateFunction,
  url?: string,
) => {
  const {
    animations,
    onAnimationEnd,
  } = options;

  const animatedTimings: Animated.CompositeAnimation[] = [];

  Object.entries(animations).forEach(([key, config]) => {
    const type = key as AnimationType;
    const {
      value = new Animated.Value(
        type === 'opacity' ? 1 : type === 'scale' ? 1 : 0
      ),
      toValue = type === 'opacity' ? 0 : 100,
      duration = 300,
    } = config;

    if (type === 'rotate') {
      const rotateAnim = value as Animated.Value;
      animatedTimings.push(
        Animated.timing(rotateAnim, {
          toValue: typeof toValue === 'number' ? toValue : 1,
          duration,
          useNativeDriver: true,
        })
      );
    } else {
      animatedTimings.push(
        Animated.timing(value as Animated.Value, {
          toValue: typeof toValue === 'number' ? toValue : 0,
          duration,
          useNativeDriver: true,
        })
      );
    }
  });

  Animated.parallel(animatedTimings).start(() => {
    if (navigate && url) {
      navigate(url);
    }
    if (onAnimationEnd) {
      onAnimationEnd();
    }
  });
};