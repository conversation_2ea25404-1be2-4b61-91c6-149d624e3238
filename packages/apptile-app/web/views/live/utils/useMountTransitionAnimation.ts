import { useEffect, useRef, useState, useCallback } from 'react';
import { Animated, StyleProp, ViewStyle } from 'react-native';

interface UseMountTransitionAnimationProps {
  direction: 'x' | 'y';
  distance?: number; // px
  duration?: number;
  onAnimationEnd?: () => void;
}

/**
 * Provides mount/unmount animation styles and controls for fade + translate in x or y direction.
 *
 * Usage:
 *   const { animatedStyle, triggerUnmount, isAnimating } = useMountTransitionAnimation({ direction: 'x' });
 *   <Animated.View style={animatedStyle}>...</Animated.View>
 * 
 *   To animate out before unmount:
 *   triggerUnmount();
 */
export function useMountTransitionAnimation({
  direction,
  distance = 100,
  duration = 300,
  onAnimationEnd,
}: UseMountTransitionAnimationProps) {
  const [isMounted, setIsMounted] = useState(true); // For controlled unmount
  const [isAnimating, setIsAnimating] = useState(false);

  // Animated values
  const opacity = useRef(new Animated.Value(0)).current;
  const translate = useRef(new Animated.Value(direction === 'x' ? distance : distance)).current;

  // Animate in on mount
  useEffect(() => {
    setIsAnimating(true);
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      }),
      Animated.timing(translate, {
        toValue: 0,
        duration,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsAnimating(false);
    });
  }, []);

  const triggerUnmount = useCallback(() => {
    setIsAnimating(true);
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration,
        useNativeDriver: true,
      }),
      Animated.timing(translate, {
        toValue: direction === 'x' ? -distance : -distance,
        duration,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsAnimating(false);
      setIsMounted(false);
      if (onAnimationEnd) onAnimationEnd();
    });
  }, [direction, distance, duration, onAnimationEnd, opacity, translate]);

  const animatedStyle: StyleProp<ViewStyle> = {
    opacity,
    transform: [
      direction === 'x'
        ? { translateX: translate }
        : { translateY: translate },
    ],
  };

  return {
    animatedStyle,
    triggerUnmount,
    isMounted,
    isAnimating,
  };
}
