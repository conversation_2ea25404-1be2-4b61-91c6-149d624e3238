import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Linking} from 'react-native';

const PlanUpgradeBanner = () => {
  const handleUpgradeClick = () => {
    Linking.openURL('http://apptile.io/pricing');
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={handleUpgradeClick}
        accessibilityRole="link"
        // @ts-ignore - These props only work on web
        target="_blank"
        rel="noopener noreferrer">
        <Text style={styles.text}>Select a Plan →</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#dbe7ff', // Light blue background
    paddingVertical: 11,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    color: '#0a5fff', // Bright blue text
    fontSize: 15,
    fontWeight: '500',
  },
});

export default PlanUpgradeBanner;
