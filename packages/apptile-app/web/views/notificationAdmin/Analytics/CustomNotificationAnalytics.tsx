import TextElement from '@/root/web/components-v2/base/TextElement';
import React, {FC, useCallback, useEffect, useState} from 'react';
import {Icon, MaterialCommunityIcons} from 'apptile-core';
import {Image, Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {useNavigate, useParams} from '@/root/web/routing.web';
import theme from '@/root/web/styles-v2/theme';
import {IPlatformDeliveryStats, oneSignalGetRecordTransformer} from '../shared/oneSignalTransformer';
import {OneSignalPushNotificationApi} from '@/root/web/api/OneSignalPushNotificationApi';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {OutcomeTimeRangeEnum} from '@/root/web/api/ApiTypes';
import {CampaignCard} from '../shared';
import _, {get, keyBy} from 'lodash';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';

const deliveryStatusMapping = {
  successful: {
    title: 'Delivered',
    description: 'Number of notifications delivered to the users.',
  },
  // received: {title: 'Received', description: 'The number of devices that successfully received the notification.'},
  failed: {title: 'Unsubscribed', description: 'The number of devices that unsubscribed from push notifications.'},
  converted: {title: 'Clicks', description: 'Number of devices that have clicked/tapped the notification.'},
};

const outcomeDescriptionMapping = {
  Click: 'Tracks total number of clicks/opens (IOS + Android).',
  'Session Duration': 'Cumulative session time of all sessions resulting from a push notification.',
  Purchase: 'Total Sales value',
};

const outcomesFilterOption = [
  {
    value: OutcomeTimeRangeEnum.THIRTY_DAYS,
    label: '30 Days',
  },
  {
    value: OutcomeTimeRangeEnum.TWENTY_FOUR_HOUR,
    label: '24 Hours',
  },
  {
    value: OutcomeTimeRangeEnum.SIXTY_MINUTES,
    label: '60 Mins',
  },
];

const DelievryStatsFilterOption = [
  {
    value: 'ANDROID',
    label: 'Android',
  },
  {
    value: 'IOS',
    label: 'IOS',
  },
];

const CustomNotificationAnalytics = () => {
  const navigate = useNavigate();
  const param = useParams();
  const [loading, setLoading] = useState(true);
  const [platformDeliveryStats, setPlatformDeliveryStats] = useState<any>(null);

  const [outcomes, setOutcomes] = useState<any>(null);
  const [currentNotification, setCurrentNotification] = useState();
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const appID = param.id as string;
  const notificationId = param.notificationId as string;
  const [outcomePlatform, setOutcomePlatform] = useState('ANDROID');
  const [outcomeTime, setOutcomeTime] = useState(OutcomeTimeRangeEnum.THIRTY_DAYS);

  const callGetNotificationApi = useCallback(
    async timeRange => {
      const notificationData = await OneSignalPushNotificationApi.getNotification(
        oneSignalAppId,
        notificationId,
        appID,
        timeRange,
      );
      if (notificationData.status >= 200 && notificationData.status <= 299) {
        const transformed = oneSignalGetRecordTransformer(notificationData?.data);
        setPlatformDeliveryStats(transformed.platformDeliveryStats);
        setOutcomes(transformed.outcomes);
        setCurrentNotification(transformed);
        setLoading(false);
      } else {
        setLoading(false);
        throw new Error();
      }
    },
    [appID, notificationId, oneSignalAppId, outcomeTime],
  );

  useEffect(() => {
    if (appID && notificationId && oneSignalAppId) {
      callGetNotificationApi(outcomeTime);
    }
  }, [appID, callGetNotificationApi, notificationId, oneSignalAppId, outcomeTime]);

  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  const fetchconfig = async () => {
    const response = await BuildManagerApi.getActiveAppAsset(appID);
    if (response.data) {
      const mappedResponse = keyBy(response.data, 'assetClass');
      setLogoUrl(get(mappedResponse, ['icon', 'url'], ''));
    }
  };

  useEffect(() => {
    if (appID) {
      fetchconfig();
    }
  }, [appID]);

  const androidDeliveryStats: IPlatformDeliveryStats = platformDeliveryStats?.android;
  const iosDeliveryStatus: IPlatformDeliveryStats = platformDeliveryStats?.ios;
  return (
    <View style={[styles.container]}>
      <ScrollView contentContainerStyle={styles.wrapper}>
        <View style={styles.contentContainer}>
          <Pressable style={styles.backButtonContainer} onPress={() => navigate(-1)}>
            <MaterialCommunityIcons name="chevron-left" size={20} color={'#000000'} />
            <TextElement fontWeight="500" style={styles.backButton}>
              Back
            </TextElement>
          </Pressable>
          <View style={styles.headerContainer}>
            <View style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter]}>
              <TextElement
                color="SECONDARY"
                fontWeight="600"
                style={{...styles.fontFamily, fontSize: 28, lineHeight: 36}}>
                Analytics
              </TextElement>
            </View>
          </View>
          <View
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: loading ? 'center' : 'flex-start',
              minHeight: loading && 700,
            }}>
            {loading ? (
              <Image
                source={require('@/root/web/assets/images/preloader.svg')}
                style={{width: 100, height: 100, display: 'flex', alignSelf: 'center'}}
              />
            ) : (
              <View style={styles.bottomContainer}>
                {/* Left - Campaign Details */}
                <View style={styles.leftPanel}>
                  <View style={(styles.headerRow, {height: 30, placeContent: 'center'})}>
                    <TextElement style={styles.heading}>Campaign Details</TextElement>
                  </View>

                  <View style={styles.cardWrapper}>
                    <CampaignCard
                      logoUrl={logoUrl}
                      record={currentNotification}
                      // record={{title: '', body: '', imageUrl: ''}}
                      isShowAll={false}
                      isScheduled={false}
                      isAutomated={false}
                      containerStyles={{width: '100%', padding: 0}}
                    />
                  </View>
                </View>

                <View style={{height: '100%', borderWidth: 1, borderColor: '#E5E5E5'}}></View>

                {/* Right - Analytics Section */}
                <View style={styles.rightPanel}>
                  {/* Outcomes */}
                  <View style={styles.headerRow}>
                    <TextElement style={styles.heading}>Outcomes</TextElement>
                    <View style={styles.segmentControl}>
                      {outcomesFilterOption.map((o, index) => {
                        return (
                          <Pressable
                            key={index}
                            onPress={() => {
                              setOutcomeTime(o.value);
                              setOutcomes(null);
                              callGetNotificationApi(o.value);
                            }}>
                            <TextElement
                              key={index}
                              style={[styles.segment, o.value === outcomeTime && styles.segmentActive]}>
                              {o.label}
                            </TextElement>
                          </Pressable>
                        );
                      })}
                    </View>
                  </View>

                  <View style={styles.cardRow}>
                    {outcomes ? (
                      <>
                        {outcomes.map((outcome: any, index: number) => (
                          <React.Fragment key={index}>
                            <View style={styles.statCard}>
                              <View style={{display: 'flex', flexDirection: 'row', alignContent: 'center'}}>
                                <TextElement style={styles.cardLabel}>
                                  {outcome?.title}
                                  <Tooltip
                                    visible={true}
                                    tooltip={outcomeDescriptionMapping[outcome?.id]}
                                    position="top"
                                    containerStyles={{marginLeft: 5}}
                                    toolTipMenuStyles={{width: 200, paddingHorizontal: 5, paddingVertical: 5}}>
                                    <Icon
                                      color="grey"
                                      iconType="MaterialCommunityIcons"
                                      name="information-outline"
                                      size={12}
                                      style={{alignSelf: 'center'}}
                                    />
                                  </Tooltip>
                                </TextElement>
                              </View>
                              <TextElement style={styles.cardValue}>{outcome?.value}</TextElement>
                            </View>
                            {outcomes.length - 1 !== index && <View style={styles.seperator}></View>}
                          </React.Fragment>
                        ))}
                      </>
                    ) : (
                      <Image
                        source={require('@/root/web/assets/images/preloader.svg')}
                        style={{width: 40, height: 40}}
                      />
                    )}
                    {/*     description={outcomeDescriptionMapping[outcome?.id]} */}
                  </View>

                  {/* Delivery Stats */}
                  <View style={[styles.headerRow, {paddingTop: 25}]}>
                    <TextElement style={styles.heading}>Delivery Stats</TextElement>
                    <View style={styles.segmentControl}>
                      {DelievryStatsFilterOption.map((o, index) => {
                        return (
                          <Pressable key={index} onPress={() => setOutcomePlatform(o.value)}>
                            <TextElement
                              key={index}
                              style={[styles.segment, o.value === outcomePlatform && styles.segmentActive]}>
                              {o.label}
                            </TextElement>
                          </Pressable>
                        );
                      })}
                    </View>
                  </View>

                  <View style={styles.cardRow}>
                    {outcomePlatform === 'ANDROID' && (
                      <>
                        {androidDeliveryStats ? (
                          _.map(Object.keys(deliveryStatusMapping), (data, index) => (
                            <React.Fragment key={index}>
                              <Pressable onPress={() => {}} style={styles.statCard}>
                                <View style={{display: 'flex', flexDirection: 'row', alignContent: 'center'}}>
                                  <TextElement style={styles.cardLabel}>
                                    {deliveryStatusMapping[data]?.title}
                                    <Tooltip
                                      visible={true}
                                      tooltip={deliveryStatusMapping[data]?.description}
                                      position="top"
                                      containerStyles={{marginLeft: 5}}
                                      toolTipMenuStyles={{width: 200, paddingHorizontal: 5, paddingVertical: 5}}>
                                      <Icon
                                        color="grey"
                                        iconType="MaterialCommunityIcons"
                                        name="information-outline"
                                        size={12}
                                        style={{alignSelf: 'center'}}
                                      />
                                    </Tooltip>
                                  </TextElement>
                                </View>
                                <TextElement style={styles.cardValue}>{androidDeliveryStats[data]}</TextElement>
                              </Pressable>
                              {Object.keys(deliveryStatusMapping).length - 1 !== index && (
                                <View style={styles.seperator}></View>
                              )}
                            </React.Fragment>
                          ))
                        ) : (
                          <TextElement color="SECONDARY" fontSize="lg" style={styles.marginTop10}>
                            No data available!
                          </TextElement>
                        )}
                      </>
                    )}
                    {/* // description={deliveryStatusMapping[data]?.description} */}

                    {outcomePlatform === 'IOS' && (
                      <>
                        {iosDeliveryStatus ? (
                          _.map(Object.keys(deliveryStatusMapping), (data, index) => (
                            <React.Fragment key={index}>
                              <View style={styles.statCard}>
                                <View style={{display: 'flex', flexDirection: 'row', alignContent: 'center'}}>
                                  <TextElement style={styles.cardLabel}>
                                    {deliveryStatusMapping[data]?.title}
                                    <Tooltip
                                      visible={true}
                                      tooltip={deliveryStatusMapping[data]?.description}
                                      position="top"
                                      containerStyles={{marginLeft: 5}}
                                      toolTipMenuStyles={{width: 200, paddingHorizontal: 5, paddingVertical: 5}}>
                                      <Icon
                                        color="grey"
                                        iconType="MaterialCommunityIcons"
                                        name="information-outline"
                                        size={12}
                                        style={{alignSelf: 'center'}}
                                      />
                                    </Tooltip>
                                  </TextElement>
                                </View>
                                <TextElement style={styles.cardValue}>{iosDeliveryStatus[data]}</TextElement>
                              </View>
                              {Object.keys(deliveryStatusMapping).length - 1 !== index && (
                                <View style={styles.seperator}></View>
                              )}
                            </React.Fragment>
                          ))
                        ) : (
                          <TextElement color="SECONDARY" fontSize="lg" style={styles.marginTop10}>
                            No data available!
                          </TextElement>
                        )}
                      </>
                    )}
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
  },
  contentContainer: {maxWidth: 1230, width: '95%', minWidth: 768, marginTop: 24},
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  backButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  backButton: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    color: '#000000',
  },
  headerContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  bottomContainer: {
    display: 'flex',
    flexDirection: 'row',
    padding: 25,
    alignItems: 'flex-start',
    gap: 20,
    alignSelf: 'stretch',
    backgroundColor: '#FFFFFF',
    marginTop: 24,
    borderRadius: 10,
  },
  leftPanel: {
    width: '35%',
  },
  rightPanel: {
    flex: 2,
    backgroundColor: 'white',
    borderRadius: 10,
    display: 'flex',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    lineHeight: 20,
    fontFamily: 'Work Sans',
  },
  cardWrapper: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 10,
  },
  seperator: {
    borderWidth: 1,
    height: 'auto',
    borderColor: '#E5E5E5',
  },
  notificationCard: {
    flexDirection: 'row',
    gap: 8,
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 4,
  },
  cardTextWrapper: {
    flexShrink: 1,
  },
  cardTitle: {
    fontWeight: '700',
    fontSize: 12,
    marginBottom: 2,
  },
  cardDescription: {
    fontSize: 11,
    color: '#334155',
  },
  triggeredText: {
    marginTop: 8,
    fontSize: 11,
    color: '#64748b',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  segmentControl: {
    flexDirection: 'row',
    gap: 5,
    backgroundColor: '#F4F4F4',
    padding: 2,
    borderRadius: 10,
  },
  segment: {
    fontSize: 12,
    color: '#000000',
    paddingVertical: 6,
    paddingHorizontal: 10,
    fontWeight: 400,
    fontFamily: 'Work Sans',
    borderRadius: 10,
  },
  segmentActive: {
    backgroundColor: '#FFFFFF',
    color: '#1060E0',
    fontWeight: 500,
  },
  cardRow: {
    flexDirection: 'row',
    gap: 20,
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingVertical: 10,
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: 10,
  },
  cardLabel: {
    fontSize: 12,
    color: '#000000',
    lineHeight: 14,
    fontFamily: 'Work Sans',
    fontWeight: '500',
  },
  cardValue: {
    fontSize: 28,
    color: '#000000',
    lineHeight: 36,
    fontFamily: 'Work Sans',
    fontWeight: '500',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
});

export default CustomNotificationAnalytics;
