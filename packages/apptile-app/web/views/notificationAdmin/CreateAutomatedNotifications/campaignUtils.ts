import { Campaign, CreateCampaignPayload, UpdateCampaignPayload } from '@/root/web/api/AutomatedNotificationsManagerApi';

export const CAMPAIGN_TYPES = {
  ABANDONED_CART: 'abandoned_cart',
  BACK_IN_STOCK: 'back_in_stock',
  CUSTOMER_WINBACK: 'customer_winback',
  NEW_USER_WELCOME: 'new_user_welcome',
  ORDER_SUCCESS: 'order_success',
} as const;

export type CampaignType = typeof CAMPAIGN_TYPES[keyof typeof CAMPAIGN_TYPES];

/**
 * Convert delay from hours/days to seconds
 * Rounds to integer and enforces minimum of 30 seconds
 */
export function convertDelayToSeconds(value: number, unit: 'hours' | 'days'): number {
  let seconds: number;
  
  if (unit === 'hours') {
    seconds = value * 3600; // 1 hour = 3600 seconds
  } else {
    seconds = value * 86400; // 1 day = 86400 seconds
  }
  
  // Round to integer
  seconds = Math.round(seconds);
  
  // Enforce minimum of 30 seconds
  return Math.max(seconds, 30);
}

/**
 * Convert delay from seconds to hours/days
 */
export function convertDelayFromSeconds(seconds: number): { value: number; unit: 'hours' | 'days' } {
  if (seconds < 86400) {
    // Less than 24 hours, show in hours
    const hours = seconds / 3600;
    return {
      value: Math.round(hours * 100) / 100, // Round to 2 decimal places to preserve fractional hours
      unit: 'hours'
    };
  } else {
    // 24 hours or more, show in days
    const days = seconds / 86400;
    return {
      value: Math.round(days * 100) / 100, // Round to 2 decimal places to preserve fractional days
      unit: 'days'
    };
  }
}

/**
 * Convert form data to campaign payload
 */
export function formDataToCampaignPayload(formData: {
  title: string;
  body: string;
  notificationDelay: number;
  selectTime: 'hours' | 'days';
  deepLinkUrl?: string;
  imageUrl?: string;
  isActive?: boolean;
}): CreateCampaignPayload {
  const payload: CreateCampaignPayload = {
    notificationTitle: formData.title,
    notificationBody: formData.body,
    delayInSeconds: convertDelayToSeconds(formData.notificationDelay, formData.selectTime),
    notificationDeepLink: formData.deepLinkUrl || undefined,
    notificationImageUrl: formData.imageUrl || undefined,
  };

  // Only include isActive if explicitly provided
  if (formData.isActive !== undefined) {
    payload.isActive = formData.isActive;
  }

  return payload;
}

/**
 * Convert campaign to form data
 */
export function campaignToFormData(campaign: Campaign) {
  const delay = convertDelayFromSeconds(campaign.delayInSeconds);
  
  return {
    title: campaign.notificationTitle,
    body: campaign.notificationBody,
    notificationDelay: delay.value,
    selectTime: delay.unit,
    deepLinkUrl: campaign.notificationDeepLink || '',
    imageUrl: campaign.notificationImageUrl || '',
    isActive: campaign.isActive,
  };
}

/**
 * Map campaign type to the list item type for backward compatibility
 */
export function mapCampaignTypeToListType(campaignType: string): string {
  switch (campaignType) {
    case CAMPAIGN_TYPES.ABANDONED_CART:
      return 'abandon_cart';
    case CAMPAIGN_TYPES.BACK_IN_STOCK:
      return 'back_in_stock';
    case CAMPAIGN_TYPES.CUSTOMER_WINBACK:
      return 'customer_winback';
    case CAMPAIGN_TYPES.NEW_USER_WELCOME:
      return 'new_user_welcome';
    case CAMPAIGN_TYPES.ORDER_SUCCESS:
      return 'order_success';
    default:
      return campaignType;
  }
}

/**
 * Map list item type to campaign type
 */
export function mapListTypeToCampaignType(listType: string): string {
  switch (listType) {
    case 'abandon_cart':
      return CAMPAIGN_TYPES.ABANDONED_CART;
    case 'back_in_stock':
      return CAMPAIGN_TYPES.BACK_IN_STOCK;
    case 'customer_winback':
      return CAMPAIGN_TYPES.CUSTOMER_WINBACK;
    case 'new_user_welcome':
      return CAMPAIGN_TYPES.NEW_USER_WELCOME;
    case 'order_success':
      return CAMPAIGN_TYPES.ORDER_SUCCESS;
    default:
      return listType;
  }
}
