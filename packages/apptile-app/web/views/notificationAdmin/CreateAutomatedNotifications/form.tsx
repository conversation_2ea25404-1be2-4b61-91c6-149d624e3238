import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Pressable, Image} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {DeepLinkConstructor, PreviewBlock} from '../shared';
import Button from '@/root/web/components-v2/base/Button';
import TextElement from '@/root/web/components-v2/base/TextElement';
import CodeInputControlV2 from '../../../components/controls-v2/CodeInputControl';
import AiNotificationCreator from '../aiNotificationCreator';
import Icon from '../shared/Icon';
import {NotificationDelayConstructor} from '../shared/NotificationDelayConstructor';
import {MaterialCommunityIcons} from 'apptile-core';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {useLocation, useNavigate} from 'react-router-dom';
import AssetChooseDialog from '../../../components/controls/assetEditor/assetChooseDialog';

type FormProps = {
  notification: any; // Replace 'any' with the actual type if available
  setShowForm: (value: boolean) => void;
  isEdit: boolean;
  setIsEdit: (value: boolean) => void;
  title: string;
  setTitle: (value: string) => void;
  body: string;
  setBody: (value: string) => void;
  notificationDelay: number;
  setNotificationDelay: (value: number) => void;
  imageUrl: string;
  setImageUrl: (value: string) => void;
  deepLinkPage: string;
  setDeepLinkPage: (value: string) => void;
  selectTime: 'hours' | 'days';
  setSelectTime: (value: 'hours' | 'days') => void;
  selectItemValue: string;
  setItemValue: (value: string) => void;
  showPopover: boolean;
  setShowPopover: (value: boolean) => void;
  deepLinkUrl: string;
  setDeepLinkUrl: (value: string) => void;
  selectedAsset: string;
  setSelectedAsset: any;
  deactivateNotification: () => void;
  apiLoad: boolean;
};
const Form = (props: FormProps) => {
  const {
    notification,
    setShowForm,
    isEdit,
    setIsEdit,
    title,
    setTitle,
    body,
    setBody,
    notificationDelay,
    setNotificationDelay,
    imageUrl,
    setImageUrl,
    deepLinkPage,
    setDeepLinkPage,
    selectTime,
    setSelectTime,
    setItemValue,
    selectItemValue,
    showPopover,
    setShowPopover,
    deepLinkUrl,
    setDeepLinkUrl,
    selectedAsset,
    setSelectedAsset,
    deactivateNotification,
    apiLoad,
  } = props;
  const navigate = useNavigate();
  const location = useLocation();

  // Add a key state to force re-render of CodeInputControl components
  const [inputKey, setInputKey] = useState(0);

  const HandleGoBack = () => {
    setShowForm(false);
    // Don't reset form values here - let the parent component handle the reset
    // This prevents interference with loading saved campaign data

    const params = new URLSearchParams(location.search);
    params.delete('notificationType');

    navigate({
      pathname: location.pathname,
      search: params.toString() ? `?${params.toString()}` : '',
    });
  };
  const removeSelectedAsset = () => {
    setImageUrl('');
  };

  return (
    <View style={{flex: 1, flexDirection: 'row'}}>
      <View style={[styles.rightPaneWrapper]}>
        <Icon
          iconName={'chevron-left'}
          handlePress={HandleGoBack}
          size={20}
          color={'#888888'}
          backgroundColor={'#F3F3F3'}
        />
        <View
          style={[styles.infoContainer, {borderColor: notification.currentCampaign?.isActive ? '#6ACE70' : '#E5E5E5'}]}>
          <View>
            <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 8}}>
              <TextElement style={{...styles.contentLabel, ...styles.fontFamily}} fontWeight="600">
                {notification.title}
              </TextElement>
              {notification.currentCampaign && (
                <View style={styles.statusContainer}>
                  {notification.currentCampaign.isActive ? (
                    <MaterialCommunityIcons name={'checkbox-blank-circle'} size={12} color={'#6AC370'} />
                  ) : (
                    <MaterialCommunityIcons name={'pause'} size={14} color={'#CE2029'} />
                  )}
                  <TextElement
                    style={{...styles.fontFamily, fontSize: 10, marginLeft: 6}}
                    color="#000000"
                    fontWeight="400">
                    {notification.currentCampaign.isActive ? 'Active' : 'Paused'}
                  </TextElement>
                </View>
              )}
            </View>
            <TextElement style={{...styles.contentLabel, ...styles.fontFamily, fontSize: 13}} fontWeight="500">
              Condition:
              <TextElement
                style={{...styles.contentLabel, ...styles.fontFamily, fontSize: 13, marginLeft: 7}}
                fontWeight="400">
                {notification.condition}
              </TextElement>
            </TextElement>

            {/* Only show trigger section for campaigns that need delay configuration */}
            {notification.type !== 'back_in_stock' && notification.type !== 'order_success' && (
              <View style={{marginTop: 15}}>
                <TextElement style={{...styles.contentLabel, fontSize: '14px'}}>Trigger After</TextElement>
                <View>
                  <NotificationDelayConstructor
                    selectedAction={selectTime}
                    updateSelectedAction={setSelectTime}
                    itemValue={notificationDelay}
                    setItemValue={setNotificationDelay}
                  />
                </View>
              </View>
            )}
          </View>
          {isEdit && notification.currentCampaign?.isActive && (
            <View>
              <Button
                disabled={apiLoad}
                onPress={deactivateNotification}
                containerStyles={{
                  ...styles.fontFamily,
                  ...styles.deactivateButton,
                }}
                textStyles={{color: '#CE2029'}}>
                Deactivate
              </Button>
            </View>
          )}
        </View>

        <View style={{borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 12, padding: 15, marginTop: 20}}>
          <TextElement style={styles.contentLabel}>Title</TextElement>
          <CodeInputControlV2
            value={title}
            placeholder="Exclusive collection is calling you 💕"
            onChange={val => setTitle(val)}
            singleLine={true}
            key={inputKey}
          />

          <View style={{marginTop: 20}}>
            <View style={styles.contentHeading}>
              <TextElement style={styles.contentLabel}>Body</TextElement>
            </View>
            <CodeInputControlV2
              value={body}
              placeholder="Be the first to shop and get 20% off everything. Don't miss it! 👀✌️"
              onChange={val => setBody(val)}
              singleLine={false}
              noOfLines={4}
              key={inputKey}
            />
          </View>
          <View style={{display: 'flex', alignSelf: 'flex-end', paddingBottom: 4}}>
            <AiNotificationCreator
              onConfirm={() => {
                setInputKey(prev => prev + 1);
              }}
              record={{title, body}}
              handleChange={(field: string, value: string) => {
                if (field === 'title') {
                  setTitle(value);
                } else if (field === 'body') {
                  setBody(value);
                }
              }}
            />
          </View>
        </View>
        {/* <View style={{display: 'flex', alignSelf: 'flex-end'}}>
          <AiNotificationCreator 
            onConfirm={() => {
              setInputKey(prev => prev + 1);
            }} 
            record={{title, body}}
            handleChange={(field: string, value: string) => {
              if (field === 'title') {
                setTitle(value);
              } else if (field === 'body') {
                setBody(value);
              }
            }}
          />
        </View> */}
        <View style={{marginTop: 15}}>
          <View style={{...styles.contentHeading, justifyContent: 'flex-start', gap: 5}}>
            <TextElement style={styles.contentLabel}>Image</TextElement>
            <TextElement style={{...styles.contentLabel, color: 'grey'}}>(Optional)</TextElement>
          </View>
        </View>
        {!!imageUrl && (
          <View style={[styles.imgContainer]}>
            <Pressable onPress={removeSelectedAsset} style={styles.removeAsset}>
              <MaterialCommunityIcons name="close" size={18} color="#FFF" />
            </Pressable>
            <Image style={styles.cardImage} source={imageUrl} />
          </View>
        )}
        {!imageUrl && (
          <View style={[styles.imageUpload]}>
            <Pressable
              onPress={() => {
                setShowPopover(true);
              }}>
              <TextElement style={(styles.contentLabel, styles.clickToUpload)} fontWeight="500">
                Click to upload
              </TextElement>
            </Pressable>
            <TextElement style={(styles.contentLabel, styles.clickToUploadSubHeading)} fontWeight="400">
              File supported: JPG, PNG (max. 2 mb)
            </TextElement>
          </View>
        )}

        {/* Commented out - Take user to section for automated campaigns
        <View style={{marginTop: 29}}>
          <TextElement style={styles.contentLabel}>Take user to</TextElement>
          <View>
            <DeepLinkConstructor
              selectedAction={deepLinkPage}
              updateSelectedAction={setDeepLinkPage}
              itemValue={selectItemValue}
              isAutomated={true}
              updateRecord={setDeepLinkUrl}
            />
          </View>
        </View>
        */}
      </View>
      <View style={{backgroundColor: '#EBE9E1', width: '40%'}}>
        <PreviewBlock title={title} imageUrl={imageUrl} description={body} />
      </View>
      <AssetChooseDialog
        askURL={false}
        currentAssetId={selectedAsset}
        onSelectAsset={(assetId, url) => {
          setSelectedAsset(assetId);
          if (url) {
            setImageUrl(url);
          }
        }}
        onCloseDialog={val => {
          setShowPopover(val);
        }}
        showDialog={showPopover}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  deactivateButton: {
    fontSize: '14px',
    paddingVertical: 10,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#CE2029',
    width: 'fit-content',
  },
  infoContainer: {
    borderWidth: 1,
    borderColor: '#6ACE70',
    borderRadius: 12,
    padding: 15,
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginTop: 15,
  },
  rightPaneContainer: {
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  rightPaneWrapper: {
    padding: 25,
    width: '60%',
    height: '100vh',
    backgroundColor: theme.DEFAULT_COLOR,
    overflowY: 'scroll',
    paddingBottom: 120,
  },
  contentLabel: {
    color: '#000000',
    fontSize: 14,
  },
  cardImage: {
    width: 120,
    height: 120,
  },
  contentHeading: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginBottom: 10,
  },
  imgContainer: {
    position: 'relative',
    width: '100%',
  },
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    position: 'absolute',
    top: 0,
    left: 100,
    zIndex: 1,
  },
  imageUpload: {
    width: '100%',
    height: 145,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E6E6E6',
    borderStyle: 'dashed',
  },
  fontFamily: {
    fontFamily: 'Work Sans',
  },
  clickToUpload: {
    textDecorationLine: 'underline',
    color: theme.CTA,
    textDecorationColor: theme.CTA,
    fontSize: 12,
  },
  clickToUploadSubHeading: {
    color: '#000000',
    marginTop: 8,
    fontSize: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F3F5',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 50,
    marginLeft: 10,
  },
});

export default Form;
