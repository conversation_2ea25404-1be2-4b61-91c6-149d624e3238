import TextElement from '@/root/web/components-v2/base/TextElement';
import theme from '@/root/web/styles-v2/theme';
import {Icon, MaterialCommunityIcons} from 'apptile-core';
import {useEffect, useState} from 'react';
import {StyleSheet, View, Pressable} from 'react-native';
import Form from './form';
import {useParams} from 'react-router';
import {isEmpty} from 'lodash';
import {AutomatedNotificationsManagerApi, Campaign} from '@/root/web/api/AutomatedNotificationsManagerApi';
import {useDispatch} from 'react-redux';
import {makeToast} from '@/root/web/actions/toastActions';
import {
  convertDelayFromSeconds,
  formDataToCampaignPayload,
  campaignToFormData,
  mapListTypeToCampaignType,
  mapCampaignTypeToListType,
} from './campaignUtils';
import {generateProductDeepLink, generateHomeDeepLink} from '../generateDeepLink';
import {useSelector} from 'react-redux';
import {apptileStateSelector} from 'apptile-core';
import {getLinkingPrefixesInDevAndWeb} from '@/root/app/common/utils/getLinkingPrefixes';
import {List} from '../shared/AutomatedNotificationList';
import Button from '../../../components-v2/base/Button';
import {useLocation} from 'react-router';

// notificationList.tsx
const NotificationList = () => {
  const [showForm, setShowForm] = useState(false);
  const [campaignData, setCampaignData] = useState<Campaign[]>([]);
  const [notification, setNotification] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [apiLoad, setApiLoad] = useState(false);
  const [selectItemValue, setItemValue] = useState('');
  const [showPopover, setShowPopover] = useState(false);
  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [deepLinkPage, setDeepLinkPage] = useState<'product' | 'collection' | 'home'>('home');
  const [deepLinkUrl, setDeepLinkUrl] = useState('');
  const [selectTime, setSelectTime] = useState<'days' | 'hours'>('hours');
  const [notificationDelay, setNotificationDelay] = useState(1);
  const [isEdit, setIsEdit] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<any>();
  const [currentCampaignType, setCurrentCampaignType] = useState('');
  const [loadedPrefix, setLoadedPrefix] = useState<string[]>(['']);
  
  const apptileState = useSelector(apptileStateSelector);
  const {current: currentAppConfig} = useSelector((state: any) => state.appConfig);
  const assetState = useSelector((state: any) => state.asset);

  // Handle asset selection
  useEffect(() => {
    if (selectedAsset && assetState.assetsById[selectedAsset]) {
      const currentAsset = assetState.assetsById[selectedAsset];
      setImageUrl(currentAsset.thumbUrl || currentAsset.fileUrl);
    }
  }, [selectedAsset, assetState]);

  // Load linking prefixes
  useEffect(() => {
    if (apptileState.appId) {
      getLinkingPrefixesInDevAndWeb(apptileState.appId as string).then(
        prefixes => prefixes.length && setLoadedPrefix(prefixes as string[]),
      );
    }
  }, [apptileState]);

  const resetFormState = () => {
    setTitle('');
    setBody('');
    setImageUrl('');
    setSelectTime('hours');
    setNotificationDelay(1);
    setDeepLinkUrl('');
    setDeepLinkPage('home');
    setIsEdit(false);
    setSelectedAsset(''); // Reset selected asset to prevent image persistence
  };

  // Function to get path prefix from screen name
  const getPathPrefixFromScreenName = (screenName: string) => {
    const settingsInfo = currentAppConfig?.get('settings')?.get('Linking')?.get('values')?.get('links');
    const getProductLinkingUrl = settingsInfo?.find((setting: any) => setting.screenName === screenName)?.linkingURL;
    const pathPrefix = getProductLinkingUrl ? getProductLinkingUrl?.split('/:')[0] : '';
    return pathPrefix;
  };

  // Function to generate default campaign content based on campaign type
  const generateDefaultCampaignContent = (campaignType: string) => {
    const mappedType = mapListTypeToCampaignType(campaignType);
    
    if (mappedType === 'abandoned_cart') {
      return {
        title: 'Complete Your Purchase! 🛒',
        body: 'Your cart is waiting for you! Don\'t miss out on these amazing items. Complete your purchase now and get them delivered to your door.',
        notificationDelay: 2, // 2 hours
        selectTime: 'hours' as const,
      };
    } else if (mappedType === 'back_in_stock') {
      return {
        title: 'Good News! Your Item is Back! 🎉',
        body: 'The item you\'ve been waiting for is now back in stock! Get it before it\'s gone again.',
        notificationDelay: 0.01,
        selectTime: 'hours' as const,
      };
    } else if (mappedType === 'customer_winback') {
      return {
        title: 'We Miss You! Come Back for More 💝',
        body: 'It\'s been a while since your last visit! We have exciting new products and exclusive offers waiting just for you. Come back and discover what\'s new!',
        notificationDelay: 7, // 7 days
        selectTime: 'days' as const,
      };
    } else if (mappedType === 'new_user_welcome') {
      return {
        title: 'Welcome to Our Store! 🎊',
        body: 'Thank you for joining us! Get ready to discover amazing products, exclusive deals, and a shopping experience like no other. Let\'s get started!',
        notificationDelay: 1, // 1 hour
        selectTime: 'hours' as const,
      };
    } else if (mappedType === 'order_success') {
      return {
        title: 'Order Confirmed! 🎉',
        body: 'Great news! Your order has been successfully placed and is being processed. You\'ll receive tracking information soon. Thank you for shopping with us!',
        notificationDelay: 0.01,
        selectTime: 'hours' as const,
      };
    }
    
    // Default fallback
    return {
      title: 'Don\'t Miss Out!',
      body: 'We have something special waiting for you.',
      notificationDelay: 1,
      selectTime: 'hours' as const,
    };
  };

  // Function to generate automatic deeplinks based on campaign type
  const generateAutomaticDeepLink = (campaignType: string) => {
    if (!loadedPrefix || loadedPrefix.length === 0) {
      return '';
    }
    
    const deepLinkPrefix = loadedPrefix[0] + '://';
    const mappedType = mapListTypeToCampaignType(campaignType);
    
    if (mappedType === 'abandoned_cart') {
      // For abandoned cart, generate deeplink to /cart
      const {deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
      return deepLinkURL + 'cart';
    } else if (mappedType === 'back_in_stock') {
      // For back in stock, generate deeplink to product with {{productHandle}} placeholder
      const pathPrefix = getPathPrefixFromScreenName('Product');
      const {deepLinkURL} = generateProductDeepLink(deepLinkPrefix, '{{productHandle}}', pathPrefix);
      return deepLinkURL;
    } else if (mappedType === 'customer_winback') {
      // For customer winback, generate deeplink to home page
      const {deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
      return deepLinkURL;
    } else if (mappedType === 'new_user_welcome') {
      // For new user welcome, generate deeplink to home page
      const {deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
      return deepLinkURL;
    } else if (mappedType === 'order_success') {
      // For order success, generate deeplink to home page
      const {deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
      return deepLinkURL;
    }
    
    // Default fallback
    const {deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
    return deepLinkURL;
  };

  const handleNotificationForm = (currentCampaign: Campaign | null, cardDetails: any) => {
    setShowForm(true);
    setNotification({...cardDetails, currentCampaign});
    setCurrentCampaignType(mapListTypeToCampaignType(cardDetails.type));
    
    if (currentCampaign) {
      const formData = campaignToFormData(currentCampaign);
      setTitle(formData.title);
      setBody(formData.body);
      setImageUrl(formData.imageUrl);
      setSelectTime(formData.selectTime);
      setNotificationDelay(formData.notificationDelay);
      setDeepLinkUrl(formData.deepLinkUrl);
      setIsEdit(true);
    } else {
      // Reset form for new campaign and set defaults
      resetFormState();
      const autoDeepLink = generateAutomaticDeepLink(cardDetails.type);
      const defaultContent = generateDefaultCampaignContent(cardDetails.type);
      
      setDeepLinkUrl(autoDeepLink);
      setTitle(defaultContent.title);
      setBody(defaultContent.body);
      setNotificationDelay(defaultContent.notificationDelay);
      
      // Handle back in stock special case (1 minute = 60 seconds)
      if (mapListTypeToCampaignType(cardDetails.type) === 'back_in_stock') {
        setSelectTime('hours'); // We'll convert 1 minute to 0.0167 hours for now
        setNotificationDelay(0.0167); // 1 minute = 1/60 hours
      } else {
        setSelectTime(defaultContent.selectTime);
      }
    }
  };

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const notificationType = queryParams.get('notificationType');

  useEffect(() => {
    if (!notificationType || !campaignData.length) return;

    const matchingCard = List.find(item => item.type === notificationType);
    const campaignType = mapListTypeToCampaignType(notificationType);
    const matchingCampaign = campaignData.find(c => 
      c.campaignType === campaignType || 
      c.campaignType === campaignType.toUpperCase()
    );

    if (matchingCard) {
      handleNotificationForm(matchingCampaign || null, matchingCard);
    }
  }, [notificationType, campaignData]);

  const param = useParams();
  const appID = param.id;
  const dispatch = useDispatch();

  const listCampaigns = async () => {
    try {
      if (!appID) return;
      
      const response = await AutomatedNotificationsManagerApi.listCampaigns<Campaign[]>(appID);
      if (response.status >= 200 && response.status <= 299) {
        setCampaignData(response.data);
        setLoading(false);
      } else {
        throw new Error();
      }
    } catch (err) {
      setLoading(false);
      dispatch(
        makeToast({
          content: 'Something went wrong! Please contact support.',
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  useEffect(() => {
    if (isEmpty(appID)) return;
    listCampaigns();
  }, [appID]);

  const saveNewCampaign = async (isPublishing = false) => {
    try {
      if (!isPublishing) {
        setApiLoad(true);
      }
      
      if (!title.trim()) {
        dispatch(
          makeToast({
            content: 'Title is required.',
            appearances: 'error',
            duration: 3000,
          }),
        );
        if (!isPublishing) {
          setApiLoad(false);
        }
        return false;
      }
      if (!notificationDelay) {
        dispatch(
          makeToast({
            content: 'Trigger is required.',
            appearances: 'error',
            duration: 3000,
          }),
        );
        if (!isPublishing) {
          setApiLoad(false);
        }
        return false;
      }

      if (!body.trim()) {
        dispatch(
          makeToast({
            content: 'Body is required.',
            appearances: 'error',
            duration: 3000,
          }),
        );
        if (!isPublishing) {
          setApiLoad(false);
        }
        return false;
      }

      const payload = formDataToCampaignPayload({
        title,
        body,
        notificationDelay,
        selectTime,
        deepLinkUrl,
        imageUrl,
        // Don't send isActive - server preserves current status
      });

      if (!appID || !currentCampaignType) {
        throw new Error('Missing required parameters');
      }

      await AutomatedNotificationsManagerApi.createOrUpsertCampaign(appID, currentCampaignType, payload);
      
      if (!isPublishing) {
        setApiLoad(false);
        dispatch(
          makeToast({
            content: 'Campaign saved successfully.',
            appearances: 'success',
            duration: 3000,
          }),
        );
        listCampaigns();
      }
      
      return true;
    } catch (error) {
      console.error(error);
      if (!isPublishing) {
        setApiLoad(false);
        dispatch(
          makeToast({
            content: 'Campaign save failed.',
            appearances: 'error',
            duration: 3000,
          }),
        );
      }
      return false;
    }
  };

  const publishCampaign = async () => {
    setApiLoad(true);
    try {
      if (!appID || !currentCampaignType) {
        throw new Error('Missing required parameters');
      }

      // First call the normal PUT API to save the campaign
      const saveSuccess = await saveNewCampaign(true);
      if (!saveSuccess) {
        setApiLoad(false);
        return;
      }
      
      // Then call the publish endpoint to sync with DynamoDB
      await AutomatedNotificationsManagerApi.publishCampaign(appID, currentCampaignType);
      
      dispatch(
        makeToast({
          content: 'Campaign published successfully.',
          appearances: 'success',
          duration: 3000,
        }),
      );
      setApiLoad(false);
      setShowForm(false);
      resetFormState();
      listCampaigns();
    } catch (error) {
      setApiLoad(false);
      console.error(error);
      dispatch(
        makeToast({
          content: 'Campaign publish failed.',
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  const deactivateCampaign = async () => {
    try {
      setApiLoad(true);
      if (!appID || !currentCampaignType) {
        throw new Error('Missing required parameters');
      }

      await AutomatedNotificationsManagerApi.toggleCampaignStatus(appID, currentCampaignType, { isActive: false });
      
      dispatch(
        makeToast({
          content: 'Campaign deactivated successfully.',
          appearances: 'success',
          duration: 3000,
        }),
      );
      setApiLoad(false);
      setShowForm(false);
      listCampaigns();
    } catch (error) {
      setApiLoad(false);
      console.error(error);
      dispatch(
        makeToast({
          content: 'Campaign deactivate failed.',
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  return (
    <View style={[styles.container]}>
      <View style={styles.contentContainer}>
        <View style={styles.headerConatiner}>
          <View style={{display: 'flex', flexDirection: 'column', width: 'fit-content', alignSelf: 'center'}}>
            <TextElement color="SECONDARY" fontWeight="600" style={{...styles.fontFamily, fontSize: 20}}>
              Automated Push Notification
            </TextElement>
          </View>
          {showForm && (
            <View style={{display: 'flex', flexDirection: 'row', width: 'fit-content'}}>
              <Button
                disabled={apiLoad}
                onPress={() => saveNewCampaign()}
                color="SECONDARY"
                containerStyles={{
                  ...styles.fontFamily,
                  paddingHorizontal: 20,
                  paddingVertical: 8,
                  borderColor: '#000000',
                  backgroundColor: '#f3f3f3',
                  borderWidth: 0,
                  marginRight: 10,
                }}
                textStyles={{
                  fontSize: 14,
                  lineHeight: 14,
                  color: '#000000',
                  borderColor: '#000000',
                  alignSelf: 'center',
                }}>
                Save
              </Button>
              <Button
                disabled={apiLoad}
                onPress={publishCampaign}
                color="CTA"
                containerStyles={{...styles.fontFamily, paddingHorizontal: 20, paddingVertical: 8}}
                textStyles={{fontSize: 14, lineHeight: 14}}>
                Publish
              </Button>
            </View>
          )}
        </View>
        {!showForm ? (
          <View style={styles.container}>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
                padding: 25,
              }}>
              {List.map((item, index) => {
                const campaignType = mapListTypeToCampaignType(item.type);
                const campaign = campaignData.find(c => 
                  c.campaignType === campaignType || 
                  c.campaignType === campaignType.toUpperCase()
                );
                // Show active only if campaign exists AND is active, otherwise show paused
                const isActive = campaign ? campaign.isActive : false;
                
                return (
                  <Pressable
                    key={index}
                    onPress={() => handleNotificationForm(campaign || null, item)}
                    style={{...styles.cardConatiner, borderColor: isActive ? '#6ACE70' : '#E5E5E5', display: 'flex', justifyContent: 'space-between'}}>
                    <View style={{display: 'flex', flexDirection: 'row', width: '80%'}}>
                      <View style={{marginRight: 18, display: 'flex', alignSelf: 'center'}}>
                        <Icon iconType={item.iconType as any} name={item.icon} color="black" size={20} />
                      </View>
                      <View style={{flex: 1, paddingRight: 10}}>
                        <View style={{flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap', marginBottom: 5}}>
                          <TextElement
                            fontWeight="500"
                            style={{...styles.title, ...styles.fontFamily, margin: 0, fontSize: 14, marginRight: 8}}>
                            {item.title}
                          </TextElement>
                          <View style={styles.styleContainer}>
                            {isActive ? (
                              <MaterialCommunityIcons name={'checkbox-blank-circle'} size={12} color={'#6AC370'} />
                            ) : (
                              <MaterialCommunityIcons name={'pause'} size={14} color={'#CE2029'} />
                            )}

                            <TextElement
                              style={{...styles.fontFamily, fontSize: 10, marginHorizontal: 6}}
                              color="#000000"
                              fontWeight="400">
                              {isActive ? 'Active' : 'Paused'}
                            </TextElement>
                          </View>
                        </View>

                        <TextElement style={{...styles.description, ...styles.fontFamily, fontSize: 12, flexWrap: 'wrap'}}>
                          {item.description}
                        </TextElement>
                      </View>
                    </View>
                    <View style={{marginRight: 18, display: 'flex', alignSelf: 'center'}}>
                      <Icon iconType="MaterialCommunityIcons" name="pencil-outline" color="black" size={24} />
                    </View>
                  </Pressable>
                );
              })}
            </View>
          </View>
        ) : (
          <Form
            notification={notification}
            setShowForm={setShowForm}
            isEdit={isEdit}
            setIsEdit={setIsEdit}
            title={title}
            setTitle={setTitle}
            body={body}
            setBody={setBody}
            notificationDelay={notificationDelay}
            setNotificationDelay={setNotificationDelay}
            imageUrl={imageUrl}
            setImageUrl={setImageUrl}
            deepLinkPage={deepLinkPage}
            setDeepLinkPage={setDeepLinkPage}
            selectTime={selectTime}
            setSelectTime={setSelectTime}
            setItemValue={setItemValue}
            selectItemValue={selectItemValue}
            showPopover={showPopover}
            setShowPopover={setShowPopover}
            deepLinkUrl={deepLinkUrl}
            setDeepLinkUrl={setDeepLinkUrl}
            selectedAsset={selectedAsset}
            setSelectedAsset={setSelectedAsset}
            apiLoad={apiLoad}
            deactivateNotification={deactivateCampaign}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.DEFAULT_COLOR,
    display: 'flex',
    flex: 1,
    height: '100vh',
  },
  previewCard: {
    width: '260px',
    display: 'flex',
    alignSelf: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 10,
    padding: 7,
  },
  title: {
    color: '#475569',
    marginBottom: 5,
    width: 'fit-content',
  },
  description: {
    color: '#64748b',
    marginBottom: 10,
    width: 'fit-content',
  },
  row: {
    flexDirection: 'row',
  },
  cardConatiner: {
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#6ACE70',
    borderRadius: 12,
    padding: 18,
    width: '49%',
    marginBottom: 20,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {
    maxWidth: '100%',
    width: '100%',
    minWidth: 768,
  },
  headerConatiner: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: theme.DEFAULT_COLOR,
    borderBottomWidth: 1,
    borderColor: '#E5E5E5',
    minHeight: 76
  },
  fontFamily: {
    fontFamily: 'Work Sans',
  },
  styleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F3F5',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 50,
    marginLeft: 10,
  },
});

export default NotificationList;
