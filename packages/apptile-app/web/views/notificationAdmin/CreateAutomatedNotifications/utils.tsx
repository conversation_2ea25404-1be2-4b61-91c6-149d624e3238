export const parseISODuration = (isoDuration: string): {value: number; unit: 'hours' | 'days'} | null => {
  const hoursMatch = isoDuration.match(/^PT(\d+)H$/);
  if (hoursMatch) {
    return {value: parseInt(hoursMatch[1], 10), unit: 'hours'};
  }

  const daysMatch = isoDuration.match(/^P(\d+)D$/);
  if (daysMatch) {
    return {value: parseInt(daysMatch[1], 10), unit: 'days'};
  }

  return null;
};

export const formatToISODuration = (value: number, unit: 'hours' | 'days'): string => {
  if (!value || value <= 0) return '';

  if (unit === 'hours') {
    return `PT${value}H`; // "Period Time <n> Hours"
  } else if (unit === 'days') {
    return `P${value}D`; // "Period <n> Days"
  }

  return '';
};