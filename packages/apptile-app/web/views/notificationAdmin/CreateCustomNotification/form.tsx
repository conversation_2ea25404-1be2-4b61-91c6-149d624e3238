import React from 'react';
import {StyleSheet, View, Pressable, Image} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {DeepLinkConstructor, PreviewBlock} from '../shared';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import TextElement from '@/root/web/components-v2/base/TextElement';
import AiNotificationCreator from '../aiNotificationCreator';
import DateAndTimeControl from '../shared/DateAndTimePicker';
import AssetChooseDialog from '../../../components/controls/assetEditor/assetChooseDialog';
import {MaterialCommunityIcons} from 'apptile-core';
import _ from 'lodash';
import CheckboxControl from '@/root/web/components/controls/CheckboxControl';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';

type SchedulingProps = {
  record: any;
  setRecord: any;
  handleChange: (field: string, value: any) => void;
  selectPage: string;
  setSelectPage: (page: string) => void;
  selectItemValue: string;
  selectedAsset: string;
  setSelectedAsset: (assetId: string) => void;
  showPopover: boolean;
  setShowPopover: (show: boolean) => void;
  changedAt: any;
  setChangedAt: (date: any) => void;
  isEdit: boolean;
};
const Form: React.FC<SchedulingProps> = ({
  record,
  setRecord,
  handleChange,
  selectPage,
  setSelectPage,
  selectItemValue,
  selectedAsset,
  setSelectedAsset,
  showPopover,
  setShowPopover,
  changedAt,
  setChangedAt,
  isEdit,
}) => {
  const handleAiNotificationConfirm = () => {
    setChangedAt(Date.now());
  };

  return (
    <View style={{flex: 1, flexDirection: 'row'}}>
      <View style={styles.rightPaneWrapper}>
        <View style={{borderWidth: 1, borderColor: !isEdit ? '#E5E5E5' : '#6ACE70', borderRadius: 12, padding: 15}}>
          <TextElement style={styles.contentLabel}>Title</TextElement>
          <CodeInputControlV2
            value={record.title}
            placeholder="Exclusive collection is calling you 💕"
            onChange={val => handleChange('title', val)}
            singleLine={true}
            key={changedAt}
          />

          <View style={{marginTop: 20}}>
            <View style={styles.contentHeading}>
              <TextElement style={styles.contentLabel}>Body</TextElement>
            </View>
            <CodeInputControlV2
              value={record.body}
              placeholder="Be the first to shop and get 20% off everything. Don't miss it! 👀✌️"
              onChange={val => handleChange('body', val)}
              singleLine={false}
              noOfLines={4}
              key={changedAt}
            />
          </View>
          <View style={{display: 'flex', alignSelf: 'flex-end'}}>
            <AiNotificationCreator
              onConfirm={handleAiNotificationConfirm}
              record={record}
              handleChange={handleChange}
            />
          </View>
        </View>
        <View style={{marginTop: 20}}>
          <View style={{...styles.contentHeading, justifyContent: 'flex-start', gap: 5}}>
            <TextElement style={styles.contentLabel}>Image</TextElement>
            <TextElement style={{...styles.contentLabel, color: 'grey'}}>(Optional)</TextElement>
          </View>
        </View>

        {!selectedAsset && !record.imageUrl && (
          <View style={[styles.imageUpload]}>
            <Pressable
              onPress={() => {
                setShowPopover(true);
              }}>
              <TextElement
                style={{
                  ...styles.contentLabel,
                  textDecorationLine: 'underline',
                  color: theme.CTA,
                  textDecorationColor: theme.CTA,
                  fontSize: 12,
                  textAlign: 'center',
                }}
                fontWeight="500">
                Click to upload
              </TextElement>

              <TextElement
                style={{
                  ...styles.contentLabel,
                  color: '#000000',
                  marginTop: 8,
                  fontSize: 12,
                  textAlign: 'center',
                }}
                fontWeight="400">
                File supported: JPG, PNG (max. 2 mb)
              </TextElement>
            </Pressable>
          </View>
        )}
        {!!record.imageUrl && (
          <View style={[styles.imgContainer]}>
            <Pressable
              onPress={() => {
                handleChange('imageUrl', '');
                setSelectedAsset('');
              }}
              style={styles.removeAsset}>
              <MaterialCommunityIcons name="close" size={18} color="#FFF" />
            </Pressable>
            <Image style={styles.cardImage} source={record.imageUrl} />
          </View>
        )}

        <View style={{marginTop: 20}}>
          <TextElement style={styles.contentLabel}>Take user to</TextElement>
          <View>
            <DeepLinkConstructor
              selectedAction={selectPage}
              updateSelectedAction={setSelectPage}
              itemValue={selectItemValue}
              updateRecord={handleChange}
            />
          </View>
        </View>
        <View style={{marginTop: 20}}>
          <View style={{...styles.contentHeading, justifyContent: 'space-between'}}>
            <TextElement style={styles.contentLabel}>Schedule</TextElement>
            <View style={{display: 'flex', justifyContent: 'center', alignSelf: 'center'}}>
              <CheckboxControl value={record.isScheduled} onChange={v => handleChange('isScheduled', v)} />
            </View>
          </View>
        </View>

        {record.isScheduled && (
          <View style={{}}>
            <TextElement style={styles.contentLabel}>Pick a time</TextElement>
            <DateAndTimeControl
              value={(record.scheduledAt ? record.scheduledAt : new Date())?.toISOString()}
              label={''}
              name={'Date'}
              onChange={function (value: string): void {
                handleChange('scheduledAt', new Date(value));
              }}
            />
          </View>
        )}
      </View>

      <AssetChooseDialog
        askURL={false}
        currentAssetId={selectedAsset}
        onSelectAsset={assetId => {
          setSelectedAsset(assetId);
        }}
        onCloseDialog={val => {
          setShowPopover(val);
        }}
        showDialog={showPopover}
      />
      <View style={{backgroundColor: '#EBE9E1', width: '40%'}}>
        <PreviewBlock title={record.title} imageUrl={record.imageUrl} description={record.body} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rightPaneContainer: {
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  rightPaneWrapper: {
    width: '60%',
    padding: 30,
    backgroundColor: theme.DEFAULT_COLOR,
    height: '100vh',
    overflowY: 'scroll',
    paddingBottom: 100,
  },
  rightPaneHeading: {
    fontSize: 17,
    color: theme.TEXT_COLOR,
  },
  contentLabel: {
    color: '#000000',
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    bottom: 25,
  },
  buttonWidth: {
    width: '45%',
    minHeight: 40,
  },
  solidButton: {
    backgroundColor: '#262626',
    borderColor: '#262626',
  },
  outlineButton: {borderColor: '#262626'},
  outlineButtonText: {
    color: '#262626',
  },
  rightPaneContent: {
    flex: 1,
    marginTop: 45,
  },
  rowCenter: {flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'},
  actionButton: {borderRadius: 20, alignItems: 'center', flexDirection: 'row', justifyContent: 'center', width: '100%'},
  actionButtonLabel: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 16,
    width: 100,
    textAlign: 'center',
    color: '#FFF',
  },
  overLayButton: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  overLayBottomButton: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  dateTimePickerContainer: {
    height: 450,
    width: 350,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  pickerContainer: {
    borderWidth: 1,
    color: '#000',
    padding: 5,
    borderRadius: 26,
    flexDirection: 'row-reverse',
    width: 125,
  },
  toggleContainer: {
    flexDirection: 'row',
  },
  spacer: {
    height: '100%',
    width: 8,
  },
  separator: {
    height: 0.75,
    width: '100%',
    backgroundColor: '#BFBFBF',
    marginTop: 16,
    marginBottom: 32,
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
  cardImage: {
    width: 120,
    height: 120,
  },
  rightPaneSubHeading: {
    color: '#1D1D1C',
    marginBottom: 15,
  },
  contentHeading: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginBottom: 10,
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  imgContainer: {
    position: 'relative',
    width: '100%',
  },
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    position: 'absolute',
    top: 0,
    left: 100,
    zIndex: 1,
  },
  imageUpload: {
    width: '100%',
    height: 145,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E6E6E6',
    borderStyle: 'dashed',
  },
});

export default Form;

const Separator: React.FC = () => {
  return <View style={styles.separator} />;
};
