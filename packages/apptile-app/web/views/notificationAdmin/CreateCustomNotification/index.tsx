import React, {useEffect, useState} from 'react';
import {Image, ScrollView, StyleSheet, View} from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import Button from '../../../components-v2/base/Button';
import theme from '../../../styles-v2/theme';
import Form from './form';
import {useNavigate, useParams, useSearchParams} from 'react-router-dom';
import {makeToast} from '@/root/web/actions/toastActions';
import {oneSignalCreateRecordTransformer, oneSignalGetRecordTransformer} from '../shared/oneSignalTransformer';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {get, isEmpty} from 'lodash';
import _ from 'lodash';
import {OneSignalPushNotificationApi} from '@/root/web/api/OneSignalPushNotificationApi';

const CreateCustomNotification = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const param = useParams();
  const [searchParams] = useSearchParams();
  const appID = param.id as string;

  const [selectPage, setSelectPage] = React.useState<'product' | 'collection' | 'home'>('home');
  const [selectItemValue, setItemValue] = React.useState('');
  const [showPopover, setShowPopover] = React.useState(false);
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const [fetchingNotification, setFetchingNotification] = useState(false);
  const [changedAt, setChangedAt] = React.useState(Date.now());

  const [record, setRecord] = useState({
    title: '',
    body: '',
    status: '',
    isScheduled: false,
    scheduledAt: new Date(),
    targetAudienceType: 'ALL_USERS',
    userSegmentName: '',
    userSegmentId: '',
    imageUrl: '',
    deepLinkUrl: '',
    deepLinkMetaData: {},
    deliveryFrequency: 'ONCE',
    frequencyIntervalDays: '1',
    triggerTime: new Date(),
    endDate: null,
  });

  const [selectedAsset, setSelectedAsset] = useState('');
  const [isRequestTrigger, setRequestTriggered] = useState(false);
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const currentAsset = assetState.assetsById[selectedAsset];
  const notificationId = searchParams.get('notificationId');

  React.useEffect(() => {
    if (isEmpty(appID)) return;

    const fetchNotification = async () => {
      try {
        setFetchingNotification(true);
        let stateObj;

        const notificationResponse = await OneSignalPushNotificationApi.getNotification(
          oneSignalAppId,
          notificationId,
          appID,
          '1mo',
        );

        if (notificationResponse.status >= 200 && notificationResponse.status <= 299) {
          stateObj = oneSignalGetRecordTransformer(notificationResponse?.data);
          setFetchingNotification(false);
        } else {
          setFetchingNotification(false);
          throw new Error();
        }
        setRecord(stateObj);
      } catch (err) {}
    };

    if (searchParams.get('action') === 'edit' && notificationId && hasOneSignal) {
      fetchNotification();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appID, searchParams, hasOneSignal, oneSignalAppId]);

  const handleChange = (key: keyof typeof record, value: any) => {
    setRecord(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  React.useEffect(() => {
    if (currentAsset) {
      handleChange('imageUrl', currentAsset.thumbUrl);
    }
  }, [currentAsset]);

  const {current: currentAppConfig} = useSelector((state: EditorRootState) => state.appConfig);

  //deeplink
  const settingsInfo: [
    {
      screenName: string;
      linkingURL: string;
      params: string[];
    },
  ] = currentAppConfig?.get('settings')?.get('Linking')?.get('values')?.get('links');

  const getScreenNameFromPathPrefix = (pathPrefix: string) => {
    const getScreenName = settingsInfo.find(setting => setting.linkingURL?.split('/:')[0] === pathPrefix)?.screenName;
    return getScreenName;
  };

  React.useEffect(() => {
    let currentPage = get(record, ['deepLinkMetaData', 'UIEntity', 'action'], null);
    let itemValue = get(record, ['deepLinkMetaData', 'UIEntity', 'data', 'handle'], null);
    const parts = record?.deepLinkUrl && record?.deepLinkUrl?.split('://');
    const remaining = parts[1];
    const segments = remaining?.split('/');
    if (!currentPage) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          currentPage = _.toLower(getScreenNameFromPathPrefix(segments[0]));
        }
      }
    }
    if (!itemValue) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          itemValue = segments[1];
        }
      }
    }

    if (itemValue) {
      setItemValue(itemValue);
    }
    if (currentPage) {
      setSelectPage(currentPage);
    }
  }, [record]);

  React.useEffect(() => {
    let currentPage = get(record, ['deepLinkMetaData', 'UIEntity', 'action'], null);
    let itemValue = get(record, ['deepLinkMetaData', 'UIEntity', 'data', 'handle'], null);
    const parts = record.deepLinkUrl?.split('://');
    const remaining = parts[1];
    const segments = remaining?.split('/');
    if (!currentPage) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          currentPage = _.toLower(getScreenNameFromPathPrefix(segments[0]));
        }
      }
    }
    if (!itemValue) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          itemValue = segments[1];
        }
      }
    }

    if (itemValue) {
      setItemValue(itemValue);
    }
    if (currentPage) {
      setSelectPage(currentPage);
    }
  }, [record]);

  const validateNotification = () => {
    if (!record.title || record.title.trim() === '') {
      dispatch(makeToast({content: 'Title is required', appearances: 'error'}));
      return false;
    }

    if (!record.body || record.body.trim() === '') {
      dispatch(makeToast({content: 'Body is required', appearances: 'error'}));
      return false;
    }

    if (record.isScheduled && (!record.scheduledAt || isNaN(new Date(record.scheduledAt).getTime()))) {
      dispatch(makeToast({content: 'Scheduled date and time is required', appearances: 'error'}));
      return false;
    }

    return true;
  };

  const createPush = async () => {
    try {
      if (isRequestTrigger) return;

      if (isEmpty(appID)) return;

      let cronRepeatPattern = null;

      const createRecord = {
        appId: appID,
        title: record.title,
        body: record.body,
        targetAudienceType: record.targetAudienceType,
        userSegmentName: record.userSegmentName,
        userSegmentId: record.userSegmentId,
        imageObject: {
          url: record.imageUrl,
        },
        deeplinkUrl: record.deepLinkUrl,
        status: 'PENDING',
        deeplinkMetadata: record.deepLinkMetaData,
        deliveryFrequency: record.deliveryFrequency,
        endDate: record.endDate,
        repeatPattern: cronRepeatPattern,
        deliveryType: record.isScheduled ? 'SCHEDULED' : 'INSTANT',
        scheduledAt: record.isScheduled ? record.scheduledAt : null,
      };

      setRequestTriggered(true);
      let response;
      if (hasOneSignal) {
        const transformedRecord = oneSignalCreateRecordTransformer(createRecord, oneSignalAppId, 'PENDING');
        console.log('xxx transformedRecord', transformedRecord);
        dispatch(
          makeToast({
            content: 'Creating notification...',
            appearances: 'warning',
          }),
        );
        response = await OneSignalPushNotificationApi.sendPushNotification(appID, transformedRecord);
        if (response.status >= 200 && response.status <= 299) {
          dispatch(
            makeToast({
              content: 'Notification created successfully',
              appearances: 'success',
            }),
          );
          setRequestTriggered(false);
          setTimeout(() => {
            navigate(-1);
          }, 2000);
        }
      }
    } catch (err) {
      logger.error(err);
      const errorMessage = err?.response?.data?.errors?.[0] || 'Unable to create notification';
      setRequestTriggered(false);
      dispatch(
        makeToast({
          content: errorMessage,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  const updatePush = async () => {
    try {
      dispatch(
        makeToast({
          content: 'Updating notification...',
          appearances: 'warning',
        }),
      );
      if (isRequestTrigger) return;

      // validate all required fields
      if (isEmpty(appID) || isEmpty(notificationId)) return;
      let cronRepeatPattern = null;

      const updateManualRecord = {
        appId: appID,
        title: record.title,
        body: record.body,
        targetAudienceType: record.targetAudienceType,
        userSegmentName: record.userSegmentName,
        userSegmentId: record.userSegmentId,
        imageObject: {
          url: record.imageUrl,
        },
        status: 'PENDING',
        deeplinkUrl: record.deepLinkUrl,
        deeplinkMetadata: record.deepLinkMetaData,
        deliveryFrequency: record.deliveryFrequency,
        endDate: record.endDate,
        repeatPattern: cronRepeatPattern,
        deliveryType: record.isScheduled ? 'SCHEDULED' : 'INSTANT',
        scheduledAt: record.isScheduled ? record.scheduledAt : null,
      };

      setRequestTriggered(true);
      let response: any = {};
      if (hasOneSignal) {
        const transformedRecord = oneSignalCreateRecordTransformer(updateManualRecord, oneSignalAppId, status);
        response = await OneSignalPushNotificationApi.sendPushNotification(appID, transformedRecord);
        await OneSignalPushNotificationApi.stopPushNotification(appID, notificationId, oneSignalAppId);

        if (response.status >= 200 && response.status <= 299) {
          dispatch(
            makeToast({
              content: 'Notification updated successfully',
              appearances: 'success',
            }),
          );
          setRequestTriggered(false);
          setTimeout(() => {
            navigate(-1);
          }, 2000);
        } else {
          throw new Error();
        }
      }
    } catch (err) {
      setRequestTriggered(false);
      console.error(err);
      dispatch(
        makeToast({
          content: 'Unable to update notification',
          appearances: 'error',
        }),
      );
    }
  };

  const sendPushHandler = () => {
    if (!validateNotification()) return;

    if (searchParams.get('action') === 'edit') {
      updatePush();
    } else {
      createPush();
    }
  };

  return (
    <View style={[styles.container]}>
      <View style={styles.contentContainer}>
        <View style={styles.headerConatiner}>
          <View style={{display: 'flex', flexDirection: 'column', width: 'fit-content', alignSelf: 'center'}}>
            <TextElement color="SECONDARY" fontWeight="600" style={{...styles.fontFamily, fontSize: 20}}>
              Custom Push Notification
            </TextElement>
          </View>
          {!fetchingNotification && (
            <View style={{display: 'flex', flexDirection: 'row', width: 'fit-content'}}>
              {record.isScheduled ? (
                <Button
                  disabled={
                    isRequestTrigger || !record.title || !record.body || (record.isScheduled && !record.scheduledAt)
                  }
                  onPress={sendPushHandler}
                  color="CTA"
                  containerStyles={{fontSize: 14, ...styles.fontFamily, paddingVertical: 10}}>
                  Schedule
                </Button>
              ) : (
                <Button
                  disabled={isRequestTrigger || !record.title || !record.body}
                  onPress={sendPushHandler}
                  color="CTA"
                  containerStyles={{fontSize: 14, ...styles.fontFamily, paddingVertical: 10}}>
                  Send Now
                </Button>
              )}
            </View>
          )}
        </View>

        <View
          style={{
            display: 'flex',
            justifyContent: fetchingNotification ? 'center' : 'flex-start',
            minHeight: fetchingNotification && 700,
          }}>
          {fetchingNotification ? (
            <View style={styles.loaderContainer}>
              <Image
                source={require('@/root/web/assets/images/preloader.svg')}
                style={{width: 120, height: 120, display: 'flex', alignSelf: 'center'}}
              />
            </View>
          ) : (
            <Form
              record={record}
              handleChange={handleChange}
              selectPage={selectPage}
              setSelectPage={setSelectPage}
              selectItemValue={selectItemValue}
              setItemValue={setItemValue}
              showPopover={showPopover}
              setShowPopover={setShowPopover}
              selectedAsset={selectedAsset}
              setSelectedAsset={setSelectedAsset}
              changedAt={changedAt}
              setChangedAt={setChangedAt}
              isEdit={searchParams.get('action') === 'edit' ? true : false}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {
    maxWidth: '100%',
    width: '100%',
    minWidth: 768,
  },
  headerConatiner: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    flexDirection: 'row',
    gap: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: theme.DEFAULT_COLOR,
    borderBottomWidth: 1,
    borderColor: '#E5E5E5',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  fontFamily: {
    fontFamily: 'Work Sans',
  },
  loaderContainer: {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
});

export default CreateCustomNotification;
