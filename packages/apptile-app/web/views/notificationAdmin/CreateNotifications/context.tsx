import React from 'react';
import {isEmpty} from 'lodash';
import validator from 'validator';
import moment from 'moment';

const defaultRecord: RecordType = {
  title: '',
  body: '',
  status: '',
  isScheduled: false,
  scheduledAt: new Date(),
  targetAudienceType: '',
  userSegmentName: '',
  userSegmentId: '',
  imageUrl: '',
  deepLinkUrl: '',
  deepLinkMetaData: {},
  deliveryFrequency: '',
  frequencyIntervalDays: '1',
  triggerTime: new Date(),
  endDate: null,
};

export type RecordType = {
  title: string | null;
  body: string | null;
  status: string;
  isScheduled: boolean;
  scheduledAt: Date | null;
  targetAudienceType: string;
  userSegmentName: string | null;
  userSegmentId: string | null;
  imageUrl: string | null;
  deepLinkUrl: string | null;
  deepLinkMetaData: Record<string, string> | null;
  deliveryFrequency: string | null;
  frequencyIntervalDays: string;
  triggerTime: Date;
  endDate: Date | null;
};

const defaultContext = {
  record: defaultRecord,
  updateRecord: (_key: string, _value: any) => {},
  updateRawRecord: (_val: RecordType) => {},
  isValid: true,
};

export type INotificationPlaygroundContext = typeof defaultContext;
export const NotificationPlaygroundContext = React.createContext<INotificationPlaygroundContext>(defaultContext);

export const NotificationContextProvider: React.FC = props => {
  const [isRecordValid, setRecordValid] = React.useState(true);
  const [record, setRecord] = React.useState<RecordType>({
    title: '',
    body: '',
    status: '',
    isScheduled: false,
    scheduledAt: new Date(),
    targetAudienceType: 'ALL_USERS',
    userSegmentName: '',
    userSegmentId: '',
    imageUrl: '',
    deepLinkUrl: '',
    deepLinkMetaData: {},
    deliveryFrequency: 'ONCE',
    frequencyIntervalDays: '1',
    triggerTime: new Date(),
    endDate: null,
  });

  React.useEffect(() => {
    let isValid = true;

    for (const key in validationMap) {
      const validatorFunc = validationMap[key];
      if (!validatorFunc(record[key], record)) {
        isValid = false;
      }
    }

    setRecordValid(isValid);
  }, [record]);

  const updateRecord = (key: string, value: any) => {
    setRecord(prev => {
      return {...prev, [key]: value};
    });
  };

  const updateRawRecord = (val: INotificationPlaygroundContext['record']) => {
    setRecord(val);
  };

  return (
    <NotificationPlaygroundContext.Provider value={{record, updateRecord, updateRawRecord, isValid: isRecordValid}}>
      {props.children}
    </NotificationPlaygroundContext.Provider>
  );
};

export const useNotificationPlaygroundContext = () => {
  return React.useContext(NotificationPlaygroundContext);
};

export const validationMap: Partial<Record<keyof RecordType, (...args: any[]) => boolean>> = {
  title: (val: string, _record?: RecordType) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },

  body: (val: string, _record?: RecordType) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },

  scheduledAt: (val: Date, record?: RecordType) => {
    if (!record) {
      return false;
    }

    if (!record.isScheduled) {
      return true;
    }

    if (record.status === 'LIVE') {
      return true;
    }

    return moment(val).isAfter(new Date());
  },

  userSegmentName: (val: string, record?: RecordType) => {
    if (!record) {
      return false;
    }

    if (record.targetAudienceType === 'ALL_USERS') {
      return true;
    }

    if (isEmpty(val)) {
      return false;
    }

    return true;
  },

  userSegmentId: (val: string, record?: RecordType) => {
    if (!record) {
      return false;
    }

    if (record.targetAudienceType === 'ALL_USERS') {
      return true;
    }

    if (isEmpty(val)) {
      return false;
    }

    return true;
  },
  deliveryFrequency: (val: string, record?: RecordType) => {
    if (!record) {
      return false;
    }
    if (isEmpty(val)) {
      return false;
    }

    return true;
  },

  triggerTime: (val: string, record?: RecordType) => {
    if (!record) {
      return false;
    }
    if (val == null && record.deliveryFrequency === 'RECURRING') {
      return false;
    }

    return true;
  },

  frequencyIntervalDays: (val: string, record?: RecordType) => {
    if (!record) {
      return false;
    }
    if (isEmpty(val) && record.deliveryFrequency === 'RECURRING') {
      return false;
    }

    return true;
  },
};
