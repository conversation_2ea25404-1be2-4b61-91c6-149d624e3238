import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {useNotificationPaneContext} from '../context';
import {NotificationContextProvider} from './context';
import {get, keyBy} from 'lodash';
import {useParams} from '@/root/web/routing.web';
import LeftSideBar from '@/root/web/layout-v2/LeftSidebar';
import {NotificationPaneContext} from '@/root/web/views/notificationAdmin/context';
import CreateCustomNotification from '../CreateCustomNotification';
import NotificationList from '../CreateAutomatedNotifications/notificationList';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {useSelector} from 'react-redux';
import { EditorRootState } from '@/root/web/store/EditorRootState';

type playgroundProps = {};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

const Playground = React.forwardRef<View, playgroundProps>(() => {
  const isCustom = location.pathname.includes('custom');
  const isAutomate = location.pathname.includes('automated');
  const {appIcon} = useNotificationPaneContext();
  const platformState = useSelector((state: EditorRootState) => state.platform);

  const {buildStatus} = platformState;
  const [isVersionValid, setIsVersionValid] = useState(false);
  if (buildStatus?.liveBuild?.ios?.build && !isVersionValid) {
    const currentVersion = parseFloat(buildStatus?.liveBuild?.ios?.build?.buildSourceGitHeadName.replace('v0.', ''));
    const valid =
      buildStatus?.liveBuild?.ios?.build?.buildSourceGitHeadName == 'latest' ||
      currentVersion >= 18.29 ||
      (isNaN(currentVersion) && new Date(buildStatus?.liveBuild?.ios?.build?.createdAt).getTime() > 1751932800000);
    if (isVersionValid !== valid) setIsVersionValid(valid);
  }
  if (buildStatus?.status == 'no-builds' && !isVersionValid) {
    setIsVersionValid(true);
  }

  return (
    <View style={[styles.container, StyleSheet.absoluteFill]}>
      <LeftSideBar mainBar="NOTIFICATION" />
      {isCustom ? (
        <CreateCustomNotification />
      ) : isAutomate && isVersionValid ? (
        <NotificationList />
      ) : (
        <CreateCustomNotification />
      )}
    </View>
  );
});

const PlaygroundWithContext = () => {
  const [activePane, setActivePane] = React.useState('');
  const [style, setStyle] = React.useState('text');
  const params = useParams();
  const appID = params.id;

  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchconfig = async () => {
      const response = await BuildManagerApi.getActiveAppAsset(appID);
      if (response.data) {
        const mappedResponse = keyBy(response.data, 'assetClass');
        setLogoUrl(get(mappedResponse, ['icon', 'url'], ''));
      }
    };
    if (appID) fetchconfig();
  }, [appID]);

  const isCustom = location.pathname.includes('custom');
  React.useEffect(() => {
    setActivePane(isCustom ? 'Custom' : 'Automated');
  }, [isCustom]);

  const updatePane = (pane: string) => {
    setActivePane(pane);
  };

  return (
    <NotificationPaneContext.Provider
      value={{pane: activePane, setPane: updatePane, style: style, setStyle: setStyle, appIcon: logoUrl}}>
      <NotificationContextProvider>
        <Playground />
      </NotificationContextProvider>
    </NotificationPaneContext.Provider>
  );
};

export default PlaygroundWithContext;
