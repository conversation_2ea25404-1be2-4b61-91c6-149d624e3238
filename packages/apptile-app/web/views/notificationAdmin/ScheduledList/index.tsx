import React, {useState, useCallback, useEffect} from 'react';
import {View, Text, StyleSheet, ScrollView, Pressable, Image, TouchableOpacity} from 'react-native';

import {CampaignCard} from '../shared';
import theme from '@/root/web/styles-v2/theme';
import {useParams} from '@/root/web/routing.web';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import {IManualNotification} from '../declaration';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {oneSignalGetRecordsTransformer} from '../shared/oneSignalTransformer';
import {OneSignalPushNotificationApi} from '@/root/web/api/OneSignalPushNotificationApi';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {get, keyBy} from 'lodash';

const styles = StyleSheet.create({
  rowLayout: {
    flexDirection: 'row',
  },
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
  },
  headerContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contentContainer: {width: '100%', minWidth: 768, paddingVertical: 25, paddingHorizontal: 30},
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  campaignList: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    backgroundColor: '#ffffff',
    marginVertical: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  backButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  backButton: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    color: '#000000',
  },
  noHistory: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    alignSelf: 'center',
  },
  bodyText: {
    color: theme.TEXT_COLOR,
    fontFamily: theme.FONT_FAMILY,
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  marginTop20: {
    marginTop: 20,
  },
  fontFamily: {
    fontFamily: 'Work Sans',
  },
});

const ScheduledList: React.FC = () => {
  const param = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const appID = param.id as string;

  const [currentPaginationData, setCurrentPaginationData] = useState<{
    limit: number | null;
    offset: number | null;
    totalCount: number | null;
  }>({
    limit: null,
    offset: null,
    totalCount: null,
  });

  const [list, setList] = React.useState<IManualNotification[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  const fetchconfig = async () => {
    const response = await BuildManagerApi.getActiveAppAsset(appID);
    if (response.data) {
      const mappedResponse = keyBy(response.data, 'assetClass');
      setLogoUrl(get(mappedResponse, ['icon', 'url'], ''));
    }
  };

  const listManualNotification = useCallback(
    async (limit: number, offset: number) => {
      const notificationData = await OneSignalPushNotificationApi.getNotifications(
        oneSignalAppId,
        appID,
        limit,
        offset,
      );
      // const templateData = await OneSignalPushNotificationApi.viewTemplates(oneSignalAppId, appID, 50, 0);
      const requiredNotifications = notificationData?.data?.notifications?.filter(n => !n.completed_at && !n.canceled);
      // const requiredTemplates = templateData?.data?.templates?.filter(t => !t.name.startsWith('OneSignal Push:'));

      if (notificationData.status >= 200 && notificationData.status <= 299) {
        const {limit, offset, total_count} = notificationData?.data;
        setCurrentPaginationData({limit: limit, offset, totalCount: total_count});
        setList([
          ...oneSignalGetRecordsTransformer(requiredNotifications),
          // ...oneSignalTemplatesTransformer(requiredTemplates),
        ]);
        setLoading(false);
      } else {
        setLoading(false);
        throw new Error();
      }
    },
    [appID, oneSignalAppId],
  );

  React.useEffect(() => {
    if (hasOneSignal) {
      listManualNotification(20, 0);
    }
  }, [dispatch, param.id, hasOneSignal, oneSignalAppId, appID, listManualNotification]);

  useEffect(() => {
    if (appID) {
      fetchconfig();
    }
  }, [appID]);

  return (
    <View style={[styles.container]}>
      <ScrollView contentContainerStyle={styles.wrapper}>
        <View style={styles.contentContainer}>
          <Pressable style={styles.backButtonContainer} onPress={() => navigate('../notifications')}>
            <MaterialCommunityIcons name="chevron-left" size={20} color={'#000000'} />
            <TextElement fontWeight="500" style={styles.backButton}>
              Back
            </TextElement>
          </Pressable>
          <View style={styles.headerContainer}>
            <View style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter]}>
              <TextElement color="SECONDARY" fontWeight="600" style={{...styles.fontFamily, fontSize: 28}}>
                Scheduled
              </TextElement>
            </View>
            {/* <Pagination
              getNotifications={listManualNotification}
              limit={currentPaginationData.limit}
              offset={currentPaginationData.offset}
              totalCount={currentPaginationData.totalCount}
            /> */}
          </View>

          <View
            style={{
              ...styles.campaignList,
              justifyContent: loading || list.length === 0 ? 'center' : 'flex-start',
              minHeight: loading && 700,
            }}>
            {loading ? (
              <Image
                source={require('@/root/web/assets/images/preloader.svg')}
                style={{width: 100, height: 100, display: 'flex', alignSelf: 'center'}}
              />
            ) : (
              <>
                {list.map((entry, index) => (
                  <CampaignCard record={entry} key={index} isShowAll={true} logoUrl={logoUrl} />
                ))}

                {list.length === 0 && (
                  <View style={styles.noHistory}>
                    <Text style={styles.bodyText}>No scheduled campaigns found</Text>
                  </View>
                )}
              </>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const Pagination = ({getNotifications, limit, offset, totalCount}) => {
  if (!limit || offset === null || totalCount === null) return null;

  const currentPage = offset / limit + 1;
  const totalPages = Math.ceil(totalCount / limit);
  const windowSize = 4;

  // Determine start page of the sliding window
  let startPage = Math.max(1, currentPage - (currentPage % 2 === 0 ? 1 : 2));
  let endPage = Math.min(totalPages, startPage + windowSize - 1);

  // If we're near the end, adjust window
  if (endPage - startPage + 1 < windowSize) {
    startPage = Math.max(1, endPage - windowSize + 1);
  }

  const pages = [];
  if (currentPage > 1) {
    pages.push(
      <TextElement key="dots" style={{marginHorizontal: 4, fontSize: 16}}>
        ...
      </TextElement>,
    );
  }
  for (let i = startPage; i <= endPage; i++) {
    pages.push(
      <TouchableOpacity
        key={i}
        onPress={() => getNotifications(limit, (i - 1) * limit)}
        style={{
          width: 30,
          height: 30,
          borderRadius: 15,
          backgroundColor: i === currentPage ? '#1D4ED8' : '#ffffff',
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: 4,
          borderWidth: 1,
          borderColor: '#ccc',
        }}>
        <Text style={{color: i === currentPage ? '#ffffff' : '#000000', fontWeight: '500'}}>{i}</Text>
      </TouchableOpacity>,
    );
  }
  if (currentPage < totalPages - 1) {
    pages.push(
      <TextElement key="dots" style={{marginHorizontal: 4, fontSize: 16}}>
        ...
      </TextElement>,
    );
  }
  return (
    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center', alignSelf: 'center'}}>
      {currentPage > 1 && (
        <TouchableOpacity
          onPress={() => getNotifications(limit, offset - limit)}
          style={{
            marginRight: 10,
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 15,
            backgroundColor: '#f3f4f6',
            borderWidth: 1,
            borderColor: '#ccc',
          }}>
          <Text style={{color: '#000'}}>Previous</Text>
        </TouchableOpacity>
      )}

      {pages}

      {currentPage < totalPages && (
        <TouchableOpacity
          onPress={() => getNotifications(limit, offset + limit)}
          style={{
            marginLeft: 10,
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 15,
            backgroundColor: '#f3f4f6',
            borderWidth: 1,
            borderColor: '#ccc',
          }}>
          <Text style={{color: '#000'}}>Next</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ScheduledList;
