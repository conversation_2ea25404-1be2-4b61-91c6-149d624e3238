import React, {useState, useEffect} from 'react';
import {View, Text, TouchableOpacity, ScrollView, Alert, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNotificationPlaygroundContext} from '../manualNotification/context';
import {Image} from 'react-native';

import Button from '@/root/web/components-v2/base/Button';
import {toneOptions} from './constants';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';
import DropDownControl from '@/root/web/components/controls/DropDownControl';
import Popover, {PopoverMode} from 'react-native-popover-view';
import theme from '@/root/web/styles-v2/theme';
import GenAiApi from '@/root/web/api/GenAiApi';

const callOpenAI = async (message: string) => {
  try {
    const response = await GenAiApi.enhanceNotification(message);
    const data = response.data;

    if (!data.body) {
      throw new Error('Something went wrong');
    }

    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

export const styles = StyleSheet.create({
  modalContent: {
    flex: 1,
    flexDirection: 'column',
    // minHeight: 500,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
    padding: 16,
  },
  popoverContainer: {},
  popoverContent: {},

  container: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  keyboardAvoidView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  headerContainer: {
    marginBottom: 8,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    fontFamily: theme.FONT_FAMILY,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: theme.FONT_FAMILY,
  },

  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'black',
    marginBottom: 6,
    marginTop: 16,
    fontFamily: theme.FONT_FAMILY,
  },
  textAreaContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    minHeight: 80,
    textAlignVertical: 'top',
    fontFamily: theme.FONT_FAMILY,
  },
  charCount: {
    position: 'absolute',
    bottom: 4,
    right: 8,
    fontSize: 10,
    fontFamily: theme.FONT_FAMILY,
  },
  normalText: {
    color: '#9ca3af',
    fontFamily: theme.FONT_FAMILY,
  },
  warningText: {
    color: '#f59e0b',
    fontFamily: theme.FONT_FAMILY,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: 'black',
    marginBottom: 6,
    fontFamily: theme.FONT_FAMILY,
  },
  toneContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    gap: 6,
  },
  toneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  selectedTone: {
    backgroundColor: '#f0f9ff',
    borderColor: theme.PRIMARY,
  },
  toneLabel: {
    fontSize: 12,
    marginLeft: 4,
    color: '#4b5563',
    fontWeight: '400',
    fontFamily: theme.FONT_FAMILY,
  },
  selectedToneText: {
    color: theme.PRIMARY,
    fontFamily: theme.FONT_FAMILY,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  actionButton: {
    borderRadius: 6,
    backgroundColor: theme.PRIMARY,
  },
  templateSection: {
    marginTop: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    gap: 6,
    marginBottom: 24,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 4,
    borderRadius: 6,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    height: 36,
    minWidth: 80,
  },
  activeTab: {
    backgroundColor: '#f0f9ff',
    borderColor: theme.PRIMARY,
  },
  tabLabel: {
    marginLeft: 6,
    color: '#4b5563',
    fontWeight: '500',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
  },
  activeTabLabel: {
    color: theme.PRIMARY,
    fontFamily: theme.FONT_FAMILY,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  templateHeaderText: {
    flex: 1,
  },
  templateTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
    fontFamily: theme.FONT_FAMILY,
  },
  templateDescription: {
    fontSize: 12,
    color: '#6b7280',
    fontFamily: theme.FONT_FAMILY,
  },
  divider: {
    marginVertical: 16,
  },
  fieldContainer: {
    marginBottom: 16,
  },

  picker: {
    height: 50,
    fontFamily: theme.FONT_FAMILY,
  },
  templateButtonsContainer: {
    flexDirection: 'row',
    // justifyContent: 'flex-end',
    marginTop: 8,
  },
  templateButton: {
    flex: 1,
    marginHorizontal: 2,
  },
  generateButton: {
    backgroundColor: theme.PRIMARY,
  },
  closeIconContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    // backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 5,
    // shadowColor: '#000',
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
    // elevation: 5,
  },
  flexContainer: {
    flexGrow: 1,
    flexDirection: 'row',
    gap: 24,
  },
  leftPane: {flex: 1},
  rightPane: {flex: 1},
  card: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    // alignItems: 'center',
    justifyContent: 'space-between',
  },
  dividerLine: {
    width: 2,
    backgroundColor: theme.CONTROL_BORDER,
    marginHorizontal: 12,
  },
});

const AiNotificationCreator = ({onConfirm, record, handleChange}) => {
  // const {record, handleChange} = useNotificationPlaygroundContext();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedTone, setSelectedTone] = useState('polish');
  const [isRefining, setIsRefining] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');
  const [changedAt, setChangedAt] = useState(Date.now());

  const [templateData, setTemplateData] = useState({
    liveEvent: {
      eventName: '',
      eventTime: '',
    },
    saleAnnouncement: {
      discountDetails: '',
      saleDuration: '',
    },
    newArrivals: {
      productName: '',
      highlightFeature: '',
    },
    holidaySeason: {
      holiday: '',
      promotionDetails: '',
      endDate: '',
    },
    backInStock: {
      itemName: '',
    },
    priceDrop: {
      productName: '',
      newPrice: '',
    },
    limitedStock: {
      itemName: '',
      remainingQty: '',
    },
    eventReminder: {
      eventTitle: '',
      eventDateTime: '',
    },
    newsletterRelease: {
      issueTitle: '',
      summary: '',
    },

    flashSale: {
      saleName: '',
      discount: '',
      duration: '',
    },
    productLaunch: {
      productName: '',
      launchDate: '',
    },
  });

  const [activeTemplate, setActiveTemplate] = useState('liveEvent');

  const HOLIDAY_OPTIONS = [
    {value: 'christmas', label: 'Christmas'},
    {value: 'thanksgiving', label: 'Thanksgiving'},
    {value: 'halloween', label: 'Halloween'},
    {value: 'new-year', label: 'New Year'},
    {value: 'black-friday', label: 'Black Friday'},
    {value: 'valentines', label: "Valentine's Day"},
    {value: 'easter', label: 'Easter'},
    {value: 'mothers-day', label: "Mother's Day"},
    {value: 'fathers-day', label: "Father's Day"},
    {value: 'cyber-monday', label: 'Cyber Monday'},
  ];

  const templateOptions = [
    {
      id: 'liveEvent',
      title: 'Live Event',
      description: 'Create notifications for ongoing live events',
      icon: 'video',
      iconColor: '#ef4444',
      fields: [
        {
          name: 'eventName',
          label: 'Event Name',
          placeholder: 'Enter event name',
          value: templateData.liveEvent.eventName,
        },
        {
          name: 'eventTime',
          label: 'Event Time',
          placeholder: 'e.g., Starting in 10 minutes',
          value: templateData.liveEvent.eventTime,
        },
      ],
    },
    {
      id: 'saleAnnouncement',
      title: 'Sale Announcement',
      description: 'Promote discounts and special offers',
      icon: 'tag',
      iconColor: '#10b981',
      fields: [
        {
          name: 'discountDetails',
          label: 'Discount Details',
          placeholder: 'e.g., 30% off all items',
          value: templateData.saleAnnouncement.discountDetails,
        },
        {
          name: 'saleDuration',
          label: 'Sale Duration',
          placeholder: 'e.g., Today only, This weekend',
          value: templateData.saleAnnouncement.saleDuration,
        },
      ],
    },
    {
      id: 'newArrivals',
      title: 'New Arrivals',
      description: 'Announce new products or collections',
      icon: 'package-variant-closed',
      iconColor: '#3b82f6',
      fields: [
        {
          name: 'productName',
          label: 'Product/Collection Name',
          placeholder: 'e.g., Summer Collection 2025',
          value: templateData.newArrivals.productName,
        },
        {
          name: 'highlightFeature',
          label: 'Highlight Feature (optional)',
          placeholder: 'e.g., Eco-friendly materials',
          value: templateData.newArrivals.highlightFeature,
        },
      ],
    },
    {
      id: 'holidaySeason',
      title: 'Holiday Sale',
      description: 'Create holiday-themed promotions',
      icon: 'gift',
      iconColor: '#f59e0b',
      fields: [
        {
          name: 'holiday',
          label: 'Holiday/Occasion',
          type: 'select',
          placeholder: 'Select holiday',
          value: templateData.holidaySeason.holiday,
          options: HOLIDAY_OPTIONS,
        },
        {
          name: 'promotionDetails',
          label: 'Promotion Details',
          placeholder: 'e.g., Up to 50% off holiday favorites',
          value: templateData.holidaySeason.promotionDetails,
        },
        {
          name: 'endDate',
          label: 'End Date (optional)',
          placeholder: 'e.g., Dec 24, Until supplies last',
          value: templateData.holidaySeason.endDate,
        },
      ],
    },
  ];

  const updateTemplateField = (templateId: string, fieldName: string, value: string) => {
    setTemplateData(prev => ({
      ...prev,
      [templateId]: {
        ...prev[templateId],
        [fieldName]: value,
      },
    }));
  };

  const validateTemplate = (templateId: string) => {
    switch (templateId) {
      case 'liveEvent':
        return templateData.liveEvent.eventName.trim() !== '' && templateData.liveEvent.eventTime.trim() !== '';
      case 'saleAnnouncement':
        return (
          templateData.saleAnnouncement.discountDetails.trim() !== '' &&
          templateData.saleAnnouncement.saleDuration.trim() !== ''
        );
      case 'newArrivals':
        return templateData.newArrivals.productName.trim() !== '';
      case 'holidaySeason':
        return templateData.holidaySeason.holiday !== '' && templateData.holidaySeason.promotionDetails.trim() !== '';
      case 'flashSale':
        return (
          templateData.flashSale.saleName.trim() !== '' &&
          templateData.flashSale.discount.trim() !== '' &&
          templateData.flashSale.duration.trim() !== ''
        );
      case 'productLaunch':
        return (
          templateData.productLaunch.productName.trim() !== '' && templateData.productLaunch.launchDate.trim() !== ''
        );
      default:
        return false;
    }
  };

  const refineText = async () => {
    if (!body.trim()) {
      Alert.alert('Error', 'Please enter a message body to refine');
      return;
    }

    setIsRefining(true);

    try {
      const toneDescriptions = {
        polish: 'professional, elegant, and refined',
        crisp: 'concise, direct, and to-the-point',
        fun: 'playful, upbeat, and engaging',
        quirky: 'unconventional, distinctive, and memorable with personality',
        urgent: 'time-sensitive, compelling, and action-oriented',
        friendly: 'warm, approachable, and conversational',
        hype: 'energetic, exciting, and enthusiastic',
        professional: 'formal, respectful, and business-like',
        minimal: 'simple, clean, and straightforward',
        playful: 'lighthearted, fun, and entertaining',
        authoritative: 'confident, commanding, and trustworthy',
        luxury: 'premium, sophisticated, and exclusive',
        sarcastic: 'witty, ironic, and humorous',
        excited: 'enthusiastic, joyful, and celebratory',
        chill: 'relaxed, calm, and laid-back',
      };

      const prompt = `
        Reply with the title and body in json format: {"title": "[Generated Text]", "body": "[Generated Body]"}
        Refine the following push notification title and body to make them more engaging and effective.

        Text: "${title}"
        Body: "${body}"

        Tone: ${toneDescriptions[selectedTone]}

        Requirements:
        1. Text: Maximum 50 characters
        2. Body: Maximum 150 characters
        3. Keep the core information intact
        4. Make them more engaging and appropriate for a push notification
        5. Respond with the refined title and body in the format:
           Text: [Refined Text]
           Body: [Refined Body]
      `;

      const json = await callOpenAI(prompt);
      if (json.title) setTitle(json.title.trim());
      if (json.body) setBody(json.body.trim());
      setChangedAt(Date.now());

      Alert.alert('Success', 'Your notification has been refined');
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to refine text');
    } finally {
      setIsRefining(false);
    }
  };

  const generateFromTemplate = async (templateId: string) => {
    setIsGenerating(true);

    try {
      let prompt =
        'Reply with the title and body in json format: {"title": "[Generated Text]", "body": "[Generated Body]"}';

      switch (templateId) {
        case 'liveEvent':
          prompt += `
            Create an engaging push notification title and body for a live event.

            Event name: "${templateData.liveEvent.eventName}"
            Event time: "${templateData.liveEvent.eventTime}"

            The notification should:
            1. Text: Maximum 50 characters
            2. Body: Maximum 150 characters
            3. Include the event name and time information
            4. Be compelling and create urgency
            5. Use relevant emojis to make it more engaging 🎉📅🔥
            6. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        case 'saleAnnouncement':
          prompt += `
            Create an engaging push notification title and body for a sale announcement.

            Discount details: "${templateData.saleAnnouncement.discountDetails}"
            Sale duration: "${templateData.saleAnnouncement.saleDuration}"

            The notification should:
            1. Text: Maximum 50 characters
            2. Body: Maximum 150 characters
            3. Include the discount details and time information
            4. Be compelling and create excitement
            5. Use relevant emojis to highlight the sale 🛍️💸🔥
            6. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        case 'newArrivals':
          prompt += `
            Create an engaging push notification for new product arrivals.

            Product/Collection name: "${templateData.newArrivals.productName}"
            ${
              templateData.newArrivals.highlightFeature
                ? `Highlight feature: "${templateData.newArrivals.highlightFeature}"`
                : ''
            }

            The notification should:
            1. Text: Maximum 50 characters
            2. Body: Maximum 150 characters
            3. Include the product/collection name
            4. Highlight the main feature if provided
            5. Use relevant emojis to make it exciting ✨🆕📦
            6. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        case 'holidaySeason':
          prompt += `
            Create an engaging push notification for a holiday season sale.

            Holiday/Occasion: "${templateData.holidaySeason.holiday}"
            Promotion details: "${templateData.holidaySeason.promotionDetails}"
            ${templateData.holidaySeason.endDate ? `End date: "${templateData.holidaySeason.endDate}"` : ''}

            The notification should:
            1. Text: Maximum 50 characters
            2. Body: Maximum 150 characters
            3. Include the holiday/occasion and promotion details
            4. Create a sense of urgency if an end date is provided
            5. Use festive emojis to match the holiday theme 🎄🎁✨
            6. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        case 'flashSale':
          prompt += `
            Create an engaging push notification for a flash sale.
            
            Sale name: "${templateData.flashSale.saleName}"
            Discount: "${templateData.flashSale.discount}"
            Duration: "${templateData.flashSale.duration}"
            
            The notification should:
            1. Be exciting and create urgency
            2. Maximum 150 characters
            3. Include the sale name, discount, and duration
            4. Use emojis to create urgency and excitement ⚡⏳🔥
            5. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        case 'productLaunch':
          prompt += `
            Create an engaging push notification for a product launch.
            
            Product name: "${templateData.productLaunch.productName}"
            Launch date: "${templateData.productLaunch.launchDate}"
            
            The notification should:
            1. Be exciting and create anticipation
            2. Maximum 150 characters
            3. Include the product name and launch date
            4. Use emojis to build anticipation 🚀✨📅
            5. Respond with the title and body in the format:
               Text: [Generated Text]
               Body: [Generated Body]
          `;
          break;
        default:
          throw new Error('Invalid template');
      }

      const json = await callOpenAI(prompt);
      if (json.title) setTitle(json.title.trim());
      if (json.body) setBody(json.body.trim());
      setChangedAt(Date.now());

      Alert.alert('Success', 'Notification generated successfully 🎉');
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to generate notification 😞');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfirm = async () => {
    if (title.trim().length === 0 || body.trim().length === 0) {
      Alert.alert('Error', 'Both title and body are required');
      return;
    }
    try {
      setModalVisible(false);
      await handleChange('title', title);
      await handleChange('body', body);
      onConfirm();
    } catch (error) {
      Alert.alert('Error', 'Failed to create notification');
    }
  };

  const renderNotificationComposer = changedAt => {
    return (
      <View style={styles.card}>
        <View>
          <Text style={styles.sectionTitle}>Notification Text</Text>
          <CodeInputControlV2
            placeholder="Enter your notification title here..."
            value={title}
            onChange={setTitle}
            key={changedAt}
          />
          <Text style={styles.sectionTitle}>Notification Body</Text>
          {/* Special View */}
          <View style={styles.textAreaContainer}>
            <CodeInputControlV2
              placeholder="Enter your push notification body here..."
              noOfLines={5}
              value={body}
              onChange={setBody}
              key={changedAt}
            />
            <Text style={[styles.charCount, body.length > 130 ? styles.warningText : styles.normalText]}>
              {body.length}/150
            </Text>
          </View>

          <Text style={styles.fieldLabel}>Select Tone</Text>
          <View style={styles.toneContainer}>
            {toneOptions.map(tone => (
              <TouchableOpacity
                key={tone.key}
                style={[styles.toneButton, selectedTone === tone.key && styles.selectedTone]}
                onPress={() => setSelectedTone(tone.key)}>
                <Icon name={tone.icon} size={16} color={selectedTone === tone.key ? theme.PRIMARY : '#666'} />
                <Text style={[styles.toneLabel, selectedTone === tone.key && styles.selectedToneText]}>
                  {tone.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.actionButtonContainer}>
          <Button
            color="PRIMARY"
            variant={'OUTLINED-PILL'}
            containerStyles={{backgroundColor: 'white'}}
            onPress={refineText}
            disabled={isRefining || body.trim().length === 0}
            loading={isRefining}
            icon="auto-fix">
            {isRefining ? 'Refining...' : 'Refine Text'}
          </Button>
          <Button
            onPress={handleConfirm}
            color="CTA"
            disabled={isGenerating || body.trim().length === 0 || title.trim().length === 0}
            loading={isGenerating}
            icon="check">
            Confirm
          </Button>
        </View>
      </View>
    );
  };

  const renderTemplateTabs = () => {
    return (
      <ScrollView contentContainerStyle={styles.tabsContainer} showsHorizontalScrollIndicator={false}>
        {templateOptions.map(template => (
          <TouchableOpacity
            key={template.id}
            style={[styles.tabButton, activeTemplate === template.id && styles.activeTab]}
            onPress={() => setActiveTemplate(template.id)}>
            <Icon name={template.icon} size={20} color={activeTemplate === template.id ? theme.PRIMARY : '#666'} />
            <Text style={[styles.tabLabel, activeTemplate === template.id && styles.activeTabLabel]}>
              {template.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderTemplateForm = () => {
    const activeTemplateData = templateOptions.find(t => t.id === activeTemplate);

    if (!activeTemplateData) return null;

    return (
      <View>
        <View>
          <View style={styles.templateHeader}>
            <View style={[styles.iconCircle, {backgroundColor: `${activeTemplateData.iconColor}20`}]}>
              <Icon name={activeTemplateData.icon} size={24} color={activeTemplateData.iconColor} />
            </View>
            <View style={styles.templateHeaderText}>
              <Text style={styles.templateTitle}>{activeTemplateData.title}</Text>
              <Text style={styles.templateDescription}>{activeTemplateData.description}</Text>
            </View>
          </View>

          {activeTemplateData.fields.map(field => (
            <View key={field.name} style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>{field.label}</Text>

              {field.type === 'select' ? (
                <DropDownControl
                  value={field.value}
                  onChange={value => updateTemplateField(activeTemplate, field.name, value)}
                  options={field.options.map(option => option.value)}
                />
              ) : (
                <CodeInputControlV2
                  style={styles.input}
                  placeholder={field.placeholder}
                  value={field.value}
                  onChange={value => updateTemplateField(activeTemplate, field.name, value)}
                />
              )}
            </View>
          ))}
        </View>

        <View style={styles.templateButtonsContainer}>
          <Button
            color="PRIMARY"
            variant={'OUTLINED-PILL'}
            containerStyles={{backgroundColor: 'white'}}
            onPress={() => generateFromTemplate(activeTemplate)}
            disabled={isGenerating || !validateTemplate(activeTemplate)}
            loading={isGenerating}>
            {isGenerating ? 'Generating...' : 'Generate'}
          </Button>
        </View>
      </View>
    );
  };

  useEffect(() => {
    if (record) {
      setTitle(record.title || '');
      setBody(record.body || '');
    }
  }, [record]);

  return (
    <View>
      <View>
        <Button
          color="PRIMARY"
          variant={'OUTLINED-PILL'}
          containerStyles={{backgroundColor: 'white', marginTop: 16}}
          onPress={() => setModalVisible(true)}>
          <Image
            source={require('@/root/app/assets/sparkle-icon.svg')}
            style={{width: 15, height: 15, marginRight: 8}}
          />
          Create with AI
        </Button>
      </View>
      <Popover
        isVisible={modalVisible}
        mode={PopoverMode.RN_MODAL}
        onRequestClose={() => {
          setModalVisible(false);
        }}
        popoverStyle={{flexBasis: 'auto', width: '60vw', minHeight: '80vh', maxHeight: '95vh', borderRadius: 10}}
        backgroundStyle={{backgroundColor: 'rgba(0,0,0,0.3)'}}>
        <View style={styles.modalContent}>
          <TouchableOpacity style={styles.closeIconContainer} onPress={() => setModalVisible(false)}>
            <Icon name="close" size={24} color="#000" />
          </TouchableOpacity>
          <View style={styles.container}>
            <View style={styles.headerContainer}>
              <Text style={styles.headerTitle}>AI Push Notification Creator</Text>
              <Text style={styles.headerSubtitle}>
                Create engaging notifications for your customers with AI assistance
              </Text>
            </View>
            <View style={styles.flexContainer}>
              {/* Left Side: Generate from Template */}
              <View style={styles.leftPane}>
                <Text style={styles.sectionTitle}>Generate from Template</Text>
                {renderTemplateTabs()}
                {renderTemplateForm()}
              </View>

              {/* Divider */}
              <View style={styles.dividerLine} />

              {/* Right Side: Notification Creator */}
              <View style={styles.rightPane}>{renderNotificationComposer(changedAt)}</View>
            </View>
          </View>
        </View>
      </Popover>
    </View>
  );
};

export default AiNotificationCreator;
