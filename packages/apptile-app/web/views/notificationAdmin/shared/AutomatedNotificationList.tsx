export const List = [
  {
    title: 'Abandoned Cart',
    type: 'abandon_cart',
    icon: 'cart-remove',
    iconType: 'MaterialCommunityIcons',
    description: 'Recover lost sales with a purchase reminder',
    condition: 'User left items in their cart',
  },
  {
    title: 'Back in Stock',
    type: 'back_in_stock',
    icon: 'notifications',
    iconType: 'Ionicons',
    description: 'Notify users when favourites are back in stock',
    condition: 'When a product is back in stock',
  },
  {
    title: 'Customer Winback',
    type: 'customer_winback',
    icon: 'heart-plus',
    iconType: 'MaterialCommunityIcons',
    description: 'Bring back inactive customers with a timely nudge',
    condition: 'User has been inactive for a period',
  },
  {
    title: 'New User Welcome',
    type: 'new_user_welcome',
    icon: 'hand-wave',
    iconType: 'MaterialCommunityIcons',
    description: 'Welcome new users and guide their first steps',
    condition: 'User creates an account',
  },
  {
    title: 'Order Success',
    type: 'order_success',
    icon: 'check-circle',
    iconType: 'MaterialCommunityIcons',
    description: 'Confirm a successful purchase instantly and reassure customers',
    condition: 'Order is successfully placed',
  },
];
