import React, {useEffect} from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';
import Modal from '@/root/web/components-v2/base/Modal';
import {TimePickerModal, DatePickerModalContent} from 'rn-dates';
import theme from '../../../styles-v2/theme';
import moment from 'moment';
import Button from '../../../components-v2/base/Button';
import {MaterialCommunityIcons} from 'apptile-core';
import CodeInputControl from '@/root/web/components/controls/CodeInputControl';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {ApptileWebIcon} from '../../../icons/ApptileWebIcon.web';

interface DateAndTimeControlProps {
  value: string;
  label: string;
  name: string;
  disableBinding?: boolean;
  defaultValue?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  showTime?: boolean;
  showDate?: boolean;
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateTimePickerContainer: {
    width: 328,
    borderRadius: 28,
  },
  DateInputStyle: {
    padding: 10,
  },
  TimeInputStyle: {
    padding: 10,
  },
  icon: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  modalTop: {
    padding: 14,
    borderBottomColor: theme.INPUT_BORDER,
    borderBottomWidth: 1,
  },
  modalMiddle: {
    paddingHorizontal: 10,
  },
  modalBottom: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopColor: theme.INPUT_BORDER,
    borderTopWidth: 1,
  },
});

const DateAndTimeControl: React.FC<DateAndTimeControlProps> = ({
  onChange,
  label,
  value,
  disableBinding = true,
  showTime = true,
  showDate = true,
  ...props
}) => {
  const [isDatePickerVisible, setDatePickerVisible] = React.useState(false);
  const [isTimePickerVisible, setTimePickerVisible] = React.useState(false);
  const currentDate = new Date(new Date().getTime() + 360000);
  const [selectedDate, setSelectedDate] = React.useState(
    new Date(value).toString() === 'Invalid Date' ? currentDate : new Date(value),
  );
  const [isBindedValue, setIsBindedValue] = React.useState(
    typeof value === 'string' && value.startsWith('{{') ? true : false,
  );
  useEffect(() => {
    const bindCheck = typeof value === 'string' && value.startsWith('{{') ? true : false;
    setIsBindedValue(bindCheck);
    const dateValue = new Date(value).toString() === 'Invalid Date' ? currentDate : new Date(value);
    if (!bindCheck && selectedDate.getTime() != dateValue.getTime()) setSelectedDate(dateValue);
  }, [isBindedValue, selectedDate.getTime(), setSelectedDate, value]);

  const [isBinding, setIsBinding] = React.useState(false);

  return (
    <>
      <View style={{...styles.container, width:"100%"}}>
        {label && (
          <View style={commonStyles.labelContainer}>
            {!disableBinding && !isBinding && (
              <Pressable onPress={() => setIsBinding(true)}>
                <MaterialCommunityIcons name="flash" size={18} />
              </Pressable>
            )}
            {!disableBinding && isBinding && (
              <Pressable onPress={() => setIsBinding(false)}>
                <MaterialCommunityIcons name="flash-off" size={18} />
              </Pressable>
            )}
            <Text style={[commonStyles.labelText]}>{label}</Text>
          </View>
        )}
        {isBinding && (
          <View style={[label ? commonStyles.inputContainer : {width: '100%'}]}>
            <CodeInputControl
              defaultValue={value}
              value={value}
              placeholder="{{item.value}}"
              onChange={(val: number) => onChange(val)}
            />
          </View>
        )}
        {!isBindedValue && !isBinding && (
          <View style={{display: 'flex', flexDirection:"row",width:"100%", flex: 1, gap:20}}>
            <View style={{flex: 1}}>
              {showDate && (
                <Pressable
                  style={[styles.DateInputStyle, commonStyles.input]}
                  onPress={() => {
                    setDatePickerVisible(true);
                  }}>
                  <Text style={commonStyles.inputText}>{moment(selectedDate).format('DD/MM/YYYY')}</Text>
                  <View style={styles.icon}>
                    <ApptileWebIcon name={'calender'} size={18} />
                  </View>
                </Pressable>
              )}
            </View>
            <View style={{flex: 1}}>
              {showTime && (
                <Pressable
                  style={[styles.TimeInputStyle, commonStyles.input]}
                  onPress={() => {
                    setTimePickerVisible(true);
                  }}>
                  <Text style={commonStyles.inputText}>{moment(selectedDate).format('hh:mm A')}</Text>
                  <View style={styles.icon}>
                    <ApptileWebIcon name={'clock'} size={18} />
                  </View>
                </Pressable>
              )}
            </View>
          </View>
        )}
        {isBindedValue && !isBinding && (
          <View style={styles.DateInputStyle}>
            <Text style={commonStyles.inputText}>{value}</Text>
          </View>
        )}
      </View>
      <Modal
        onVisibleChange={setDatePickerVisible}
        visible={isDatePickerVisible}
        content={
          <DateTimePicker
            pickerType="DATE"
            date={selectedDate}
            onChange={onChange}
            setPickerVisible={setDatePickerVisible}
          />
        }
      />
      <Modal
        onVisibleChange={setTimePickerVisible}
        visible={isTimePickerVisible}
        content={
          <DateTimePicker
            pickerType="TIME"
            date={selectedDate}
            onChange={onChange}
            setPickerVisible={setTimePickerVisible}
          />
        }
      />
    </>
  );
};

type DateTimePickerProps = {
  onChange: (value: string) => void;
  date: Date;
  pickerType: 'DATE' | 'TIME';
  setPickerVisible: (value: boolean) => void;
};
const DateTimePicker: React.FC<DateTimePickerProps> = props => {
  const [currentComp, setCurrentComp] = React.useState<'DATE' | 'TIME'>(props.pickerType || 'DATE');
  const {onChange, date, setPickerVisible} = props;
  const [currentDate, setCurrentDate] = React.useState<Date>(date);

  const themeValue = {
    secondaryColor: '#fff',
    primaryColor: theme.CONTROL_ACTIVE_COLOR,
    accentColor: theme.CONTROL_INPUT_COLOR,
    backgroundColor: theme.INPUT_BACKGROUND,
    fontFamily: theme.FONT_FAMILY,
  };

  const onDateChange = React.useCallback(
    params => {
      let pickedDate = new Date(params.date);
      let updatedHours = new Date(pickedDate.setHours(currentDate.getHours()));
      let updatedMinutes = new Date(updatedHours.setMinutes(currentDate.getMinutes()));
      let updatedSeconds = new Date(updatedMinutes.setSeconds(0));
      setCurrentDate(new Date(updatedSeconds));
    },
    [currentDate],
  );

  const onTimeChange = React.useCallback(
    ({hours, minutes}) => {
      let pickedDate = currentDate;
      let updatedHours = new Date(pickedDate.setHours(hours));
      let updatedMinutes = new Date(updatedHours.setMinutes(minutes));
      let updatedSeconds = new Date(updatedMinutes.setSeconds(0));
      setCurrentDate(new Date(updatedSeconds));
    },
    [currentDate],
  );

  return (
    <View style={styles.dateTimePickerContainer}>
      <View style={styles.modalTop}>
        <Text style={commonStyles.baseText}>Select {currentComp == 'DATE' ? 'Date' : 'Time'}</Text>
        {currentComp === 'DATE' && (
          <View style={{marginTop: 25, flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={[commonStyles.baseText, {fontSize: 30, lineHeight: 32}]}>
              {currentDate.toDateString()?.slice(0, -4)}
            </Text>
            <ApptileWebIcon name="edit-icon" size={30} />
          </View>
        )}
      </View>
      <View style={styles.modalMiddle}>
        {currentComp === 'DATE' && (
          <DatePickerModalContent
            locale="en"
            mode="single"
            date={currentDate}
            onChange={onDateChange}
            validRange={{
              startDate: new Date(),
              endDate: moment().add(30, 'days').toDate(),
            }}
            themeValue={themeValue}
          />
        )}
        {currentComp === 'TIME' && (
          <TimePickerModal
            modal={false}
            visible={true}
            hours={currentDate.getHours()}
            minutes={currentDate.getMinutes()}
            uppercase={true}
            locale="en"
            onChange={onTimeChange}
            themeValue={themeValue}
          />
        )}
      </View>
      {new Date(currentDate).getTime() < new Date().getTime() && (
        <View>
          <Text
            style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingBottom: 30, textAlign: 'center'}]}>
            Date &amp; Time can not be in the past.
          </Text>
        </View>
      )}
      <View style={styles.modalBottom}>
        <Button
          variant="TEXT"
          color="SECONDARY"
          onPress={() => {
            setPickerVisible(false);
          }}>
          Cancel
        </Button>
        <Button
          variant="TEXT"
          color="SECONDARY"
          disabled={new Date(currentDate).getTime() < new Date().getTime()}
          onPress={() => {
            setPickerVisible(false);
            onChange(new Date(currentDate).toISOString());
          }}>
          Ok
        </Button>
      </View>
    </View>
  );
};

export default DateAndTimeControl;
