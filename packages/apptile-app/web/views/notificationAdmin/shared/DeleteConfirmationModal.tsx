import Button from '@/root/web/components-v2/base/Button';
import React from 'react';
import {Modal, View, Text, StyleSheet} from 'react-native';

const DeleteConfirmationModal = ({
  visible,
  onCancel,
  onDelete,
  loading,
}: {
  visible: boolean;
  onCancel: () => void;
  onDelete: () => void;
  loading: boolean;
}) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <Text style={styles.title}>Are you sure you want to delete this notification?</Text>
          <Text style={styles.subtitle}>This can't be undone.</Text>

          <View style={styles.buttons}>
            <Button
              disabled={loading}
              onPress={onCancel}
              containerStyles={styles.cancelButton}
              textStyles={styles.cancelText}>
              Cancel
            </Button>

            <Button
              loading={loading}
              disabled={loading}
              onPress={onDelete}
              containerStyles={styles.deleteButton}
              textStyles={styles.deleteText}>
              {!loading && 'Delete'}
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: 398,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    elevation: 4,
  },
  title: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 10,
    textAlign: 'left',
    color: '#111',
  },
  subtitle: {
    fontSize: 14,
    color: '#555',
    marginBottom: 20,
    textAlign: 'left',
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 10,
  },
  cancelButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 34,
    borderWidth: 1,
    borderColor: '#000',
    backgroundColor: 'white',
  },
  cancelText: {
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
  },
  deleteButton: {
    width: 85,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 34,
    backgroundColor: '#ce2029',
  },
  deleteText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
});

export default DeleteConfirmationModal;
