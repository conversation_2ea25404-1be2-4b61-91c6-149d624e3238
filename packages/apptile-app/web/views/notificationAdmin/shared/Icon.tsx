import {MaterialCommunityIcons} from 'apptile-core';
import React from 'react';
import {Pressable} from 'react-native';

type IconProps = {
  iconName: string;
  handlePress: () => void;
  size?: number;
  color?: string;
  backgroundColor?: string;
};

export const Icon: React.FC<IconProps> = ({
  iconName,
  handlePress,
  size = 24,
  color = '#000000',
  backgroundColor = '#FFFFFF',
}) => {
  return (
    <Pressable
      onPress={handlePress}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: backgroundColor,
        width: 'fit-content',
        borderRadius: 50,
        width: 40,
        height: 40,
      }}>
      <MaterialCommunityIcons name={iconName} size={size} color={color} />
    </Pressable>
  );
};

export default Icon;
