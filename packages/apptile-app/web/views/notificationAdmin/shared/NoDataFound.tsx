import TextElement from '@/root/web/components-v2/base/TextElement';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import React from 'react';
import {Image, StyleSheet, View} from 'react-native';

type NoDataFoundProps = {
  text: string;
};

const NoDataFound: React.FC<NoDataFoundProps> = ({text}) => {
  return (
    <View style={styles.noProducts}>
      <Image style={styles.emptyImage} source={require('../../../assets/images/snapshot-no-result.png')} />
      <TextElement style={[commonStyles.baseText, {fontSize: 12, fontWeight: '500', color: '#000'}]}>
        {text}
      </TextElement>
    </View>
  );
};

const styles = StyleSheet.create({
  noProducts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 45,
    marginTop: 25,
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
  },
  emptyImage: {width: 150, height: 150},
});

export default NoDataFound;
