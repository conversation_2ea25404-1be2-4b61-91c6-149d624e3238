import React, {useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {useDispatch} from 'react-redux';
import DropDownControl from '../../../components/controls/DropDownControl';
import CodeInputControl from '../../../components/controls/CodeInputControl';

type INotificationDelayConstructor = {
  selectedAction: string;
  updateSelectedAction: (value: 'hours' | 'days') => void;
  itemValue: number;
  setItemValue: (value: number) => void;
};

export const NotificationDelayConstructor: React.FC<INotificationDelayConstructor> = ({
  selectedAction,
  updateSelectedAction,
  itemValue,
  setItemValue,
}) => {
  return (
    <View style={{display: 'flex', justifyContent: 'space-evenly', flexDirection: 'row', flex: 1, gap: 20}}>
      <View style={{flex: 1, width: 10}}>
        <CodeInputControl
          value={itemValue.toString()}
          placeholder="1"
          onChange={val => setItemValue(parseFloat(val) || 1)}
          singleLine={true}
          noOfLines={1}
          key={'notificationDelay'}
        />
      </View>
      <View style={{flex: 1}}>
        <DropDownControl
          label=""
          value={selectedAction}
          onChange={val => {
            updateSelectedAction(val as 'hours' | 'days');
            // if (val === 'hours') {
            //   updateDeepLink('hours');
            // }
          }}
          options={[
            {selectTime: 'Hours', value: 'hours'},
            {selectTime: 'Days', value: 'days'},
          ]}
          defaultValue={selectedAction}
          disableBinding={true}
          nameKey={'selectTime'}
          valueKey={'value'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  itemPickerWrapper: {borderColor: 'transparent'},
  spacer: {
    // height: 10,
    // width: '100%',
  },
});
