import Button from '@/root/web/components-v2/base/Button';
import React from 'react';
import {Modal, View, Text, StyleSheet} from 'react-native';

const UnsavedChangesModal = ({
  visible,
  onDiscard,
  onContinueEditing,
}: {
  visible: boolean;
  onDiscard: () => void;
  onContinueEditing: () => void;
}) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <Text style={styles.title}>
            You have unsaved changes. Are you sure you want to quit without saving?
          </Text>

          <View style={styles.buttons}>
            <Button
              onPress={onDiscard}
              containerStyles={styles.discardButton}
              textStyles={styles.discardText}>
              Discard Changes
            </Button>

            <Button
              onPress={onContinueEditing}
              containerStyles={styles.continueButton}
              textStyles={styles.continueText}>
              Continue Editing
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: 398,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    elevation: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '400',
    color: '#111',
    marginBottom: 24,
    textAlign: 'left',
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  discardButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 34,
    backgroundColor: '#f2f2f2',
  },
  discardText: {
    fontSize: 16,
    color: '#111',
    fontWeight: '600',
  },
  continueButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 34,
    backgroundColor: '#0061FF',
  },
  continueText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
});

export default UnsavedChangesModal;
