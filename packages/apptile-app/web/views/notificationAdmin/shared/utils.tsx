export const formatTriggeredDate = (isoDateString: string, isScheduled: boolean) => {
  const date = new Date(isoDateString);

  // Options for date and time
  const day = date.getDate();
  const month = date.toLocaleString('en-US', {month: 'short'});
  const time = date.toLocaleString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  return isScheduled
    ? `Scheduled for ${day}${getDaySuffix(day)} ${month}, ${time}`
    : `Triggered on ${day}${getDaySuffix(day)} ${month}, ${time}`;
};

export const getDaySuffix = (day: number): string => {
  if (day > 3 && day < 21) return 'th';
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
};

export const getReachAndClicks = (record: any) => {
  const iosStats = record?.platformDeliveryStats?.ios || {};
  const androidStats = record?.platformDeliveryStats?.android || {};

  const totalReach = (iosStats.successful || 0) + (androidStats.successful || 0);
  const totalClicks = (iosStats.converted || 0) + (androidStats.converted || 0);

  return {totalReach, totalClicks};
};
