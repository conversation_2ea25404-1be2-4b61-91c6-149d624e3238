import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, View, Image, Animated} from 'react-native';
import {useParams} from 'react-router';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import {EditorRootState} from '../../store/EditorRootState';
import {
  replaceAppConfig,
  fetchAppAssets,
  saveImageRecord,
  uploadAppAsset,
  fetchIntegrations,
  fetchMyAddOns,
  saveAppState,
} from '../../actions/editorActions';
import {updateBasicAppInfo, updateStoreDetails, fetchBrand} from '../../actions/onboardingActions';
import {MaterialCommunityIcons, modelUpdateAction, updateSettingsValue, BrandSettingsTypes} from 'apptile-core';
import theme from '../../styles-v2/theme';
import Analytics from '../../lib/segment';
import {updateColor} from '../../actions/themeActions';
import {updateUniversalTypography} from '../../actions/themeActions';
import {useNavigate} from '../../routing.web';
import onboardingAnimation from './onboardingLoader.json';
import {Player} from '@lottiefiles/react-lottie-player';

const BRAND_LOGO_ASSET_ID = BrandSettingsTypes.BRAND_LOGO_ASSET_ID;
const BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY;

const steps = [
  'Installing theme',
  'Adding Products to your app',
  'Adding Collections to your app',
  'Linking Policies',
  'Creating All Screens',
  'Setup Complete',
];

const getBrandSelector = (state: EditorRootState) => {
  return state.onboarding.brandData;
};

// Add a selector to get the shop domain from the app model
const getShopPrimaryDomainSelector = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop', 'primaryDomain', 'host']);
};

const Onboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {orgId, appId, forkId, branchName} = useParams();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [currentStep, setCurrentStep] = useState(-1);
  const [collection, setCollection] = useState({});

  const {storeData, storeStatus, mandatoryFields, webflowData} = useSelector(
    (state: EditorRootState) => state.onboarding,
  );

  const blueprints = useSelector((state: EditorRootState) => state.blueprint.blueprints);
  const {storeName, appSaveId} = storeData;
  const {themes} = webflowData;
  const {isOnboarded, nameChanged} = storeStatus;
  const {filled, filling} = mandatoryFields;

  const orgsState = useSelector((state: EditorRootState) => state.orgs);
  const isAppOnboarded = _.get(orgsState, ['appsById', appId, 'isOnboarded'], '');

  // Get the shop domain from the app model
  const shopDomain = useSelector(getShopPrimaryDomainSelector);

  // Logo auto uploader states
  const currentBrand = useSelector(getBrandSelector);
  const {logos, colors, fonts, fetched, fetching} = currentBrand;
  const brandLogo = _.find(_.find(logos, ['type', 'logo'])?.formats, ['format', 'png'])?.src;
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const {isUploading} = assetState.assetUploader;
  const lastLogoAssetId = _.last(assetState.assetIds) as string;
  const [uploadInitiated, setUploadInitiated] = useState(false);
  const [logoUploaded, setLogoUploaded] = useState(false);

  // Add state variables for color and font setup
  const [colorSetupInitiated, setColorSetupInitiated] = useState(false);
  const [colorSetupCompleted, setColorSetupCompleted] = useState(false);
  const [fontSetupInitiated, setFontSetupInitiated] = useState(false);
  const [fontSetupCompleted, setFontSetupCompleted] = useState(false);

  useEffect(() => {
    if (isAppOnboarded) {
      navigate(`/dashboard/${orgId}/app/${appId}/f/${forkId}/b/${branchName}/app-editor`);
    }
  }, []);
  // Set collection from themes directly
  useEffect(() => {
    if (blueprints && blueprints?.length > 0) {
      setCollection(blueprints[0]);
    }
  }, [blueprints]);

  // Handle initial setup and navigation
  useEffect(() => {
    if (storeName) {
      dispatch(updateStoreDetails({storeName: storeName}));

      if (!appId) return;
      dispatch(fetchMyAddOns(appId as string));
      dispatch(fetchIntegrations());
    }
  }, [appId, dispatch, isAppOnboarded, navigate, orgId, storeName]);

  // Replace app config with blueprint
  useEffect(() => {
    collection?.id &&
      dispatch(
        replaceAppConfig({
          appId,
          appSaveId,
          blueprintId: collection?.id,
          onboarding: true,
          orgId,
        }),
      );
  }, [appId, appSaveId, collection, dispatch, orgId]);

  // Update basic app info
  useEffect(() => {
    if (isOnboarded) {
      if (collection && collection.id) {
        dispatch(
          updateBasicAppInfo({
            appId,
            infoObject: {
              name: storeName,
              activeBlueprintUUID: collection?.id,
            },
          }),
        );
      }
    }
  }, [appId, isOnboarded, navigate, orgId, storeName, nameChanged, dispatch, collection, filling, filled]);

  // Navigate to app editor when setup is complete
  useEffect(() => {
    if (currentStep === steps.length) {
      dispatch(saveAppState(false, true));
      Analytics.track('editor:onboarding_complete', {
        appId,
        orgId,
        storeName,
        blueprintId: collection?.id,
        timeToComplete: Date.now() - window.performance.timing.navigationStart,
        stepsCompleted: steps.length,
        brandDataApplied: {
          logo: logoUploaded,
          colors: colorSetupCompleted,
          fonts: fontSetupCompleted,
        },
      });
      setTimeout(() => {
        navigate(`/dashboard/${orgId}/app/${appId}/f/${forkId}/b/${branchName}/brand-settings?from=onboarding`);
      }, 1500);
    }
  }, [appId, currentStep, dispatch, navigate, orgId, steps.length, forkId, branchName]);

  // Progress through steps
  useEffect(() => {
    setTimeout(() => {
      if (currentStep == steps.length - 2) {
        if (isOnboarded && nameChanged && filling && filled) {
          setCurrentStep(steps.length);
          Analytics.track('editor:onboarding_step_complete', {
            appId,
            orgId,
            step: currentStep + 1,
            stepName: steps[currentStep + 1],
            isLastStep: true,
          });
        }
      } else {
        if (isOnboarded && nameChanged && filling && filled) {
          setCurrentStep(steps.length);
        } else {
          setCurrentStep(currentStep + 1);
          Analytics.track('editor:onboarding_step_complete', {
            appId,
            orgId,
            step: currentStep + 1,
            stepName: steps[currentStep + 1],
            isLastStep: false,
          });
        }
      }
    }, 2000);
  }, [currentStep, filled, filling, isOnboarded, nameChanged, steps.length]);

  // Animate message fade in
  useEffect(() => {
    fadeAnim.setValue(0);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 700,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  // Getting the logo assets
  useEffect(() => {
    if (!isUploading && appId) fetchAppAssets(appId, 1, 15);
  }, [appId, isUploading]);

  // Logo auto upload functionality
  const triggerModalUpdate = React.useCallback(
    val => {
      const modelUpdate = [
        {
          selector: ['Apptile', 'brandLogoAssetId'],
          newValue: val,
        },
      ];
      dispatch(modelUpdateAction(modelUpdate, undefined, true));
    },
    [dispatch],
  );

  const uploadImage = async () => {
    if (brandLogo && !uploadInitiated && !logoUploaded) {
      let blob = await fetch(brandLogo).then(r => r.blob());
      blob.path = brandLogo;
      dispatch(uploadAppAsset({file: blob}));
      setUploadInitiated(true);
    }
  };
  const onLogoUpdate = React.useCallback(
    newPrefixes => {
      Analytics.track('editor:onboarding_autoBrandLogoAdded', {
        appId,
        orgId,
        logoSource: brandLogo,
        assetId: newPrefixes,
        uploadDuration: Date.now() - window.performance?.timing?.navigationStart,
      });
      dispatch(saveImageRecord(newPrefixes));
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_LOGO_ASSET_ID, newPrefixes));
      triggerModalUpdate(newPrefixes);
      setLogoUploaded(true);
    },
    [dispatch, triggerModalUpdate, brandLogo, appId, orgId],
  );

  // Automatically upload logo when brand data is fetched and we're at step 1 or higher
  useEffect(() => {
    if (fetched && !fetching && brandLogo && currentStep >= 1 && !uploadInitiated && !logoUploaded) {
      uploadImage();
    }
  }, [fetched, fetching, brandLogo, currentStep, uploadInitiated, logoUploaded]);

  // Put the latest asset id in the app model
  useEffect(() => {
    if (lastLogoAssetId && uploadInitiated && !logoUploaded) {
      onLogoUpdate(lastLogoAssetId);
    }
  }, [lastLogoAssetId, isUploading, onLogoUpdate, uploadInitiated, logoUploaded]);

  // Fetch brand data when the component mounts
  useEffect(() => {
    if (shopDomain) {
      Analytics.track('editor:onboarding_brand_fetch_initiated', {
        appId,
        orgId,
        shopDomain,
        storeName,
      });
      dispatch(fetchBrand(shopDomain));
    }
  }, [dispatch, shopDomain]);

  // Add color palette setup functionality
  useEffect(() => {
    if (fetched && !fetching && colors && !colorSetupInitiated && !colorSetupCompleted && currentStep >= 1) {
      setColorSetupInitiated(true);

      // Find accent and dark colors from brand data
      const accentColor = _.find(colors, {type: 'accent'});
      const darkColor = _.find(colors, {type: 'dark'});

      if (accentColor?.hex && darkColor?.hex) {
        // Update primary and secondary colors
        dispatch(
          updateColor({
            colorName: 'primary',
            colorCode: accentColor.hex,
            mode: 'light',
          }),
        );

        dispatch(
          updateColor({
            colorName: 'secondary',
            colorCode: darkColor.hex,
            mode: 'light',
          }),
        );

        Analytics.track('editor:onboarding_autoBrandColorsAdded', {
          appId,
          orgId,
          primaryColor: accentColor.hex,
          secondaryColor: darkColor.hex,
          totalColors: colors.length,
          colorTypes: colors.map(c => c.type),
        });
        setColorSetupCompleted(true);
      }
    }
  }, [fetched, fetching, colors, colorSetupInitiated, colorSetupCompleted, currentStep, dispatch]);

  // Add font text styles setup functionality
  useEffect(() => {
    if (fetched && !fetching && fonts && !fontSetupInitiated && !fontSetupCompleted && currentStep >= 1) {
      setFontSetupInitiated(true);

      // Find heading and body fonts from brand data
      const headingFont = _.find(fonts, {type: 'heading'});
      const bodyFont = _.find(fonts, {type: 'body'});

      if (headingFont?.family) {
        // Update heading typography
        dispatch(
          updateUniversalTypography({
            typographyName: 'heading',
            typographyItem: {
              fontFamily: headingFont.family,
              fontSize: 24,
              fontWeight: '700',
              fontStyle: 'normal',
              letterSpacing: 0,
              lineHeight: 32,
            },
          }),
        );
      }

      if (bodyFont?.family) {
        // Update body typography
        dispatch(
          updateUniversalTypography({
            typographyName: 'body',
            typographyItem: {
              fontFamily: bodyFont.family,
              fontSize: 16,
              fontWeight: '400',
              fontStyle: 'normal',
              letterSpacing: 0,
              lineHeight: 24,
            },
          }),
        );

        // Also update subHeading typography
        dispatch(
          updateUniversalTypography({
            typographyName: 'subHeading',
            typographyItem: {
              fontFamily: bodyFont.family,
              fontSize: 18,
              fontWeight: '500',
              fontStyle: 'normal',
              letterSpacing: 0,
              lineHeight: 26,
            },
          }),
        );

        Analytics.track('editor:onboarding_autoBrandFontsAdded', {
          appId,
          orgId,
          headingFont: headingFont?.family,
          bodyFont: bodyFont?.family,
          totalFonts: fonts.length,
          fontTypes: fonts.map(f => f.type),
        });
        setFontSetupCompleted(true);
      }
    }
  }, [fetched, fetching, fonts, fontSetupInitiated, fontSetupCompleted, currentStep, dispatch]);

  return (
    <View style={styles.rootContainer}>
      <View style={styles.container}>
        {/* Left Mockup Section */}
        <View style={styles.leftSection}>
          <Player src={onboardingAnimation} autoplay loop style={styles.mockupImage} />
        </View>

        {/* Right Steps Section */}
        <View style={styles.rightSection}>
          <View style={styles.stepsContainer}>
            {steps.map((step, idx) => (
              <View key={idx} style={styles.stepItem}>
                {idx < currentStep ? (
                  <MaterialCommunityIcons name="checkbox-marked-circle" size={24} color={theme.CTA_BACKGROUND} />
                ) : idx === currentStep ? (
                  <Image
                    style={{width: 20, aspectRatio: 1, tintColor: theme.CTA_BACKGROUND}}
                    source={require('@/root/web/assets/images/preloader-theme.svg')}
                  />
                ) : (
                  <Image
                    style={{width: 24, aspectRatio: 1, tintColor: '#80808099'}}
                    source={require('@/root/web/assets/images/quater-circle-outline.svg')}
                  />
                )}
                <Text
                  style={[
                    styles.stepText,
                    {
                      color: idx < currentStep ? '#000' : idx === currentStep ? theme.CTA_BACKGROUND : '#C4C4C4',
                      fontWeight: idx === currentStep ? 'bold' : 'normal',
                    },
                  ]}>
                  {step}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  leftSection: {
    height: '100%',
  },
  mockupImage: {
    height: '100vh',
  },
  rightSection: {
    flex: 1,
    padding: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  stepsContainer: {
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 28,
  },
  stepText: {
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'System',
  },
});

export default Onboard;
