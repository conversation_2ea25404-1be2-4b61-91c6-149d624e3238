import Button from '@/root/web/components-v2/base/Button';
import React from 'react';
import {Modal, View, Text, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface FeedbackModalProps {
  visible: boolean;
  onClose: (visible: boolean) => void;
  onReview: () => void;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({visible, onClose, onReview}) => {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={[styles.closeIconContainer, Platform.OS === 'web' && styles.closeIconWeb]}
            onPress={() => onClose(false)}>
            <Icon name="close" size={24} color="#000" />
          </TouchableOpacity>

          <Text style={styles.stars}>★ ★ ★ ★ ★</Text>

          <Text style={styles.headerTitle}>Love using Apptile?</Text>
          <Text style={styles.headerSubtitle}>
            Leave us a review on the Shopify App Store – it only takes 30 seconds!
          </Text>
          <Button variant="FILLED-PILL" color="CTA" onPress={onReview}>
            Leave a review
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default FeedbackModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: 390,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 25,
    elevation: 5,
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  closeIconContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    borderRadius: 20,
    padding: 5,
  },
  closeIconWeb: {
    outlineStyle: 'none',
  },
  stars: {
    fontSize: 22,
    marginBottom: 10,
    color: '#FFC107',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 6,
    textAlign: 'center',
    color: '#1f2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
});
