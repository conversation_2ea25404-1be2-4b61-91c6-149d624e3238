import Button from '@/root/web/components-v2/base/Button';
import {COLORS} from '@/root/web/styles-v2/types';
import React, {useCallback} from 'react';

interface ButtonProp {
  title: string;
  disabled?: boolean;
  loading?: boolean;
  color?: COLORS;
}

const TalkToUsButton: React.FC<ButtonProp> = ({title, disabled = false, loading = false, color = 'CTA'}) => {
  // HUBSPOT URL
  const consultationUrl = 'https://meetings.hubspot.com/devdutt-manay/demo';

  const openConsultationLink = useCallback(() => {
    window.open(consultationUrl, '_blank');
  }, []);

  return (
    <>
      <Button
        color={color}
        loading={loading}
        disabled={disabled}
        onPress={openConsultationLink}
        containerStyles={{width: '100%'}}>
        {title || 'Book a demo'}
      </Button>
    </>
  );
};

export default TalkToUsButton;
