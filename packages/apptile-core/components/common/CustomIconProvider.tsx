import React, {useState} from 'react';
// import {Platform} from 'react-native';
// import {useSelector} from 'react-redux';

// import CustomIconContext from './CustomIconContext';
// import createCustomIconSet from '../../common/utils/createCustomIconSet';

const CustomIconProvider: React.FC = ({children}) => {
  // const app = useSelector(apptileStateSelector);
  // const [Icon, setIcon] = useState(null);
  // React.useEffect(() => {
  //   if (!app.appId) return;
  //   const API = Platform.OS === 'web' ? AssetsApiWeb : AssetsApi;
  //   const getIcons = async () => {
  //     API.getIconsForCurrentApp(app.appId as string)
  //       .then(res => {
  //         if (!res.data.fontFile) return;
  //         const glyphMap = (res.data.icons as string[]).reduce((acc, icon) => ({...acc, [icon]: icon}), {});
  //         createCustomIconSet.getInstance(res.data.fontFile, glyphMap).then((icon: any) => setIcon(() => icon));
  //       })
  //       .catch(e => logger.error('getIconsForCurrentApp', e));
  //   };
  //   getIcons();
  // }, [app.appId]);

  // return <CustomIconContext.Provider value={Icon}>{children}</CustomIconContext.Provider>;
  return children;
};

export default CustomIconProvider;
