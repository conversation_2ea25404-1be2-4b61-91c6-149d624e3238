import {PluginConfig} from '../../../../common/datatypes/types';
import {Selector} from '../../../../common/DependencyGraph/types';
import {AppDispatch} from '../../../../store';
import {Api} from '../../../../api/Api';
import logger from '../../../../common/logger';
import {modelUpdateAction} from '../../../../actions/AppModelActions';

export const fetchForkInfoAction = async (
  _dispatch: AppDispatch,
  _config: PluginConfig,
  _model: any,
  _selector: Selector,
  params: any,
) => {
  try {
    const cdnBaseUrl = _model.get('forkInfoCDNBaseUrl') || 'https://cdn.apptile.io';
    const appUUID = _model.get('appUUID');

    if (!appUUID) {
      throw new Error('App UUID is required to fetch fork info');
    }

    const forkInfoUrl = `${cdnBaseUrl}/fork-info/${appUUID}.json`;
    const response = await Api.get(forkInfoUrl);

    if (response?.data) {
      const newModelUpdates = [
        {
          selector: _selector.concat(['forkInfo']),
          newValue: response.data,
        },
      ];

      _dispatch(modelUpdateAction(newModelUpdates, undefined, true));
    }

    return;
  } catch (e) {
    logger.error('Error fetching fork info:', e);
  }
};
