// Paste this in the Pipeline Script IOS-UPLOAD-PIPELINE

pipeline {
    agent {label 'mac'} 
    
    stages {
        stage('Uploading IOS App') {
            steps {
                script {
                    
                    // currentBuild.displayName="$buildName"
                    // Add your other commands here
                    def uploadConfig = params.uploadConfig
                    sh '''
                     cd /Users/<USER>/Downloads/ReactNativeTSProjeectJenkins/packages/build-scripts
                    UPLOAD_CONFIG="$uploadConfig" PLATFORM="ios" node uploadBuild.js
                    '''
                }
            }
        }
    }
    
        post {
        aborted {
            echo 'Job was aborted (killed). Performing cleanup or additional actions.'
            // Add your actions to be executed on job abortion (kill)
        }

    }

    
    
    
}