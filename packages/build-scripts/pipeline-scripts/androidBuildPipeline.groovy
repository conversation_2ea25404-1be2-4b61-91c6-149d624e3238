
// Paste this in the Pipeline Script android-code-build

pipeline {
    agent { label 'android' } // Ensure this label matches your target node

    environment {
        AWS_ACCESS_KEY_ID = credentials('AWS_ACCESS_KEY_ID')
        AWS_SECRET_ACCESS_KEY = credentials('AWS_SECRET_ACCESS_KEY')
    }

    stages {
        stage('Build') {
            steps {
                script {
                    currentBuild.displayName = "$buildName"
                    
                    // awsCodeBuild step
                    awsCodeBuild(
                        artifactEncryptionDisabledOverride: '',
                        artifactLocationOverride: '',
                        artifactNameOverride: '',
                        artifactNamespaceOverride: '',
                        artifactPackagingOverride: '',
                        artifactPathOverride: '',
                        artifactTypeOverride: '',
                        awsAccessKey: env.AWS_ACCESS_KEY_ID,
                        awsSecretKey: env.AWS_SECRET_ACCESS_KEY,
                        buildSpecFile: '',
                        buildTimeoutOverride: '',
                        cacheLocationOverride: '',
                        cacheModesOverride: '',
                        cacheTypeOverride: '',
                        certificateOverride: '',
                        cloudWatchLogsGroupNameOverride: '',
                        cloudWatchLogsStatusOverride: '',
                        cloudWatchLogsStreamNameOverride: '',
                        computeTypeOverride: '',
                        credentialsId: '',
                        credentialsType: 'keys',
                        cwlStreamingDisabled: '',
                        downloadArtifacts: 'false',
                        downloadArtifactsRelativePath: '',
                        envParameters: '',
                        envVariables: '[ { BUILD_CONFIG,$buildConfig},{PLATFORM,android},{BUILD_NUMBER,$BUILD_NUMBER},{ BRANCH_NAME,$branchName}]',
                        environmentTypeOverride: '',
                        exceptionFailureMode: '',
                        gitCloneDepthOverride: '',
                        imageOverride: '',
                        insecureSslOverride: '',
                        localSourcePath: '',
                        overrideArtifactName: '',
                        privilegedModeOverride: '',
                        projectName: 'android-builder',
                        proxyHost: '',
                        proxyPort: '',
                        region: 'us-east-1',
                        reportBuildStatusOverride: '',
                        s3LogsEncryptionDisabledOverride: '',
                        s3LogsLocationOverride: '',
                        s3LogsStatusOverride: '',
                        sourceControlType:'project',
                        secondaryArtifactsOverride: '',
                        secondarySourcesOverride: '',
                        secondarySourcesVersionOverride: '',
                        serviceRoleOverride: '',
                        sourceLocationOverride: '',
                        sourceVersion: '',
                        sseAlgorithm: '',
                        workspaceExcludes: '',
                        workspaceIncludes: '',
                        workspaceSubdir: ''
                    )
                }
            }
        }
    }
}
