
// Paste this in the Pipeline Script for Enabling IOS Notification

pipeline {
    agent {label 'mac'} 
    
    stages {
        stage('Enabling IOS Notification') {
            steps {
                script {
                    currentBuild.displayName="$buildName"
                    // Add your other commands here
                    def buildConfig = params.buildConfig
                    sh '''
                      cd /Users/<USER>/Downloads/ReactNativeTSProjeectJenkins/packages/build-scripts
                    config="$config" node enableIosNotification.js
                    '''
                }
            }
        }
    }
    
        post {
        aborted {
            echo 'Job was aborted (killed). Performing cleanup or additional actions.'
            // Add your actions to be executed on job abortion (kill)
        }

    }

    
    
    
}