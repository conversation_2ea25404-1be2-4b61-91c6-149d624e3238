
// Paste this in the Pipeline Script JENKINS IOS Build Pipeline

//  BUILD IS PARAMETERIZED IN CONFIGURE PIPELINE AND ADD THESE FOLLOWING PARAMETERS 
//SO THAT JENKINS WILL PASS THESE VARAIBLES IN BUILD ENIRONMENT

// @param 

// buildConfig -- distribution.config json that is sent through build manager
// buildName -- build name for j<PERSON><PERSON> to name this build



pipeline {
    agent {label 'mac'} 
        environment {
        GITHUB_SSH_KEY = credentials('GITHUB_SSH_KEY')
    }
    
    stages {
        stage('Building IOS') {
            steps {
                script {
                   
                   
                    currentBuild.displayName="$buildName"
                    def jsonString = params.buildConfig
                    def startIndex = jsonString.indexOf('"buildSourceGitHeadName"')
                    def branchName="main"
                    if (startIndex != -1) {
                        startIndex = jsonString.indexOf(':', startIndex)
                    
                        // If ":" is found, look for the next occurrence of double quotes
                        if (startIndex != -1) {
                            startIndex = jsonString.indexOf('"', startIndex + 1)
                    
                            // If double quotes are found, find the next occurrence of double quotes
                            if (startIndex != -1) {
                                def endIndex = jsonString.indexOf('"', startIndex + 1)
                    
                                // Extract the value between the double quotes
                                if (endIndex != -1) {
                                    def buildSourceGitHeadName = jsonString.substring(startIndex + 1, endIndex)
                                    branchName = buildSourceGitHeadName
                                    print "branchName: " + branchName
                                } else {
                                    println "Value not found after double quotes"
                                }
                            } else {
                                println "Closing double quotes not found"
                            }
                        } else {
                            println "Colon not found"
                        }
                    } else {
                        println "Key 'buildSourceGitHeadName' not found"
                    }
                    print "branchName before shell script: " + branchName
                    
                    def myScript = '''
                        cd ~/Downloads
                        export APPTILE_ANALYTICS_SEGMENT_KEY=
                        mkdir -p ./ReactNativeTSProjeect
                        mv ./ReactNativeTSProjeect ./ReactNativeTSProjeect_delete
                        rm -rf ./ReactNativeTSProjeect_delete &
                        
                    '''
                    myScript += 'git clone --branch=' + branchName + ' https://'+env.GITHUB_SSH_KEY+'@github.com/clearsight-dev/ReactNativeTSProjeect.git --depth=1'
                    sh myScript
                    
                    def filePath = '/Users/<USER>/Downloads/ReactNativeTSProjeect/build-android.sh'
                    // Check if the file exists
                    if (fileExists(filePath)) {
                        print "Running on split code"
                        
                        myScript = '''
        cd ~/Downloads/ReactNativeTSProjeect
        
        gsed -i 's/^echo "$archive_logs" | grep -q "Archive Succeeded"$/# &/g' ~/Downloads/ReactNativeTSProjeect/packages/apptile-app/devops/scripts/ios/distribution.build.sh
                
                  if [ -f ~/Downloads/ReactNativeTSProjeect/packages/build-scripts/utils/tweaks/devops/scripts/ios/distribution.build.sh ]; then
                   gsed -i 's/^echo "$archive_logs" | grep -q "Archive Succeeded"$/# &/g' ~/Downloads/ReactNativeTSProjeect/packages/build-scripts/utils/tweaks/devops/scripts/ios/distribution.build.sh
                fi
        
                if [ -f ./build-ios.sh ]; then
                    chmod +x ./build-ios.sh
                    ./build-ios.sh
                else
                    echo "File build-ios.sh does not exist."
                    ./build-android.sh
                fi
'''

                        withEnv(["BUILD_CONFIG=${jsonString}", "PLATFORM=ios","BUILD_NUMBER=${env.BUILD_NUMBER}"]) {
                            // Now you can use the environment variable within this block
                            sh myScript
                        }
                    } else {
                        print "Running on unsplit code"
                        withEnv(["BUILD_CONFIG=${jsonString}", "PLATFORM=ios"]) {
                            sh '''
                                cd /Users/<USER>/Downloads/ReactNativeTSProjeect
                                npm i
                                cd /Users/<USER>/Downloads/build-scripts
                                node index.js
                            '''
                        }
                    }
                }
            }
        }
    }
    
        post {
        success {
            
           
            
            sh ''' 
             if [ -f ~/Downloads/ReactNativeTSProjeect/packages/build-scripts/cache.js ]; then
                echo 'Node Modules is being cached....'
                 cd ~/Downloads/ReactNativeTSProjeect/packages/build-scripts
                node cache.js
              fi
             
            '''
        }
        always {
            sh '''
                echo "Clearing Watchman watches..."
                watchman watch-del-all
            '''
        }
    
    }
}
