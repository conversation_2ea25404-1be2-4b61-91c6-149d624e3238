// Paste this in the Pipeline Script JENKINS PREBUILD-PIPELINE


pipeline {
    agent {label 'mac'} 
    

    stages {
        stage('Prebuild Registration Pipeline') {
            steps {
                script {
                    // Add your other commands here
                    
                        sh '''
                         cd /Users/<USER>/Downloads/ReactNativeTSProjeectJenkins/packages/build-scripts
                         PREBUILD_CONFIG="$preBuildConfig" node preBuildPipeline.js
                        '''
                     
                }
            }
        }
    }
}
