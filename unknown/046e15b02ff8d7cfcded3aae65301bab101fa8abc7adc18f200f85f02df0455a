#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define the permissions based on type
const PERMISSIONS = {
  wifi: '<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>',
  audio:
    '<uses-permission android:name="android.permission.RECORD_AUDIO"/>\n<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>',
  bluetooth: '<uses-permission android:name="android.permission.BLUETOOTH"/>',
  storage: '<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>',
  phone: '<uses-permission android:name="android.permission.READ_PHONE_STATE"/>',
  wakelock: '<uses-permission android:name="android.permission.WAKE_LOCK"/>',
};

// Check if at least one permission type argument was provided
const args = process.argv.slice(3);
if (args.length < 1) {
  console.log('No Permissions Added in Manisfest during build Runtime');
  process.exit(0);
}

const MANIFEST_FILE = process.argv[2];

// Read the AndroidManifest.xml file
fs.readFile(MANIFEST_FILE, 'utf8', (err, data) => {
  if (err) {
    console.error(`Error reading ${MANIFEST_FILE}:`, err);
    process.exit(1);
  }

  let modified = false;

  // Iterate over all provided permission types
  args.forEach(permissionType => {
    // Check if the provided permission type is valid
    if (!PERMISSIONS[permissionType]) {
      console.log(`Invalid permission type: ${permissionType}`);
      console.log(`Valid types: ${Object.keys(PERMISSIONS).join(', ')}`);
      return;
    }

    // Add the permissions to the AndroidManifest.xml file
    const permissionsToAdd = PERMISSIONS[permissionType].split('\n');
    permissionsToAdd.forEach(permission => {
      if (!data.includes(permission)) {
        // Insert the permission before the closing </manifest> tag
        data = data.replace(/<\/manifest>/, `    ${permission}\n</manifest>`);
        console.log(`Added: ${permission}`);
        modified = true;
      } else {
        console.log(`Already exists: ${permission}`);
      }
    });
  });

  // Write the modified data back to the AndroidManifest.xml file if changes were made
  if (modified) {
    fs.writeFile(MANIFEST_FILE, data, 'utf8', err => {
      if (err) {
        console.error(`Error writing ${MANIFEST_FILE}:`, err);
        process.exit(1);
      }
      console.log(`Permissions for types '${args.join(', ')}' have been added to ${MANIFEST_FILE}.`);
    });
  } else {
    console.log('No modifications were necessary.');
  }
});
