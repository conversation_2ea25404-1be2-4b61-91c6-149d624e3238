//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by <PERSON> on 16/05/24.
//

import UserNotifications

class NotificationService: UNNotificationServiceExtension {

    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    // override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
    //     self.contentHandler = contentHandler
    //     bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
        
    //     if let bestAttemptContent = bestAttemptContent {
    //         // Modify the notification content here...
    //         bestAttemptContent.title = "\(bestAttemptContent.title) [modified]"
            
    //         contentHandler(bestAttemptContent)
    //     }
    // }

    let queue = DispatchQueue(label: "notification-service-extension")

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        queue.async {
            self.contentHandler = contentHandler
            self.bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
            
            if let bestAttemptContent = self.bestAttemptContent {
                // Modify the notification content here...
                bestAttemptContent.title = "\(bestAttemptContent.title)"
                
                // Check for image attachment
                if let imageUrlString = request.content.userInfo["rich-media"] as? String, let imageUrl = URL(string: imageUrlString), let imageType = request.content.userInfo["rich-media-type"] as? String {
                    self.downloadImageFrom(url: imageUrl, type: imageType) { (attachment) in
                        if let attachment = attachment {
                            bestAttemptContent.attachments = [attachment]
                        }
                        contentHandler(bestAttemptContent)
                    }
                } else {
                    contentHandler(bestAttemptContent)
                }
            }
        }
    }

    func downloadImageFrom(url: URL, type: String, with completionHandler: @escaping (UNNotificationAttachment?) -> Void) {
        let task = URLSession.shared.downloadTask(with: url) { (downloadedUrl, response, error) in
        self.queue.async {
            if let error = error {
                print("Error downloading image: \(error)")
                completionHandler(nil)
            } else if let downloadedUrl = downloadedUrl {
                var urlPath = URL(fileURLWithPath: NSTemporaryDirectory())
                let uniqueURLEnding = ProcessInfo.processInfo.globallyUniqueString + ".\(type)"
                urlPath = urlPath.appendingPathComponent(uniqueURLEnding)
                
                try? FileManager.default.moveItem(at: downloadedUrl, to: urlPath)
                
                do {
                    let attachment = try UNNotificationAttachment(identifier: "image", url: urlPath, options: nil)
                    completionHandler(attachment)

                    try FileManager.default.removeItem(at: urlPath)
                } catch {
                    print("Error: \(error)")
                    completionHandler(nil)
                }
            }
        }
        }
        task.resume()
    }
    
    override func serviceExtensionTimeWillExpire() {
        // Called just before the extension will be terminated by the system.
        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
        if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }

}
