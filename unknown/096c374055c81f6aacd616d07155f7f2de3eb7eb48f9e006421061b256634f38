package org.io.apptile.ApptilePreviewDemo

import android.app.Application
import android.content.Context
import android.content.res.Resources
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.bridge.JavaScriptExecutorFactory;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.flipper.ReactNativeFlipper
import com.facebook.react.modules.i18nmanager.I18nUtil
import com.facebook.soloader.SoLoader

import com.facebook.react.modules.systeminfo.AndroidInfoHelpers

import io.csie.kudo.reactnative.v8.executor.V8ExecutorFactory

import javax.annotation.Nullable

/* MoengageDependency (Don't remove) import com.moengage.core.MoEngage
import com.moengage.core.DataCenter
import com.moengage.core.config.NotificationConfig
import com.moengage.react.MoEInitializer MoengageDependencyEnd */
/* FireworkDependency (Don't remove) import com.fireworksdk.bridge.models.enums.FWPlayerLaunchBehavior 
import com.fireworksdk.bridge.reactnative.FWReactNativeSDK
import com.firework.livestream.singlehost.SingleHostLivestreamPlayerInitializer FireworkDependencyEnd */
// KlaviyoDependency (Don't remove) import com.klaviyo.analytics.Klaviyo

/* ForCleverTap (Don't remove) import com.clevertap.android.sdk.ActivityLifecycleCallback ForCleverTapEnd */

class MainApplication : Application(), ReactApplication {

  override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): List<ReactPackage> =
            PackageList(this).packages.apply {
              // Packages that cannot be autolinked yet can be added manually here, for example:
              add(RNGetValuesPackage())
              add(RNApptilePackage())
              add(PIPPackage())
            }

        override fun getJSMainModuleName(): String = "index"

        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

        override fun getJavaScriptExecutorFactory(): JavaScriptExecutorFactory? {
            return V8ExecutorFactory(
                applicationContext,
                packageName,
                AndroidInfoHelpers.getFriendlyDeviceName(),
                useDeveloperSupport
            )
        }

        @Nullable
        override fun getBundleAssetName(): String {
          return "index.android.bundle";
        }
        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
      }

  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    // Must be called before super.onCreate()
    /* ForCleverTap (Don't remove)  ActivityLifecycleCallback.register(this); ForCleverTapEnd */

    super.onCreate()
    SoLoader.init(this, false)
    /* FireworkDependency (Don't remove) 
    FWReactNativeSDK.addLivestreamPlayerInitializer(SingleHostLivestreamPlayerInitializer());
    FWReactNativeSDK.init(this); FireworkDependencyEnd */

    // KlaviyoDependency (Don't remove) Klaviyo.initialize("<KLAVIYO_COMPANY_ID>", applicationContext);

    // disable RTL
    val sharedI18nUtilInstance = I18nUtil.getInstance()
    sharedI18nUtilInstance.forceRTL(this, false)
    sharedI18nUtilInstance.allowRTL(this, false)

    /* MoengageDependency (Don't remove) val moEngage = MoEngage.Builder(this, "<MoEngageAppId>", DataCenter.DATA_CENTER_<MoEngageDatacenter>).configureNotificationMetaData(NotificationConfig(R.mipmap.ic_launcher_round, R.mipmap.ic_launcher_round))
    MoEInitializer.initializeDefaultInstance(applicationContext, moEngage, true)  MoengageDependencyEnd */

    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }

    ReactNativeFlipper.initializeFlipper(this, reactNativeHost.reactInstanceManager)
  }

  companion object {
    fun compareVersions(version1: String, version2: String): Int {
      val parts1 = version1.split("\\.")
      val parts2 = version2.split("\\.")

      for (i in 0 until Math.min(parts1.size, parts2.size)) {
        val part1 = parts1[i].toInt()
        val part2 = parts2[i].toInt()

        if (part1 < part2) {
          return -1
        } else if (part1 > part2) {
          return 1
        }
      }
      return 0
    }
  }
}
