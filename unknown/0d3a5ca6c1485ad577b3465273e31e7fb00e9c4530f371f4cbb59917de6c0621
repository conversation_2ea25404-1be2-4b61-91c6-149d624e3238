import CleverTap from 'clevertap-react-native';
import _ from 'lodash';

const prevEvent: [any, any] = [null, null];
export const sendToCleverTap = (eventName: string, eventData: Record<string, any>) => {
  if (!eventName) return;
  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;
  if (_.isEqual(prevEvent, [eventName, _eventData])) return;
  prevEvent[0] = eventName;
  prevEvent[1] = _eventData;

  logger.info('sendToCleverTap', eventName, _eventData);

  if (
    eventName === 'customerLogIn' ||
    eventName === 'customerRegistered' ||
    eventName === 'login' ||
    eventName === 'register' ||
    eventName === 'signup' ||
    eventName === 'registered'
  ) {
    CleverTap.onUserLogin({
      Name: _eventData?.firstName,
      Identity: _eventData.emailId,
      Email: _eventData?.emailId,
      custom1: 43,
    });
  } else {
    CleverTap.recordEvent(eventName, _eventData);
  }
};
