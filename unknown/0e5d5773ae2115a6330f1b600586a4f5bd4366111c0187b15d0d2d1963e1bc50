import _, {throttle} from 'lodash';
import React, {FC, useState, ReactNode, useRef, useCallback, useEffect, Children, useContext, useMemo} from 'react';
import {findNodeHandle, Pressable, StyleSheet, Text, View} from 'react-native';
import {bindActionCreators} from 'redux';
import {connect, useDispatch, useSelector} from 'react-redux';
import {useDrag, useDragLayer, useDrop} from 'react-dnd';

import iconCharacters from '@/root/web/fonts/iconCharacters.json';
import {WidgetTreeNode} from 'apptile-core';
import {selectPlugin} from 'apptile-core';
// import {AppDispatch} from '../../../../store';
import {
  CreateWidget,
  CreateWidgetPayload,
  DragDropItemTypes,
  isCreateWidget,
  MoveWidget,
  MoveWidgetPayload,
} from 'apptile-core';
import {PluginSubType} from 'apptile-core';
import {selectPageIdForPageKey} from 'apptile-core';
import {useHoverHook} from 'apptile-core';
import {isContainerTypeWidget} from 'apptile-core';
import useEndDrag from '../../../../common/utils/useEndDrag';
import {selectAppIsTilesOnly} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';
import {getEmptyImage} from 'react-dnd-html5-backend';
import {selectModuleMandatoryCheck} from '@/root/web/selectors/EditorModuleSelectors';
import {selectModuleByUUID} from 'apptile-core';
import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import {allAvailablePlans, Plan} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import Button from '@/root/web/components-v2/base/Button';
import {setOpenPremiumModal} from '@/root/web/actions/editorActions';

interface EditableWidgetBaseProps {
  widget: WidgetTreeNode;
  isEditable: boolean;
  children?: ReactNode;
}

interface EditableWidgetProps extends EditableWidgetBaseProps {
  selectPlugin: (pluginSel: string[]) => void;
  setOpenPremiumModal: (modal: boolean, premiumModalPlan: string) => void;
}

// const getLastHoverPosition = (draggedItem?: CreateWidget | MoveWidget) => {
//   const lastHoveredItem = draggedItem?.payload?.lastHoveredItem;
//   if (!lastHoveredItem || draggedItem?.payload?.pluginId === lastHoveredItem?.id) return 'none';
//   const {x, y, width, height} = lastHoveredItem.boundingRect;
//   const {x: clientX, y: clientY} = lastHoveredItem.lastHoverCoordinates;
//   let hoverPosition =
//     x <= clientX && clientX < x + width && y - 8 < clientY && clientY <= y + height / 2 ? 'top' : 'bottom';
//   if (!lastHoveredItem.isContainer && lastHoveredItem.parentFlexDirection === 'row')
//     hoverPosition =
//       x - 8 <= clientX && clientX < x + width / 2 && y < clientY && clientY <= y + height ? 'left' : 'right';
//   return hoverPosition as 'top' | 'bottom' | 'left' | 'right' | 'none';
// };

const checkIfScrollView = (ele: HTMLElement) =>
  ['scroll', 'auto'].includes(getComputedStyle(ele).overflow) ||
  ['scroll', 'auto'].includes(getComputedStyle(ele).overflowY) ||
  ['scroll', 'auto'].includes(getComputedStyle(ele).overflowX);

// const getParentWidget = (parent: HTMLElement): HTMLElement => {
//   if (!parent || parent.id || parent.draggable || !parent.parentElement) {
//     if (parent && checkIfScrollView(parent)) return parent.firstChild as HTMLElement;
//     return parent;
//   }
//   return getParentWidget(parent.parentElement);
// };
const getParentWidget = (parent: HTMLElement): HTMLElement => {
  let result = null;
  let itersLeft = 50;
  while (!result && itersLeft-- > 0) {
    if (!parent || parent.id || parent.draggable || !parent.parentElement) {
      if (parent && checkIfScrollView(parent)) return parent.firstChild as HTMLElement;
      result = parent;
    } else {
      parent = getParentWidget(parent.parentElement);
    }
  }
  return result;
};

const indicatorPositions = {
  none: {},
  top: {top: '-8px', left: 0, right: 0, height: '8px'},
  bottom: {bottom: '-8px', left: 0, right: 0, height: '8px'},
  left: {top: 0, bottom: 0, left: '-8px', width: '8px'},
  right: {top: 0, bottom: 0, right: '-8px', width: '8px'},
};

const EditableWidget: FC<EditableWidgetProps> = ({widget, isEditable, selectPlugin, setOpenPremiumModal, children}) => {
  const {layout, id, instance, pageKey, widgetType, selected, inModule, config} = widget;
  const idkey = id + (instance || '');

  const isTilesOnly = useSelector(selectAppIsTilesOnly);
  isEditable = isEditable && (!isTilesOnly || !inModule || widgetType === 'ModuleInstance');
  const isModule = widgetType === 'ModuleInstance';

  const pageId = useSelector(selectPageIdForPageKey(pageKey));
  const onDragEnd = useEndDrag();
  const containerId = layout?.container;
  const isContainer = isContainerTypeWidget(widgetType);
  const moduleMandatoryField = useSelector(selectModuleMandatoryCheck(pageId, id));
  const moduleUUID = config?.get('config')?.get('moduleUUID') ?? '';
  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));
  const moduleInstanceConfig = useSelector(state => selectPluginConfig(state, pageId, id));
  const [usingProTile, setUsingProTile] = useState(false);
  const [variantGatingState, setVariantGatingState] = useState();
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);

  useEffect(() => {
    const variantGating = moduleInstanceConfig?.get('config')?.get('variantGating') ?? allAvailablePlans.CORE;
    setUsingProTile(!currentPlanFeatures.includes(variantGating) ?? false);
    setVariantGatingState(variantGating);
  }, [currentPlanFeatures, moduleInstanceConfig]);
  const isMovable = !moduleRecord?.isMovable
    ? true
    : currentPlanFeatures.includes(allAvailablePlans[moduleRecord?.isMovable as Plan]) ?? true;
  const elementRef = useRef<any>(null);
  const overlayRef = useRef<any>(null);
  const parentWidget = getParentWidget(elementRef.current?.parentElement);
  const [setHoverRef, isHovered, isHoverSource] = useHoverHook();
  const {isDragActive} = useDragLayer(monitor => ({isDragActive: monitor.isDragging()}));
  const [isTopOrBottom, setIsTopOrBottom] = useState(true);
  const [position, setPosition] = useState('none');

  const handleWidgetHover = useCallback(
    (item: MoveWidget | CreateWidget, monitor) => {
      const currentEle = elementRef?.current;
      const {
        prevDrag,
        payload: {pluginId},
      } = item;
      if (id) {
        if (pluginId === containerId || pluginId === id) return;
      }
      const parentFlexDirection =
        isModule && isTilesOnly
          ? 'column'
          : parentWidget && getComputedStyle(parentWidget).getPropertyValue('flex-direction');
      const lastHoveredItem = {
        ...widget,
        lastHoverCoordinates: monitor.getClientOffset(),
        boundingRect: currentEle.getBoundingClientRect(),
        isContainer: isContainerTypeWidget(widgetType),
        parentFlexDirection,
      };

      const offset = monitor.getClientOffset();
      const {x: dragX, y: dragY} = offset;
      const viewHandle = findNodeHandle(currentEle);
      const bounds = viewHandle?.getBoundingClientRect();
      const {x, y, width, height} = bounds;
      const offsetX = dragX - x;
      const offsetY = dragY - y;
      let positionName;
      if (parentFlexDirection === 'row' || parentFlexDirection === 'row-reverse')
        positionName = offsetX > width / 2 ? 'right' : 'left';
      else {
        positionName = offsetY > height / 2 ? 'bottom' : 'top';
      }
      const beforeRefWidget = ['top', 'left'].includes(positionName);
      const afterRefWidget = ['right', 'bottom'].includes(positionName);

      const siblings = Array.from(parentWidget?.children ?? []).filter(i => i.tagName !== 'STYLE');
      const currEleIndex = siblings.findIndex(i => i.id === currentEle.id);

      setPosition(positionName);
      setIsTopOrBottom(
        !!((currEleIndex === 0 && beforeRefWidget) || (currEleIndex === siblings.length - 1 && afterRefWidget)),
      );

      let payload: MoveWidgetPayload | CreateWidgetPayload;
      payload = {
        ...item.payload,
        afterRefWidget,
        refWidget: id,
        container: isContainer && !isModule ? id : containerId,
        lastHoveredItem,
      };
      if (isCreateWidget(item)) payload = {...payload, pageId};

      if (
        prevDrag?.container === payload.container &&
        prevDrag?.refWidget === payload.refWidget &&
        prevDrag?.afterRefWidget === payload.afterRefWidget &&
        _.isEqual(prevDrag?.lastHoveredItem?.lastHoverCoordinates, monitor.getClientOffset())
      ) {
        // nothing changed return
        return;
      }
      item.prevDrag = item.payload;
      item.payload = payload;
    },
    [containerId, id, isContainer, isModule, isTilesOnly, pageId, parentWidget, widget, widgetType],
  );
  const hoverCallback = useCallback(
    (item, monitor): any => {
      if (monitor.isOver({shallow: true}) /*|| item?.payload?.container === containerId */) {
        handleWidgetHover(item, monitor);
      }
    },
    [handleWidgetHover],
  );
  const throttledHoverCallback = useMemo(() => throttle(hoverCallback, 66), [hoverCallback]);
  const throttledHover = useCallback(
    (item, monitor) => {
      throttledHoverCallback(item, monitor);
    },
    [throttledHoverCallback],
  );

  const [{isOver, isOverCurrentItem, canDrop}, drop] = useDrop({
    accept: DragDropItemTypes.WIDGET,
    collect(monitor) {
      return {
        canDrop: monitor.canDrop(),
        isOver: monitor.isOver(),
        isOverCurrentItem: monitor.isOver({shallow: true}),
      };
    },
    canDrop: () => isContainer,
    hover: throttledHover,
  });

  const [{item}, drag, dragPreviewRef] = useDrag({
    type: DragDropItemTypes.WIDGET,
    item: () => {
      const dragItem = {
        type: 'widget',
        action: 'move',
        payload: {
          pluginId: id,
          pluginType: widgetType as PluginSubType,
          pageId: pageId,
          container: layout.container,
        },
      };
      return dragItem;
    },
    collect(monitor) {
      return {
        item: monitor.getItem(),
      };
    },
    canDrag(monitor) {
      const currentEle = elementRef?.current;
      if (!currentEle) return false;
      // const {x, y, width, height} = currentEle.getBoundingClientRect();
      // const {x: clientX, y: clientY} = monitor.getInitialClientOffset();
      // const isHandlerDragged = !(x < clientX && clientX < x + width && y < clientY && clientY < y + height);
      // if (!isHandlerDragged) return false;
      return true;
    },
    end: onDragEnd,
  });

  useEffect(() => {
    dragPreviewRef(getEmptyImage(), {captureDraggingState: true});
  }, [dragPreviewRef]);

  const getClasses = useCallback(
    () =>
      _.reject(
        [
          'widget',
          !isDragActive && isHovered && 'widget__hover',
          selected && 'widget__selected',
          isHoverSource && 'widget__hover_source',
          isDragActive && isHoverSource && 'widget__drag_source',
          isOver && canDrop && 'widget__over_droppable',
          isOverCurrentItem && canDrop && 'widget__over_drop_target',
          isOverCurrentItem && (!canDrop || isModule) && 'widget__over_drop_disabled_item',
        ],
        x => !x,
      ),
    [canDrop, isDragActive, isHoverSource, isHovered, isModule, isOver, isOverCurrentItem, selected],
  );

  const setNativeElementProps = useCallback(() => {
    const refHandle = elementRef?.current && findNodeHandle(elementRef.current);
    if (!refHandle) return;
    const cssCode = `${iconCharacters[widgetType?.toLowerCase().replace('widget', '')] || '≅'}`;
    refHandle.onclick = e => {
      // const {x, y, width, height} = e.target.getBoundingClientRect();
      // const isHandlerClicked = !(x < e.clientX && e.clientX < x + width && y < e.clientY && e.clientY < y + height);
      // if (isHandlerClicked) selectPlugin([pageKey, 'plugins', id]);
      // else props.onClick && props.onClick(e);
      selectPlugin([pageKey, 'plugins', id]);
      e.stopPropagation();
    };
    // const overflow = getComputedStyle(refHandle).getPropertyValue('overflow');
    refHandle.style.position =
      getComputedStyle(refHandle).getPropertyValue('position') === 'absolute' ? 'absolute' : 'relative';
    refHandle.classList.remove(
      'widget__hover',
      'widget__selected',
      'widget__hover_source',
      'widget__drag_source',
      'widget__over_droppable',
      'widget__over_drop_target',
      'widget__over_drop_disabled_item',
    );
    refHandle.classList.add(...getClasses());
    refHandle.setAttribute('data-handler-text', isHoverSource || isOverCurrentItem ? `${cssCode} ${idkey}` : cssCode);
    if (overlayRef.current) {
      if (parentWidget instanceof HTMLElement && refHandle instanceof HTMLElement) {
        if (isModule && moduleMandatoryField) {
          const currentEleDimentions = getUpdatedOverlayDimentions();
          const currentOverlay = overlayRef.current;
          const currentEleStyle = getComputedStyle(currentOverlay);
          const currentEleWidth = parseInt(currentEleStyle.getPropertyValue('width'), 10);
          const currentEleHeight = parseInt(currentEleStyle.getPropertyValue('height'), 10);
          const currentOverlayDimentions = {width: currentEleWidth, height: currentEleHeight};
          if (!_.isEqual(currentOverlayDimentions, currentEleDimentions)) {
            setUpdatedOverlayDimentions(currentEleDimentions);
          }
        }
      }

      overlayRef.current.onclick = e => {
        if (usingProTile) {
          setOpenPremiumModal(true, variantGatingState ?? ' ');
        }
        selectPlugin([pageKey, 'plugins', id]);
        e.stopPropagation();
      };
    }
  }, [
    widgetType,
    getClasses,
    isHoverSource,
    isOverCurrentItem,
    idkey,
    selectPlugin,
    pageKey,
    id,
    isModule,
    moduleMandatoryField,
    getUpdatedOverlayDimentions,
    usingProTile,
    setOpenPremiumModal,
  ]);

  const setElementRef = useCallback(
    ref => {
      if (ref) {
        const refHandle = ref && findNodeHandle(ref);
        if (refHandle) {
          // console.dir(refHandle);
          if (isMovable) {
            drag(drop(refHandle));
          }
          refHandle.id = idkey;
          elementRef.current = refHandle;
          // Create a new ref for Hover.
          const hoverRef = React.createRef();
          hoverRef.current = refHandle;
          setHoverRef(hoverRef);
        }
      }
    },
    [isMovable, idkey, setHoverRef, drag, drop],
  );
  const setElementOverlayRef = useCallback(
    ref => {
      if (ref) {
        const refHandle = ref && findNodeHandle(ref);
        if (refHandle) {
          if (isMovable) {
            drag(drop(refHandle));
          }
          refHandle.id = idkey;
          overlayRef.current = refHandle;
        }
      }
    },
    [drag, drop, idkey, isMovable],
  );

  useEffect(() => {
    if (elementRef.current) setNativeElementProps();
  });

  const getUpdatedOverlayDimentions = useCallback(() => {
    const updatedMandatoryDimentions = {height: -22, width: -3};
    const currentEle = elementRef?.current;
    if (parentWidget instanceof HTMLElement && currentEle instanceof HTMLElement) {
      const currentEleStyle = getComputedStyle(currentEle);
      const currentEleWidth = parseInt(currentEleStyle.getPropertyValue('width'), 10);
      const currentEleHeight = parseInt(currentEleStyle.getPropertyValue('height'), 10);
      const currentEleMarginWidth =
        parseInt(currentEleStyle.getPropertyValue('margin-left'), 10) +
        parseInt(currentEleStyle.getPropertyValue('margin-right'), 10);
      const currentEleMarginHeight =
        parseInt(currentEleStyle.getPropertyValue('margin-top'), 10) +
        parseInt(currentEleStyle.getPropertyValue('margin-bottom'), 10);
      updatedMandatoryDimentions.width = isNaN(currentEleWidth)
        ? 0
        : currentEleWidth + (isNaN(currentEleMarginWidth) ? 0 : currentEleMarginWidth);
      updatedMandatoryDimentions.height = isNaN(currentEleHeight)
        ? 0
        : currentEleHeight + (isNaN(currentEleMarginHeight) ? 0 : currentEleMarginHeight);
    }
    updatedMandatoryDimentions.height+=36
    return updatedMandatoryDimentions;
  }, [parentWidget]);


  const [updatedOverlayDimentions, setUpdatedOverlayDimentions] = useState(getUpdatedOverlayDimentions());
  useEffect(() => setUpdatedOverlayDimentions(getUpdatedOverlayDimentions()), [getUpdatedOverlayDimentions]);

  try {
    if (!children) return null;
    return isEditable ? (
      <React.Fragment key={idkey}>
        {(!isModule || (isModule && !moduleMandatoryField)) &&
          React.cloneElement(Children.only(children), {
            ref: setElementRef,
          })}

        {isModule && moduleMandatoryField && (
          <View
            style={{flexGrow: 0, flexShrink: 1, flexBasis: 'auto'}}
            onLayout={() => {
              setUpdatedOverlayDimentions(getUpdatedOverlayDimentions());
            }}>
            <View style={[styles.topBar, styles.centeredView]}>
              <Text style={[styles.w70, styles.text]}>Setup your Tile</Text>
              <MaterialCommunityIcons name="arrow-right" color={theme.PRIMARY_COLOR} size={theme.LINE_HEIGHT_ALERT} />
            </View>
            {React.cloneElement(Children.only(children), {
              ref: setElementRef,
            })}
            <View
              // eslint-disable-next-line react-native/no-inline-styles
              style={[
                {
                  width: updatedOverlayDimentions.width,
                  height: updatedOverlayDimentions.height,
                },
                styles.container,
                styles.centeredView,
              ]}
              ref={setElementOverlayRef}>
              {/* <View style={[styles.borderDots, {top: -2, right: -2}]} />
                <View style={[styles.borderDots, {top: -2, left: -2}]} />
                <View style={[styles.borderDots, {bottom: -2, right: -2}]} />
                <View style={[styles.borderDots, {bottom: -2, left: -2}]} /> */}
            </View>
          </View>
        )}
        {isModule && !moduleMandatoryField && usingProTile && (
          <Pressable
            style={{flexGrow: 0, flexShrink: 1, flexBasis: 'auto'}}
            onLayout={() => {
              setUpdatedOverlayDimentions(getUpdatedOverlayDimentions());
            }}>
            {React.cloneElement(Children.only(children), {
              ref: setElementRef,
            })}
            <View
              // eslint-disable-next-line react-native/no-inline-styles
              style={[
                {
                  width: updatedOverlayDimentions.width,
                  height: updatedOverlayDimentions.height,
                },
                styles.lightContainer,
                styles.lightCenteredView,
              ]}
              ref={setElementOverlayRef}>
              <Button variant="PILL" color="PRIMARY" containerStyles={{backgroundColor: '#fff'}}>
                Upgrade To Pro
              </Button>
            </View>
          </Pressable>
        )}
      </React.Fragment>
    ) : (
      children
    );
  } catch (e) {
    logger.error(e);
    return <Text>Error Rendering {idkey}</Text>;
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 1000,
    border: 'solid 2px #1060E0',
    cursor: 'pointer',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  lightContainer: {
    position: 'absolute',
    zIndex: 1000,
    cursor: 'pointer',
  },
  lightCenteredView: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundImage: 'linear-gradient(to bottom, #0003 0%, #0000 20%, #0000 80%,#0006 100%)',
    paddingBottom: 20,
  },
  w70: {
    width: '70%',
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
    color: theme.PRIMARY_COLOR,
    fontWeight: theme.FONT_WEIGHT,
    fontSize: theme.LINE_HEIGHT_ALERT,
    lineHeight: theme.LINE_HEIGHT_ALERT,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 23, 
  },
  borderDots: {
    position: 'absolute',
    padding: 4,
    border: 'solid 2px #1060E0',
    zIndex: 1000,
    backgroundColor: '#fff',
  },
  topBar: {
    flexDirection: 'row',
    height: 36,
    backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    flexBasis: 'auto',
  }
});

const mapDispatchToProps = (dispatch: any) => {
  return bindActionCreators({selectPlugin, setOpenPremiumModal}, dispatch);
};
const ConnectedEditableWidget = connect(null, mapDispatchToProps)(EditableWidget);

const EditableWidgetTilesModeHOC = props => {
  const {widget, isEditable, children} = props;
  const {layout, id, instance, pageKey, widgetType, selected, inModule, children: widgetChildren} = widget;
  const isTilesOnly = useSelector(selectAppIsTilesOnly);
  const isModule = widgetType === 'ModuleInstance';
  const isEmptyContainer =
    widgetType === 'ContainerWidget' &&
    (widgetChildren?.length === 0 || (!widgetChildren && children?.props?.children?.length == 0));
  let editable = isEditable && (!isTilesOnly || (isTilesOnly && (isModule || isEmptyContainer)));
  return editable ? <ConnectedEditableWidget {...props} /> : <>{children}</>;
};

export default EditableWidgetTilesModeHOC;
