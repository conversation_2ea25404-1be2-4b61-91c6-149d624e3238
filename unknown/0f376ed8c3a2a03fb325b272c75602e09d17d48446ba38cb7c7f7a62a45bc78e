diff --git a/node_modules/react-native/Libraries/Lists/FlatList.js b/node_modules/react-native/Libraries/Lists/FlatList.js
index 1b2ee17..eb17551 100644
--- a/node_modules/react-native/Libraries/Lists/FlatList.js
+++ b/node_modules/react-native/Libraries/Lists/FlatList.js
@@ -420,6 +420,7 @@ class FlatList<ItemT> extends React.PureComponent<Props<ItemT>, void> {
 
   constructor(props: Props<ItemT>) {
     super(props);
+    this.props = props;
     this._checkProps(this.props);
     if (this.props.viewabilityConfigCallbackPairs) {
       this._virtualizedListPairs =
