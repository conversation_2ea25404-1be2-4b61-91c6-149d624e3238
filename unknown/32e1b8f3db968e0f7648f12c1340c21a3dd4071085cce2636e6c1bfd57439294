import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';

export default class AppApi {
  static baseURL = '/api/app';
  static getApiUrl() {
    return Api.API_SERVER + AppApi.baseURL;
  }

  static fetchCurrentApp(): AxiosPromise<AppConfigResponse> {
    return Api.get(AppApi.getApiUrl() + `/my-app`);
  }

  static updateCurrentApp(
    appId: number,
    {publishedAppSaveId}: {publishedAppSaveId: number},
  ): AxiosPromise<AppConfigResponse> {
    return Api.put(AppApi.getApiUrl() + `/${appId}`, {publishedAppSaveId});
  }

  static updateBasicAppInfo(
    appId: number,
    infoObject: {isOnboarded?: boolean; isEditorOnboarded?: boolean; name?: string},
  ): AxiosPromise<AppConfigResponse> {
    return Api.put(AppApi.getApiUrl() + `/updateBasicInfo/${appId}`, infoObject);
  }
}
