#import "PreviewDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTLinkingManager.h>

@implementation PreviewDelegate

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  NSURL *jsCodeLocation;
  NSBundle *main = [NSBundle mainBundle];
  jsCodeLocation = [main URLForResource:@"main" withExtension:@"bundle"];
  [RNDynamicBundle setDefaultBundleURL:jsCodeLocation];

  NSString *APPTILE_BASE_FRAMEWORK_VERSION = [[NSBundle mainBundle].infoDictionary objectForKey:@"APPTILE_BASE_FRAMEWORK_VERSION"];
  NSString *activeBundle = [NSString stringWithFormat:@"%@", [RNDynamicBundle _getActiveBundle] ?: @""];
  if (APPTILE_BASE_FRAMEWORK_VERSION.length && activeBundle.length && [self compareVersions:APPTILE_BASE_FRAMEWORK_VERSION withVersion:activeBundle] > 0) {
      [RNDynamicBundle _setActiveBundle:nil];
  }
 
  return [RNDynamicBundle resolveBundleURL]; 
}

- (void)dynamicBundle:(RNDynamicBundle *)dynamicBundle requestsReloadForBundleURL:(NSURL *)bundleURL
{
  self.window.rootViewController.view = [self getRootViewForBundleURL:bundleURL];
}

- (RCTRootView *)getRootViewForBundleURL:(NSURL *)bundleURL
{
  NSURL *initialUrl = self.launchOptions[@"UlApplicationLaunchOptionsURLKey"];

  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:self.launchOptions];
  RNDynamicBundle *dynamicBundle = [bridge moduleForClass:[RNDynamicBundle class]];
  dynamicBundle.delegate = self.delegate;
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge moduleName:@"Apptile" initialProperties:@{@"initialUrl": [NSString stringWithFormat:@"%@",initialUrl]}];
  
  if (@available(iOS 13.0, *)) {
      rootView.backgroundColor = [UIColor systemBackgroundColor];
  } else {
      rootView.backgroundColor = [UIColor whiteColor];
  }
  
  return rootView;
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
  return [RCTLinkingManager application:app openURL:url options:options];
}

- (NSInteger)compareVersions:(NSString *)version1 withVersion:(NSString *)version2 {
    NSArray *parts1 = [version1 componentsSeparatedByString:@"."];
    NSArray *parts2 = [version2 componentsSeparatedByString:@"."];

    for (NSInteger i = 0; i < MIN(parts1.count, parts2.count); i++) {
        NSInteger part1 = [parts1[i] integerValue];
        NSInteger part2 = [parts2[i] integerValue];

        if (part1 < part2) {
            return -1;
        } else if (part1 > part2) {
            return 1;
        }
    }
    return 0;
}


@end
