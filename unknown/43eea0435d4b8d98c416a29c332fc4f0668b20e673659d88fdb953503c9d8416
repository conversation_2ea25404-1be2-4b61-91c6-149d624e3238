# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip
 
platform :ios, min_ios_version_supported
prepare_react_native_project!

$RNFirebaseAsStaticFramework = true


# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled
 
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end


target 'ImageNotification' do
  use_frameworks!
  pod 'Firebase/Messaging'
  # to avoid GoogleUtilities version conflict since firebase analytics and firebase Messaging can have different version of GoogleUtilities
  pod 'GoogleUtilities'
  # KlaviyoDependency (Don't remove) pod 'KlaviyoSwiftExtension'
  # MoengageDependency (Don't remove) pod 'MoEngageRichNotification'
  # ForCleverTap (Don't remove) pod 'CTNotificationService'
  # OneSignalDependency (Don't remove) pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
end

target 'NotificationContentExtension' do
  use_frameworks!
  # MoengageDependency (Don't remove) pod 'MoEngageRichNotification'
  # ForCleverTap (Don't remove) pod 'CTNotificationContent'
end


target 'ReactNativeTSProject' do
  use_frameworks!
  config = use_native_modules!
  # ForCleverTap (Don't remove) pod 'clevertap-react-native', :path => '../node_modules/clevertap-react-native'

  use_react_native!(
    :path => config[:reactNativePath],

    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    # :flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",

    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  # to avoid GoogleUtilities version conflict since firebase analytics and firebase Messaging can have different version of GoogleUtilities
  pod 'GoogleUtilities'
  pod 'Firebase/Analytics'
  # FireworkDependency (Don't remove) pod 'FireworkVideoIVSSupport', '0.4.0'

  # KlaviyoDependency (Don't remove) pod 'react-native-klaviyo', :path => '../node_modules/react-native-klaviyo'

  #END to avoid GoogleUtilities version conflict since firebase analytics and firebase Messaging can have different version of GoogleUtilities
  # pod 'RNSentry', :path => '../node_modules/@sentry/react-native/RNSentry.podspec'

  pre_install do |installer|
    Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
    installer.pod_targets.each do |pod|
      if pod.name.eql?('RNPermissions') || pod.name.start_with?('Permission-') || pod.name.eql?('RNReanimated') || pod.name.eql?('RNScreens')
        def pod.build_type;
          # Uncomment the line corresponding to your CocoaPods version
          Pod::BuildType.static_library # >= 1.9
          # Pod::Target::BuildType.static_library # < 1.9
        end
      end
    end
  end
  permissions_path = '../node_modules/react-native-permissions/ios'
  # pod 'Permission-AppTrackingTransparency', :path => "#{permissions_path}/AppTrackingTransparency"
  # pod 'Permission-Camera', :path => "#{permissions_path}/Camera"

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    installer.pods_project.build_configurations.each do |config|
      config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
      #to make the pods work with xcode 15+
      xcode_base_version = `xcodebuild -version | grep 'Xcode' | awk '{print $2}' | cut -d . -f 1`

      installer.pods_project.targets.each do |target|
          target.build_configurations.each do |config|
              # For xcode 15+ only
               if config.base_configuration_reference && Integer(xcode_base_version) >= 15
                  xcconfig_path = config.base_configuration_reference.real_path
                  xcconfig = File.read(xcconfig_path)
                  xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
                  File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
              end

                # KlaviyoDependency (Don't remove) config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
          end
      end
    end
    # installer.pods_project.targets.each do |target|
    #   case target.name
    #   when 'RCT-Folly'
    #     target.build_configurations.each do |config|
    #       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '9.0'
    #     end
    #   end
    # end
  end
  
  # ...but if you bump iOS deployment target, Flipper barfs again "Time.h:52:17: error: typedef redefinition with different types"
  # We need to make one crude patch to RCT-Folly - set `__IPHONE_10_0` to our iOS target + 1
  # https://github.com/facebook/flipper/issues/834 - 84 comments and still going...
  `sed -i -e  $'s/__IPHONE_10_0/__IPHONE_12_0/' Pods/RCT-Folly/folly/portability/Time.h`
end
