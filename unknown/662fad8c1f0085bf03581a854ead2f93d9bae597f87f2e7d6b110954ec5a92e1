{
	"rn-component": {
		"scope": "typescriptreact",
		"prefix": [
			"apptile-component"
		],
		"body": [
			"import React from 'react';",
			"import {StyleSheet, View} from 'react-native';",
			"",
			"type ${TM_FILENAME_BASE}Props = {};",
			"",
			"const styles = StyleSheet.create({});",
			"",
			"const ${TM_FILENAME_BASE} = React.forwardRef<View, ${TM_FILENAME_BASE}Props>((props, ref) => {",
			"  const {} = props;",
			"  return ${1:null};",
			"});",
			"",
			"export default ${TM_FILENAME_BASE};",
			"",
		],
		"description": "React functional component in our code structure."
	},
	"rn-sub-component": {
		"scope": "typescriptreact",
		"prefix": [
			"apptile-sub-component"
		],
		"body": [
			"type ${1:ComponentName}Props = {};",
			"const ${1:ComponentName}: React.FC<${1:ComponentName}Props> = props => {",
			"  const {} = props;",
			"  return null;",
			"};",
		],
		"description": "React functional sub-component, to be used along with apptile-component."
	}
}