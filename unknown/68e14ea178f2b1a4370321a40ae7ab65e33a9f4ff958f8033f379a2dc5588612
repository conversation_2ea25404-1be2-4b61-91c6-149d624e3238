import _ from 'lodash';
import appsFlyer from 'react-native-appsflyer';

const prevEvent: [any, any] = [null, null];

export const sendToAppsflyer = (eventName: string, eventData: Record<string, any>) => {
  if (!eventName) return;
  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;
  if (_.isEqual(prevEvent, [eventName, _eventData])) return;
  prevEvent[0] = eventName;
  prevEvent[1] = _eventData;

  logger.info('sendToAppsflyer', eventName, _eventData);

  let appsFlyerEvent = {};
  if (_eventData.jsonData) {
    try {
      const _jsonData = JSON.parse(_eventData.jsonData);
      appsFlyerEvent = {..._eventData, ..._jsonData};
    } catch (e) {
      /* ignore */
    }
  }
  appsFlyer.logEvent(
    eventName,
    appsFlyerEvent,
    result => {
      logger.info('appsFlyer.logEvent', result);
    },
    error => {
      logger.error('appsFlyer.logEvent', error);
    },
  );
};
