import { NativeModules, Platform } from "react-native";

const { RNGetValues } = NativeModules;

const iOSGetter = (key: string) => {
  return new Promise(function (resolve, reject) {
    RNGetValues.getKey(key, (error: Error, data: any) => {
      if (error) {
        reject(error);
      } else {
        resolve(data);
      }
    });
  });
};

const androidGetter = (key: string) => {
  return new Promise(function (resolve, reject) {
    return RNGetValues.getKey(
      key,
      (error: Error) => {
        reject(error);
      },
      (data: any) => {
        resolve(data);
      }
    );
  });
};

const getEmbeddedKey = Platform.OS === "ios" ? iOSGetter : androidGetter;

export default getEmbeddedKey;
