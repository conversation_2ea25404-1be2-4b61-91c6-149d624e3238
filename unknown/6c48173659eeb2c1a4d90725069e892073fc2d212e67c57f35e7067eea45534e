import axios, { AxiosPromise } from 'axios';
import _ from 'lodash';
import { Api } from './Api';
import { AppConfigResponse } from '../../../apptile-core/api/ApiTypes';

export class CartAssistApi {
  static baseURL = '/shopify-shop-manager/cart-assist/customer'
  static proxyURL = '/shopify-app-proxy/cart-sync'
  static getApiUrl() {
    return Api.API_SERVER + CartAssistApi.baseURL
  }
  static searchCustomers(appId: string, query: string, first: number, namespace: string): AxiosPromise<AppConfigResponse> {
    return Api.post(CartAssistApi.getApiUrl() + `/search`, {
      query,
      first,
      namespace
    }, {
      headers: {
        'x-shopify-app-id': appId,
      },
    })
  }
  static getCustomer(appId: string, customerId: string, namespace: string): AxiosPromise<AppConfigResponse> {
    return Api.post(CartAssistApi.getApiUrl() + `/metafields`, {
      customerId,
      namespace
    }, {
      headers: {
        'x-shopify-app-id': appId,
      },
    })
  }
  static async updateMetafield(appId: string, customerId: string, cartId: string) {
    return Api.post(CartAssistApi.getApiUrl() + `/update`, { 
      cartId,
      customerId
    }, {
      headers: {
        'x-shopify-app-id': appId,
      }
    })
  }
}