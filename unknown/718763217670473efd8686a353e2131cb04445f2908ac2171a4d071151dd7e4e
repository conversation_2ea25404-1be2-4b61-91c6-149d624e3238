import {useEffect, useState} from 'react';
import {Linking} from 'react-native';

export const useInitialURL = (_initialUrl = null) => {
  const [url, setUrl] = useState<string | null>(_initialUrl);
  const [processing, setProcessing] = useState(true);

  useEffect(() => {
    const getUrlAsync = async () => {
      const initialUrl = await Linking.getInitialURL();
      logger.info('Initial URL: ', initialUrl);

      if (initialUrl) setUrl(initialUrl);
      setProcessing(false);
    };

    getUrlAsync();
  }, []);

  return {url, processing};
};
