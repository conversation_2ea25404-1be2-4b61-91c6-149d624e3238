#import <React/RCTBridgeDelegate.h>
#import <UIKit/UIKit.h>
#import <Firebase.h>

#import "RNDynamicBundle.h"
#import "RNGetValues.h"

@class RCTRootView;

@protocol PreviewDelegate <NSObject>

- (void)dynamicBundle:(RNDynamicBundle *)dynamicBundle requestsReloadForBundleURL:(NSURL *)bundleURL;

@end

@interface PreviewDelegate : UIResponder <UIApplicationDelegate, RNDynamicBundleDelegate, RCTBridgeDelegate, FIRMessagingDelegate, UNUserNotificationCenterDelegate>

@property (weak) id<PreviewDelegate> delegate;
@property (nonatomic, strong) UIWindow *window;
@property (nonatomic, strong) NSDictionary *launchOptions;

- (RCTRootView *)getRootViewForBundleURL:(NSURL *)bundleURL;

@end
