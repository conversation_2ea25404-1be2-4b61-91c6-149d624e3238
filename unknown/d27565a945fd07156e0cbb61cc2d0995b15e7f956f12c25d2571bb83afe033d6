import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {UploadAppAsset} from '../actions/editorActions';
import {FetchCloudinaryVideosResponse} from './ApiTypes';

export default class UploaderApi {
  static baseURL = '/api/uploader/v1';
  static getApiUrl() {
    return Api.API_SERVER + UploaderApi.baseURL;
  }

  static UploadAssetApi(
    {file, metaData}: UploadAppAsset,
    onUploadProgress: ({total, loaded}) => void,
  ): AxiosPromise<AppConfigResponse> {
    const data = new FormData();

    data.append('files', file);
    if (metaData) data.append('metaData', JSON.stringify(metaData));

    // return Api.post(AssetsApi.getApiUrl() + `/upload`);
    return Api.post(UploaderApi.getApiUrl() + '/upload', data, {onUploadProgress});
  }

  static GetCloudinaryVideos(orgId: string, nextCursor?: string): AxiosPromise<FetchCloudinaryVideosResponse> {
    return Api.get(
      UploaderApi.getApiUrl() + `/cloudinary-uploader/${orgId}` + (nextCursor ? `?nextCursor=${nextCursor}` : ''),
    );
  }
}
