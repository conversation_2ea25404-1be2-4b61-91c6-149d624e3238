import {debounce, throttle} from 'lodash';
import React, {Ref, useCallback, useMemo, useRef} from 'react';
import {useDrop} from 'react-dnd';
import {findNodeHandle} from 'react-native';
import {
  isCreateWidget,
  DragDropItemTypes,
  CreateWidget,
  CreateWidgetPayload,
  isMoveWidget,
  MoveWidget,
  MoveWidgetPayload,
} from 'apptile-core';
import {ApptileFlexboxProps} from '../../ApptileFlexbox';

export const useFlexBoxDropHandler = (_ApptileFlexbox: React.Component<ApptileFlexboxProps>) =>
  React.forwardRef((props: ApptileFlexboxProps, forwardedRef: Ref<any>) => {
    const viewRef = useRef();
    const {containerId, pageId, previewWidgetAdd, previewWidgetMove, undoPreview} = props;

    const hoverCallback = useCallback((item, monitor): any => {
      if (monitor.isOver({shallow: true}) || item?.payload?.container === containerId) {
        if (isCreateWidget(item)) {
          return handleCreateWidgetHover(item, monitor);
        } else if (isMoveWidget(item)) {
          return handleMoveWidgetHover(item, monitor);
        }
      }
    }, []);
    const throttledHoverCallback = useMemo(() => throttle(hoverCallback, 66), [hoverCallback]);
    const throttledHover = useCallback((item, monitor) => {
      throttledHoverCallback(item, monitor);
    }, []);

    const handleCreateWidgetHover = useCallback((item: CreateWidget, monitor) => {
      const {prevDrag} = item;

      const payload: CreateWidgetPayload = {
        ...item.payload,
        pageId,
        container: containerId,
      };

      if (
        prevDrag?.container == payload.container &&
        prevDrag?.refWidget == payload.refWidget &&
        prevDrag?.afterRefWidget == payload.afterRefWidget
      ) {
        // nothing changed return
        return;
      }
      item.prevDrag = item.payload;
      item.payload = payload;
      // logger.info('FlexBox Drag hover: ', item.payload);
      return previewWidgetAdd(item, monitor);
    }, []);

    const handleMoveWidgetHover = useCallback((item: MoveWidget, monitor) => {
      const {
        prevDrag,
        payload: {pluginId},
      } = item;
      if (pluginId === containerId) return;

      const payload: MoveWidgetPayload = {
        ...item.payload,
        container: containerId,
      };

      if (
        prevDrag?.container == payload.container &&
        prevDrag?.refWidget == payload.refWidget &&
        prevDrag?.afterRefWidget == payload.afterRefWidget
      ) {
        // nothing changed return
        return;
      }
      item.prevDrag = item.payload;
      item.payload = payload;
      // logger.info('FlexBox Drag hover: ', item.payload);
      return previewWidgetMove(item, monitor);
    }, []);

    const [{isOver}, dropRef] = useDrop({
      accept: DragDropItemTypes.WIDGET,
      drop: (item, monitor) => {
        if (!monitor.getDropResult()) {
          logger.info(`Received in ${containerId}`, item);
          return {msg: `Received`};
        } else return null;
      },
      hover: throttledHover,
      collect: monitor => ({
        isOver: !!monitor.isOver(),
      }),
    });

    const setDropRefs = useCallback(ref => {
      viewRef.current = ref;
      if (forwardedRef) typeof forwardedRef === 'object' ? forwardedRef.current = ref : forwardedRef(ref);
      dropRef(ref && findNodeHandle(ref));
    }, []);

    return <_ApptileFlexbox {...props} forwardedRef={setDropRefs} />;
  });
