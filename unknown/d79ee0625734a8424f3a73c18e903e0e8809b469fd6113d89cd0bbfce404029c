import {FirebaseMessagingTypes} from '@react-native-firebase/messaging';
import {PermissionsAndroid, Platform} from 'react-native';
import PushNotification from 'react-native-push-notification';

const createChannel = async () => {
  const channelObject = {
    channelId: 'fcm_fallback_notification_channel',
    channelName: 'Default',
  };
  return new Promise((resolve, reject) => {
    PushNotification.createChannel(channelObject, created => {
      resolve(created);
      logger.info(`createChannel Notification returned: ${created}`);
    });
  });
};

PushNotification.getChannels(function (channel_ids) {
  logger.info(`Notification Channels: `, channel_ids); // ['channel_id_1']
});

export const showLocalNotification = async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
  logger.info(`showLocalNotification: ${JSON.stringify(remoteMessage)}`);
  PushNotification.localNotification({
    channelId: 'fcm_fallback_notification_channel',
    message: remoteMessage.notification?.body as string,
    title: remoteMessage.notification?.title,
    bigPictureUrl: remoteMessage.notification?.android?.imageUrl,
    smallIcon: remoteMessage.notification?.android?.imageUrl,
    userInfo: remoteMessage?.data,
  });
};

export const checkNotificationPermission = async () => {
  try {
    let permissionStatus = 'granted';
    // The permission exists only for Android API versions bigger than 33 (Android 13),
    // we can assume it's always granted beforehand
    if (Number(Platform.Version) >= 33) {
      permissionStatus = await PermissionsAndroid.request('android.permission.POST_NOTIFICATIONS');
    }

    if (__DEV__) {
      //To test notificaion on emulator.
      createChannel();
    }

    logger.info('Android notification permission status:', permissionStatus);
    return permissionStatus === 'granted';
  } catch (error) {
    logger.error('Android notification permission error');
    return false;
  }
};
