#import "NotificationService.h"
#import "FirebaseMessaging.h"
/* MoengageDependency (Don't remove) @import MoEngageRichNotification; MoengageDependencyEnd */
/* OneSignalDependency (Don't remove) #import <OneSignalExtension/OneSignalExtension.h> OneSignalDependencyEnd */

@interface NotificationService ()

@property (nonatomic, strong) void (^contentHandler)(UNNotificationContent *contentToDeliver);
@property (nonatomic, strong) UNMutableNotificationContent *bestAttemptContent;
@property (nonatomic, strong) UNNotificationRequest *receivedRequest;

@end

@implementation NotificationService

- (void)didReceiveNotificationRequest:(UNNotificationRequest *)request withContentHandler:(void (^)(UNNotificationContent * _Nonnull))contentHandler {
    self.contentHandler = contentHandler;
    self.bestAttemptContent = [request.content mutableCopy];

    /* MoengageDependency (Don't remove) @try {
        [MoEngageSDKRichNotification setAppGroupID: @"group.com.apptile.apptilepreviewdemo.notification"];
        [MoEngageSDKRichNotification handleWithRichNotificationRequest:request withContentHandler:contentHandler];
    } @catch (NSException *exception) {
        NSLog(@"MoEngage : exception : %@",exception);
    } MoengageDependencyEnd */ 

    /* OneSignalDependency (Don't remove) self.receivedRequest = request;
    [OneSignalExtension didReceiveNotificationExtensionRequest:self.receivedRequest withMutableNotificationContent:self.bestAttemptContent withContentHandler:self.contentHandler]; OneSignalDependencyEnd */
    
    [[FIRMessaging extensionHelper] populateNotificationContent:self.bestAttemptContent withContentHandler:contentHandler];
}

- (void)serviceExtensionTimeWillExpire {
    // Called just before the extension will be terminated by the system.
    // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.

    /* OneSignalDependency (Don't remove) [OneSignalExtension serviceExtensionTimeWillExpireRequest:self.receivedRequest withMutableNotificationContent:self.bestAttemptContent]; OneSignalDependencyEnd */

    self.contentHandler(self.bestAttemptContent);
}

@end
