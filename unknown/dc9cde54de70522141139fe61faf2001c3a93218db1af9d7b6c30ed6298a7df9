//
//  Mappers.swift
//  ShopifyApplePay
//
//  Created by <PERSON><PERSON> on 04/10/22.
//

import Foundation
import PassKit

class Mappers {
  class func createResult(_ key: String, _ value: NSDictionary?) -> NSDictionary {
    return [key: value ?? NSNull()]
  }
  
  
  class func mapToShippingMethods(shippingMethods: NSArray?) -> [PKShippingMethod] {
    var shippingMethodsList: [PKShippingMethod] = []
    
    if let methods = shippingMethods as? [[String : Any]] {
      for method in methods {
        let label = method["label"] as? String ?? ""
        let amount = NSDecimalNumber(string: method["amount"] as? String ?? "")
        let identifier = method["identifier"] as! String
        let detail = method["detail"] as? String ?? ""
        let pm = PKShippingMethod.init(
          label: label,
          amount: amount,
          type: method["isPending"] as? Bool ?? false
          ? PKPaymentSummaryItemType.pending : PKPaymentSummaryItemType.final
        )
        pm.identifier = identifier
        pm.detail = detail
        shippingMethodsList.append(pm)
      }
    }
    
    return shippingMethodsList
  }
  
  class func mapFromShippingMethod(shippingMethod: PKShippingMethod) -> NSDictionary {
    
    let method: NSDictionary = [
      "detail": shippingMethod.detail ?? "",
      "identifier": shippingMethod.identifier ?? "",
      "amount": shippingMethod.amount.stringValue,
      "type": shippingMethod.type.rawValue,
      "label": shippingMethod.label,
    ]
    
    return method
  }
  
  class func mapToShippingMethod(apptileShippingMethod: NSDictionary) -> PKShippingMethod {
    let detail = apptileShippingMethod["description"] ?? "" as String
    let identifier = apptileShippingMethod["id"] ?? ""
    let amount = NSDecimalNumber(value: (apptileShippingMethod["amount"] ?? 0) as! Double)
    let label = (apptileShippingMethod["label"] as! String)
    let shippingMethod = PKShippingMethod(label: label, amount: amount)
    shippingMethod.identifier = identifier as! String
    shippingMethod.detail = detail as! String
    return shippingMethod
  }
  
  class func mapFromShippingContact(shippingContact: PKContact) -> NSDictionary {
    
   
    let name: NSDictionary = [
      "familyName": shippingContact.name?.familyName ?? "",
      "namePrefix": shippingContact.name?.namePrefix ?? "",
      "nameSuffix": shippingContact.name?.nameSuffix ?? "",
      "givenName": shippingContact.name?.givenName ?? "",
      "middleName": shippingContact.name?.middleName ?? "",
      "nickname": shippingContact.name?.nickname ?? "",
    ]
    let contact: NSDictionary = [
      "emailAddress": shippingContact.emailAddress ?? "",
      "phoneNumber": shippingContact.phoneNumber?.stringValue ?? "",
      "name": name,
      "postalAddress": [
        "city": shippingContact.postalAddress?.city,
        "country": shippingContact.postalAddress?.country,
        "postalCode": shippingContact.postalAddress?.postalCode,
        "state": shippingContact.postalAddress?.state,
        "street": shippingContact.postalAddress?.street,
        "isoCountryCode": shippingContact.postalAddress?.isoCountryCode,
        "subAdministrativeArea": shippingContact.postalAddress?.subAdministrativeArea,
        "subLocality": shippingContact.postalAddress?.subLocality
      ],
    ]
    
    return contact
  }
  
  
//  class func mapToPayCheckout(rawCheckout: NSDictionary) -> PayCheckout {
//    
//    let checkoutId = rawCheckout["checkoutId"] as? String ?? ""
//    let needsShipping = rawCheckout["needsShipping"] as? Bool ?? false
//    let shippingAddress = PayAddress()
//    let currencyCode = rawCheckout["currencyCode"] as? String ?? ""
//    let paymentDue = NSDecimalNumber(string: rawCheckout["paymentDue"] as? String ?? "")
//    let countryCode = rawCheckout["countryCode"] as? String ?? ""
//    
//    return PayCheckout(checkoutId: checkoutId, needsShipping: needsShipping, shippingAddress: PayAddress(), currencyCode: currencyCode, paymentDue: paymentDue as NSDecimalNumber, countryCode: countryCode)
//  }
  
  class func mapToApptileSession(rawCheckout: NSDictionary) -> ApptileSession {
    let checkoutId = rawCheckout["checkoutId"] as? String ?? ""
    let payeeName = rawCheckout["payeeName"] as? String ?? ""
    let shippingAddress = PayAddress()
    let dictLineItems = rawCheckout["lineItems"] as? [NSDictionary] ?? []
    let lineItems = dictLineItems.map{
      Mappers.mapToApptileLineItem(rawLineItem: $0)
    }
    let currencyCode = rawCheckout["currencyCode"] as? String ?? ""
    let paymentDue = NSDecimalNumber(value: rawCheckout["paymentDue"] as? Double ?? 0)
    let countryCode = rawCheckout["countryCode"] as? String ?? ""
    let requiresShipping = rawCheckout["requiresShipping"] as! Bool
    
    return ApptileSession(checkoutId: checkoutId, payeeName: payeeName, lineItems: lineItems, shippingAddress: PayAddress(), currencyCode: currencyCode, paymentDue: paymentDue, countryCode: countryCode, requiresShipping:requiresShipping)
  }
  
  class func mapToApptileLineItem(rawLineItem: NSDictionary) -> ApptileLineItem {
    let label = rawLineItem["label"] as? String ?? ""
    let price = NSDecimalNumber(value:rawLineItem["amount"] as? Double ?? 0)
    let quantity = NSDecimalNumber(value: rawLineItem["quantity"] as? Double ?? 1)
    
    return ApptileLineItem(label: label, price: price, quantity: quantity)
  }
  
  class func mapFromCNContact(contact: CNContact?) -> NSDictionary! {
    if let contact = contact {
      let name: NSDictionary = [
        "familyName": contact.familyName ?? "",
        "namePrefix": contact.namePrefix ?? "",
        "nameSuffix": contact.nameSuffix ?? "",
        "givenName": contact.givenName ?? "",
        "middleName": contact.middleName ?? "",
        "nickname": contact.nickname ?? "",
      ]
      //    let contact: NSDictionary = [
      //      "emailAddress": contact.emailAddress ?? "",
      //      "phoneNumber": contact.phoneNumber?.stringValue ?? "",
      //      "name": name,
      //      "postalAddress": [
      //        "city": contact.postalAddress?.city,
      //        "country": contact.postalAddress?.country,
      //        "postalCode": contact.postalAddress?.postalCode,
      //        "state": contact.postalAddress?.state,
      //        "street": contact.postalAddress?.street,
      //        "isoCountryCode": contact.postalAddress?.isoCountryCode,
      //        "subAdministrativeArea": contact.postalAddress?.subAdministrativeArea,
      //        "subLocality": contact.postalAddress?.subLocality
      //      ],
      //    ]
      return name
    }
    return nil
  }
  
  class func mapFromPaymentToken(token: PKPaymentToken) -> NSDictionary {
    let paymentTokenDict: NSDictionary = [
      "transactionIdentifier": token.transactionIdentifier,
      "paymentMethod": Mappers.mapFromPaymentMethod (paymentMethod: token.paymentMethod),
      "paymentData": token.paymentData.base64EncodedString()
    ]
    return paymentTokenDict
  }
  
  class func mapFromPaymentMethod(paymentMethod: PKPaymentMethod) -> NSDictionary {
    let paymentMethodDict: NSMutableDictionary = [
      "displayName": paymentMethod.displayName ?? "",
      "network": Mappers.mapFromPaymentNetwork(paymentNetwork: paymentMethod.network!) ?? NSNull(),
      "type": Mappers.mapFromPaymentMethodType(paymentMethodType: paymentMethod.type) ?? NSNull(),
    ]
    if #available(iOS 13.0, *) {
      paymentMethodDict.setValue(_: Mappers.mapFromCNContact(contact: (paymentMethod.billingAddress ?? nil)),forKey: "billingAddress")
    } else {
      // Fallback on earlier versions
    }
    return NSDictionary(dictionary: paymentMethodDict)
  }
  
  class func mapFromPaymentNetwork(paymentNetwork: PKPaymentNetwork?) -> String! {
    if let paymentNetwork = paymentNetwork {
      switch paymentNetwork {
      case PKPaymentNetwork.amex: return "amex"
      case PKPaymentNetwork.masterCard: return "masterCard"
      case PKPaymentNetwork.visa: return "visa"
      case PKPaymentNetwork.discover: return "discover"
      case PKPaymentNetwork.vPay: return "vPay"
      default: return nil
      }
    }
    return nil
  }
  
  class func mapFromPaymentMethodType(paymentMethodType: PKPaymentMethodType?) -> String! {
    if let paymentMethodType = paymentMethodType {
      switch paymentMethodType {
      case PKPaymentMethodType.credit: return "credit"
      case PKPaymentMethodType.debit: return "debit"
      case PKPaymentMethodType.eMoney: return "emoney"
      case PKPaymentMethodType.prepaid: return "prepaid"
      case PKPaymentMethodType.store: return "store"
      case PKPaymentMethodType.unknown: return "unknown"
      default: return nil
      }
    }
    return nil
  }
  
  class func mapFromPaymentAuthorization(payment: PKPayment) -> NSDictionary {
    
    let paymentDict: NSDictionary = [
      "token":  Mappers.mapFromPaymentToken(token: payment.token), // String(data: payment.token.paymentData, encoding: .utf8)!,
      "billingContact":  Mappers.mapFromShippingContact(shippingContact: payment.billingContact!),
      "shippingContact": (payment.shippingContact != nil) ? Mappers.mapFromShippingContact(shippingContact: payment.shippingContact!) : NSNull(),
      "shippingMethod":  (payment.shippingMethod != nil) ? Mappers.mapFromShippingMethod(shippingMethod: payment.shippingMethod!) : NSNull(),
    ]
    return paymentDict
  }
  
  
}

