import Immutable from 'immutable';
import _ from 'lodash';
import {createSelector} from 'reselect';
import {AppModelType} from 'apptile-core';
import {selectStageModel} from 'apptile-core';

export const shopifyModelSelector = createSelector(
  selectStageModel,
  (state, dsType: string) => 'shopifyV_22_10',
  (appModel: AppModelType, dsType: string): Immutable.Map<string, any> => {
    const dsModel = appModel.values?.find((modelVal: Immutable.Map<string, any>, modelKey: stringify) => {
      return dsType === modelVal.get('pluginType', null);
    });
    return dsModel;
  },
);
