const path = require('path');
const zegoRoot = path.resolve(__dirname, "../../zego-express-engine-reactnative");
// console.log("Zego rootpath is: ", zegoRoot);

// For pip don't remove
const enableLivelyPIP = false;
let zegopackage = {};
if (enableLivelyPIP) {
  zegopackage = {
    "zego-express-engine-reactnative": {
      root: zegoRoot,
      "platforms": {
        "ios": {
          "podspecPath": path.resolve(zegoRoot, "react-native-zego-express-engine.podspec"),
          "version": "3.14.5",
          "configurations": [],
          "scriptPhases": []
        },
        "android": {
          "sourceDir": path.resolve(zegoRoot, "android"),
          "packageImportPath": "import im.zego.reactnative.RCTZegoExpressEnginePackage;",
          "packageInstance": "new RCTZegoExpressEnginePackage()",
          "buildTypes": [],
          "componentDescriptors": [],
          "cmakeListsPath": path.resolve(zegoRoot, "android/build/generated/source/codegen/jni/CMakeLists.txt")
        }
      }
    }
  };
}

module.exports = {
  "dependencies": {
    "react-native-reanimated": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    "lottie-react-native": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    "lottie-ios": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    ...zegopackage    
  }
}
