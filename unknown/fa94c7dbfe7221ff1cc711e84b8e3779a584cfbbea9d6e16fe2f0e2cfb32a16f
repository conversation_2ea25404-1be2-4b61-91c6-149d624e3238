import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import _ from 'lodash';
import {useDispatch, useSelector} from 'react-redux';

import {
  navigateToScreen, 
  createDeepEqualSelector,
  Ionicons, 
  MaterialCommunityIcons, 
  performHapticFeedback, 
  selectPluginConfig, 
  datasourceTypeModelSel,
  useTheme,
  goBack
} from 'apptile-core';


const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');

const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);

const HarneyHeader: React.FC = props => {
  const dispatch = useDispatch();
  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);

  const [cartItems, setCartItems] = useState([]);
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);

  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);

  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);

  const navigateBack = useCallback(() => {
    performHapticFeedback('tap');
    goBack();
  }, []);

  const navigateCart = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const {themeEvaluator} = useTheme();
  const primaryColor = themeEvaluator('colors.primary');
  const onPrimaryColor = themeEvaluator('colors.onPrimary');
  const navBorderColor = themeEvaluator('colors.navBorder');

  return (
    <View style={[fixedStyles.headerContainer, {borderColor: navBorderColor}]}>
      <View style={[fixedStyles.leftContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} hitSlop={5} onPress={navigateBack}>
          <MaterialCommunityIcons size={28} name="chevron-left" color="#333" />
        </Pressable>
      </View>
      <View style={[fixedStyles.rightContainer]}>
        <Pressable style={[fixedStyles.headerButtons]} onPress={navigateCart}>
          <Ionicons size={28} name="cart-outline" color="#333" />
          {cartItems?.length ? (
            <View style={[fixedStyles.iconBadge, {backgroundColor: primaryColor}]}>
              <Text style={[fixedStyles.iconBadgeText, {color: onPrimaryColor}]}>
                {cartItems ? _.sumBy(cartItems, v => v?.newQuantity) : 0}
              </Text>
            </View>
          ) : (
            <></>
          )}
        </Pressable>
      </View>
    </View>
  );
};

const fixedStyles = StyleSheet.create({
  headerContainer: {
    minHeight: 55,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
  },

  leftContainer: {
    flexDirection: 'row',
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginLeft: 12,
    flexGrow: 1,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtons: {
    width: 36,
    height: 36,
    padding: 4,
    borderRadius: 12,
    marginRight: 10,
  },
  iconBadge: {
    position: 'absolute',
    right: 2,
    top: 0,
    width: 18,
    height: 18,
    borderRadius: 18,
    backgroundColor: '#000000',
    flex: 1,
    flexGrow: 0,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 2,
    paddingRight: 2,
  },
  iconBadgeText: {
    fontSize: 10,
    color: '#fff',
  },
});

export default HarneyHeader;
